{"Event":"SparkListenerLogStart","Spark Version":"3.3.0-SNAPSHOT"}
{"Event":"SparkListenerResourceProfileAdded","Resource Profile Id":0,"Executor Resource Requests":{"cores":{"Resource Name":"cores","Amount":1,"Discovery Script":"","Vendor":""},"memory":{"Resource Name":"memory","Amount":1024,"Discovery Script":"","Vendor":""},"offHeap":{"Resource Name":"offHeap","Amount":0,"Discovery Script":"","Vendor":""}},"Task Resource Requests":{"cpus":{"Resource Name":"cpus","Amount":1.0}}}
{"Event":"SparkListenerExecutorAdded","Timestamp":1642039451891,"Executor ID":"driver","Executor Info":{"Host":"*************","Total Cores":8,"Log Urls":{},"Attributes":{},"Resources":{},"Resource Profile Id":0}}
{"Event":"SparkListenerBlockManagerAdded","Block Manager ID":{"Executor ID":"driver","Host":"*************","Port":61039},"Maximum Memory":384093388,"Timestamp":1642039451909,"Maximum Onheap Memory":384093388,"Maximum Offheap Memory":0}
{"Event":"SparkListenerEnvironmentUpdate","JVM Information":{"Java Home":"/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre","Java Version":"1.8.0_312 (Azul Systems, Inc.)","Scala Version":"version 2.12.15"},"Spark Properties":{"spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.scheduler.mode":"FIFO","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"},"Hadoop Properties":{"hadoop.service.shutdown.timeout":"30s","yarn.resourcemanager.amlauncher.thread-count":"50","yarn.sharedcache.enabled":"false","fs.s3a.connection.maximum":"48","yarn.nodemanager.numa-awareness.numactl.cmd":"/usr/bin/numactl","fs.viewfs.overload.scheme.target.o3fs.impl":"org.apache.hadoop.fs.ozone.OzoneFileSystem","fs.s3a.impl":"org.apache.hadoop.fs.s3a.S3AFileSystem","yarn.app.mapreduce.am.scheduler.heartbeat.interval-ms":"1000","yarn.timeline-service.timeline-client.number-of-async-entities-to-merge":"10","hadoop.security.kms.client.timeout":"60","hadoop.http.authentication.kerberos.principal":"HTTP/_HOST@LOCALHOST","mapreduce.jobhistory.loadedjob.tasks.max":"-1","yarn.resourcemanager.application-tag-based-placement.enable":"false","mapreduce.framework.name":"local","yarn.sharedcache.uploader.server.thread-count":"50","yarn.nodemanager.log-aggregation.roll-monitoring-interval-seconds.min":"3600","yarn.nodemanager.linux-container-executor.nonsecure-mode.user-pattern":"^[_.A-Za-z0-9][-@_.A-Za-z0-9]{0,255}?[$]?$","tfile.fs.output.buffer.size":"262144","yarn.app.mapreduce.am.job.task.listener.thread-count":"30","yarn.nodemanager.node-attributes.resync-interval-ms":"120000","yarn.nodemanager.container-log-monitor.interval-ms":"60000","hadoop.security.groups.cache.background.reload.threads":"3","yarn.resourcemanager.webapp.cross-origin.enabled":"false","fs.AbstractFileSystem.ftp.impl":"org.apache.hadoop.fs.ftp.FtpFs","hadoop.registry.secure":"false","hadoop.shell.safely.delete.limit.num.files":"100","mapreduce.job.acl-view-job":" ","fs.s3a.s3guard.ddb.background.sleep":"25ms","fs.s3a.retry.limit":"7","mapreduce.jobhistory.loadedjobs.cache.size":"5","fs.s3a.s3guard.ddb.table.create":"false","fs.viewfs.overload.scheme.target.s3a.impl":"org.apache.hadoop.fs.s3a.S3AFileSystem","yarn.nodemanager.amrmproxy.enabled":"false","yarn.timeline-service.entity-group-fs-store.with-user-dir":"false","mapreduce.shuffle.pathcache.expire-after-access-minutes":"5","mapreduce.input.fileinputformat.split.minsize":"0","yarn.resourcemanager.container.liveness-monitor.interval-ms":"600000","yarn.resourcemanager.client.thread-count":"50","io.seqfile.compress.blocksize":"1000000","yarn.nodemanager.runtime.linux.docker.allowed-container-runtimes":"runc","fs.viewfs.overload.scheme.target.http.impl":"org.apache.hadoop.fs.http.HttpFileSystem","yarn.resourcemanager.nodemanagers.heartbeat-interval-slowdown-factor":"1.0","yarn.sharedcache.checksum.algo.impl":"org.apache.hadoop.yarn.sharedcache.ChecksumSHA256Impl","yarn.nodemanager.amrmproxy.interceptor-class.pipeline":"org.apache.hadoop.yarn.server.nodemanager.amrmproxy.DefaultRequestInterceptor","yarn.timeline-service.entity-group-fs-store.leveldb-cache-read-cache-size":"10485760","mapreduce.reduce.shuffle.fetch.retry.interval-ms":"1000","mapreduce.task.profile.maps":"0-2","yarn.scheduler.include-port-in-node-name":"false","yarn.nodemanager.admin-env":"MALLOC_ARENA_MAX=$MALLOC_ARENA_MAX","yarn.resourcemanager.node-removal-untracked.timeout-ms":"60000","mapreduce.am.max-attempts":"2","hadoop.security.kms.client.failover.sleep.base.millis":"100","mapreduce.jobhistory.webapp.https.address":"0.0.0.0:19890","yarn.node-labels.fs-store.impl.class":"org.apache.hadoop.yarn.nodelabels.FileSystemNodeLabelsStore","yarn.nodemanager.collector-service.address":"${yarn.nodemanager.hostname}:8048","fs.trash.checkpoint.interval":"0","mapreduce.job.map.output.collector.class":"org.apache.hadoop.mapred.MapTask$MapOutputBuffer","yarn.resourcemanager.node-ip-cache.expiry-interval-secs":"-1","hadoop.http.authentication.signature.secret.file":"*********(redacted)","hadoop.jetty.logs.serve.aliases":"true","yarn.resourcemanager.placement-constraints.handler":"disabled","yarn.timeline-service.handler-thread-count":"10","yarn.resourcemanager.max-completed-applications":"1000","yarn.nodemanager.aux-services.manifest.enabled":"false","yarn.resourcemanager.system-metrics-publisher.enabled":"false","yarn.resourcemanager.placement-constraints.algorithm.class":"org.apache.hadoop.yarn.server.resourcemanager.scheduler.constraint.algorithm.DefaultPlacementAlgorithm","yarn.sharedcache.webapp.address":"0.0.0.0:8788","fs.s3a.select.input.csv.quote.escape.character":"\\\\","yarn.resourcemanager.delegation.token.renew-interval":"*********(redacted)","yarn.sharedcache.nm.uploader.replication.factor":"10","hadoop.security.groups.negative-cache.secs":"30","yarn.app.mapreduce.task.container.log.backups":"0","mapreduce.reduce.skip.proc-count.auto-incr":"true","fs.viewfs.overload.scheme.target.swift.impl":"org.apache.hadoop.fs.swift.snative.SwiftNativeFileSystem","hadoop.security.group.mapping.ldap.posix.attr.gid.name":"gidNumber","ipc.client.fallback-to-simple-auth-allowed":"false","yarn.nodemanager.resource.memory.enforced":"true","yarn.client.failover-proxy-provider":"org.apache.hadoop.yarn.client.ConfiguredRMFailoverProxyProvider","yarn.timeline-service.http-authentication.simple.anonymous.allowed":"true","ha.health-monitor.check-interval.ms":"1000","yarn.nodemanager.runtime.linux.runc.host-pid-namespace.allowed":"false","hadoop.metrics.jvm.use-thread-mxbean":"false","ipc.[port_number].faircallqueue.multiplexer.weights":"8,4,2,1","yarn.acl.reservation-enable":"false","yarn.resourcemanager.store.class":"org.apache.hadoop.yarn.server.resourcemanager.recovery.FileSystemRMStateStore","yarn.app.mapreduce.am.hard-kill-timeout-ms":"10000","fs.s3a.etag.checksum.enabled":"false","yarn.nodemanager.container-metrics.enable":"true","ha.health-monitor.rpc.connect.max.retries":"1","yarn.timeline-service.client.fd-clean-interval-secs":"60","yarn.resourcemanager.nodemanagers.heartbeat-interval-scaling-enable":"false","yarn.resourcemanager.nodemanagers.heartbeat-interval-ms":"1000","hadoop.common.configuration.version":"3.0.0","fs.s3a.s3guard.ddb.table.capacity.read":"0","yarn.nodemanager.remote-app-log-dir-suffix":"logs","yarn.nodemanager.container-log-monitor.dir-size-limit-bytes":"**********","yarn.nodemanager.windows-container.cpu-limit.enabled":"false","yarn.nodemanager.runtime.linux.docker.privileged-containers.allowed":"false","file.blocksize":"67108864","hadoop.http.idle_timeout.ms":"60000","hadoop.registry.zk.retry.ceiling.ms":"60000","yarn.scheduler.configuration.leveldb-store.path":"${hadoop.tmp.dir}/yarn/system/confstore","yarn.sharedcache.store.in-memory.initial-delay-mins":"10","mapreduce.jobhistory.principal":"jhs/<EMAIL>","mapreduce.map.skip.proc-count.auto-incr":"true","fs.s3a.committer.name":"file","mapreduce.task.profile.reduces":"0-2","hadoop.zk.num-retries":"1000","yarn.webapp.xfs-filter.enabled":"true","fs.viewfs.overload.scheme.target.hdfs.impl":"org.apache.hadoop.hdfs.DistributedFileSystem","seq.io.sort.mb":"100","yarn.scheduler.configuration.max.version":"100","yarn.timeline-service.webapp.https.address":"${yarn.timeline-service.hostname}:8190","yarn.resourcemanager.scheduler.address":"${yarn.resourcemanager.hostname}:8030","yarn.node-labels.enabled":"false","yarn.resourcemanager.webapp.ui-actions.enabled":"true","mapreduce.task.timeout":"600000","yarn.sharedcache.client-server.thread-count":"50","hadoop.security.groups.shell.command.timeout":"0s","hadoop.security.crypto.cipher.suite":"AES/CTR/NoPadding","yarn.nodemanager.elastic-memory-control.oom-handler":"org.apache.hadoop.yarn.server.nodemanager.containermanager.linux.resources.DefaultOOMHandler","yarn.resourcemanager.connect.max-wait.ms":"900000","fs.defaultFS":"file:///","yarn.minicluster.use-rpc":"false","ipc.[port_number].decay-scheduler.decay-factor":"0.5","fs.har.impl.disable.cache":"true","yarn.webapp.ui2.enable":"false","io.compression.codec.bzip2.library":"system-native","yarn.webapp.filter-invalid-xml-chars":"false","yarn.nodemanager.runtime.linux.runc.layer-mounts-interval-secs":"600","fs.s3a.select.input.csv.record.delimiter":"\\n","fs.s3a.change.detection.source":"etag","ipc.[port_number].backoff.enable":"false","yarn.nodemanager.distributed-scheduling.enabled":"false","mapreduce.shuffle.connection-keep-alive.timeout":"5","yarn.resourcemanager.webapp.https.address":"${yarn.resourcemanager.hostname}:8090","yarn.webapp.enable-rest-app-submissions":"true","mapreduce.jobhistory.address":"0.0.0.0:10020","yarn.resourcemanager.nm-tokens.master-key-rolling-interval-secs":"*********(redacted)","yarn.is.minicluster":"false","yarn.nodemanager.address":"${yarn.nodemanager.hostname}:0","fs.abfss.impl":"org.apache.hadoop.fs.azurebfs.SecureAzureBlobFileSystem","fs.AbstractFileSystem.s3a.impl":"org.apache.hadoop.fs.s3a.S3A","mapreduce.task.combine.progress.records":"10000","yarn.resourcemanager.epoch.range":"0","yarn.resourcemanager.am.max-attempts":"2","yarn.nodemanager.runtime.linux.runc.image-toplevel-dir":"/runc-root","yarn.nodemanager.linux-container-executor.cgroups.hierarchy":"/hadoop-yarn","fs.AbstractFileSystem.wasbs.impl":"org.apache.hadoop.fs.azure.Wasbs","yarn.timeline-service.entity-group-fs-store.cache-store-class":"org.apache.hadoop.yarn.server.timeline.MemoryTimelineStore","yarn.nodemanager.runtime.linux.runc.allowed-container-networks":"host,none,bridge","fs.ftp.transfer.mode":"BLOCK_TRANSFER_MODE","ipc.server.log.slow.rpc":"false","ipc.server.reuseaddr":"true","fs.ftp.timeout":"0","yarn.resourcemanager.node-labels.provider.fetch-interval-ms":"1800000","yarn.router.webapp.https.address":"0.0.0.0:8091","yarn.nodemanager.webapp.cross-origin.enabled":"false","fs.wasb.impl":"org.apache.hadoop.fs.azure.NativeAzureFileSystem","yarn.resourcemanager.auto-update.containers":"false","yarn.app.mapreduce.am.job.committer.cancel-timeout":"60000","yarn.scheduler.configuration.zk-store.parent-path":"/confstore","yarn.nodemanager.default-container-executor.log-dirs.permissions":"710","yarn.app.attempt.diagnostics.limit.kc":"64","fs.viewfs.overload.scheme.target.swebhdfs.impl":"org.apache.hadoop.hdfs.web.SWebHdfsFileSystem","yarn.client.failover-no-ha-proxy-provider":"org.apache.hadoop.yarn.client.DefaultNoHARMFailoverProxyProvider","fs.s3a.change.detection.mode":"server","ftp.bytes-per-checksum":"512","yarn.nodemanager.resource.memory-mb":"-1","fs.AbstractFileSystem.abfs.impl":"org.apache.hadoop.fs.azurebfs.Abfs","yarn.timeline-service.writer.flush-interval-seconds":"60","fs.s3a.fast.upload.active.blocks":"4","yarn.resourcemanager.submission-preprocessor.enabled":"false","hadoop.security.credential.clear-text-fallback":"true","yarn.nodemanager.collector-service.thread-count":"5","ipc.[port_number].scheduler.impl":"org.apache.hadoop.ipc.DefaultRpcScheduler","fs.azure.secure.mode":"false","mapreduce.jobhistory.joblist.cache.size":"20000","fs.ftp.host":"0.0.0.0","yarn.timeline-service.writer.async.queue.capacity":"100","yarn.resourcemanager.fs.state-store.num-retries":"0","yarn.resourcemanager.nodemanager-connect-retries":"10","yarn.nodemanager.log-aggregation.num-log-files-per-app":"30","hadoop.security.kms.client.encrypted.key.cache.low-watermark":"0.3f","fs.s3a.committer.magic.enabled":"true","yarn.timeline-service.client.max-retries":"30","dfs.ha.fencing.ssh.connect-timeout":"30000","yarn.log-aggregation-enable":"false","yarn.system-metrics-publisher.enabled":"false","mapreduce.reduce.markreset.buffer.percent":"0.0","fs.AbstractFileSystem.viewfs.impl":"org.apache.hadoop.fs.viewfs.ViewFs","yarn.resourcemanager.nodemanagers.heartbeat-interval-speedup-factor":"1.0","mapreduce.task.io.sort.factor":"10","yarn.nodemanager.amrmproxy.client.thread-count":"25","ha.failover-controller.new-active.rpc-timeout.ms":"60000","yarn.nodemanager.container-localizer.java.opts":"-Xmx256m","mapreduce.jobhistory.datestring.cache.size":"200000","mapreduce.job.acl-modify-job":" ","yarn.nodemanager.windows-container.memory-limit.enabled":"false","yarn.timeline-service.webapp.address":"${yarn.timeline-service.hostname}:8188","yarn.app.mapreduce.am.job.committer.commit-window":"10000","yarn.nodemanager.container-manager.thread-count":"20","yarn.minicluster.fixed.ports":"false","hadoop.tags.system":"YARN,HDFS,NAMENODE,DATANODE,REQUIRED,SECURITY,KERBEROS,PERFORMANCE,CLIENT\n      ,SERVER,DEBUG,DEPRECATED,COMMON,OPTIONAL","yarn.cluster.max-application-priority":"0","yarn.timeline-service.ttl-enable":"true","mapreduce.jobhistory.recovery.store.fs.uri":"${hadoop.tmp.dir}/mapred/history/recoverystore","hadoop.caller.context.signature.max.size":"40","ipc.[port_number].decay-scheduler.backoff.responsetime.enable":"false","yarn.client.load.resource-types.from-server":"false","ha.zookeeper.session-timeout.ms":"10000","ipc.[port_number].decay-scheduler.metrics.top.user.count":"10","tfile.io.chunk.size":"1048576","fs.s3a.s3guard.ddb.table.capacity.write":"0","yarn.dispatcher.print-events-info.threshold":"5000","mapreduce.job.speculative.slowtaskthreshold":"1.0","io.serializations":"org.apache.hadoop.io.serializer.WritableSerialization, org.apache.hadoop.io.serializer.avro.AvroSpecificSerialization, org.apache.hadoop.io.serializer.avro.AvroReflectSerialization","hadoop.security.kms.client.failover.sleep.max.millis":"2000","hadoop.security.group.mapping.ldap.directory.search.timeout":"10000","yarn.scheduler.configuration.store.max-logs":"1000","yarn.nodemanager.node-attributes.provider.fetch-interval-ms":"600000","fs.swift.impl":"org.apache.hadoop.fs.swift.snative.SwiftNativeFileSystem","yarn.nodemanager.local-cache.max-files-per-directory":"8192","hadoop.http.cross-origin.enabled":"false","hadoop.zk.acl":"world:anyone:rwcda","yarn.nodemanager.runtime.linux.runc.image-tag-to-manifest-plugin.num-manifests-to-cache":"10","mapreduce.map.sort.spill.percent":"0.80","yarn.timeline-service.entity-group-fs-store.scan-interval-seconds":"60","yarn.node-attribute.fs-store.impl.class":"org.apache.hadoop.yarn.server.resourcemanager.nodelabels.FileSystemNodeAttributeStore","fs.s3a.retry.interval":"500ms","yarn.timeline-service.client.best-effort":"false","yarn.resourcemanager.webapp.delegation-token-auth-filter.enabled":"*********(redacted)","hadoop.security.group.mapping.ldap.posix.attr.uid.name":"uidNumber","fs.AbstractFileSystem.swebhdfs.impl":"org.apache.hadoop.fs.SWebHdfs","yarn.nodemanager.elastic-memory-control.timeout-sec":"5","fs.s3a.select.enabled":"true","mapreduce.ifile.readahead":"true","yarn.timeline-service.leveldb-timeline-store.ttl-interval-ms":"300000","yarn.timeline-service.reader.webapp.address":"${yarn.timeline-service.webapp.address}","yarn.resourcemanager.placement-constraints.algorithm.pool-size":"1","yarn.timeline-service.hbase.coprocessor.jar.hdfs.location":"/hbase/coprocessor/hadoop-yarn-server-timelineservice.jar","hadoop.security.kms.client.encrypted.key.cache.num.refill.threads":"2","yarn.resourcemanager.scheduler.class":"org.apache.hadoop.yarn.server.resourcemanager.scheduler.capacity.CapacityScheduler","yarn.app.mapreduce.am.command-opts":"-Xmx1024m","fs.s3a.metadatastore.fail.on.write.error":"true","hadoop.http.sni.host.check.enabled":"false","mapreduce.cluster.local.dir":"${hadoop.tmp.dir}/mapred/local","io.mapfile.bloom.error.rate":"0.005","fs.client.resolve.topology.enabled":"false","yarn.nodemanager.runtime.linux.allowed-runtimes":"default","yarn.sharedcache.store.class":"org.apache.hadoop.yarn.server.sharedcachemanager.store.InMemorySCMStore","ha.failover-controller.graceful-fence.rpc-timeout.ms":"5000","ftp.replication":"3","fs.getspaceused.jitterMillis":"60000","hadoop.security.uid.cache.secs":"14400","mapreduce.job.maxtaskfailures.per.tracker":"3","fs.s3a.metadatastore.impl":"org.apache.hadoop.fs.s3a.s3guard.NullMetadataStore","io.skip.checksum.errors":"false","yarn.app.mapreduce.client-am.ipc.max-retries-on-timeouts":"3","yarn.timeline-service.webapp.xfs-filter.xframe-options":"SAMEORIGIN","fs.s3a.connection.timeout":"200000","yarn.app.mapreduce.am.webapp.https.enabled":"false","mapreduce.job.max.split.locations":"15","yarn.resourcemanager.nm-container-queuing.max-queue-length":"15","yarn.resourcemanager.delegation-token.always-cancel":"*********(redacted)","hadoop.registry.zk.session.timeout.ms":"60000","yarn.federation.cache-ttl.secs":"300","mapreduce.jvm.system-properties-to-log":"os.name,os.version,java.home,java.runtime.version,java.vendor,java.version,java.vm.name,java.class.path,java.io.tmpdir,user.dir,user.name","yarn.resourcemanager.opportunistic-container-allocation.nodes-used":"10","yarn.timeline-service.entity-group-fs-store.active-dir":"/tmp/entity-file-history/active","mapreduce.shuffle.transfer.buffer.size":"131072","yarn.timeline-service.client.retry-interval-ms":"1000","yarn.timeline-service.flowname.max-size":"0","yarn.http.policy":"HTTP_ONLY","fs.s3a.socket.send.buffer":"8192","fs.AbstractFileSystem.abfss.impl":"org.apache.hadoop.fs.azurebfs.Abfss","yarn.sharedcache.uploader.server.address":"0.0.0.0:8046","yarn.resourcemanager.delegation-token.max-conf-size-bytes":"*********(redacted)","hadoop.http.authentication.token.validity":"*********(redacted)","mapreduce.shuffle.max.connections":"0","yarn.minicluster.yarn.nodemanager.resource.memory-mb":"4096","mapreduce.job.emit-timeline-data":"false","yarn.nodemanager.resource.system-reserved-memory-mb":"-1","hadoop.kerberos.min.seconds.before.relogin":"60","mapreduce.jobhistory.move.thread-count":"3","yarn.resourcemanager.admin.client.thread-count":"1","yarn.dispatcher.drain-events.timeout":"300000","ipc.[port_number].decay-scheduler.backoff.responsetime.thresholds":"10s,20s,30s,40s","fs.s3a.buffer.dir":"${hadoop.tmp.dir}/s3a","hadoop.ssl.enabled.protocols":"TLSv1.2","mapreduce.jobhistory.admin.address":"0.0.0.0:10033","yarn.log-aggregation-status.time-out.ms":"600000","mapreduce.shuffle.port":"13562","yarn.resourcemanager.max-log-aggregation-diagnostics-in-memory":"10","yarn.nodemanager.health-checker.interval-ms":"600000","yarn.router.clientrm.interceptor-class.pipeline":"org.apache.hadoop.yarn.server.router.clientrm.DefaultClientRequestInterceptor","yarn.resourcemanager.zk-appid-node.split-index":"0","ftp.blocksize":"67108864","yarn.nodemanager.runtime.linux.sandbox-mode.local-dirs.permissions":"read","yarn.router.rmadmin.interceptor-class.pipeline":"org.apache.hadoop.yarn.server.router.rmadmin.DefaultRMAdminRequestInterceptor","yarn.nodemanager.log-container-debug-info.enabled":"true","yarn.resourcemanager.activities-manager.app-activities.max-queue-length":"100","yarn.resourcemanager.application-https.policy":"NONE","yarn.client.max-cached-nodemanagers-proxies":"0","yarn.nodemanager.linux-container-executor.cgroups.delete-delay-ms":"20","yarn.nodemanager.delete.debug-delay-sec":"0","yarn.nodemanager.pmem-check-enabled":"true","yarn.nodemanager.disk-health-checker.max-disk-utilization-per-disk-percentage":"90.0","mapreduce.app-submission.cross-platform":"false","yarn.resourcemanager.work-preserving-recovery.scheduling-wait-ms":"10000","yarn.nodemanager.container-retry-minimum-interval-ms":"1000","hadoop.security.groups.cache.secs":"300","yarn.federation.enabled":"false","yarn.workflow-id.tag-prefix":"workflowid:","fs.azure.local.sas.key.mode":"false","ipc.maximum.data.length":"134217728","fs.s3a.endpoint":"s3.amazonaws.com","mapreduce.shuffle.max.threads":"0","yarn.router.pipeline.cache-max-size":"25","yarn.resourcemanager.nm-container-queuing.load-comparator":"QUEUE_LENGTH","yarn.resourcemanager.resource-tracker.nm.ip-hostname-check":"false","hadoop.security.authorization":"false","mapreduce.job.complete.cancel.delegation.tokens":"*********(redacted)","fs.s3a.paging.maximum":"5000","nfs.exports.allowed.hosts":"* rw","yarn.nodemanager.amrmproxy.ha.enable":"false","mapreduce.jobhistory.http.policy":"HTTP_ONLY","yarn.sharedcache.store.in-memory.check-period-mins":"720","hadoop.security.group.mapping.ldap.ssl":"false","fs.s3a.downgrade.syncable.exceptions":"true","yarn.client.application-client-protocol.poll-interval-ms":"200","yarn.scheduler.configuration.leveldb-store.compaction-interval-secs":"86400","yarn.timeline-service.writer.class":"org.apache.hadoop.yarn.server.timelineservice.storage.HBaseTimelineWriterImpl","ha.zookeeper.parent-znode":"/hadoop-ha","yarn.resourcemanager.submission-preprocessor.file-refresh-interval-ms":"60000","yarn.nodemanager.log-aggregation.policy.class":"org.apache.hadoop.yarn.server.nodemanager.containermanager.logaggregation.AllContainerLogAggregationPolicy","mapreduce.reduce.shuffle.merge.percent":"0.66","hadoop.security.group.mapping.ldap.search.filter.group":"(objectClass=group)","yarn.resourcemanager.placement-constraints.scheduler.pool-size":"1","yarn.resourcemanager.activities-manager.cleanup-interval-ms":"5000","yarn.nodemanager.resourcemanager.minimum.version":"NONE","mapreduce.job.speculative.speculative-cap-running-tasks":"0.1","yarn.admin.acl":"*","ipc.[port_number].identity-provider.impl":"org.apache.hadoop.ipc.UserIdentityProvider","yarn.nodemanager.recovery.supervised":"false","yarn.sharedcache.admin.thread-count":"1","yarn.resourcemanager.ha.automatic-failover.enabled":"true","yarn.nodemanager.container-log-monitor.total-size-limit-bytes":"**********0","mapreduce.reduce.skip.maxgroups":"0","mapreduce.reduce.shuffle.connect.timeout":"180000","yarn.nodemanager.health-checker.scripts":"script","yarn.resourcemanager.address":"${yarn.resourcemanager.hostname}:8032","ipc.client.ping":"true","mapreduce.task.local-fs.write-limit.bytes":"-1","fs.adl.oauth2.access.token.provider.type":"*********(redacted)","mapreduce.shuffle.ssl.file.buffer.size":"65536","yarn.resourcemanager.ha.automatic-failover.embedded":"true","yarn.nodemanager.resource-plugins.gpu.docker-plugin":"nvidia-docker-v1","fs.s3a.s3guard.consistency.retry.interval":"2s","hadoop.ssl.enabled":"false","fs.s3a.multipart.purge":"false","yarn.scheduler.configuration.store.class":"file","yarn.resourcemanager.nm-container-queuing.queue-limit-stdev":"1.0f","mapreduce.job.end-notification.max.attempts":"5","mapreduce.output.fileoutputformat.compress.codec":"org.apache.hadoop.io.compress.DefaultCodec","yarn.nodemanager.container-monitor.procfs-tree.smaps-based-rss.enabled":"false","ipc.client.bind.wildcard.addr":"false","yarn.resourcemanager.webapp.rest-csrf.enabled":"false","ha.health-monitor.connect-retry-interval.ms":"1000","yarn.nodemanager.keytab":"/etc/krb5.keytab","mapreduce.jobhistory.keytab":"/etc/security/keytab/jhs.service.keytab","fs.s3a.threads.max":"64","yarn.nodemanager.runtime.linux.docker.image-update":"false","mapreduce.reduce.shuffle.input.buffer.percent":"0.70","fs.viewfs.overload.scheme.target.abfss.impl":"org.apache.hadoop.fs.azurebfs.SecureAzureBlobFileSystem","yarn.dispatcher.cpu-monitor.samples-per-min":"60","yarn.nodemanager.runtime.linux.docker.allowed-container-networks":"host,none,bridge","yarn.nodemanager.node-labels.resync-interval-ms":"120000","hadoop.tmp.dir":"/tmp/hadoop-${user.name}","mapreduce.job.maps":"2","mapreduce.jobhistory.webapp.rest-csrf.custom-header":"X-XSRF-Header","mapreduce.job.end-notification.max.retry.interval":"5000","yarn.log-aggregation.retain-check-interval-seconds":"-1","yarn.resourcemanager.resource-tracker.client.thread-count":"50","yarn.nodemanager.containers-launcher.class":"org.apache.hadoop.yarn.server.nodemanager.containermanager.launcher.ContainersLauncher","yarn.rm.system-metrics-publisher.emit-container-events":"false","yarn.timeline-service.leveldb-timeline-store.start-time-read-cache-size":"10000","yarn.resourcemanager.ha.automatic-failover.zk-base-path":"/yarn-leader-election","io.seqfile.local.dir":"${hadoop.tmp.dir}/io/local","fs.s3a.s3guard.ddb.throttle.retry.interval":"100ms","fs.AbstractFileSystem.wasb.impl":"org.apache.hadoop.fs.azure.Wasb","mapreduce.client.submit.file.replication":"10","mapreduce.jobhistory.minicluster.fixed.ports":"false","fs.s3a.multipart.threshold":"128M","yarn.resourcemanager.webapp.xfs-filter.xframe-options":"SAMEORIGIN","mapreduce.jobhistory.done-dir":"${yarn.app.mapreduce.am.staging-dir}/history/done","ipc.client.idlethreshold":"4000","yarn.nodemanager.linux-container-executor.cgroups.strict-resource-usage":"false","mapreduce.reduce.input.buffer.percent":"0.0","yarn.nodemanager.runtime.linux.docker.userremapping-gid-threshold":"1","yarn.nodemanager.webapp.rest-csrf.enabled":"false","fs.ftp.host.port":"21","ipc.ping.interval":"60000","yarn.resourcemanager.history-writer.multi-threaded-dispatcher.pool-size":"10","yarn.resourcemanager.admin.address":"${yarn.resourcemanager.hostname}:8033","file.client-write-packet-size":"65536","ipc.client.kill.max":"10","mapreduce.reduce.speculative":"true","hadoop.security.key.default.bitlength":"128","mapreduce.job.reducer.unconditional-preempt.delay.sec":"300","yarn.nodemanager.disk-health-checker.interval-ms":"120000","yarn.nodemanager.log.deletion-threads-count":"4","fs.s3a.committer.abort.pending.uploads":"true","yarn.webapp.filter-entity-list-by-user":"false","yarn.resourcemanager.activities-manager.app-activities.ttl-ms":"600000","ipc.client.connection.maxidletime":"10000","mapreduce.task.io.sort.mb":"100","yarn.nodemanager.localizer.client.thread-count":"5","io.erasurecode.codec.rs.rawcoders":"rs_native,rs_java","io.erasurecode.codec.rs-legacy.rawcoders":"rs-legacy_java","yarn.sharedcache.admin.address":"0.0.0.0:8047","yarn.resourcemanager.placement-constraints.algorithm.iterator":"SERIAL","yarn.nodemanager.localizer.cache.cleanup.interval-ms":"600000","hadoop.security.crypto.codec.classes.aes.ctr.nopadding":"org.apache.hadoop.crypto.OpensslAesCtrCryptoCodec, org.apache.hadoop.crypto.JceAesCtrCryptoCodec","mapreduce.job.cache.limit.max-resources-mb":"0","fs.s3a.connection.ssl.enabled":"true","yarn.nodemanager.process-kill-wait.ms":"5000","mapreduce.job.hdfs-servers":"${fs.defaultFS}","yarn.app.mapreduce.am.webapp.https.client.auth":"false","hadoop.workaround.non.threadsafe.getpwuid":"true","fs.df.interval":"60000","ipc.[port_number].decay-scheduler.thresholds":"13,25,50","fs.s3a.multiobjectdelete.enable":"true","yarn.sharedcache.cleaner.resource-sleep-ms":"0","yarn.nodemanager.disk-health-checker.min-healthy-disks":"0.25","hadoop.shell.missing.defaultFs.warning":"false","io.file.buffer.size":"65536","fs.viewfs.overload.scheme.target.wasb.impl":"org.apache.hadoop.fs.azure.NativeAzureFileSystem","hadoop.security.group.mapping.ldap.search.attr.member":"member","hadoop.security.random.device.file.path":"/dev/urandom","hadoop.security.sensitive-config-keys":"*********(redacted)","fs.s3a.s3guard.ddb.max.retries":"9","fs.viewfs.overload.scheme.target.file.impl":"org.apache.hadoop.fs.LocalFileSystem","hadoop.rpc.socket.factory.class.default":"org.apache.hadoop.net.StandardSocketFactory","yarn.intermediate-data-encryption.enable":"false","yarn.resourcemanager.connect.retry-interval.ms":"30000","yarn.nodemanager.container.stderr.pattern":"{*stderr*,*STDERR*}","yarn.scheduler.minimum-allocation-mb":"1024","yarn.app.mapreduce.am.staging-dir":"/tmp/hadoop-yarn/staging","mapreduce.reduce.shuffle.read.timeout":"180000","hadoop.http.cross-origin.max-age":"1800","io.erasurecode.codec.xor.rawcoders":"xor_native,xor_java","fs.s3a.s3guard.consistency.retry.limit":"7","fs.s3a.connection.establish.timeout":"5000","mapreduce.job.running.map.limit":"0","yarn.minicluster.control-resource-monitoring":"false","hadoop.ssl.require.client.cert":"false","hadoop.kerberos.kinit.command":"kinit","yarn.federation.state-store.class":"org.apache.hadoop.yarn.server.federation.store.impl.MemoryFederationStateStore","fs.s3a.delegation.tokens.enabled":"*********(redacted)","mapreduce.reduce.log.level":"INFO","hadoop.security.dns.log-slow-lookups.threshold.ms":"1000","mapreduce.job.ubertask.enable":"false","adl.http.timeout":"-1","yarn.resourcemanager.placement-constraints.retry-attempts":"3","hadoop.caller.context.enabled":"false","hadoop.security.group.mapping.ldap.num.attempts":"3","yarn.nodemanager.vmem-pmem-ratio":"2.1","hadoop.rpc.protection":"authentication","ha.health-monitor.rpc-timeout.ms":"45000","yarn.nodemanager.remote-app-log-dir":"/tmp/logs","hadoop.zk.timeout-ms":"10000","fs.s3a.s3guard.cli.prune.age":"86400000","yarn.nodemanager.resource.pcores-vcores-multiplier":"1.0","yarn.nodemanager.runtime.linux.sandbox-mode":"disabled","yarn.app.mapreduce.am.containerlauncher.threadpool-initial-size":"10","fs.viewfs.overload.scheme.target.webhdfs.impl":"org.apache.hadoop.hdfs.web.WebHdfsFileSystem","fs.s3a.committer.threads":"8","hadoop.zk.retry-interval-ms":"1000","hadoop.security.crypto.buffer.size":"8192","yarn.nodemanager.node-labels.provider.fetch-interval-ms":"600000","mapreduce.jobhistory.recovery.store.leveldb.path":"${hadoop.tmp.dir}/mapred/history/recoverystore","yarn.client.failover-retries-on-socket-timeouts":"0","fs.s3a.ssl.channel.mode":"default_jsse","yarn.nodemanager.resource.memory.enabled":"false","fs.azure.authorization.caching.enable":"true","hadoop.security.instrumentation.requires.admin":"false","yarn.nodemanager.delete.thread-count":"4","mapreduce.job.finish-when-all-reducers-done":"true","hadoop.registry.jaas.context":"Client","yarn.timeline-service.leveldb-timeline-store.path":"${hadoop.tmp.dir}/yarn/timeline","io.map.index.interval":"128","yarn.resourcemanager.nm-container-queuing.max-queue-wait-time-ms":"100","fs.abfs.impl":"org.apache.hadoop.fs.azurebfs.AzureBlobFileSystem","mapreduce.job.counters.max":"120","mapreduce.jobhistory.webapp.rest-csrf.enabled":"false","yarn.timeline-service.store-class":"org.apache.hadoop.yarn.server.timeline.LeveldbTimelineStore","mapreduce.jobhistory.move.interval-ms":"180000","fs.s3a.change.detection.version.required":"true","yarn.nodemanager.localizer.fetch.thread-count":"4","yarn.resourcemanager.scheduler.client.thread-count":"50","hadoop.ssl.hostname.verifier":"DEFAULT","yarn.timeline-service.leveldb-state-store.path":"${hadoop.tmp.dir}/yarn/timeline","mapreduce.job.classloader":"false","mapreduce.task.profile.map.params":"${mapreduce.task.profile.params}","ipc.client.connect.timeout":"20000","hadoop.security.auth_to_local.mechanism":"hadoop","yarn.timeline-service.app-collector.linger-period.ms":"60000","yarn.nm.liveness-monitor.expiry-interval-ms":"600000","yarn.resourcemanager.reservation-system.planfollower.time-step":"1000","yarn.resourcemanager.activities-manager.scheduler-activities.ttl-ms":"600000","yarn.nodemanager.runtime.linux.docker.enable-userremapping.allowed":"true","yarn.webapp.api-service.enable":"false","yarn.nodemanager.recovery.enabled":"false","mapreduce.job.end-notification.retry.interval":"1000","fs.du.interval":"600000","fs.ftp.impl":"org.apache.hadoop.fs.ftp.FTPFileSystem","yarn.nodemanager.container.stderr.tail.bytes":"4096","yarn.nodemanager.disk-health-checker.disk-free-space-threshold.enabled":"true","hadoop.security.group.mapping.ldap.read.timeout.ms":"60000","hadoop.security.groups.cache.warn.after.ms":"5000","file.bytes-per-checksum":"512","mapreduce.outputcommitter.factory.scheme.s3a":"org.apache.hadoop.fs.s3a.commit.S3ACommitterFactory","hadoop.security.groups.cache.background.reload":"false","yarn.nodemanager.container-monitor.enabled":"true","yarn.nodemanager.elastic-memory-control.enabled":"false","net.topology.script.number.args":"100","mapreduce.task.merge.progress.records":"10000","yarn.nodemanager.localizer.address":"${yarn.nodemanager.hostname}:8040","yarn.timeline-service.keytab":"/etc/krb5.keytab","mapreduce.reduce.shuffle.fetch.retry.timeout-ms":"30000","yarn.resourcemanager.rm.container-allocation.expiry-interval-ms":"600000","yarn.nodemanager.container-executor.exit-code-file.timeout-ms":"2000","mapreduce.fileoutputcommitter.algorithm.version":"1","yarn.resourcemanager.work-preserving-recovery.enabled":"true","mapreduce.map.skip.maxrecords":"0","yarn.sharedcache.root-dir":"/sharedcache","fs.s3a.retry.throttle.limit":"20","hadoop.http.authentication.type":"simple","fs.viewfs.overload.scheme.target.oss.impl":"org.apache.hadoop.fs.aliyun.oss.AliyunOSSFileSystem","mapreduce.job.cache.limit.max-resources":"0","mapreduce.task.userlog.limit.kb":"0","ipc.[port_number].weighted-cost.handler":"1","yarn.resourcemanager.scheduler.monitor.enable":"false","ipc.client.connect.max.retries":"10","hadoop.registry.zk.retry.times":"5","yarn.nodemanager.resource-monitor.interval-ms":"3000","yarn.nodemanager.resource-plugins.gpu.allowed-gpu-devices":"auto","mapreduce.job.sharedcache.mode":"disabled","yarn.nodemanager.webapp.rest-csrf.custom-header":"X-XSRF-Header","mapreduce.shuffle.listen.queue.size":"128","yarn.scheduler.configuration.mutation.acl-policy.class":"org.apache.hadoop.yarn.server.resourcemanager.scheduler.DefaultConfigurationMutationACLPolicy","mapreduce.map.cpu.vcores":"1","yarn.log-aggregation.file-formats":"TFile","yarn.timeline-service.client.fd-retain-secs":"300","fs.s3a.select.output.csv.field.delimiter":",","yarn.nodemanager.health-checker.timeout-ms":"1200000","hadoop.user.group.static.mapping.overrides":"dr.who=;","fs.azure.sas.expiry.period":"90d","fs.s3a.select.output.csv.record.delimiter":"\\n","mapreduce.jobhistory.recovery.store.class":"org.apache.hadoop.mapreduce.v2.hs.HistoryServerFileSystemStateStoreService","fs.viewfs.overload.scheme.target.https.impl":"org.apache.hadoop.fs.http.HttpsFileSystem","fs.s3a.s3guard.ddb.table.sse.enabled":"false","yarn.resourcemanager.fail-fast":"${yarn.fail-fast}","yarn.resourcemanager.proxy-user-privileges.enabled":"false","yarn.router.webapp.interceptor-class.pipeline":"org.apache.hadoop.yarn.server.router.webapp.DefaultRequestInterceptorREST","yarn.nodemanager.resource.memory.cgroups.soft-limit-percentage":"90.0","mapreduce.job.reducer.preempt.delay.sec":"0","hadoop.util.hash.type":"murmur","yarn.nodemanager.disk-validator":"basic","yarn.app.mapreduce.client.job.max-retries":"3","fs.viewfs.overload.scheme.target.ftp.impl":"org.apache.hadoop.fs.ftp.FTPFileSystem","mapreduce.reduce.shuffle.retry-delay.max.ms":"60000","hadoop.security.group.mapping.ldap.connection.timeout.ms":"60000","mapreduce.task.profile.params":"-agentlib:hprof=cpu=samples,heap=sites,force=n,thread=y,verbose=n,file=%s","yarn.app.mapreduce.shuffle.log.backups":"0","yarn.nodemanager.container-diagnostics-maximum-size":"10000","hadoop.registry.zk.retry.interval.ms":"1000","yarn.nodemanager.linux-container-executor.cgroups.delete-timeout-ms":"1000","fs.AbstractFileSystem.file.impl":"org.apache.hadoop.fs.local.LocalFs","yarn.nodemanager.log-aggregation.roll-monitoring-interval-seconds":"-1","mapreduce.jobhistory.cleaner.interval-ms":"86400000","hadoop.registry.zk.quorum":"localhost:2181","yarn.nodemanager.runtime.linux.runc.allowed-container-runtimes":"runc","mapreduce.output.fileoutputformat.compress":"false","yarn.resourcemanager.am-rm-tokens.master-key-rolling-interval-secs":"*********(redacted)","fs.s3a.assumed.role.session.duration":"30m","hadoop.security.group.mapping.ldap.conversion.rule":"none","hadoop.ssl.server.conf":"ssl-server.xml","fs.s3a.retry.throttle.interval":"100ms","seq.io.sort.factor":"100","fs.viewfs.overload.scheme.target.ofs.impl":"org.apache.hadoop.fs.ozone.RootedOzoneFileSystem","yarn.sharedcache.cleaner.initial-delay-mins":"10","mapreduce.client.completion.pollinterval":"5000","hadoop.ssl.keystores.factory.class":"org.apache.hadoop.security.ssl.FileBasedKeyStoresFactory","yarn.app.mapreduce.am.resource.cpu-vcores":"1","yarn.timeline-service.enabled":"false","yarn.nodemanager.runtime.linux.docker.capabilities":"CHOWN,DAC_OVERRIDE,FSETID,FOWNER,MKNOD,NET_RAW,SETGID,SETUID,SETFCAP,SETPCAP,NET_BIND_SERVICE,SYS_CHROOT,KILL,AUDIT_WRITE","yarn.acl.enable":"false","yarn.timeline-service.entity-group-fs-store.done-dir":"/tmp/entity-file-history/done/","hadoop.security.group.mapping.ldap.num.attempts.before.failover":"3","mapreduce.task.profile":"false","hadoop.prometheus.endpoint.enabled":"false","yarn.resourcemanager.fs.state-store.uri":"${hadoop.tmp.dir}/yarn/system/rmstore","mapreduce.jobhistory.always-scan-user-dir":"false","fs.s3a.metadatastore.metadata.ttl":"15m","yarn.nodemanager.opportunistic-containers-use-pause-for-preemption":"false","yarn.nodemanager.linux-container-executor.nonsecure-mode.local-user":"nobody","yarn.timeline-service.reader.class":"org.apache.hadoop.yarn.server.timelineservice.storage.HBaseTimelineReaderImpl","yarn.resourcemanager.configuration.provider-class":"org.apache.hadoop.yarn.LocalConfigurationProvider","yarn.nodemanager.runtime.linux.docker.userremapping-uid-threshold":"1","yarn.resourcemanager.configuration.file-system-based-store":"/yarn/conf","mapreduce.job.cache.limit.max-single-resource-mb":"0","yarn.nodemanager.runtime.linux.docker.stop.grace-period":"10","yarn.resourcemanager.resource-profiles.source-file":"resource-profiles.json","mapreduce.job.dfs.storage.capacity.kill-limit-exceed":"false","yarn.nodemanager.resource.percentage-physical-cpu-limit":"100","mapreduce.jobhistory.client.thread-count":"10","tfile.fs.input.buffer.size":"262144","mapreduce.client.progressmonitor.pollinterval":"1000","yarn.nodemanager.log-dirs":"${yarn.log.dir}/userlogs","yarn.resourcemanager.opportunistic.max.container-allocation.per.am.heartbeat":"-1","fs.automatic.close":"true","yarn.resourcemanager.delegation-token-renewer.thread-retry-interval":"*********(redacted)","fs.s3a.select.input.csv.quote.character":"\"","yarn.nodemanager.hostname":"0.0.0.0","ipc.[port_number].cost-provider.impl":"org.apache.hadoop.ipc.DefaultCostProvider","yarn.nodemanager.runtime.linux.runc.manifest-to-resources-plugin":"org.apache.hadoop.yarn.server.nodemanager.containermanager.linux.runtime.runc.HdfsManifestToResourcesPlugin","yarn.nodemanager.remote-app-log-dir-include-older":"true","yarn.nodemanager.resource.memory.cgroups.swappiness":"0","ftp.stream-buffer-size":"4096","yarn.fail-fast":"false","yarn.nodemanager.runtime.linux.runc.layer-mounts-to-keep":"100","yarn.timeline-service.app-aggregation-interval-secs":"15","hadoop.security.group.mapping.ldap.search.filter.user":"(&(objectClass=user)(sAMAccountName={0}))","ipc.[port_number].weighted-cost.lockshared":"10","yarn.nodemanager.container-localizer.log.level":"INFO","yarn.timeline-service.address":"${yarn.timeline-service.hostname}:10200","mapreduce.job.ubertask.maxmaps":"9","fs.s3a.threads.keepalivetime":"60","mapreduce.jobhistory.webapp.rest-csrf.methods-to-ignore":"GET,OPTIONS,HEAD","mapreduce.task.files.preserve.failedtasks":"false","yarn.app.mapreduce.client.job.retry-interval":"2000","ha.failover-controller.graceful-fence.connection.retries":"1","fs.s3a.select.output.csv.quote.escape.character":"\\\\","yarn.resourcemanager.delegation.token.max-lifetime":"*********(redacted)","hadoop.kerberos.keytab.login.autorenewal.enabled":"false","yarn.timeline-service.client.drain-entities.timeout.ms":"2000","yarn.nodemanager.resource-plugins.fpga.vendor-plugin.class":"org.apache.hadoop.yarn.server.nodemanager.containermanager.resourceplugin.fpga.IntelFpgaOpenclPlugin","yarn.resourcemanager.nodemanagers.heartbeat-interval-min-ms":"1000","yarn.timeline-service.entity-group-fs-store.summary-store":"org.apache.hadoop.yarn.server.timeline.LeveldbTimelineStore","mapreduce.reduce.cpu.vcores":"1","mapreduce.job.encrypted-intermediate-data.buffer.kb":"128","fs.client.resolve.remote.symlinks":"true","yarn.nodemanager.webapp.https.address":"0.0.0.0:8044","hadoop.http.cross-origin.allowed-origins":"*","mapreduce.job.encrypted-intermediate-data":"false","yarn.nodemanager.disk-health-checker.disk-utilization-threshold.enabled":"true","fs.s3a.executor.capacity":"16","yarn.timeline-service.entity-group-fs-store.retain-seconds":"604800","yarn.resourcemanager.metrics.runtime.buckets":"60,300,1440","yarn.timeline-service.generic-application-history.max-applications":"10000","yarn.nodemanager.local-dirs":"${hadoop.tmp.dir}/nm-local-dir","mapreduce.shuffle.connection-keep-alive.enable":"false","yarn.node-labels.configuration-type":"centralized","fs.s3a.path.style.access":"false","yarn.nodemanager.aux-services.mapreduce_shuffle.class":"org.apache.hadoop.mapred.ShuffleHandler","yarn.sharedcache.store.in-memory.staleness-period-mins":"10080","fs.adl.impl":"org.apache.hadoop.fs.adl.AdlFileSystem","yarn.resourcemanager.application.max-tags":"10","hadoop.domainname.resolver.impl":"org.apache.hadoop.net.DNSDomainNameResolver","yarn.resourcemanager.nodemanager.minimum.version":"NONE","mapreduce.jobhistory.webapp.xfs-filter.xframe-options":"SAMEORIGIN","yarn.app.mapreduce.am.staging-dir.erasurecoding.enabled":"false","net.topology.impl":"org.apache.hadoop.net.NetworkTopology","io.map.index.skip":"0","yarn.timeline-service.reader.webapp.https.address":"${yarn.timeline-service.webapp.https.address}","fs.ftp.data.connection.mode":"ACTIVE_LOCAL_DATA_CONNECTION_MODE","mapreduce.job.local-fs.single-disk-limit.check.kill-limit-exceed":"true","yarn.scheduler.maximum-allocation-vcores":"4","hadoop.http.cross-origin.allowed-headers":"X-Requested-With,Content-Type,Accept,Origin","yarn.nodemanager.log-aggregation.compression-type":"none","yarn.timeline-service.version":"1.0f","yarn.ipc.rpc.class":"org.apache.hadoop.yarn.ipc.HadoopYarnProtoRPC","mapreduce.reduce.maxattempts":"4","hadoop.security.dns.log-slow-lookups.enabled":"false","mapreduce.job.committer.setup.cleanup.needed":"true","hadoop.security.secure.random.impl":"org.apache.hadoop.crypto.random.OpensslSecureRandom","mapreduce.job.running.reduce.limit":"0","fs.s3a.select.errors.include.sql":"false","fs.s3a.connection.request.timeout":"0","ipc.maximum.response.length":"134217728","yarn.resourcemanager.webapp.rest-csrf.methods-to-ignore":"GET,OPTIONS,HEAD","mapreduce.job.token.tracking.ids.enabled":"*********(redacted)","hadoop.caller.context.max.size":"128","yarn.nodemanager.runtime.linux.docker.host-pid-namespace.allowed":"false","yarn.nodemanager.runtime.linux.docker.delayed-removal.allowed":"false","hadoop.registry.system.acls":"sasl:yarn@, sasl:mapred@, sasl:hdfs@","yarn.nodemanager.recovery.dir":"${hadoop.tmp.dir}/yarn-nm-recovery","fs.s3a.fast.upload.buffer":"disk","mapreduce.jobhistory.intermediate-done-dir":"${yarn.app.mapreduce.am.staging-dir}/history/done_intermediate","yarn.app.mapreduce.shuffle.log.separate":"true","yarn.log-aggregation.debug.filesize":"104857600","fs.s3a.max.total.tasks":"32","fs.s3a.readahead.range":"64K","hadoop.http.authentication.simple.anonymous.allowed":"true","fs.s3a.attempts.maximum":"20","hadoop.registry.zk.connection.timeout.ms":"15000","yarn.resourcemanager.delegation-token-renewer.thread-count":"*********(redacted)","yarn.resourcemanager.delegation-token-renewer.thread-timeout":"*********(redacted)","yarn.timeline-service.leveldb-timeline-store.start-time-write-cache-size":"10000","yarn.nodemanager.aux-services.manifest.reload-ms":"0","yarn.nodemanager.emit-container-events":"true","yarn.resourcemanager.resource-profiles.enabled":"false","yarn.timeline-service.hbase-schema.prefix":"prod.","fs.azure.authorization":"false","mapreduce.map.log.level":"INFO","ha.failover-controller.active-standby-elector.zk.op.retries":"3","yarn.resourcemanager.decommissioning-nodes-watcher.poll-interval-secs":"20","mapreduce.output.fileoutputformat.compress.type":"RECORD","yarn.resourcemanager.leveldb-state-store.path":"${hadoop.tmp.dir}/yarn/system/rmstore","yarn.timeline-service.webapp.rest-csrf.custom-header":"X-XSRF-Header","mapreduce.ifile.readahead.bytes":"4194304","yarn.sharedcache.app-checker.class":"org.apache.hadoop.yarn.server.sharedcachemanager.RemoteAppChecker","yarn.nodemanager.linux-container-executor.nonsecure-mode.limit-users":"true","yarn.nodemanager.resource.detect-hardware-capabilities":"false","mapreduce.cluster.acls.enabled":"false","mapreduce.job.speculative.retry-after-no-speculate":"1000","fs.viewfs.overload.scheme.target.abfs.impl":"org.apache.hadoop.fs.azurebfs.AzureBlobFileSystem","hadoop.security.group.mapping.ldap.search.group.hierarchy.levels":"0","yarn.resourcemanager.fs.state-store.retry-interval-ms":"1000","file.stream-buffer-size":"4096","yarn.resourcemanager.application-timeouts.monitor.interval-ms":"3000","mapreduce.map.output.compress.codec":"org.apache.hadoop.io.compress.DefaultCodec","mapreduce.map.speculative":"true","yarn.nodemanager.runtime.linux.runc.image-tag-to-manifest-plugin.hdfs-hash-file":"/runc-root/image-tag-to-hash","mapreduce.job.speculative.retry-after-speculate":"15000","yarn.nodemanager.linux-container-executor.cgroups.mount":"false","yarn.app.mapreduce.am.container.log.backups":"0","yarn.app.mapreduce.am.log.level":"INFO","yarn.nodemanager.runtime.linux.runc.image-tag-to-manifest-plugin":"org.apache.hadoop.yarn.server.nodemanager.containermanager.linux.runtime.runc.ImageTagToManifestPlugin","io.bytes.per.checksum":"512","mapreduce.job.reduce.slowstart.completedmaps":"0.05","yarn.timeline-service.http-authentication.type":"simple","hadoop.security.group.mapping.ldap.search.attr.group.name":"cn","yarn.nodemanager.resource-plugins.fpga.allowed-fpga-devices":"auto","yarn.timeline-service.client.internal-timers-ttl-secs":"420","fs.s3a.select.output.csv.quote.character":"\"","hadoop.http.logs.enabled":"true","fs.s3a.block.size":"32M","yarn.sharedcache.client-server.address":"0.0.0.0:8045","yarn.nodemanager.logaggregation.threadpool-size-max":"100","yarn.resourcemanager.hostname":"0.0.0.0","yarn.resourcemanager.delegation.key.update-interval":"86400000","mapreduce.reduce.shuffle.fetch.retry.enabled":"${yarn.nodemanager.recovery.enabled}","mapreduce.map.memory.mb":"-1","mapreduce.task.skip.start.attempts":"2","fs.AbstractFileSystem.hdfs.impl":"org.apache.hadoop.fs.Hdfs","yarn.nodemanager.disk-health-checker.enable":"true","fs.s3a.select.output.csv.quote.fields":"always","ipc.client.tcpnodelay":"true","ipc.client.rpc-timeout.ms":"0","yarn.nodemanager.webapp.rest-csrf.methods-to-ignore":"GET,OPTIONS,HEAD","yarn.resourcemanager.delegation-token-renewer.thread-retry-max-attempts":"*********(redacted)","ipc.client.low-latency":"false","mapreduce.input.lineinputformat.linespermap":"1","yarn.router.interceptor.user.threadpool-size":"5","ipc.client.connect.max.retries.on.timeouts":"45","yarn.timeline-service.leveldb-timeline-store.read-cache-size":"104857600","fs.AbstractFileSystem.har.impl":"org.apache.hadoop.fs.HarFs","mapreduce.job.split.metainfo.maxsize":"10000000","yarn.am.liveness-monitor.expiry-interval-ms":"600000","yarn.resourcemanager.container-tokens.master-key-rolling-interval-secs":"*********(redacted)","yarn.timeline-service.entity-group-fs-store.app-cache-size":"10","yarn.nodemanager.runtime.linux.runc.hdfs-manifest-to-resources-plugin.stat-cache-timeout-interval-secs":"360","fs.s3a.socket.recv.buffer":"8192","yarn.resourcemanager.resource-tracker.address":"${yarn.resourcemanager.hostname}:8031","yarn.nodemanager.node-labels.provider.fetch-timeout-ms":"1200000","mapreduce.job.heap.memory-mb.ratio":"0.8","yarn.resourcemanager.leveldb-state-store.compaction-interval-secs":"3600","yarn.resourcemanager.webapp.rest-csrf.custom-header":"X-XSRF-Header","yarn.nodemanager.pluggable-device-framework.enabled":"false","yarn.scheduler.configuration.fs.path":"file://${hadoop.tmp.dir}/yarn/system/schedconf","mapreduce.client.output.filter":"FAILED","hadoop.http.filter.initializers":"org.apache.hadoop.http.lib.StaticUserWebFilter","mapreduce.reduce.memory.mb":"-1","yarn.timeline-service.hostname":"0.0.0.0","file.replication":"1","yarn.nodemanager.container-metrics.unregister-delay-ms":"10000","yarn.nodemanager.container-metrics.period-ms":"-1","mapreduce.fileoutputcommitter.task.cleanup.enabled":"false","yarn.nodemanager.log.retain-seconds":"10800","yarn.timeline-service.entity-group-fs-store.cleaner-interval-seconds":"3600","ipc.[port_number].callqueue.impl":"java.util.concurrent.LinkedBlockingQueue","yarn.resourcemanager.keytab":"/etc/krb5.keytab","hadoop.security.group.mapping.providers.combined":"true","mapreduce.reduce.merge.inmem.threshold":"1000","yarn.timeline-service.recovery.enabled":"false","fs.azure.saskey.usecontainersaskeyforallaccess":"true","yarn.sharedcache.nm.uploader.thread-count":"20","yarn.resourcemanager.nodemanager-graceful-decommission-timeout-secs":"3600","ipc.[port_number].weighted-cost.lockfree":"1","mapreduce.shuffle.ssl.enabled":"false","yarn.timeline-service.hbase.coprocessor.app-final-value-retention-milliseconds":"259200000","yarn.nodemanager.opportunistic-containers-max-queue-length":"0","yarn.resourcemanager.state-store.max-completed-applications":"${yarn.resourcemanager.max-completed-applications}","mapreduce.job.speculative.minimum-allowed-tasks":"10","fs.s3a.aws.credentials.provider":"\n    org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider,\n    org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider,\n    com.amazonaws.auth.EnvironmentVariableCredentialsProvider,\n    org.apache.hadoop.fs.s3a.auth.IAMInstanceCredentialsProvider\n  ","yarn.log-aggregation.retain-seconds":"-1","yarn.nodemanager.disk-health-checker.min-free-space-per-disk-mb":"0","mapreduce.jobhistory.max-age-ms":"604800000","hadoop.http.cross-origin.allowed-methods":"GET,POST,HEAD","yarn.resourcemanager.opportunistic-container-allocation.enabled":"false","mapreduce.jobhistory.webapp.address":"0.0.0.0:19888","hadoop.system.tags":"YARN,HDFS,NAMENODE,DATANODE,REQUIRED,SECURITY,KERBEROS,PERFORMANCE,CLIENT\n      ,SERVER,DEBUG,DEPRECATED,COMMON,OPTIONAL","yarn.log-aggregation.file-controller.TFile.class":"org.apache.hadoop.yarn.logaggregation.filecontroller.tfile.LogAggregationTFileController","yarn.client.nodemanager-connect.max-wait-ms":"180000","yarn.resourcemanager.webapp.address":"${yarn.resourcemanager.hostname}:8088","mapreduce.jobhistory.recovery.enable":"false","mapreduce.reduce.shuffle.parallelcopies":"5","fs.AbstractFileSystem.webhdfs.impl":"org.apache.hadoop.fs.WebHdfs","fs.trash.interval":"0","yarn.app.mapreduce.client.max-retries":"3","hadoop.security.authentication":"simple","mapreduce.task.profile.reduce.params":"${mapreduce.task.profile.params}","yarn.app.mapreduce.am.resource.mb":"1536","mapreduce.input.fileinputformat.list-status.num-threads":"1","yarn.nodemanager.container-executor.class":"org.apache.hadoop.yarn.server.nodemanager.DefaultContainerExecutor","io.mapfile.bloom.size":"1048576","yarn.timeline-service.ttl-ms":"604800000","yarn.resourcemanager.nm-container-queuing.min-queue-length":"5","yarn.nodemanager.resource.cpu-vcores":"-1","mapreduce.job.reduces":"1","fs.s3a.multipart.size":"64M","fs.s3a.select.input.csv.comment.marker":"#","yarn.scheduler.minimum-allocation-vcores":"1","mapreduce.job.speculative.speculative-cap-total-tasks":"0.01","hadoop.ssl.client.conf":"ssl-client.xml","mapreduce.job.queuename":"default","mapreduce.job.encrypted-intermediate-data-key-size-bits":"128","fs.s3a.metadatastore.authoritative":"false","ipc.[port_number].weighted-cost.response":"1","yarn.nodemanager.webapp.xfs-filter.xframe-options":"SAMEORIGIN","ha.health-monitor.sleep-after-disconnect.ms":"1000","yarn.app.mapreduce.shuffle.log.limit.kb":"0","hadoop.security.group.mapping":"org.apache.hadoop.security.JniBasedUnixGroupsMappingWithFallback","yarn.client.application-client-protocol.poll-timeout-ms":"-1","mapreduce.jobhistory.jhist.format":"binary","mapreduce.task.stuck.timeout-ms":"600000","yarn.resourcemanager.application.max-tag.length":"100","yarn.resourcemanager.ha.enabled":"false","dfs.client.ignore.namenode.default.kms.uri":"false","hadoop.http.staticuser.user":"dr.who","mapreduce.task.exit.timeout.check-interval-ms":"20000","mapreduce.jobhistory.intermediate-user-done-dir.permissions":"770","mapreduce.task.exit.timeout":"60000","yarn.nodemanager.linux-container-executor.resources-handler.class":"org.apache.hadoop.yarn.server.nodemanager.util.DefaultLCEResourcesHandler","mapreduce.reduce.shuffle.memory.limit.percent":"0.25","yarn.resourcemanager.reservation-system.enable":"false","mapreduce.map.output.compress":"false","ha.zookeeper.acl":"world:anyone:rwcda","ipc.server.max.connections":"0","yarn.nodemanager.runtime.linux.docker.default-container-network":"host","yarn.router.webapp.address":"0.0.0.0:8089","yarn.scheduler.maximum-allocation-mb":"8192","yarn.resourcemanager.scheduler.monitor.policies":"org.apache.hadoop.yarn.server.resourcemanager.monitor.capacity.ProportionalCapacityPreemptionPolicy","yarn.sharedcache.cleaner.period-mins":"1440","yarn.nodemanager.resource-plugins.gpu.docker-plugin.nvidia-docker-v1.endpoint":"http://localhost:3476/v1.0/docker/cli","yarn.app.mapreduce.am.container.log.limit.kb":"0","ipc.client.connect.retry.interval":"1000","yarn.timeline-service.http-cross-origin.enabled":"false","fs.wasbs.impl":"org.apache.hadoop.fs.azure.NativeAzureFileSystem$Secure","yarn.resourcemanager.nodemanagers.heartbeat-interval-max-ms":"1000","yarn.federation.subcluster-resolver.class":"org.apache.hadoop.yarn.server.federation.resolver.DefaultSubClusterResolverImpl","yarn.resourcemanager.zk-state-store.parent-path":"/rmstore","fs.s3a.select.input.csv.field.delimiter":",","mapreduce.jobhistory.cleaner.enable":"true","yarn.timeline-service.client.fd-flush-interval-secs":"10","hadoop.security.kms.client.encrypted.key.cache.expiry":"43200000","yarn.client.nodemanager-client-async.thread-pool-max-size":"500","mapreduce.map.maxattempts":"4","yarn.resourcemanager.nm-container-queuing.sorting-nodes-interval-ms":"1000","fs.s3a.committer.staging.tmp.path":"tmp/staging","yarn.nodemanager.sleep-delay-before-sigkill.ms":"250","yarn.resourcemanager.nm-container-queuing.min-queue-wait-time-ms":"10","mapreduce.job.end-notification.retry.attempts":"0","yarn.nodemanager.resource.count-logical-processors-as-cores":"false","hadoop.registry.zk.root":"/registry","adl.feature.ownerandgroup.enableupn":"false","yarn.resourcemanager.zk-max-znode-size.bytes":"1048576","mapreduce.job.reduce.shuffle.consumer.plugin.class":"org.apache.hadoop.mapreduce.task.reduce.Shuffle","yarn.resourcemanager.delayed.delegation-token.removal-interval-ms":"*********(redacted)","yarn.nodemanager.localizer.cache.target-size-mb":"10240","fs.s3a.committer.staging.conflict-mode":"append","mapreduce.client.libjars.wildcard":"true","fs.s3a.committer.staging.unique-filenames":"true","yarn.nodemanager.node-attributes.provider.fetch-timeout-ms":"1200000","fs.s3a.list.version":"2","ftp.client-write-packet-size":"65536","ipc.[port_number].weighted-cost.lockexclusive":"100","fs.AbstractFileSystem.adl.impl":"org.apache.hadoop.fs.adl.Adl","yarn.nodemanager.container-log-monitor.enable":"false","hadoop.security.key.default.cipher":"AES/CTR/NoPadding","yarn.client.failover-retries":"0","fs.s3a.multipart.purge.age":"86400","mapreduce.job.local-fs.single-disk-limit.check.interval-ms":"5000","net.topology.node.switch.mapping.impl":"org.apache.hadoop.net.ScriptBasedMapping","yarn.nodemanager.amrmproxy.address":"0.0.0.0:8049","ipc.server.listen.queue.size":"256","ipc.[port_number].decay-scheduler.period-ms":"5000","yarn.nodemanager.runtime.linux.runc.image-tag-to-manifest-plugin.cache-refresh-interval-secs":"60","map.sort.class":"org.apache.hadoop.util.QuickSort","fs.viewfs.rename.strategy":"SAME_MOUNTPOINT","hadoop.security.kms.client.authentication.retry-count":"1","fs.permissions.umask-mode":"022","fs.s3a.assumed.role.credentials.provider":"org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider","yarn.nodemanager.runtime.linux.runc.privileged-containers.allowed":"false","yarn.nodemanager.vmem-check-enabled":"true","yarn.nodemanager.numa-awareness.enabled":"false","yarn.nodemanager.recovery.compaction-interval-secs":"3600","yarn.app.mapreduce.client-am.ipc.max-retries":"3","yarn.federation.registry.base-dir":"yarnfederation/","yarn.nodemanager.health-checker.run-before-startup":"false","mapreduce.job.max.map":"-1","mapreduce.job.local-fs.single-disk-limit.bytes":"-1","mapreduce.shuffle.pathcache.concurrency-level":"16","mapreduce.job.ubertask.maxreduces":"1","mapreduce.shuffle.pathcache.max-weight":"10485760","hadoop.security.kms.client.encrypted.key.cache.size":"500","hadoop.security.java.secure.random.algorithm":"SHA1PRNG","ha.failover-controller.cli-check.rpc-timeout.ms":"20000","mapreduce.jobhistory.jobname.limit":"50","fs.s3a.select.input.compression":"none","yarn.client.nodemanager-connect.retry-interval-ms":"10000","ipc.[port_number].scheduler.priority.levels":"4","yarn.timeline-service.state-store-class":"org.apache.hadoop.yarn.server.timeline.recovery.LeveldbTimelineStateStore","yarn.nodemanager.env-whitelist":"JAVA_HOME,HADOOP_COMMON_HOME,HADOOP_HDFS_HOME,HADOOP_CONF_DIR,CLASSPATH_PREPEND_DISTCACHE,HADOOP_YARN_HOME,HADOOP_HOME,PATH,LANG,TZ","yarn.sharedcache.nested-level":"3","yarn.timeline-service.webapp.rest-csrf.methods-to-ignore":"GET,OPTIONS,HEAD","fs.azure.user.agent.prefix":"unknown","yarn.resourcemanager.zk-delegation-token-node.split-index":"*********(redacted)","yarn.nodemanager.numa-awareness.read-topology":"false","yarn.nodemanager.webapp.address":"${yarn.nodemanager.hostname}:8042","rpc.metrics.quantile.enable":"false","yarn.registry.class":"org.apache.hadoop.registry.client.impl.FSRegistryOperationsService","mapreduce.jobhistory.admin.acl":"*","yarn.resourcemanager.system-metrics-publisher.dispatcher.pool-size":"10","yarn.scheduler.queue-placement-rules":"user-group","hadoop.http.authentication.kerberos.keytab":"${user.home}/hadoop.keytab","yarn.resourcemanager.recovery.enabled":"false","fs.s3a.select.input.csv.header":"none","yarn.nodemanager.runtime.linux.runc.hdfs-manifest-to-resources-plugin.stat-cache-size":"500","yarn.timeline-service.webapp.rest-csrf.enabled":"false","yarn.nodemanager.disk-health-checker.min-free-space-per-disk-watermark-high-mb":"0"},"System Properties":{"java.io.tmpdir":"/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/","line.separator":"\n","path.separator":":","sun.management.compiler":"HotSpot 64-Bit Tiered Compilers","SPARK_SUBMIT":"true","sun.cpu.endian":"little","java.specification.version":"1.8","java.vm.specification.name":"Java Virtual Machine Specification","java.vendor":"Azul Systems, Inc.","java.vm.specification.version":"1.8","user.home":"/Users/<USER>","file.encoding.pkg":"sun.io","ftp.nonProxyHosts":"local|*.local|169.254/16|*.169.254/16","sun.arch.data.model":"64","sun.boot.library.path":"/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib","user.dir":"/Users/<USER>/Code/stczwd/spark/dist","java.library.path":"/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.","sun.cpu.isalist":"","os.arch":"aarch64","java.vm.version":"25.312-b07","jetty.git.hash":"526006ecfa3af7f1a27ef3a288e2bef7ea9dd7e8","java.endorsed.dirs":"/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/endorsed","java.runtime.version":"1.8.0_312-b07","java.vm.info":"mixed mode","java.ext.dirs":"/Users/<USER>/Library/Java/Extensions:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java","java.runtime.name":"OpenJDK Runtime Environment","jdk.vendor.version":"Zulu *********-CA-macos-aarch64","file.separator":"/","java.class.version":"52.0","scala.usejavacp":"true","java.specification.name":"Java Platform API Specification","sun.boot.class.path":"/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/resources.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/rt.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/sunrsasign.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/jsse.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/jce.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/charsets.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/jfr.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/classes","file.encoding":"UTF-8","user.timezone":"Asia/Shanghai","java.specification.vendor":"Oracle Corporation","sun.java.launcher":"SUN_STANDARD","os.version":"12.1","sun.os.patch.level":"unknown","gopherProxySet":"false","java.vm.specification.vendor":"Oracle Corporation","jdk.lang.Process.launchMechanism":"POSIX_SPAWN","user.country":"CN","sun.jnu.encoding":"UTF-8","http.nonProxyHosts":"local|*.local|169.254/16|*.169.254/16","user.language":"zh","socksNonProxyHosts":"local|*.local|169.254/16|*.169.254/16","java.vendor.url":"http://www.azul.com/","java.awt.printerjob":"sun.lwawt.macosx.CPrinterJob","java.awt.graphicsenv":"sun.awt.CGraphicsEnvironment","awt.toolkit":"sun.lwawt.macosx.LWCToolkit","os.name":"Mac OS X","java.vm.vendor":"Azul Systems, Inc.","java.vendor.url.bug":"http://www.azul.com/support/","user.name":"lijunqing","java.vm.name":"OpenJDK 64-Bit Server VM","sun.java.command":"org.apache.spark.deploy.SparkSubmit --class org.apache.spark.repl.Main --name Spark shell spark-shell","java.home":"/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre","java.version":"1.8.0_312","sun.io.unicode.encoding":"UnicodeBig"},"Classpath Entries":{"/Users/<USER>/Code/stczwd/spark/dist/jars/antlr4-runtime-4.8.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/janino-3.0.16.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/json4s-jackson_2.12-3.7.0-M11.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-hive_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/HikariCP-2.5.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/velocity-1.5.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-metastore-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/shims-0.9.23.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-codec-1.15.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-catalyst_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/tink-1.6.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/httpclient-4.5.13.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-repl_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/paranamer-2.8.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/pickle-1.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/audience-annotations-0.5.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/lapack-2.2.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jakarta.annotation-api-1.3.5.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-transport-4.1.72.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/log4j-core-2.17.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-tags_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/arpack-2.2.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jersey-container-servlet-core-2.34.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/arrow-vector-6.0.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-yarn_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jersey-container-servlet-2.34.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/scala-reflect-2.12.15.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/objenesis-3.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-handler-4.1.72.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-math3-3.4.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/blas-2.2.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-service-rpc-3.1.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-transport-classes-epoll-4.1.72.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spire-util_2.12-0.17.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/metrics-graphite-4.2.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-tcnative-classes-2.0.46.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/leveldbjni-all-1.8.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/datanucleus-rdbms-4.1.19.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jersey-common-2.34.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-unsafe_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/metrics-jvm-4.2.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-network-common_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/threeten-extra-1.5.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spire-platform_2.12-0.17.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-kvstore_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-cli-1.5.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/cats-kernel_2.12-2.1.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/minlog-1.3.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jpam-1.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/log4j-1.2-api-2.17.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/flatbuffers-java-1.12.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/stax-api-1.0.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-collections-3.2.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-transport-native-unix-common-4.1.72.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/json4s-scalap_2.12-3.7.0-M11.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jakarta.xml.bind-api-2.3.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hk2-utils-2.6.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-codec-4.1.72.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/antlr-runtime-3.5.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-jdbc-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/ivy-2.5.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hadoop-shaded-guava-1.1.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/json4s-core_2.12-3.7.0-M11.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jakarta.inject-2.6.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jackson-databind-2.13.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-hive-thriftserver_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/arrow-format-6.0.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/opencsv-2.3.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/derby-10.14.2.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/parquet-jackson-1.12.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/breeze-macros_2.12-1.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/JLargeArrays-1.5.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/javax.jdo-3.2.0-m3.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/istack-commons-runtime-3.0.8.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/bonecp-0.8.0.RELEASE.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/algebra_2.12-2.0.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spire-macros_2.12-0.17.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/orc-mapreduce-1.7.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-transport-native-kqueue-4.1.72.Final-osx-aarch_64.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/json4s-ast_2.12-3.7.0-M11.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/parquet-column-1.12.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-io-2.11.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-net-3.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jackson-module-scala_2.12-2.13.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-text-1.6.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jline-2.14.6.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-shims-scheduler-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/parquet-hadoop-1.12.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-streaming_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hadoop-client-runtime-3.3.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-shims-common-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/kryo-shaded-4.0.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/guava-14.0.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/shapeless_2.12-2.3.3.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jackson-core-asl-1.9.13.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/orc-shims-1.7.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-shims-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/stream-2.9.6.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/xbean-asm9-shaded-4.20.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/xz-1.8.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jdo-api-3.0.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-buffer-4.1.72.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/compress-lzf-1.0.3.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jackson-core-2.13.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jersey-server-2.34.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-common-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-lang-2.6.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/lz4-java-1.8.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/macro-compat_2.12-1.1.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/arrow-memory-netty-6.0.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jakarta.validation-api-2.0.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/metrics-core-4.2.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/snappy-java-1.1.8.4.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-resolver-4.1.72.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/transaction-api-1.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-common-4.1.72.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/curator-framework-2.13.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/zookeeper-3.6.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hk2-api-2.6.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jodd-core-3.5.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-mllib-local_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/log4j-slf4j-impl-2.17.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-storage-api-2.7.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-exec-2.3.9-core.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/scala-compiler-2.12.15.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-transport-native-kqueue-4.1.72.Final-osx-x86_64.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jakarta.ws.rs-api-2.1.6.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/httpcore-4.4.14.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-llap-common-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/curator-client-2.13.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/chill_2.12-0.10.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/javolution-5.5.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/log4j-api-2.17.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/zookeeper-jute-3.6.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-transport-native-epoll-4.1.72.Final-linux-aarch_64.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-lang3-3.12.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/zstd-jni-1.5.1-1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/scala-parser-combinators_2.12-1.1.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/json-1.8.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-shims-0.23-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/conf/":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/parquet-format-structures-1.12.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/protobuf-java-2.5.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/RoaringBitmap-0.9.23.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/arpack_combined_all-0.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/osgi-resource-locator-1.0.3.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/parquet-common-1.12.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jaxb-runtime-2.3.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-compiler-3.0.16.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jsr305-3.0.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-sql_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-launcher_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/curator-recipes-2.13.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/dropwizard-metrics-hadoop-metrics2-reporter-0.1.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-crypto-1.1.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hk2-locator-2.6.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jackson-annotations-2.13.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/scala-library-2.12.15.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/arrow-memory-core-6.0.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/metrics-json-4.2.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/core-1.1.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/activation-1.1.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/scala-xml_2.12-1.2.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jakarta.servlet-api-4.0.3.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-network-shuffle_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hadoop-client-api-3.3.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-transport-native-epoll-4.1.72.Final-linux-x86_64.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/breeze_2.12-1.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-core_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-compress-1.21.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/oro-2.0.8.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-logging-1.1.3.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/super-csv-2.2.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/avro-1.11.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/htrace-core4-4.1.0-incubating.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/metrics-jmx-4.2.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/chill-java-0.10.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/JTransforms-3.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jcl-over-slf4j-1.7.32.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-pool-1.5.4.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/slf4j-api-1.7.32.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-mllib_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-sketch_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/datanucleus-core-4.1.17.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/gson-2.2.4.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/avro-mapred-1.11.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spire_2.12-0.17.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/parquet-encoding-1.12.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jul-to-slf4j-1.7.32.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jackson-mapper-asl-1.9.13.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jersey-hk2-2.34.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/ST4-4.0.4.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/libfb303-0.9.3.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/aircompressor-0.21.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/scala-collection-compat_2.12-2.1.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-all-4.1.72.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/commons-dbcp-1.4.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-beeline-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/annotations-17.0.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-vector-code-gen-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/rocksdbjni-6.20.3.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/py4j-0.10.9.3.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/libthrift-0.12.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/orc-core-1.7.2.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-serde-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/spark-graphx_2.12-3.3.0-SNAPSHOT.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/datanucleus-api-jdo-4.2.4.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/javassist-3.25.0-GA.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jta-1.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/joda-time-2.10.12.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hadoop-yarn-server-web-proxy-3.3.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/hive-cli-2.3.9.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/aopalliance-repackaged-2.6.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/avro-ipc-1.11.0.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/netty-transport-classes-kqueue-4.1.72.Final.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/univocity-parsers-2.9.1.jar":"System Classpath","/Users/<USER>/Code/stczwd/spark/dist/jars/jersey-client-2.34.jar":"System Classpath"}}
{"Event":"SparkListenerApplicationStart","App Name":"Spark shell","App ID":"local-1642039451826","Timestamp":1642039450519,"User":"lijunqing"}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerSQLExecutionStart","executionId":0,"description":"count at <console>:23","details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","physicalPlanDescription":"== Physical Plan ==\nAdaptiveSparkPlan (7)\n+- HashAggregate (6)\n   +- Exchange (5)\n      +- HashAggregate (4)\n         +- Exchange (3)\n            +- Project (2)\n               +- Range (1)\n\n\n(1) Range\nOutput [1]: [id#0L]\nArguments: Range (0, 100, step=1, splits=Some(8))\n\n(2) Project\nOutput: []\nInput [1]: [id#0L]\n\n(3) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#15]\n\n(4) HashAggregate\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#8L]\nResults [1]: [count#9L]\n\n(5) Exchange\nInput [1]: [count#9L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#19]\n\n(6) HashAggregate\nInput [1]: [count#9L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#5L]\nResults [1]: [count(1)#5L AS count#6L]\n\n(7) AdaptiveSparkPlan\nOutput [1]: [count#6L]\nArguments: isFinalPlan=false\n\n","sparkPlanInfo":{"nodeName":"AdaptiveSparkPlan","simpleString":"AdaptiveSparkPlan isFinalPlan=false","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[count(1)])","children":[{"nodeName":"Exchange","simpleString":"Exchange SinglePartition, ENSURE_REQUIREMENTS, [id=#19]","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[partial_count(1)])","children":[{"nodeName":"Exchange","simpleString":"Exchange RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#15]","children":[{"nodeName":"Project","simpleString":"Project","children":[{"nodeName":"Range","simpleString":"Range (0, 100, step=1, splits=8)","children":[],"metadata":{},"metrics":[{"name":"number of output rows","accumulatorId":36,"metricType":"sum"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":34,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":35,"metricType":"nsTiming"},{"name":"records read","accumulatorId":32,"metricType":"sum"},{"name":"local bytes read","accumulatorId":30,"metricType":"size"},{"name":"fetch wait time","accumulatorId":31,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":28,"metricType":"size"},{"name":"local blocks read","accumulatorId":27,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":26,"metricType":"sum"},{"name":"data size","accumulatorId":24,"metricType":"size"},{"name":"number of partitions","accumulatorId":25,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":29,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":33,"metricType":"size"}]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":20,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":21,"metricType":"timing"},{"name":"peak memory","accumulatorId":19,"metricType":"size"},{"name":"number of output rows","accumulatorId":18,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":23,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":22,"metricType":"average"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":16,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":17,"metricType":"nsTiming"},{"name":"records read","accumulatorId":14,"metricType":"sum"},{"name":"local bytes read","accumulatorId":12,"metricType":"size"},{"name":"fetch wait time","accumulatorId":13,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":10,"metricType":"size"},{"name":"local blocks read","accumulatorId":9,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":8,"metricType":"sum"},{"name":"data size","accumulatorId":6,"metricType":"size"},{"name":"number of partitions","accumulatorId":7,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":11,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":15,"metricType":"size"}]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":2,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":3,"metricType":"timing"},{"name":"peak memory","accumulatorId":1,"metricType":"size"},{"name":"number of output rows","accumulatorId":0,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":5,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":4,"metricType":"average"}]}],"metadata":{},"metrics":[]},"time":1642039495581,"modifiedConfigs":{}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerSQLAdaptiveExecutionUpdate","executionId":0,"physicalPlanDescription":"== Physical Plan ==\nAdaptiveSparkPlan (12)\n+- == Current Plan ==\n   HashAggregate (7)\n   +- Exchange (6)\n      +- HashAggregate (5)\n         +- ShuffleQueryStage (4)\n            +- Exchange (3)\n               +- * Project (2)\n                  +- * Range (1)\n+- == Initial Plan ==\n   HashAggregate (11)\n   +- Exchange (10)\n      +- HashAggregate (9)\n         +- Exchange (8)\n            +- Project (2)\n               +- Range (1)\n\n\n(1) Range [codegen id : 1]\nOutput [1]: [id#0L]\nArguments: Range (0, 100, step=1, splits=Some(8))\n\n(2) Project [codegen id : 1]\nOutput: []\nInput [1]: [id#0L]\n\n(3) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#28]\n\n(4) ShuffleQueryStage\nOutput: []\nArguments: 0\n\n(5) HashAggregate\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#8L]\nResults [1]: [count#9L]\n\n(6) Exchange\nInput [1]: [count#9L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#33]\n\n(7) HashAggregate\nInput [1]: [count#9L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#5L]\nResults [1]: [count(1)#5L AS count#6L]\n\n(8) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#15]\n\n(9) HashAggregate\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#8L]\nResults [1]: [count#9L]\n\n(10) Exchange\nInput [1]: [count#9L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#19]\n\n(11) HashAggregate\nInput [1]: [count#9L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#5L]\nResults [1]: [count(1)#5L AS count#6L]\n\n(12) AdaptiveSparkPlan\nOutput [1]: [count#6L]\nArguments: isFinalPlan=false\n\n","sparkPlanInfo":{"nodeName":"AdaptiveSparkPlan","simpleString":"AdaptiveSparkPlan isFinalPlan=false","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[count(1)])","children":[{"nodeName":"Exchange","simpleString":"Exchange SinglePartition, ENSURE_REQUIREMENTS, [id=#33]","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[partial_count(1)])","children":[{"nodeName":"ShuffleQueryStage","simpleString":"ShuffleQueryStage 0","children":[{"nodeName":"Exchange","simpleString":"Exchange RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#28]","children":[{"nodeName":"WholeStageCodegen (1)","simpleString":"WholeStageCodegen (1)","children":[{"nodeName":"Project","simpleString":"Project","children":[{"nodeName":"Range","simpleString":"Range (0, 100, step=1, splits=8)","children":[],"metadata":{},"metrics":[{"name":"number of output rows","accumulatorId":36,"metricType":"sum"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":73,"metricType":"timing"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":71,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":72,"metricType":"nsTiming"},{"name":"records read","accumulatorId":69,"metricType":"sum"},{"name":"local bytes read","accumulatorId":67,"metricType":"size"},{"name":"fetch wait time","accumulatorId":68,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":65,"metricType":"size"},{"name":"local blocks read","accumulatorId":64,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":63,"metricType":"sum"},{"name":"data size","accumulatorId":61,"metricType":"size"},{"name":"number of partitions","accumulatorId":62,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":66,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":70,"metricType":"size"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":57,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":58,"metricType":"timing"},{"name":"peak memory","accumulatorId":56,"metricType":"size"},{"name":"number of output rows","accumulatorId":55,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":60,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":59,"metricType":"average"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":53,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":54,"metricType":"nsTiming"},{"name":"records read","accumulatorId":51,"metricType":"sum"},{"name":"local bytes read","accumulatorId":49,"metricType":"size"},{"name":"fetch wait time","accumulatorId":50,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":47,"metricType":"size"},{"name":"local blocks read","accumulatorId":46,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":45,"metricType":"sum"},{"name":"data size","accumulatorId":43,"metricType":"size"},{"name":"number of partitions","accumulatorId":44,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":48,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":52,"metricType":"size"}]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":39,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":40,"metricType":"timing"},{"name":"peak memory","accumulatorId":38,"metricType":"size"},{"name":"number of output rows","accumulatorId":37,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":42,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":41,"metricType":"average"}]}],"metadata":{},"metrics":[]}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerDriverAccumUpdates","executionId":0,"accumUpdates":[[62,10]]}
{"Event":"SparkListenerJobStart","Job ID":0,"Submission Time":1642039496191,"Stage Infos":[{"Stage ID":0,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":8,"RDD Info":[{"RDD ID":4,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[3],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":1,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[0],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":3,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[2],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":2,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[1],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":0,"Name":"ParallelCollectionRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0}],"Stage IDs":[0],"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"0","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerStageSubmitted","Stage Info":{"Stage ID":0,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":8,"RDD Info":[{"RDD ID":4,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[3],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":1,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[0],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":3,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[2],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":2,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[1],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":0,"Name":"ParallelCollectionRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039496205,"Accumulables":[],"Resource Profile Id":0},"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"0","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerTaskStart","Stage ID":0,"Stage Attempt ID":0,"Task Info":{"Task ID":0,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039496413,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":0,"Stage Attempt ID":0,"Task Info":{"Task ID":1,"Index":1,"Attempt":0,"Partition ID":1,"Launch Time":1642039496425,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":0,"Stage Attempt ID":0,"Task Info":{"Task ID":2,"Index":2,"Attempt":0,"Partition ID":2,"Launch Time":1642039496425,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":0,"Stage Attempt ID":0,"Task Info":{"Task ID":3,"Index":3,"Attempt":0,"Partition ID":3,"Launch Time":1642039496425,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":0,"Stage Attempt ID":0,"Task Info":{"Task ID":4,"Index":4,"Attempt":0,"Partition ID":4,"Launch Time":1642039496426,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":0,"Stage Attempt ID":0,"Task Info":{"Task ID":5,"Index":5,"Attempt":0,"Partition ID":5,"Launch Time":1642039496426,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":0,"Stage Attempt ID":0,"Task Info":{"Task ID":6,"Index":6,"Attempt":0,"Partition ID":6,"Launch Time":1642039496427,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":0,"Stage Attempt ID":0,"Task Info":{"Task ID":7,"Index":7,"Attempt":0,"Partition ID":7,"Launch Time":1642039496427,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":0,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":6,"Index":6,"Attempt":0,"Partition ID":6,"Launch Time":1642039496427,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039496888,"Failed":false,"Killed":false,"Accumulables":[{"ID":36,"Name":"number of output rows","Update":"12","Value":"12","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":61,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":70,"Name":"shuffle bytes written","Update":"468","Value":"468","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":71,"Name":"shuffle records written","Update":"12","Value":"12","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":72,"Name":"shuffle write time","Update":"32402787","Value":"32402787","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":73,"Name":"duration","Update":"27","Value":"27","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":74,"Name":"internal.metrics.executorDeserializeTime","Update":277,"Value":277,"Internal":true,"Count Failed Values":true},{"ID":75,"Name":"internal.metrics.executorDeserializeCpuTime","Update":131261000,"Value":131261000,"Internal":true,"Count Failed Values":true},{"ID":76,"Name":"internal.metrics.executorRunTime","Update":158,"Value":158,"Internal":true,"Count Failed Values":true},{"ID":77,"Name":"internal.metrics.executorCpuTime","Update":75295000,"Value":75295000,"Internal":true,"Count Failed Values":true},{"ID":78,"Name":"internal.metrics.resultSize","Update":1836,"Value":1836,"Internal":true,"Count Failed Values":true},{"ID":79,"Name":"internal.metrics.jvmGCTime","Update":11,"Value":11,"Internal":true,"Count Failed Values":true},{"ID":80,"Name":"internal.metrics.resultSerializationTime","Update":1,"Value":1,"Internal":true,"Count Failed Values":true},{"ID":92,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":468,"Value":468,"Internal":true,"Count Failed Values":true},{"ID":93,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":12,"Value":12,"Internal":true,"Count Failed Values":true},{"ID":94,"Name":"internal.metrics.shuffle.write.writeTime","Update":32402787,"Value":32402787,"Internal":true,"Count Failed Values":true},{"ID":96,"Name":"internal.metrics.input.recordsRead","Update":12,"Value":12,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":319351248,"JVMOffHeapMemory":183565360,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":18842,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":18842,"OffHeapUnifiedMemory":0,"DirectPoolMemory":3897,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":13,"MinorGCTime":120,"MajorGCCount":4,"MajorGCTime":353,"TotalGCTime":473},"Task Metrics":{"Executor Deserialize Time":277,"Executor Deserialize CPU Time":131261000,"Executor Run Time":158,"Executor CPU Time":75295000,"Peak Execution Memory":0,"Result Size":1836,"JVM GC Time":11,"Result Serialization Time":1,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":468,"Shuffle Write Time":32402787,"Shuffle Records Written":12},"Input Metrics":{"Bytes Read":0,"Records Read":12},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":0,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":5,"Index":5,"Attempt":0,"Partition ID":5,"Launch Time":1642039496426,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039496891,"Failed":false,"Killed":false,"Accumulables":[{"ID":36,"Name":"number of output rows","Update":"13","Value":"25","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":61,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":70,"Name":"shuffle bytes written","Update":"472","Value":"940","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":71,"Name":"shuffle records written","Update":"13","Value":"25","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":72,"Name":"shuffle write time","Update":"31370834","Value":"63773621","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":73,"Name":"duration","Update":"27","Value":"54","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":74,"Name":"internal.metrics.executorDeserializeTime","Update":277,"Value":554,"Internal":true,"Count Failed Values":true},{"ID":75,"Name":"internal.metrics.executorDeserializeCpuTime","Update":156363000,"Value":287624000,"Internal":true,"Count Failed Values":true},{"ID":76,"Name":"internal.metrics.executorRunTime","Update":158,"Value":316,"Internal":true,"Count Failed Values":true},{"ID":77,"Name":"internal.metrics.executorCpuTime","Update":60658000,"Value":135953000,"Internal":true,"Count Failed Values":true},{"ID":78,"Name":"internal.metrics.resultSize","Update":1836,"Value":3672,"Internal":true,"Count Failed Values":true},{"ID":79,"Name":"internal.metrics.jvmGCTime","Update":11,"Value":22,"Internal":true,"Count Failed Values":true},{"ID":80,"Name":"internal.metrics.resultSerializationTime","Update":1,"Value":2,"Internal":true,"Count Failed Values":true},{"ID":92,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":472,"Value":940,"Internal":true,"Count Failed Values":true},{"ID":93,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":13,"Value":25,"Internal":true,"Count Failed Values":true},{"ID":94,"Name":"internal.metrics.shuffle.write.writeTime","Update":31370834,"Value":63773621,"Internal":true,"Count Failed Values":true},{"ID":96,"Name":"internal.metrics.input.recordsRead","Update":13,"Value":25,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":319351248,"JVMOffHeapMemory":183565360,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":18842,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":18842,"OffHeapUnifiedMemory":0,"DirectPoolMemory":3897,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":13,"MinorGCTime":120,"MajorGCCount":4,"MajorGCTime":353,"TotalGCTime":473},"Task Metrics":{"Executor Deserialize Time":277,"Executor Deserialize CPU Time":156363000,"Executor Run Time":158,"Executor CPU Time":60658000,"Peak Execution Memory":0,"Result Size":1836,"JVM GC Time":11,"Result Serialization Time":1,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":472,"Shuffle Write Time":31370834,"Shuffle Records Written":13},"Input Metrics":{"Bytes Read":0,"Records Read":13},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":0,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":4,"Index":4,"Attempt":0,"Partition ID":4,"Launch Time":1642039496426,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039496892,"Failed":false,"Killed":false,"Accumulables":[{"ID":36,"Name":"number of output rows","Update":"12","Value":"37","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":61,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":70,"Name":"shuffle bytes written","Update":"468","Value":"1408","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":71,"Name":"shuffle records written","Update":"12","Value":"37","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":72,"Name":"shuffle write time","Update":"32008329","Value":"95781950","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":73,"Name":"duration","Update":"27","Value":"81","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":74,"Name":"internal.metrics.executorDeserializeTime","Update":277,"Value":831,"Internal":true,"Count Failed Values":true},{"ID":75,"Name":"internal.metrics.executorDeserializeCpuTime","Update":143825000,"Value":431449000,"Internal":true,"Count Failed Values":true},{"ID":76,"Name":"internal.metrics.executorRunTime","Update":158,"Value":474,"Internal":true,"Count Failed Values":true},{"ID":77,"Name":"internal.metrics.executorCpuTime","Update":42837000,"Value":178790000,"Internal":true,"Count Failed Values":true},{"ID":78,"Name":"internal.metrics.resultSize","Update":1836,"Value":5508,"Internal":true,"Count Failed Values":true},{"ID":79,"Name":"internal.metrics.jvmGCTime","Update":11,"Value":33,"Internal":true,"Count Failed Values":true},{"ID":80,"Name":"internal.metrics.resultSerializationTime","Update":1,"Value":3,"Internal":true,"Count Failed Values":true},{"ID":92,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":468,"Value":1408,"Internal":true,"Count Failed Values":true},{"ID":93,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":12,"Value":37,"Internal":true,"Count Failed Values":true},{"ID":94,"Name":"internal.metrics.shuffle.write.writeTime","Update":32008329,"Value":95781950,"Internal":true,"Count Failed Values":true},{"ID":96,"Name":"internal.metrics.input.recordsRead","Update":12,"Value":37,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":319351248,"JVMOffHeapMemory":183565360,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":18842,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":18842,"OffHeapUnifiedMemory":0,"DirectPoolMemory":3897,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":13,"MinorGCTime":120,"MajorGCCount":4,"MajorGCTime":353,"TotalGCTime":473},"Task Metrics":{"Executor Deserialize Time":277,"Executor Deserialize CPU Time":143825000,"Executor Run Time":158,"Executor CPU Time":42837000,"Peak Execution Memory":0,"Result Size":1836,"JVM GC Time":11,"Result Serialization Time":1,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":468,"Shuffle Write Time":32008329,"Shuffle Records Written":12},"Input Metrics":{"Bytes Read":0,"Records Read":12},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":0,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":2,"Index":2,"Attempt":0,"Partition ID":2,"Launch Time":1642039496425,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039496892,"Failed":false,"Killed":false,"Accumulables":[{"ID":36,"Name":"number of output rows","Update":"12","Value":"49","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":61,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":70,"Name":"shuffle bytes written","Update":"468","Value":"1876","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":71,"Name":"shuffle records written","Update":"12","Value":"49","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":72,"Name":"shuffle write time","Update":"31530251","Value":"127312201","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":73,"Name":"duration","Update":"27","Value":"108","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":74,"Name":"internal.metrics.executorDeserializeTime","Update":277,"Value":1108,"Internal":true,"Count Failed Values":true},{"ID":75,"Name":"internal.metrics.executorDeserializeCpuTime","Update":144165000,"Value":575614000,"Internal":true,"Count Failed Values":true},{"ID":76,"Name":"internal.metrics.executorRunTime","Update":158,"Value":632,"Internal":true,"Count Failed Values":true},{"ID":77,"Name":"internal.metrics.executorCpuTime","Update":47904000,"Value":226694000,"Internal":true,"Count Failed Values":true},{"ID":78,"Name":"internal.metrics.resultSize","Update":1836,"Value":7344,"Internal":true,"Count Failed Values":true},{"ID":79,"Name":"internal.metrics.jvmGCTime","Update":11,"Value":44,"Internal":true,"Count Failed Values":true},{"ID":80,"Name":"internal.metrics.resultSerializationTime","Update":1,"Value":4,"Internal":true,"Count Failed Values":true},{"ID":92,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":468,"Value":1876,"Internal":true,"Count Failed Values":true},{"ID":93,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":12,"Value":49,"Internal":true,"Count Failed Values":true},{"ID":94,"Name":"internal.metrics.shuffle.write.writeTime","Update":31530251,"Value":127312201,"Internal":true,"Count Failed Values":true},{"ID":96,"Name":"internal.metrics.input.recordsRead","Update":12,"Value":49,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":319351248,"JVMOffHeapMemory":183565360,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":18842,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":18842,"OffHeapUnifiedMemory":0,"DirectPoolMemory":3897,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":13,"MinorGCTime":120,"MajorGCCount":4,"MajorGCTime":353,"TotalGCTime":473},"Task Metrics":{"Executor Deserialize Time":277,"Executor Deserialize CPU Time":144165000,"Executor Run Time":158,"Executor CPU Time":47904000,"Peak Execution Memory":0,"Result Size":1836,"JVM GC Time":11,"Result Serialization Time":1,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":468,"Shuffle Write Time":31530251,"Shuffle Records Written":12},"Input Metrics":{"Bytes Read":0,"Records Read":12},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":0,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":0,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039496413,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039496893,"Failed":false,"Killed":false,"Accumulables":[{"ID":36,"Name":"number of output rows","Update":"12","Value":"61","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":61,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":70,"Name":"shuffle bytes written","Update":"468","Value":"2344","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":71,"Name":"shuffle records written","Update":"12","Value":"61","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":72,"Name":"shuffle write time","Update":"33968833","Value":"161281034","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":73,"Name":"duration","Update":"27","Value":"135","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":74,"Name":"internal.metrics.executorDeserializeTime","Update":277,"Value":1385,"Internal":true,"Count Failed Values":true},{"ID":75,"Name":"internal.metrics.executorDeserializeCpuTime","Update":142074000,"Value":717688000,"Internal":true,"Count Failed Values":true},{"ID":76,"Name":"internal.metrics.executorRunTime","Update":158,"Value":790,"Internal":true,"Count Failed Values":true},{"ID":77,"Name":"internal.metrics.executorCpuTime","Update":48671000,"Value":275365000,"Internal":true,"Count Failed Values":true},{"ID":78,"Name":"internal.metrics.resultSize","Update":1836,"Value":9180,"Internal":true,"Count Failed Values":true},{"ID":79,"Name":"internal.metrics.jvmGCTime","Update":11,"Value":55,"Internal":true,"Count Failed Values":true},{"ID":80,"Name":"internal.metrics.resultSerializationTime","Update":1,"Value":5,"Internal":true,"Count Failed Values":true},{"ID":92,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":468,"Value":2344,"Internal":true,"Count Failed Values":true},{"ID":93,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":12,"Value":61,"Internal":true,"Count Failed Values":true},{"ID":94,"Name":"internal.metrics.shuffle.write.writeTime","Update":33968833,"Value":161281034,"Internal":true,"Count Failed Values":true},{"ID":96,"Name":"internal.metrics.input.recordsRead","Update":12,"Value":61,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":319351248,"JVMOffHeapMemory":183565360,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":18842,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":18842,"OffHeapUnifiedMemory":0,"DirectPoolMemory":3897,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":13,"MinorGCTime":120,"MajorGCCount":4,"MajorGCTime":353,"TotalGCTime":473},"Task Metrics":{"Executor Deserialize Time":277,"Executor Deserialize CPU Time":142074000,"Executor Run Time":158,"Executor CPU Time":48671000,"Peak Execution Memory":0,"Result Size":1836,"JVM GC Time":11,"Result Serialization Time":1,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":468,"Shuffle Write Time":33968833,"Shuffle Records Written":12},"Input Metrics":{"Bytes Read":0,"Records Read":12},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":0,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":1,"Index":1,"Attempt":0,"Partition ID":1,"Launch Time":1642039496425,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039496893,"Failed":false,"Killed":false,"Accumulables":[{"ID":36,"Name":"number of output rows","Update":"13","Value":"74","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":61,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":70,"Name":"shuffle bytes written","Update":"472","Value":"2816","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":71,"Name":"shuffle records written","Update":"13","Value":"74","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":72,"Name":"shuffle write time","Update":"32707462","Value":"193988496","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":73,"Name":"duration","Update":"27","Value":"162","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":74,"Name":"internal.metrics.executorDeserializeTime","Update":277,"Value":1662,"Internal":true,"Count Failed Values":true},{"ID":75,"Name":"internal.metrics.executorDeserializeCpuTime","Update":141303000,"Value":858991000,"Internal":true,"Count Failed Values":true},{"ID":76,"Name":"internal.metrics.executorRunTime","Update":158,"Value":948,"Internal":true,"Count Failed Values":true},{"ID":77,"Name":"internal.metrics.executorCpuTime","Update":52706000,"Value":328071000,"Internal":true,"Count Failed Values":true},{"ID":78,"Name":"internal.metrics.resultSize","Update":1836,"Value":11016,"Internal":true,"Count Failed Values":true},{"ID":79,"Name":"internal.metrics.jvmGCTime","Update":11,"Value":66,"Internal":true,"Count Failed Values":true},{"ID":80,"Name":"internal.metrics.resultSerializationTime","Update":1,"Value":6,"Internal":true,"Count Failed Values":true},{"ID":92,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":472,"Value":2816,"Internal":true,"Count Failed Values":true},{"ID":93,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":13,"Value":74,"Internal":true,"Count Failed Values":true},{"ID":94,"Name":"internal.metrics.shuffle.write.writeTime","Update":32707462,"Value":193988496,"Internal":true,"Count Failed Values":true},{"ID":96,"Name":"internal.metrics.input.recordsRead","Update":13,"Value":74,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":319351248,"JVMOffHeapMemory":183565360,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":18842,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":18842,"OffHeapUnifiedMemory":0,"DirectPoolMemory":3897,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":13,"MinorGCTime":120,"MajorGCCount":4,"MajorGCTime":353,"TotalGCTime":473},"Task Metrics":{"Executor Deserialize Time":277,"Executor Deserialize CPU Time":141303000,"Executor Run Time":158,"Executor CPU Time":52706000,"Peak Execution Memory":0,"Result Size":1836,"JVM GC Time":11,"Result Serialization Time":1,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":472,"Shuffle Write Time":32707462,"Shuffle Records Written":13},"Input Metrics":{"Bytes Read":0,"Records Read":13},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":0,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":7,"Index":7,"Attempt":0,"Partition ID":7,"Launch Time":1642039496427,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039496893,"Failed":false,"Killed":false,"Accumulables":[{"ID":36,"Name":"number of output rows","Update":"13","Value":"87","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":61,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":70,"Name":"shuffle bytes written","Update":"472","Value":"3288","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":71,"Name":"shuffle records written","Update":"13","Value":"87","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":72,"Name":"shuffle write time","Update":"32328957","Value":"226317453","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":73,"Name":"duration","Update":"27","Value":"189","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":74,"Name":"internal.metrics.executorDeserializeTime","Update":277,"Value":1939,"Internal":true,"Count Failed Values":true},{"ID":75,"Name":"internal.metrics.executorDeserializeCpuTime","Update":145865000,"Value":1004856000,"Internal":true,"Count Failed Values":true},{"ID":76,"Name":"internal.metrics.executorRunTime","Update":158,"Value":1106,"Internal":true,"Count Failed Values":true},{"ID":77,"Name":"internal.metrics.executorCpuTime","Update":42833000,"Value":370904000,"Internal":true,"Count Failed Values":true},{"ID":78,"Name":"internal.metrics.resultSize","Update":1836,"Value":12852,"Internal":true,"Count Failed Values":true},{"ID":79,"Name":"internal.metrics.jvmGCTime","Update":11,"Value":77,"Internal":true,"Count Failed Values":true},{"ID":80,"Name":"internal.metrics.resultSerializationTime","Update":1,"Value":7,"Internal":true,"Count Failed Values":true},{"ID":92,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":472,"Value":3288,"Internal":true,"Count Failed Values":true},{"ID":93,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":13,"Value":87,"Internal":true,"Count Failed Values":true},{"ID":94,"Name":"internal.metrics.shuffle.write.writeTime","Update":32328957,"Value":226317453,"Internal":true,"Count Failed Values":true},{"ID":96,"Name":"internal.metrics.input.recordsRead","Update":13,"Value":87,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":319351248,"JVMOffHeapMemory":183565360,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":18842,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":18842,"OffHeapUnifiedMemory":0,"DirectPoolMemory":3897,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":13,"MinorGCTime":120,"MajorGCCount":4,"MajorGCTime":353,"TotalGCTime":473},"Task Metrics":{"Executor Deserialize Time":277,"Executor Deserialize CPU Time":145865000,"Executor Run Time":158,"Executor CPU Time":42833000,"Peak Execution Memory":0,"Result Size":1836,"JVM GC Time":11,"Result Serialization Time":1,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":472,"Shuffle Write Time":32328957,"Shuffle Records Written":13},"Input Metrics":{"Bytes Read":0,"Records Read":13},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":0,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":3,"Index":3,"Attempt":0,"Partition ID":3,"Launch Time":1642039496425,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039496893,"Failed":false,"Killed":false,"Accumulables":[{"ID":36,"Name":"number of output rows","Update":"13","Value":"100","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":61,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":70,"Name":"shuffle bytes written","Update":"472","Value":"3760","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":71,"Name":"shuffle records written","Update":"13","Value":"100","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":72,"Name":"shuffle write time","Update":"33237160","Value":"259554613","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":73,"Name":"duration","Update":"27","Value":"216","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":74,"Name":"internal.metrics.executorDeserializeTime","Update":277,"Value":2216,"Internal":true,"Count Failed Values":true},{"ID":75,"Name":"internal.metrics.executorDeserializeCpuTime","Update":81219000,"Value":1086075000,"Internal":true,"Count Failed Values":true},{"ID":76,"Name":"internal.metrics.executorRunTime","Update":158,"Value":1264,"Internal":true,"Count Failed Values":true},{"ID":77,"Name":"internal.metrics.executorCpuTime","Update":50624000,"Value":421528000,"Internal":true,"Count Failed Values":true},{"ID":78,"Name":"internal.metrics.resultSize","Update":1836,"Value":14688,"Internal":true,"Count Failed Values":true},{"ID":79,"Name":"internal.metrics.jvmGCTime","Update":11,"Value":88,"Internal":true,"Count Failed Values":true},{"ID":80,"Name":"internal.metrics.resultSerializationTime","Update":1,"Value":8,"Internal":true,"Count Failed Values":true},{"ID":92,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":472,"Value":3760,"Internal":true,"Count Failed Values":true},{"ID":93,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":13,"Value":100,"Internal":true,"Count Failed Values":true},{"ID":94,"Name":"internal.metrics.shuffle.write.writeTime","Update":33237160,"Value":259554613,"Internal":true,"Count Failed Values":true},{"ID":96,"Name":"internal.metrics.input.recordsRead","Update":13,"Value":100,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":319351248,"JVMOffHeapMemory":183565360,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":18842,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":18842,"OffHeapUnifiedMemory":0,"DirectPoolMemory":3897,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":13,"MinorGCTime":120,"MajorGCCount":4,"MajorGCTime":353,"TotalGCTime":473},"Task Metrics":{"Executor Deserialize Time":277,"Executor Deserialize CPU Time":81219000,"Executor Run Time":158,"Executor CPU Time":50624000,"Peak Execution Memory":0,"Result Size":1836,"JVM GC Time":11,"Result Serialization Time":1,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":472,"Shuffle Write Time":33237160,"Shuffle Records Written":13},"Input Metrics":{"Bytes Read":0,"Records Read":13},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerStageCompleted","Stage Info":{"Stage ID":0,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":8,"RDD Info":[{"RDD ID":4,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[3],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":1,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[0],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":3,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[2],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":2,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[1],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":0,"Name":"ParallelCollectionRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039496205,"Completion Time":1642039496907,"Accumulables":[{"ID":36,"Name":"number of output rows","Value":"100","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":61,"Name":"data size","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":70,"Name":"shuffle bytes written","Value":"3760","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":71,"Name":"shuffle records written","Value":"100","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":72,"Name":"shuffle write time","Value":"259554613","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":73,"Name":"duration","Value":"216","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":74,"Name":"internal.metrics.executorDeserializeTime","Value":2216,"Internal":true,"Count Failed Values":true},{"ID":75,"Name":"internal.metrics.executorDeserializeCpuTime","Value":1086075000,"Internal":true,"Count Failed Values":true},{"ID":76,"Name":"internal.metrics.executorRunTime","Value":1264,"Internal":true,"Count Failed Values":true},{"ID":77,"Name":"internal.metrics.executorCpuTime","Value":421528000,"Internal":true,"Count Failed Values":true},{"ID":78,"Name":"internal.metrics.resultSize","Value":14688,"Internal":true,"Count Failed Values":true},{"ID":79,"Name":"internal.metrics.jvmGCTime","Value":88,"Internal":true,"Count Failed Values":true},{"ID":80,"Name":"internal.metrics.resultSerializationTime","Value":8,"Internal":true,"Count Failed Values":true},{"ID":92,"Name":"internal.metrics.shuffle.write.bytesWritten","Value":3760,"Internal":true,"Count Failed Values":true},{"ID":93,"Name":"internal.metrics.shuffle.write.recordsWritten","Value":100,"Internal":true,"Count Failed Values":true},{"ID":94,"Name":"internal.metrics.shuffle.write.writeTime","Value":259554613,"Internal":true,"Count Failed Values":true},{"ID":96,"Name":"internal.metrics.input.recordsRead","Value":100,"Internal":true,"Count Failed Values":true}],"Resource Profile Id":0}}
{"Event":"SparkListenerJobEnd","Job ID":0,"Completion Time":1642039496914,"Job Result":{"Result":"JobSucceeded"}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerSQLAdaptiveExecutionUpdate","executionId":0,"physicalPlanDescription":"== Physical Plan ==\nAdaptiveSparkPlan (13)\n+- == Current Plan ==\n   HashAggregate (8)\n   +- ShuffleQueryStage (7)\n      +- Exchange (6)\n         +- * HashAggregate (5)\n            +- ShuffleQueryStage (4)\n               +- Exchange (3)\n                  +- * Project (2)\n                     +- * Range (1)\n+- == Initial Plan ==\n   HashAggregate (12)\n   +- Exchange (11)\n      +- HashAggregate (10)\n         +- Exchange (9)\n            +- Project (2)\n               +- Range (1)\n\n\n(1) Range [codegen id : 1]\nOutput [1]: [id#0L]\nArguments: Range (0, 100, step=1, splits=Some(8))\n\n(2) Project [codegen id : 1]\nOutput: []\nInput [1]: [id#0L]\n\n(3) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#28]\n\n(4) ShuffleQueryStage\nOutput: []\nArguments: 0\n\n(5) HashAggregate [codegen id : 2]\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#8L]\nResults [1]: [count#9L]\n\n(6) Exchange\nInput [1]: [count#9L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#49]\n\n(7) ShuffleQueryStage\nOutput [1]: [count#9L]\nArguments: 1\n\n(8) HashAggregate\nInput [1]: [count#9L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#5L]\nResults [1]: [count(1)#5L AS count#6L]\n\n(9) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#15]\n\n(10) HashAggregate\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#8L]\nResults [1]: [count#9L]\n\n(11) Exchange\nInput [1]: [count#9L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#19]\n\n(12) HashAggregate\nInput [1]: [count#9L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#5L]\nResults [1]: [count(1)#5L AS count#6L]\n\n(13) AdaptiveSparkPlan\nOutput [1]: [count#6L]\nArguments: isFinalPlan=false\n\n","sparkPlanInfo":{"nodeName":"AdaptiveSparkPlan","simpleString":"AdaptiveSparkPlan isFinalPlan=false","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[count(1)])","children":[{"nodeName":"ShuffleQueryStage","simpleString":"ShuffleQueryStage 1","children":[{"nodeName":"Exchange","simpleString":"Exchange SinglePartition, ENSURE_REQUIREMENTS, [id=#49]","children":[{"nodeName":"WholeStageCodegen (2)","simpleString":"WholeStageCodegen (2)","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[partial_count(1)])","children":[{"nodeName":"InputAdapter","simpleString":"InputAdapter","children":[{"nodeName":"ShuffleQueryStage","simpleString":"ShuffleQueryStage 0","children":[{"nodeName":"Exchange","simpleString":"Exchange RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#28]","children":[{"nodeName":"WholeStageCodegen (1)","simpleString":"WholeStageCodegen (1)","children":[{"nodeName":"Project","simpleString":"Project","children":[{"nodeName":"Range","simpleString":"Range (0, 100, step=1, splits=8)","children":[],"metadata":{},"metrics":[{"name":"number of output rows","accumulatorId":36,"metricType":"sum"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":73,"metricType":"timing"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":71,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":72,"metricType":"nsTiming"},{"name":"records read","accumulatorId":69,"metricType":"sum"},{"name":"local bytes read","accumulatorId":67,"metricType":"size"},{"name":"fetch wait time","accumulatorId":68,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":65,"metricType":"size"},{"name":"local blocks read","accumulatorId":64,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":63,"metricType":"sum"},{"name":"data size","accumulatorId":61,"metricType":"size"},{"name":"number of partitions","accumulatorId":62,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":66,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":70,"metricType":"size"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":120,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":121,"metricType":"timing"},{"name":"peak memory","accumulatorId":119,"metricType":"size"},{"name":"number of output rows","accumulatorId":118,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":123,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":122,"metricType":"average"}]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":117,"metricType":"timing"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":115,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":116,"metricType":"nsTiming"},{"name":"records read","accumulatorId":113,"metricType":"sum"},{"name":"local bytes read","accumulatorId":111,"metricType":"size"},{"name":"fetch wait time","accumulatorId":112,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":109,"metricType":"size"},{"name":"local blocks read","accumulatorId":108,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":107,"metricType":"sum"},{"name":"data size","accumulatorId":105,"metricType":"size"},{"name":"number of partitions","accumulatorId":106,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":110,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":114,"metricType":"size"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":101,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":102,"metricType":"timing"},{"name":"peak memory","accumulatorId":100,"metricType":"size"},{"name":"number of output rows","accumulatorId":99,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":104,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":103,"metricType":"average"}]}],"metadata":{},"metrics":[]}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerDriverAccumUpdates","executionId":0,"accumUpdates":[[106,1]]}
{"Event":"SparkListenerJobStart","Job ID":1,"Submission Time":1642039497010,"Stage Infos":[{"Stage ID":1,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":8,"RDD Info":[{"RDD ID":4,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[3],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":1,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[0],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":3,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[2],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":2,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[1],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":0,"Name":"ParallelCollectionRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0},{"Stage ID":2,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":10,"RDD Info":[{"RDD ID":7,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"4\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[6],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":6,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"5\",\"name\":\"WholeStageCodegen (2)\"}","Callsite":"count at <console>:23","Parent IDs":[5],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":5,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"9\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[4],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[1],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0}],"Stage IDs":[1,2],"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","__fetch_continuous_blocks_in_batch_enabled":"true","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"4\",\"name\":\"Exchange\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"0","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerStageSubmitted","Stage Info":{"Stage ID":2,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":10,"RDD Info":[{"RDD ID":7,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"4\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[6],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":6,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"5\",\"name\":\"WholeStageCodegen (2)\"}","Callsite":"count at <console>:23","Parent IDs":[5],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":5,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"9\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[4],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[1],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039497017,"Accumulables":[],"Resource Profile Id":0},"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","__fetch_continuous_blocks_in_batch_enabled":"true","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"4\",\"name\":\"Exchange\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"0","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerTaskStart","Stage ID":2,"Stage Attempt ID":0,"Task Info":{"Task ID":8,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039497053,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":2,"Stage Attempt ID":0,"Task Info":{"Task ID":9,"Index":1,"Attempt":0,"Partition ID":1,"Launch Time":1642039497055,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":2,"Stage Attempt ID":0,"Task Info":{"Task ID":10,"Index":2,"Attempt":0,"Partition ID":2,"Launch Time":1642039497055,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":2,"Stage Attempt ID":0,"Task Info":{"Task ID":11,"Index":3,"Attempt":0,"Partition ID":3,"Launch Time":1642039497055,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":2,"Stage Attempt ID":0,"Task Info":{"Task ID":12,"Index":4,"Attempt":0,"Partition ID":4,"Launch Time":1642039497056,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":2,"Stage Attempt ID":0,"Task Info":{"Task ID":13,"Index":5,"Attempt":0,"Partition ID":5,"Launch Time":1642039497056,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":2,"Stage Attempt ID":0,"Task Info":{"Task ID":14,"Index":6,"Attempt":0,"Partition ID":6,"Launch Time":1642039497056,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":2,"Stage Attempt ID":0,"Task Info":{"Task ID":15,"Index":7,"Attempt":0,"Partition ID":7,"Launch Time":1642039497056,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":2,"Stage Attempt ID":0,"Task Info":{"Task ID":16,"Index":8,"Attempt":0,"Partition ID":8,"Launch Time":1642039497114,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":2,"Stage Attempt ID":0,"Task Info":{"Task ID":17,"Index":9,"Attempt":0,"Partition ID":9,"Launch Time":1642039497115,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":2,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":14,"Index":6,"Attempt":0,"Partition ID":6,"Launch Time":1642039497056,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497115,"Failed":false,"Killed":false,"Accumulables":[{"ID":64,"Name":"local blocks read","Update":"8","Value":"8","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Update":"376","Value":"376","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Update":"10","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Update":"16","Value":"16","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Update":"59","Value":"59","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Update":"976166","Value":"976166","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Update":"12","Value":"12","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Update":"11","Value":"11","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Update":4,"Value":4,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2498000,"Value":2498000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Update":50,"Value":50,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Update":21709000,"Value":21709000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Update":2848,"Value":2848,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":8,"Value":8,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":376,"Value":376,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Update":10,"Value":10,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":59,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":1,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Update":976166,"Value":976166,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":4,"Executor Deserialize CPU Time":2498000,"Executor Run Time":50,"Executor CPU Time":21709000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":8,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":376,"Total Records Read":10},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":976166,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":2,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":10,"Index":2,"Attempt":0,"Partition ID":2,"Launch Time":1642039497055,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497116,"Failed":false,"Killed":false,"Accumulables":[{"ID":64,"Name":"local blocks read","Update":"8","Value":"16","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Update":"376","Value":"752","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Update":"10","Value":"20","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Update":"16","Value":"32","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Update":"59","Value":"118","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Update":"1","Value":"2","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Update":"1282083","Value":"2258249","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Update":"13","Value":"25","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Update":"1","Value":"2","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Update":"12","Value":"23","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Update":4,"Value":8,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2238000,"Value":4736000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Update":52,"Value":102,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Update":27082000,"Value":48791000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Update":2848,"Value":5696,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":8,"Value":16,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":376,"Value":752,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Update":10,"Value":20,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":118,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":2,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Update":1282083,"Value":2258249,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":4,"Executor Deserialize CPU Time":2238000,"Executor Run Time":52,"Executor CPU Time":27082000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":8,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":376,"Total Records Read":10},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":1282083,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":2,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":15,"Index":7,"Attempt":0,"Partition ID":7,"Launch Time":1642039497056,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497118,"Failed":false,"Killed":false,"Accumulables":[{"ID":64,"Name":"local blocks read","Update":"8","Value":"24","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Update":"380","Value":"1132","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Update":"11","Value":"31","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Update":"16","Value":"48","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Update":"59","Value":"177","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Update":"1","Value":"3","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Update":"1080959","Value":"3339208","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Update":"13","Value":"38","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Update":"1","Value":"3","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Update":"12","Value":"35","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Update":3,"Value":11,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2759000,"Value":7495000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Update":53,"Value":155,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Update":17515000,"Value":66306000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Update":2848,"Value":8544,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":8,"Value":24,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":380,"Value":1132,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Update":11,"Value":31,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":177,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":3,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Update":1080959,"Value":3339208,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":3,"Executor Deserialize CPU Time":2759000,"Executor Run Time":53,"Executor CPU Time":17515000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":8,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":380,"Total Records Read":11},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":1080959,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":2,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":12,"Index":4,"Attempt":0,"Partition ID":4,"Launch Time":1642039497056,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497118,"Failed":false,"Killed":false,"Accumulables":[{"ID":64,"Name":"local blocks read","Update":"8","Value":"32","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Update":"372","Value":"1504","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Update":"9","Value":"40","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Update":"16","Value":"64","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Update":"59","Value":"236","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Update":"1","Value":"4","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Update":"1093250","Value":"4432458","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Update":"13","Value":"51","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Update":"1","Value":"4","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Update":"12","Value":"47","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Update":4,"Value":15,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2614000,"Value":10109000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Update":54,"Value":209,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Update":21689000,"Value":87995000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Update":2848,"Value":11392,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":8,"Value":32,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":372,"Value":1504,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Update":9,"Value":40,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":236,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":4,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Update":1093250,"Value":4432458,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":4,"Executor Deserialize CPU Time":2614000,"Executor Run Time":54,"Executor CPU Time":21689000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":8,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":372,"Total Records Read":9},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":1093250,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":2,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":11,"Index":3,"Attempt":0,"Partition ID":3,"Launch Time":1642039497055,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497119,"Failed":false,"Killed":false,"Accumulables":[{"ID":64,"Name":"local blocks read","Update":"8","Value":"40","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Update":"376","Value":"1880","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Update":"10","Value":"50","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Update":"16","Value":"80","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Update":"59","Value":"295","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Update":"1","Value":"5","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Update":"1558374","Value":"5990832","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Update":"13","Value":"64","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Update":"1","Value":"5","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Update":"11","Value":"58","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Update":6,"Value":21,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Update":3452000,"Value":13561000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Update":54,"Value":263,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Update":17668000,"Value":105663000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Update":2848,"Value":14240,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":8,"Value":40,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":376,"Value":1880,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Update":10,"Value":50,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":295,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":5,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Update":1558374,"Value":5990832,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":6,"Executor Deserialize CPU Time":3452000,"Executor Run Time":54,"Executor CPU Time":17668000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":8,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":376,"Total Records Read":10},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":1558374,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":2,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":9,"Index":1,"Attempt":0,"Partition ID":1,"Launch Time":1642039497055,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497120,"Failed":false,"Killed":false,"Accumulables":[{"ID":64,"Name":"local blocks read","Update":"8","Value":"48","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Update":"372","Value":"2252","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Update":"9","Value":"59","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Update":"16","Value":"96","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Update":"59","Value":"354","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Update":"1","Value":"6","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Update":"1490249","Value":"7481081","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Update":"13","Value":"77","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Update":"1","Value":"6","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Update":"11","Value":"69","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Update":4,"Value":25,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Update":3175000,"Value":16736000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Update":57,"Value":320,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Update":18115000,"Value":123778000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Update":2848,"Value":17088,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":8,"Value":48,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":372,"Value":2252,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Update":9,"Value":59,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":354,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":6,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Update":1490249,"Value":7481081,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":4,"Executor Deserialize CPU Time":3175000,"Executor Run Time":57,"Executor CPU Time":18115000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":8,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":372,"Total Records Read":9},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":1490249,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":2,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":13,"Index":5,"Attempt":0,"Partition ID":5,"Launch Time":1642039497056,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497123,"Failed":false,"Killed":false,"Accumulables":[{"ID":64,"Name":"local blocks read","Update":"8","Value":"56","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Update":"372","Value":"2624","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Update":"9","Value":"68","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Update":"16","Value":"112","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Update":"59","Value":"413","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Update":"1","Value":"7","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Update":"673209","Value":"8154290","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Update":"12","Value":"89","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Update":"1","Value":"7","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Update":"11","Value":"80","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Update":3,"Value":28,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2177000,"Value":18913000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Update":60,"Value":380,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Update":18621000,"Value":142399000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Update":2848,"Value":19936,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":8,"Value":56,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":372,"Value":2624,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Update":9,"Value":68,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":413,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":7,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Update":673209,"Value":8154290,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":3,"Executor Deserialize CPU Time":2177000,"Executor Run Time":60,"Executor CPU Time":18621000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":8,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":372,"Total Records Read":9},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":673209,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":2,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":8,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039497053,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497124,"Failed":false,"Killed":false,"Accumulables":[{"ID":64,"Name":"local blocks read","Update":"8","Value":"64","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Update":"376","Value":"3000","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Update":"10","Value":"78","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Update":"16","Value":"128","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Update":"59","Value":"472","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Update":"1","Value":"8","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Update":"1340668","Value":"9494958","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Update":"13","Value":"102","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Update":"1","Value":"8","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Update":"11","Value":"91","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Update":3,"Value":31,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Update":3128000,"Value":22041000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Update":62,"Value":442,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Update":20132000,"Value":162531000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Update":2848,"Value":22784,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":8,"Value":64,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":376,"Value":3000,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Update":10,"Value":78,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":472,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":8,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Update":1340668,"Value":9494958,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":3,"Executor Deserialize CPU Time":3128000,"Executor Run Time":62,"Executor CPU Time":20132000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":8,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":376,"Total Records Read":10},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":1340668,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":2,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":17,"Index":9,"Attempt":0,"Partition ID":9,"Launch Time":1642039497115,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497125,"Failed":false,"Killed":false,"Accumulables":[{"ID":64,"Name":"local blocks read","Update":"8","Value":"72","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Update":"380","Value":"3380","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Update":"11","Value":"89","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Update":"16","Value":"144","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Update":"59","Value":"531","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Update":"1","Value":"9","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Update":"506707","Value":"10001665","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Update":"1","Value":"103","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Update":"1","Value":"9","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Update":"1","Value":"92","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Update":1,"Value":32,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Update":1662000,"Value":23703000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Update":6,"Value":448,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Update":5470000,"Value":168001000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Update":2848,"Value":25632,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":8,"Value":72,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":380,"Value":3380,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Update":11,"Value":89,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":531,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":9,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Update":506707,"Value":10001665,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":1,"Executor Deserialize CPU Time":1662000,"Executor Run Time":6,"Executor CPU Time":5470000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":8,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":380,"Total Records Read":11},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":506707,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":2,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":16,"Index":8,"Attempt":0,"Partition ID":8,"Launch Time":1642039497114,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497126,"Failed":false,"Killed":false,"Accumulables":[{"ID":64,"Name":"local blocks read","Update":"8","Value":"80","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Update":"380","Value":"3760","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Update":"11","Value":"100","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Update":"16","Value":"160","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Update":"59","Value":"590","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Update":"1","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Update":"568086","Value":"10569751","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Update":"1","Value":"104","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Update":"1","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Update":"1","Value":"93","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Update":1,"Value":33,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Update":1875000,"Value":25578000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Update":7,"Value":455,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Update":5421000,"Value":173422000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Update":2848,"Value":28480,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":8,"Value":80,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":380,"Value":3760,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Update":11,"Value":100,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":590,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":10,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Update":568086,"Value":10569751,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":1,"Executor Deserialize CPU Time":1875000,"Executor Run Time":7,"Executor CPU Time":5421000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":8,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":380,"Total Records Read":11},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":568086,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerStageCompleted","Stage Info":{"Stage ID":2,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":10,"RDD Info":[{"RDD ID":7,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"4\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[6],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":6,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"5\",\"name\":\"WholeStageCodegen (2)\"}","Callsite":"count at <console>:23","Parent IDs":[5],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":5,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"9\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[4],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[1],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039497017,"Completion Time":1642039497127,"Accumulables":[{"ID":64,"Name":"local blocks read","Value":"80","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":67,"Name":"local bytes read","Value":"3760","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":68,"Name":"fetch wait time","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":69,"Name":"records read","Value":"100","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":105,"Name":"data size","Value":"160","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":114,"Name":"shuffle bytes written","Value":"590","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":115,"Name":"shuffle records written","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":116,"Name":"shuffle write time","Value":"10569751","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":117,"Name":"duration","Value":"104","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":118,"Name":"number of output rows","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":121,"Name":"time in aggregation build","Value":"93","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":124,"Name":"internal.metrics.executorDeserializeTime","Value":33,"Internal":true,"Count Failed Values":true},{"ID":125,"Name":"internal.metrics.executorDeserializeCpuTime","Value":25578000,"Internal":true,"Count Failed Values":true},{"ID":126,"Name":"internal.metrics.executorRunTime","Value":455,"Internal":true,"Count Failed Values":true},{"ID":127,"Name":"internal.metrics.executorCpuTime","Value":173422000,"Internal":true,"Count Failed Values":true},{"ID":128,"Name":"internal.metrics.resultSize","Value":28480,"Internal":true,"Count Failed Values":true},{"ID":135,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Value":0,"Internal":true,"Count Failed Values":true},{"ID":136,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Value":80,"Internal":true,"Count Failed Values":true},{"ID":137,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Value":0,"Internal":true,"Count Failed Values":true},{"ID":138,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Value":0,"Internal":true,"Count Failed Values":true},{"ID":139,"Name":"internal.metrics.shuffle.read.localBytesRead","Value":3760,"Internal":true,"Count Failed Values":true},{"ID":140,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Value":0,"Internal":true,"Count Failed Values":true},{"ID":141,"Name":"internal.metrics.shuffle.read.recordsRead","Value":100,"Internal":true,"Count Failed Values":true},{"ID":142,"Name":"internal.metrics.shuffle.write.bytesWritten","Value":590,"Internal":true,"Count Failed Values":true},{"ID":143,"Name":"internal.metrics.shuffle.write.recordsWritten","Value":10,"Internal":true,"Count Failed Values":true},{"ID":144,"Name":"internal.metrics.shuffle.write.writeTime","Value":10569751,"Internal":true,"Count Failed Values":true}],"Resource Profile Id":0}}
{"Event":"SparkListenerJobEnd","Job ID":1,"Completion Time":1642039497127,"Job Result":{"Result":"JobSucceeded"}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerSQLAdaptiveExecutionUpdate","executionId":0,"physicalPlanDescription":"== Physical Plan ==\nAdaptiveSparkPlan (13)\n+- == Final Plan ==\n   * HashAggregate (8)\n   +- ShuffleQueryStage (7)\n      +- Exchange (6)\n         +- * HashAggregate (5)\n            +- ShuffleQueryStage (4)\n               +- Exchange (3)\n                  +- * Project (2)\n                     +- * Range (1)\n+- == Initial Plan ==\n   HashAggregate (12)\n   +- Exchange (11)\n      +- HashAggregate (10)\n         +- Exchange (9)\n            +- Project (2)\n               +- Range (1)\n\n\n(1) Range [codegen id : 1]\nOutput [1]: [id#0L]\nArguments: Range (0, 100, step=1, splits=Some(8))\n\n(2) Project [codegen id : 1]\nOutput: []\nInput [1]: [id#0L]\n\n(3) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#28]\n\n(4) ShuffleQueryStage\nOutput: []\nArguments: 0\n\n(5) HashAggregate [codegen id : 2]\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#8L]\nResults [1]: [count#9L]\n\n(6) Exchange\nInput [1]: [count#9L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#49]\n\n(7) ShuffleQueryStage\nOutput [1]: [count#9L]\nArguments: 1\n\n(8) HashAggregate [codegen id : 3]\nInput [1]: [count#9L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#5L]\nResults [1]: [count(1)#5L AS count#6L]\n\n(9) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#15]\n\n(10) HashAggregate\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#8L]\nResults [1]: [count#9L]\n\n(11) Exchange\nInput [1]: [count#9L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#19]\n\n(12) HashAggregate\nInput [1]: [count#9L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#5L]\nResults [1]: [count(1)#5L AS count#6L]\n\n(13) AdaptiveSparkPlan\nOutput [1]: [count#6L]\nArguments: isFinalPlan=true\n\n","sparkPlanInfo":{"nodeName":"AdaptiveSparkPlan","simpleString":"AdaptiveSparkPlan isFinalPlan=true","children":[{"nodeName":"WholeStageCodegen (3)","simpleString":"WholeStageCodegen (3)","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[count(1)])","children":[{"nodeName":"InputAdapter","simpleString":"InputAdapter","children":[{"nodeName":"ShuffleQueryStage","simpleString":"ShuffleQueryStage 1","children":[{"nodeName":"Exchange","simpleString":"Exchange SinglePartition, ENSURE_REQUIREMENTS, [id=#49]","children":[{"nodeName":"WholeStageCodegen (2)","simpleString":"WholeStageCodegen (2)","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[partial_count(1)])","children":[{"nodeName":"InputAdapter","simpleString":"InputAdapter","children":[{"nodeName":"ShuffleQueryStage","simpleString":"ShuffleQueryStage 0","children":[{"nodeName":"Exchange","simpleString":"Exchange RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#28]","children":[{"nodeName":"WholeStageCodegen (1)","simpleString":"WholeStageCodegen (1)","children":[{"nodeName":"Project","simpleString":"Project","children":[{"nodeName":"Range","simpleString":"Range (0, 100, step=1, splits=8)","children":[],"metadata":{},"metrics":[{"name":"number of output rows","accumulatorId":36,"metricType":"sum"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":73,"metricType":"timing"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":71,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":72,"metricType":"nsTiming"},{"name":"records read","accumulatorId":69,"metricType":"sum"},{"name":"local bytes read","accumulatorId":67,"metricType":"size"},{"name":"fetch wait time","accumulatorId":68,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":65,"metricType":"size"},{"name":"local blocks read","accumulatorId":64,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":63,"metricType":"sum"},{"name":"data size","accumulatorId":61,"metricType":"size"},{"name":"number of partitions","accumulatorId":62,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":66,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":70,"metricType":"size"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":120,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":121,"metricType":"timing"},{"name":"peak memory","accumulatorId":119,"metricType":"size"},{"name":"number of output rows","accumulatorId":118,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":123,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":122,"metricType":"average"}]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":117,"metricType":"timing"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":115,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":116,"metricType":"nsTiming"},{"name":"records read","accumulatorId":113,"metricType":"sum"},{"name":"local bytes read","accumulatorId":111,"metricType":"size"},{"name":"fetch wait time","accumulatorId":112,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":109,"metricType":"size"},{"name":"local blocks read","accumulatorId":108,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":107,"metricType":"sum"},{"name":"data size","accumulatorId":105,"metricType":"size"},{"name":"number of partitions","accumulatorId":106,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":110,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":114,"metricType":"size"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":152,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":153,"metricType":"timing"},{"name":"peak memory","accumulatorId":151,"metricType":"size"},{"name":"number of output rows","accumulatorId":150,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":155,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":154,"metricType":"average"}]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":149,"metricType":"timing"}]}],"metadata":{},"metrics":[]}}
{"Event":"SparkListenerJobStart","Job ID":2,"Submission Time":1642039497179,"Stage Infos":[{"Stage ID":5,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":1,"RDD Info":[{"RDD ID":10,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"15\",\"name\":\"mapPartitionsInternal\"}","Callsite":"count at <console>:23","Parent IDs":[9],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":9,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"10\",\"name\":\"WholeStageCodegen (3)\"}","Callsite":"count at <console>:23","Parent IDs":[8],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":8,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"14\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[7],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[4],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0},{"Stage ID":3,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":8,"RDD Info":[{"RDD ID":4,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[3],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":1,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[0],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":3,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"0\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[2],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":2,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[1],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":0,"Name":"ParallelCollectionRDD","Scope":"{\"id\":\"1\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":8,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0},{"Stage ID":4,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":10,"RDD Info":[{"RDD ID":7,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"4\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[6],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":6,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"5\",\"name\":\"WholeStageCodegen (2)\"}","Callsite":"count at <console>:23","Parent IDs":[5],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":5,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"9\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[4],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[3],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0}],"Stage IDs":[5,3,4],"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","__fetch_continuous_blocks_in_batch_enabled":"true","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"16\",\"name\":\"collect\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"0","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerStageSubmitted","Stage Info":{"Stage ID":5,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":1,"RDD Info":[{"RDD ID":10,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"15\",\"name\":\"mapPartitionsInternal\"}","Callsite":"count at <console>:23","Parent IDs":[9],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":9,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"10\",\"name\":\"WholeStageCodegen (3)\"}","Callsite":"count at <console>:23","Parent IDs":[8],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":8,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"14\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[7],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[4],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039497181,"Accumulables":[],"Resource Profile Id":0},"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","__fetch_continuous_blocks_in_batch_enabled":"true","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"16\",\"name\":\"collect\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"0","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerTaskStart","Stage ID":5,"Stage Attempt ID":0,"Task Info":{"Task ID":18,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039497187,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":5,"Stage Attempt ID":0,"Task Type":"ResultTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":18,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039497187,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039497248,"Failed":false,"Killed":false,"Accumulables":[{"ID":108,"Name":"local blocks read","Update":"10","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":111,"Name":"local bytes read","Update":"590","Value":"590","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":112,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":113,"Name":"records read","Update":"10","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":149,"Name":"duration","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":150,"Name":"number of output rows","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":153,"Name":"time in aggregation build","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":156,"Name":"internal.metrics.executorDeserializeTime","Update":52,"Value":52,"Internal":true,"Count Failed Values":true},{"ID":157,"Name":"internal.metrics.executorDeserializeCpuTime","Update":51010000,"Value":51010000,"Internal":true,"Count Failed Values":true},{"ID":158,"Name":"internal.metrics.executorRunTime","Update":6,"Value":6,"Internal":true,"Count Failed Values":true},{"ID":159,"Name":"internal.metrics.executorCpuTime","Update":6062000,"Value":6062000,"Internal":true,"Count Failed Values":true},{"ID":160,"Name":"internal.metrics.resultSize","Update":2656,"Value":2656,"Internal":true,"Count Failed Values":true},{"ID":167,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":168,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":10,"Value":10,"Internal":true,"Count Failed Values":true},{"ID":169,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":170,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":171,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":590,"Value":590,"Internal":true,"Count Failed Values":true},{"ID":172,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":173,"Name":"internal.metrics.shuffle.read.recordsRead","Update":10,"Value":10,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":52,"Executor Deserialize CPU Time":51010000,"Executor Run Time":6,"Executor CPU Time":6062000,"Peak Execution Memory":0,"Result Size":2656,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":10,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":590,"Total Records Read":10},"Shuffle Write Metrics":{"Shuffle Bytes Written":0,"Shuffle Write Time":0,"Shuffle Records Written":0},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerStageCompleted","Stage Info":{"Stage ID":5,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":1,"RDD Info":[{"RDD ID":10,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"15\",\"name\":\"mapPartitionsInternal\"}","Callsite":"count at <console>:23","Parent IDs":[9],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":9,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"10\",\"name\":\"WholeStageCodegen (3)\"}","Callsite":"count at <console>:23","Parent IDs":[8],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":8,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"14\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[7],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[4],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw.<init>(<console>:39)\n$line15.$read.<init>(<console>:41)\n$line15.$read$.<init>(<console>:45)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039497181,"Completion Time":1642039497249,"Accumulables":[{"ID":108,"Name":"local blocks read","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":111,"Name":"local bytes read","Value":"590","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":112,"Name":"fetch wait time","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":113,"Name":"records read","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":149,"Name":"duration","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":150,"Name":"number of output rows","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":153,"Name":"time in aggregation build","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":156,"Name":"internal.metrics.executorDeserializeTime","Value":52,"Internal":true,"Count Failed Values":true},{"ID":157,"Name":"internal.metrics.executorDeserializeCpuTime","Value":51010000,"Internal":true,"Count Failed Values":true},{"ID":158,"Name":"internal.metrics.executorRunTime","Value":6,"Internal":true,"Count Failed Values":true},{"ID":159,"Name":"internal.metrics.executorCpuTime","Value":6062000,"Internal":true,"Count Failed Values":true},{"ID":160,"Name":"internal.metrics.resultSize","Value":2656,"Internal":true,"Count Failed Values":true},{"ID":167,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Value":0,"Internal":true,"Count Failed Values":true},{"ID":168,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Value":10,"Internal":true,"Count Failed Values":true},{"ID":169,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Value":0,"Internal":true,"Count Failed Values":true},{"ID":170,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Value":0,"Internal":true,"Count Failed Values":true},{"ID":171,"Name":"internal.metrics.shuffle.read.localBytesRead","Value":590,"Internal":true,"Count Failed Values":true},{"ID":172,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Value":0,"Internal":true,"Count Failed Values":true},{"ID":173,"Name":"internal.metrics.shuffle.read.recordsRead","Value":10,"Internal":true,"Count Failed Values":true}],"Resource Profile Id":0}}
{"Event":"SparkListenerJobEnd","Job ID":2,"Completion Time":1642039497251,"Job Result":{"Result":"JobSucceeded"}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerSQLExecutionEnd","executionId":0,"time":1642039497259}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerSQLExecutionStart","executionId":1,"description":"count at <console>:23","details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","physicalPlanDescription":"== Physical Plan ==\nAdaptiveSparkPlan (7)\n+- HashAggregate (6)\n   +- Exchange (5)\n      +- HashAggregate (4)\n         +- Exchange (3)\n            +- Project (2)\n               +- Range (1)\n\n\n(1) Range\nOutput [1]: [id#10L]\nArguments: Range (0, 3347, step=13, splits=Some(5))\n\n(2) Project\nOutput: []\nInput [1]: [id#10L]\n\n(3) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#76]\n\n(4) HashAggregate\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#18L]\nResults [1]: [count#19L]\n\n(5) Exchange\nInput [1]: [count#19L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#80]\n\n(6) HashAggregate\nInput [1]: [count#19L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#15L]\nResults [1]: [count(1)#15L AS count#16L]\n\n(7) AdaptiveSparkPlan\nOutput [1]: [count#16L]\nArguments: isFinalPlan=false\n\n","sparkPlanInfo":{"nodeName":"AdaptiveSparkPlan","simpleString":"AdaptiveSparkPlan isFinalPlan=false","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[count(1)])","children":[{"nodeName":"Exchange","simpleString":"Exchange SinglePartition, ENSURE_REQUIREMENTS, [id=#80]","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[partial_count(1)])","children":[{"nodeName":"Exchange","simpleString":"Exchange RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#76]","children":[{"nodeName":"Project","simpleString":"Project","children":[{"nodeName":"Range","simpleString":"Range (0, 3347, step=13, splits=5)","children":[],"metadata":{},"metrics":[{"name":"number of output rows","accumulatorId":217,"metricType":"sum"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":215,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":216,"metricType":"nsTiming"},{"name":"records read","accumulatorId":213,"metricType":"sum"},{"name":"local bytes read","accumulatorId":211,"metricType":"size"},{"name":"fetch wait time","accumulatorId":212,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":209,"metricType":"size"},{"name":"local blocks read","accumulatorId":208,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":207,"metricType":"sum"},{"name":"data size","accumulatorId":205,"metricType":"size"},{"name":"number of partitions","accumulatorId":206,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":210,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":214,"metricType":"size"}]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":201,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":202,"metricType":"timing"},{"name":"peak memory","accumulatorId":200,"metricType":"size"},{"name":"number of output rows","accumulatorId":199,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":204,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":203,"metricType":"average"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":197,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":198,"metricType":"nsTiming"},{"name":"records read","accumulatorId":195,"metricType":"sum"},{"name":"local bytes read","accumulatorId":193,"metricType":"size"},{"name":"fetch wait time","accumulatorId":194,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":191,"metricType":"size"},{"name":"local blocks read","accumulatorId":190,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":189,"metricType":"sum"},{"name":"data size","accumulatorId":187,"metricType":"size"},{"name":"number of partitions","accumulatorId":188,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":192,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":196,"metricType":"size"}]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":183,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":184,"metricType":"timing"},{"name":"peak memory","accumulatorId":182,"metricType":"size"},{"name":"number of output rows","accumulatorId":181,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":186,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":185,"metricType":"average"}]}],"metadata":{},"metrics":[]},"time":1642039528290,"modifiedConfigs":{}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerSQLAdaptiveExecutionUpdate","executionId":1,"physicalPlanDescription":"== Physical Plan ==\nAdaptiveSparkPlan (12)\n+- == Current Plan ==\n   HashAggregate (7)\n   +- Exchange (6)\n      +- HashAggregate (5)\n         +- ShuffleQueryStage (4)\n            +- Exchange (3)\n               +- * Project (2)\n                  +- * Range (1)\n+- == Initial Plan ==\n   HashAggregate (11)\n   +- Exchange (10)\n      +- HashAggregate (9)\n         +- Exchange (8)\n            +- Project (2)\n               +- Range (1)\n\n\n(1) Range [codegen id : 1]\nOutput [1]: [id#10L]\nArguments: Range (0, 3347, step=13, splits=Some(5))\n\n(2) Project [codegen id : 1]\nOutput: []\nInput [1]: [id#10L]\n\n(3) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#89]\n\n(4) ShuffleQueryStage\nOutput: []\nArguments: 0\n\n(5) HashAggregate\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#18L]\nResults [1]: [count#19L]\n\n(6) Exchange\nInput [1]: [count#19L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#94]\n\n(7) HashAggregate\nInput [1]: [count#19L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#15L]\nResults [1]: [count(1)#15L AS count#16L]\n\n(8) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#76]\n\n(9) HashAggregate\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#18L]\nResults [1]: [count#19L]\n\n(10) Exchange\nInput [1]: [count#19L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#80]\n\n(11) HashAggregate\nInput [1]: [count#19L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#15L]\nResults [1]: [count(1)#15L AS count#16L]\n\n(12) AdaptiveSparkPlan\nOutput [1]: [count#16L]\nArguments: isFinalPlan=false\n\n","sparkPlanInfo":{"nodeName":"AdaptiveSparkPlan","simpleString":"AdaptiveSparkPlan isFinalPlan=false","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[count(1)])","children":[{"nodeName":"Exchange","simpleString":"Exchange SinglePartition, ENSURE_REQUIREMENTS, [id=#94]","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[partial_count(1)])","children":[{"nodeName":"ShuffleQueryStage","simpleString":"ShuffleQueryStage 0","children":[{"nodeName":"Exchange","simpleString":"Exchange RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#89]","children":[{"nodeName":"WholeStageCodegen (1)","simpleString":"WholeStageCodegen (1)","children":[{"nodeName":"Project","simpleString":"Project","children":[{"nodeName":"Range","simpleString":"Range (0, 3347, step=13, splits=5)","children":[],"metadata":{},"metrics":[{"name":"number of output rows","accumulatorId":217,"metricType":"sum"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":254,"metricType":"timing"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":252,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":253,"metricType":"nsTiming"},{"name":"records read","accumulatorId":250,"metricType":"sum"},{"name":"local bytes read","accumulatorId":248,"metricType":"size"},{"name":"fetch wait time","accumulatorId":249,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":246,"metricType":"size"},{"name":"local blocks read","accumulatorId":245,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":244,"metricType":"sum"},{"name":"data size","accumulatorId":242,"metricType":"size"},{"name":"number of partitions","accumulatorId":243,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":247,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":251,"metricType":"size"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":238,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":239,"metricType":"timing"},{"name":"peak memory","accumulatorId":237,"metricType":"size"},{"name":"number of output rows","accumulatorId":236,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":241,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":240,"metricType":"average"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":234,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":235,"metricType":"nsTiming"},{"name":"records read","accumulatorId":232,"metricType":"sum"},{"name":"local bytes read","accumulatorId":230,"metricType":"size"},{"name":"fetch wait time","accumulatorId":231,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":228,"metricType":"size"},{"name":"local blocks read","accumulatorId":227,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":226,"metricType":"sum"},{"name":"data size","accumulatorId":224,"metricType":"size"},{"name":"number of partitions","accumulatorId":225,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":229,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":233,"metricType":"size"}]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":220,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":221,"metricType":"timing"},{"name":"peak memory","accumulatorId":219,"metricType":"size"},{"name":"number of output rows","accumulatorId":218,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":223,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":222,"metricType":"average"}]}],"metadata":{},"metrics":[]}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerDriverAccumUpdates","executionId":1,"accumUpdates":[[243,10]]}
{"Event":"SparkListenerJobStart","Job ID":3,"Submission Time":1642039528323,"Stage Infos":[{"Stage ID":6,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":5,"RDD Info":[{"RDD ID":15,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[14],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":14,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[13],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":12,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[11],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":11,"Name":"ParallelCollectionRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":13,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[12],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0}],"Stage IDs":[6],"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","__fetch_continuous_blocks_in_batch_enabled":"true","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"1","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerStageSubmitted","Stage Info":{"Stage ID":6,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":5,"RDD Info":[{"RDD ID":15,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[14],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":14,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[13],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":12,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[11],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":11,"Name":"ParallelCollectionRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":13,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[12],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039528324,"Accumulables":[],"Resource Profile Id":0},"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","__fetch_continuous_blocks_in_batch_enabled":"true","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"1","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerTaskStart","Stage ID":6,"Stage Attempt ID":0,"Task Info":{"Task ID":19,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039528357,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":6,"Stage Attempt ID":0,"Task Info":{"Task ID":20,"Index":1,"Attempt":0,"Partition ID":1,"Launch Time":1642039528358,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":6,"Stage Attempt ID":0,"Task Info":{"Task ID":21,"Index":2,"Attempt":0,"Partition ID":2,"Launch Time":1642039528358,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":6,"Stage Attempt ID":0,"Task Info":{"Task ID":22,"Index":3,"Attempt":0,"Partition ID":3,"Launch Time":1642039528358,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":6,"Stage Attempt ID":0,"Task Info":{"Task ID":23,"Index":4,"Attempt":0,"Partition ID":4,"Launch Time":1642039528358,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":6,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":19,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039528357,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528383,"Failed":false,"Killed":false,"Accumulables":[{"ID":217,"Name":"number of output rows","Update":"51","Value":"51","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":242,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":251,"Name":"shuffle bytes written","Update":"520","Value":"520","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":252,"Name":"shuffle records written","Update":"51","Value":"51","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":253,"Name":"shuffle write time","Update":"5953700","Value":"5953700","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":254,"Name":"duration","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":255,"Name":"internal.metrics.executorDeserializeTime","Update":2,"Value":2,"Internal":true,"Count Failed Values":true},{"ID":256,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2403000,"Value":2403000,"Internal":true,"Count Failed Values":true},{"ID":257,"Name":"internal.metrics.executorRunTime","Update":18,"Value":18,"Internal":true,"Count Failed Values":true},{"ID":258,"Name":"internal.metrics.executorCpuTime","Update":12538000,"Value":12538000,"Internal":true,"Count Failed Values":true},{"ID":259,"Name":"internal.metrics.resultSize","Update":1750,"Value":1750,"Internal":true,"Count Failed Values":true},{"ID":273,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":520,"Value":520,"Internal":true,"Count Failed Values":true},{"ID":274,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":51,"Value":51,"Internal":true,"Count Failed Values":true},{"ID":275,"Name":"internal.metrics.shuffle.write.writeTime","Update":5953700,"Value":5953700,"Internal":true,"Count Failed Values":true},{"ID":277,"Name":"internal.metrics.input.recordsRead","Update":51,"Value":51,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":2,"Executor Deserialize CPU Time":2403000,"Executor Run Time":18,"Executor CPU Time":12538000,"Peak Execution Memory":0,"Result Size":1750,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":520,"Shuffle Write Time":5953700,"Shuffle Records Written":51},"Input Metrics":{"Bytes Read":0,"Records Read":51},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":6,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":23,"Index":4,"Attempt":0,"Partition ID":4,"Launch Time":1642039528358,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528384,"Failed":false,"Killed":false,"Accumulables":[{"ID":217,"Name":"number of output rows","Update":"52","Value":"103","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":242,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":251,"Name":"shuffle bytes written","Update":"520","Value":"1040","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":252,"Name":"shuffle records written","Update":"52","Value":"103","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":253,"Name":"shuffle write time","Update":"7592966","Value":"13546666","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":254,"Name":"duration","Update":"1","Value":"2","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":255,"Name":"internal.metrics.executorDeserializeTime","Update":2,"Value":4,"Internal":true,"Count Failed Values":true},{"ID":256,"Name":"internal.metrics.executorDeserializeCpuTime","Update":1995000,"Value":4398000,"Internal":true,"Count Failed Values":true},{"ID":257,"Name":"internal.metrics.executorRunTime","Update":19,"Value":37,"Internal":true,"Count Failed Values":true},{"ID":258,"Name":"internal.metrics.executorCpuTime","Update":13485000,"Value":26023000,"Internal":true,"Count Failed Values":true},{"ID":259,"Name":"internal.metrics.resultSize","Update":1750,"Value":3500,"Internal":true,"Count Failed Values":true},{"ID":273,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":520,"Value":1040,"Internal":true,"Count Failed Values":true},{"ID":274,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":52,"Value":103,"Internal":true,"Count Failed Values":true},{"ID":275,"Name":"internal.metrics.shuffle.write.writeTime","Update":7592966,"Value":13546666,"Internal":true,"Count Failed Values":true},{"ID":277,"Name":"internal.metrics.input.recordsRead","Update":52,"Value":103,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":2,"Executor Deserialize CPU Time":1995000,"Executor Run Time":19,"Executor CPU Time":13485000,"Peak Execution Memory":0,"Result Size":1750,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":520,"Shuffle Write Time":7592966,"Shuffle Records Written":52},"Input Metrics":{"Bytes Read":0,"Records Read":52},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":6,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":20,"Index":1,"Attempt":0,"Partition ID":1,"Launch Time":1642039528358,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528384,"Failed":false,"Killed":false,"Accumulables":[{"ID":217,"Name":"number of output rows","Update":"52","Value":"155","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":242,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":251,"Name":"shuffle bytes written","Update":"520","Value":"1560","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":252,"Name":"shuffle records written","Update":"52","Value":"155","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":253,"Name":"shuffle write time","Update":"5810669","Value":"19357335","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":254,"Name":"duration","Update":"1","Value":"3","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":255,"Name":"internal.metrics.executorDeserializeTime","Update":3,"Value":7,"Internal":true,"Count Failed Values":true},{"ID":256,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2006000,"Value":6404000,"Internal":true,"Count Failed Values":true},{"ID":257,"Name":"internal.metrics.executorRunTime","Update":20,"Value":57,"Internal":true,"Count Failed Values":true},{"ID":258,"Name":"internal.metrics.executorCpuTime","Update":10665000,"Value":36688000,"Internal":true,"Count Failed Values":true},{"ID":259,"Name":"internal.metrics.resultSize","Update":1750,"Value":5250,"Internal":true,"Count Failed Values":true},{"ID":273,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":520,"Value":1560,"Internal":true,"Count Failed Values":true},{"ID":274,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":52,"Value":155,"Internal":true,"Count Failed Values":true},{"ID":275,"Name":"internal.metrics.shuffle.write.writeTime","Update":5810669,"Value":19357335,"Internal":true,"Count Failed Values":true},{"ID":277,"Name":"internal.metrics.input.recordsRead","Update":52,"Value":155,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":3,"Executor Deserialize CPU Time":2006000,"Executor Run Time":20,"Executor CPU Time":10665000,"Peak Execution Memory":0,"Result Size":1750,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":520,"Shuffle Write Time":5810669,"Shuffle Records Written":52},"Input Metrics":{"Bytes Read":0,"Records Read":52},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":6,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":22,"Index":3,"Attempt":0,"Partition ID":3,"Launch Time":1642039528358,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528386,"Failed":false,"Killed":false,"Accumulables":[{"ID":217,"Name":"number of output rows","Update":"52","Value":"207","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":242,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":251,"Name":"shuffle bytes written","Update":"520","Value":"2080","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":252,"Name":"shuffle records written","Update":"52","Value":"207","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":253,"Name":"shuffle write time","Update":"6453916","Value":"25811251","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":254,"Name":"duration","Update":"1","Value":"4","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":255,"Name":"internal.metrics.executorDeserializeTime","Update":3,"Value":10,"Internal":true,"Count Failed Values":true},{"ID":256,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2176000,"Value":8580000,"Internal":true,"Count Failed Values":true},{"ID":257,"Name":"internal.metrics.executorRunTime","Update":21,"Value":78,"Internal":true,"Count Failed Values":true},{"ID":258,"Name":"internal.metrics.executorCpuTime","Update":12442000,"Value":49130000,"Internal":true,"Count Failed Values":true},{"ID":259,"Name":"internal.metrics.resultSize","Update":1750,"Value":7000,"Internal":true,"Count Failed Values":true},{"ID":273,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":520,"Value":2080,"Internal":true,"Count Failed Values":true},{"ID":274,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":52,"Value":207,"Internal":true,"Count Failed Values":true},{"ID":275,"Name":"internal.metrics.shuffle.write.writeTime","Update":6453916,"Value":25811251,"Internal":true,"Count Failed Values":true},{"ID":277,"Name":"internal.metrics.input.recordsRead","Update":52,"Value":207,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":3,"Executor Deserialize CPU Time":2176000,"Executor Run Time":21,"Executor CPU Time":12442000,"Peak Execution Memory":0,"Result Size":1750,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":520,"Shuffle Write Time":6453916,"Shuffle Records Written":52},"Input Metrics":{"Bytes Read":0,"Records Read":52},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":6,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":21,"Index":2,"Attempt":0,"Partition ID":2,"Launch Time":1642039528358,"Executor ID":"driver","Host":"*************","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528388,"Failed":false,"Killed":false,"Accumulables":[{"ID":217,"Name":"number of output rows","Update":"51","Value":"258","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":242,"Name":"data size","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":251,"Name":"shuffle bytes written","Update":"520","Value":"2600","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":252,"Name":"shuffle records written","Update":"51","Value":"258","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":253,"Name":"shuffle write time","Update":"6818330","Value":"32629581","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":254,"Name":"duration","Update":"1","Value":"5","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":255,"Name":"internal.metrics.executorDeserializeTime","Update":3,"Value":13,"Internal":true,"Count Failed Values":true},{"ID":256,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2186000,"Value":10766000,"Internal":true,"Count Failed Values":true},{"ID":257,"Name":"internal.metrics.executorRunTime","Update":23,"Value":101,"Internal":true,"Count Failed Values":true},{"ID":258,"Name":"internal.metrics.executorCpuTime","Update":10929000,"Value":60059000,"Internal":true,"Count Failed Values":true},{"ID":259,"Name":"internal.metrics.resultSize","Update":1750,"Value":8750,"Internal":true,"Count Failed Values":true},{"ID":273,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":520,"Value":2600,"Internal":true,"Count Failed Values":true},{"ID":274,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":51,"Value":258,"Internal":true,"Count Failed Values":true},{"ID":275,"Name":"internal.metrics.shuffle.write.writeTime","Update":6818330,"Value":32629581,"Internal":true,"Count Failed Values":true},{"ID":277,"Name":"internal.metrics.input.recordsRead","Update":51,"Value":258,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":3,"Executor Deserialize CPU Time":2186000,"Executor Run Time":23,"Executor CPU Time":10929000,"Peak Execution Memory":0,"Result Size":1750,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Shuffle Bytes Written":520,"Shuffle Write Time":6818330,"Shuffle Records Written":51},"Input Metrics":{"Bytes Read":0,"Records Read":51},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerStageCompleted","Stage Info":{"Stage ID":6,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":5,"RDD Info":[{"RDD ID":15,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[14],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":14,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[13],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":12,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[11],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":11,"Name":"ParallelCollectionRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":13,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[12],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039528324,"Completion Time":1642039528388,"Accumulables":[{"ID":217,"Name":"number of output rows","Value":"258","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":242,"Name":"data size","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":251,"Name":"shuffle bytes written","Value":"2600","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":252,"Name":"shuffle records written","Value":"258","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":253,"Name":"shuffle write time","Value":"32629581","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":254,"Name":"duration","Value":"5","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":255,"Name":"internal.metrics.executorDeserializeTime","Value":13,"Internal":true,"Count Failed Values":true},{"ID":256,"Name":"internal.metrics.executorDeserializeCpuTime","Value":10766000,"Internal":true,"Count Failed Values":true},{"ID":257,"Name":"internal.metrics.executorRunTime","Value":101,"Internal":true,"Count Failed Values":true},{"ID":258,"Name":"internal.metrics.executorCpuTime","Value":60059000,"Internal":true,"Count Failed Values":true},{"ID":259,"Name":"internal.metrics.resultSize","Value":8750,"Internal":true,"Count Failed Values":true},{"ID":273,"Name":"internal.metrics.shuffle.write.bytesWritten","Value":2600,"Internal":true,"Count Failed Values":true},{"ID":274,"Name":"internal.metrics.shuffle.write.recordsWritten","Value":258,"Internal":true,"Count Failed Values":true},{"ID":275,"Name":"internal.metrics.shuffle.write.writeTime","Value":32629581,"Internal":true,"Count Failed Values":true},{"ID":277,"Name":"internal.metrics.input.recordsRead","Value":258,"Internal":true,"Count Failed Values":true}],"Resource Profile Id":0}}
{"Event":"SparkListenerJobEnd","Job ID":3,"Completion Time":1642039528389,"Job Result":{"Result":"JobSucceeded"}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerSQLAdaptiveExecutionUpdate","executionId":1,"physicalPlanDescription":"== Physical Plan ==\nAdaptiveSparkPlan (13)\n+- == Current Plan ==\n   HashAggregate (8)\n   +- ShuffleQueryStage (7)\n      +- Exchange (6)\n         +- * HashAggregate (5)\n            +- ShuffleQueryStage (4)\n               +- Exchange (3)\n                  +- * Project (2)\n                     +- * Range (1)\n+- == Initial Plan ==\n   HashAggregate (12)\n   +- Exchange (11)\n      +- HashAggregate (10)\n         +- Exchange (9)\n            +- Project (2)\n               +- Range (1)\n\n\n(1) Range [codegen id : 1]\nOutput [1]: [id#10L]\nArguments: Range (0, 3347, step=13, splits=Some(5))\n\n(2) Project [codegen id : 1]\nOutput: []\nInput [1]: [id#10L]\n\n(3) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#89]\n\n(4) ShuffleQueryStage\nOutput: []\nArguments: 0\n\n(5) HashAggregate [codegen id : 2]\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#18L]\nResults [1]: [count#19L]\n\n(6) Exchange\nInput [1]: [count#19L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#110]\n\n(7) ShuffleQueryStage\nOutput [1]: [count#19L]\nArguments: 1\n\n(8) HashAggregate\nInput [1]: [count#19L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#15L]\nResults [1]: [count(1)#15L AS count#16L]\n\n(9) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#76]\n\n(10) HashAggregate\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#18L]\nResults [1]: [count#19L]\n\n(11) Exchange\nInput [1]: [count#19L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#80]\n\n(12) HashAggregate\nInput [1]: [count#19L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#15L]\nResults [1]: [count(1)#15L AS count#16L]\n\n(13) AdaptiveSparkPlan\nOutput [1]: [count#16L]\nArguments: isFinalPlan=false\n\n","sparkPlanInfo":{"nodeName":"AdaptiveSparkPlan","simpleString":"AdaptiveSparkPlan isFinalPlan=false","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[count(1)])","children":[{"nodeName":"ShuffleQueryStage","simpleString":"ShuffleQueryStage 1","children":[{"nodeName":"Exchange","simpleString":"Exchange SinglePartition, ENSURE_REQUIREMENTS, [id=#110]","children":[{"nodeName":"WholeStageCodegen (2)","simpleString":"WholeStageCodegen (2)","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[partial_count(1)])","children":[{"nodeName":"InputAdapter","simpleString":"InputAdapter","children":[{"nodeName":"ShuffleQueryStage","simpleString":"ShuffleQueryStage 0","children":[{"nodeName":"Exchange","simpleString":"Exchange RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#89]","children":[{"nodeName":"WholeStageCodegen (1)","simpleString":"WholeStageCodegen (1)","children":[{"nodeName":"Project","simpleString":"Project","children":[{"nodeName":"Range","simpleString":"Range (0, 3347, step=13, splits=5)","children":[],"metadata":{},"metrics":[{"name":"number of output rows","accumulatorId":217,"metricType":"sum"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":254,"metricType":"timing"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":252,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":253,"metricType":"nsTiming"},{"name":"records read","accumulatorId":250,"metricType":"sum"},{"name":"local bytes read","accumulatorId":248,"metricType":"size"},{"name":"fetch wait time","accumulatorId":249,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":246,"metricType":"size"},{"name":"local blocks read","accumulatorId":245,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":244,"metricType":"sum"},{"name":"data size","accumulatorId":242,"metricType":"size"},{"name":"number of partitions","accumulatorId":243,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":247,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":251,"metricType":"size"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":301,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":302,"metricType":"timing"},{"name":"peak memory","accumulatorId":300,"metricType":"size"},{"name":"number of output rows","accumulatorId":299,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":304,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":303,"metricType":"average"}]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":298,"metricType":"timing"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":296,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":297,"metricType":"nsTiming"},{"name":"records read","accumulatorId":294,"metricType":"sum"},{"name":"local bytes read","accumulatorId":292,"metricType":"size"},{"name":"fetch wait time","accumulatorId":293,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":290,"metricType":"size"},{"name":"local blocks read","accumulatorId":289,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":288,"metricType":"sum"},{"name":"data size","accumulatorId":286,"metricType":"size"},{"name":"number of partitions","accumulatorId":287,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":291,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":295,"metricType":"size"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":282,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":283,"metricType":"timing"},{"name":"peak memory","accumulatorId":281,"metricType":"size"},{"name":"number of output rows","accumulatorId":280,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":285,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":284,"metricType":"average"}]}],"metadata":{},"metrics":[]}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerDriverAccumUpdates","executionId":1,"accumUpdates":[[287,1]]}
{"Event":"SparkListenerJobStart","Job ID":4,"Submission Time":1642039528402,"Stage Infos":[{"Stage ID":7,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":5,"RDD Info":[{"RDD ID":15,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[14],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":14,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[13],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":12,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[11],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":11,"Name":"ParallelCollectionRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":13,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[12],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0},{"Stage ID":8,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":10,"RDD Info":[{"RDD ID":18,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"32\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[17],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":16,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"37\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[15],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":17,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"33\",\"name\":\"WholeStageCodegen (2)\"}","Callsite":"count at <console>:23","Parent IDs":[16],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[7],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0}],"Stage IDs":[7,8],"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","__fetch_continuous_blocks_in_batch_enabled":"true","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"32\",\"name\":\"Exchange\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"1","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerStageSubmitted","Stage Info":{"Stage ID":8,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":10,"RDD Info":[{"RDD ID":18,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"32\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[17],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":16,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"37\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[15],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":17,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"33\",\"name\":\"WholeStageCodegen (2)\"}","Callsite":"count at <console>:23","Parent IDs":[16],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[7],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039528404,"Accumulables":[],"Resource Profile Id":0},"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","__fetch_continuous_blocks_in_batch_enabled":"true","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"32\",\"name\":\"Exchange\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"1","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerTaskStart","Stage ID":8,"Stage Attempt ID":0,"Task Info":{"Task ID":24,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039528410,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":8,"Stage Attempt ID":0,"Task Info":{"Task ID":25,"Index":1,"Attempt":0,"Partition ID":1,"Launch Time":1642039528410,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":8,"Stage Attempt ID":0,"Task Info":{"Task ID":26,"Index":2,"Attempt":0,"Partition ID":2,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":8,"Stage Attempt ID":0,"Task Info":{"Task ID":27,"Index":3,"Attempt":0,"Partition ID":3,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":8,"Stage Attempt ID":0,"Task Info":{"Task ID":28,"Index":4,"Attempt":0,"Partition ID":4,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":8,"Stage Attempt ID":0,"Task Info":{"Task ID":29,"Index":5,"Attempt":0,"Partition ID":5,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":8,"Stage Attempt ID":0,"Task Info":{"Task ID":30,"Index":6,"Attempt":0,"Partition ID":6,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":8,"Stage Attempt ID":0,"Task Info":{"Task ID":31,"Index":7,"Attempt":0,"Partition ID":7,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":8,"Stage Attempt ID":0,"Task Info":{"Task ID":32,"Index":8,"Attempt":0,"Partition ID":8,"Launch Time":1642039528425,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":8,"Stage Attempt ID":0,"Task Info":{"Task ID":33,"Index":9,"Attempt":0,"Partition ID":9,"Launch Time":1642039528426,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":8,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":25,"Index":1,"Attempt":0,"Partition ID":1,"Launch Time":1642039528410,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528427,"Failed":false,"Killed":false,"Accumulables":[{"ID":245,"Name":"local blocks read","Update":"5","Value":"5","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Update":"260","Value":"260","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Update":"26","Value":"26","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Update":"16","Value":"16","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Update":"59","Value":"59","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Update":"621958","Value":"621958","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Update":2,"Value":2,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Update":1997000,"Value":1997000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Update":9,"Value":9,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Update":5764000,"Value":5764000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Update":2848,"Value":2848,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":5,"Value":5,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":260,"Value":260,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Update":26,"Value":26,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":59,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":1,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Update":621958,"Value":621958,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":2,"Executor Deserialize CPU Time":1997000,"Executor Run Time":9,"Executor CPU Time":5764000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":5,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":260,"Total Records Read":26},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":621958,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":8,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":30,"Index":6,"Attempt":0,"Partition ID":6,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528427,"Failed":false,"Killed":false,"Accumulables":[{"ID":245,"Name":"local blocks read","Update":"5","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Update":"260","Value":"520","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Update":"27","Value":"53","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Update":"16","Value":"32","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Update":"59","Value":"118","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Update":"1","Value":"2","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Update":"945999","Value":"1567957","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Update":"1","Value":"2","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Update":"1","Value":"2","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Update":2,"Value":4,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2043000,"Value":4040000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Update":10,"Value":19,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Update":6022000,"Value":11786000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Update":2848,"Value":5696,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":5,"Value":10,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":260,"Value":520,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Update":27,"Value":53,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":118,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":2,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Update":945999,"Value":1567957,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":2,"Executor Deserialize CPU Time":2043000,"Executor Run Time":10,"Executor CPU Time":6022000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":5,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":260,"Total Records Read":27},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":945999,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":8,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":24,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039528410,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528427,"Failed":false,"Killed":false,"Accumulables":[{"ID":245,"Name":"local blocks read","Update":"5","Value":"15","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Update":"260","Value":"780","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Update":"25","Value":"78","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Update":"16","Value":"48","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Update":"59","Value":"177","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Update":"1","Value":"3","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Update":"489459","Value":"2057416","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Update":"0","Value":"2","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Update":"1","Value":"3","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Update":4,"Value":8,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2389000,"Value":6429000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Update":7,"Value":26,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Update":4527000,"Value":16313000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Update":2848,"Value":8544,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":5,"Value":15,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":260,"Value":780,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Update":25,"Value":78,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":177,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":3,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Update":489459,"Value":2057416,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":4,"Executor Deserialize CPU Time":2389000,"Executor Run Time":7,"Executor CPU Time":4527000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":5,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":260,"Total Records Read":25},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":489459,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":8,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":31,"Index":7,"Attempt":0,"Partition ID":7,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528432,"Failed":false,"Killed":false,"Accumulables":[{"ID":245,"Name":"local blocks read","Update":"5","Value":"20","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Update":"260","Value":"1040","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Update":"26","Value":"104","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Update":"16","Value":"64","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Update":"59","Value":"236","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Update":"1","Value":"4","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Update":"1090458","Value":"3147874","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Update":"2","Value":"4","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Update":"1","Value":"4","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Update":"2","Value":"2","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Update":5,"Value":13,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2056000,"Value":8485000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Update":12,"Value":38,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Update":5051000,"Value":21364000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Update":2848,"Value":11392,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":5,"Value":20,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":260,"Value":1040,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Update":26,"Value":104,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":236,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":4,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Update":1090458,"Value":3147874,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":5,"Executor Deserialize CPU Time":2056000,"Executor Run Time":12,"Executor CPU Time":5051000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":5,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":260,"Total Records Read":26},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":1090458,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":8,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":27,"Index":3,"Attempt":0,"Partition ID":3,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528432,"Failed":false,"Killed":false,"Accumulables":[{"ID":245,"Name":"local blocks read","Update":"5","Value":"25","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Update":"260","Value":"1300","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Update":"26","Value":"130","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Update":"16","Value":"80","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Update":"59","Value":"295","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Update":"1","Value":"5","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Update":"857626","Value":"4005500","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Update":"1","Value":"5","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Update":"1","Value":"5","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Update":"1","Value":"3","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Update":7,"Value":20,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2320000,"Value":10805000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Update":7,"Value":45,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Update":5718000,"Value":27082000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Update":2848,"Value":14240,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":5,"Value":25,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":260,"Value":1300,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Update":26,"Value":130,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":295,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":5,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Update":857626,"Value":4005500,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":7,"Executor Deserialize CPU Time":2320000,"Executor Run Time":7,"Executor CPU Time":5718000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":5,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":260,"Total Records Read":26},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":857626,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":8,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":29,"Index":5,"Attempt":0,"Partition ID":5,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528433,"Failed":false,"Killed":false,"Accumulables":[{"ID":245,"Name":"local blocks read","Update":"5","Value":"30","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Update":"260","Value":"1560","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Update":"26","Value":"156","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Update":"16","Value":"96","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Update":"59","Value":"354","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Update":"1","Value":"6","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Update":"498790","Value":"4504290","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Update":"0","Value":"5","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Update":"1","Value":"6","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Update":"0","Value":"3","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Update":6,"Value":26,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Update":1613000,"Value":12418000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Update":11,"Value":56,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Update":4648000,"Value":31730000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Update":2848,"Value":17088,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":5,"Value":30,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":260,"Value":1560,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Update":26,"Value":156,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":354,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":6,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Update":498790,"Value":4504290,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":6,"Executor Deserialize CPU Time":1613000,"Executor Run Time":11,"Executor CPU Time":4648000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":5,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":260,"Total Records Read":26},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":498790,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":8,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":26,"Index":2,"Attempt":0,"Partition ID":2,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528435,"Failed":false,"Killed":false,"Accumulables":[{"ID":245,"Name":"local blocks read","Update":"5","Value":"35","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Update":"260","Value":"1820","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Update":"25","Value":"181","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Update":"16","Value":"112","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Update":"59","Value":"413","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Update":"1","Value":"7","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Update":"877625","Value":"5381915","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Update":"4","Value":"9","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Update":"1","Value":"7","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Update":"4","Value":"7","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Update":2,"Value":28,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Update":2226000,"Value":14644000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Update":19,"Value":75,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Update":5887000,"Value":37617000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Update":2848,"Value":19936,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":5,"Value":35,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":260,"Value":1820,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Update":25,"Value":181,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":413,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":7,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Update":877625,"Value":5381915,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":2,"Executor Deserialize CPU Time":2226000,"Executor Run Time":19,"Executor CPU Time":5887000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":5,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":260,"Total Records Read":25},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":877625,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":8,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":32,"Index":8,"Attempt":0,"Partition ID":8,"Launch Time":1642039528425,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528435,"Failed":false,"Killed":false,"Accumulables":[{"ID":245,"Name":"local blocks read","Update":"5","Value":"40","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Update":"260","Value":"2080","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Update":"25","Value":"206","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Update":"16","Value":"128","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Update":"59","Value":"472","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Update":"1","Value":"8","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Update":"574458","Value":"5956373","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Update":"0","Value":"9","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Update":"1","Value":"8","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Update":"0","Value":"7","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Update":2,"Value":30,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Update":1749000,"Value":16393000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Update":5,"Value":80,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Update":5125000,"Value":42742000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Update":2848,"Value":22784,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":5,"Value":40,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":260,"Value":2080,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Update":25,"Value":206,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":472,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":8,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Update":574458,"Value":5956373,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":2,"Executor Deserialize CPU Time":1749000,"Executor Run Time":5,"Executor CPU Time":5125000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":5,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":260,"Total Records Read":25},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":574458,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":8,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":28,"Index":4,"Attempt":0,"Partition ID":4,"Launch Time":1642039528411,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528436,"Failed":false,"Killed":false,"Accumulables":[{"ID":245,"Name":"local blocks read","Update":"5","Value":"45","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Update":"260","Value":"2340","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Update":"26","Value":"232","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Update":"16","Value":"144","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Update":"59","Value":"531","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Update":"1","Value":"9","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Update":"695666","Value":"6652039","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Update":"1","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Update":"1","Value":"9","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Update":"0","Value":"7","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Update":5,"Value":35,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Update":1966000,"Value":18359000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Update":16,"Value":96,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Update":5508000,"Value":48250000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Update":2848,"Value":25632,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":5,"Value":45,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":260,"Value":2340,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Update":26,"Value":232,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":531,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":9,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Update":695666,"Value":6652039,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":5,"Executor Deserialize CPU Time":1966000,"Executor Run Time":16,"Executor CPU Time":5508000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":5,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":260,"Total Records Read":26},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":695666,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":8,"Stage Attempt ID":0,"Task Type":"ShuffleMapTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":33,"Index":9,"Attempt":0,"Partition ID":9,"Launch Time":1642039528426,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528437,"Failed":false,"Killed":false,"Accumulables":[{"ID":245,"Name":"local blocks read","Update":"5","Value":"50","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Update":"260","Value":"2600","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Update":"26","Value":"258","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Update":"16","Value":"160","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Update":"59","Value":"590","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Update":"1","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Update":"535084","Value":"7187123","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Update":"0","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Update":"1","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Update":"0","Value":"7","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Update":4,"Value":39,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Update":1688000,"Value":20047000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Update":4,"Value":100,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Update":4639000,"Value":52889000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Update":2848,"Value":28480,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":5,"Value":50,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":260,"Value":2600,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Update":26,"Value":258,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Update":59,"Value":590,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Update":1,"Value":10,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Update":535084,"Value":7187123,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":4,"Executor Deserialize CPU Time":1688000,"Executor Run Time":4,"Executor CPU Time":4639000,"Peak Execution Memory":0,"Result Size":2848,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":5,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":260,"Total Records Read":26},"Shuffle Write Metrics":{"Shuffle Bytes Written":59,"Shuffle Write Time":535084,"Shuffle Records Written":1},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerStageCompleted","Stage Info":{"Stage ID":8,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":10,"RDD Info":[{"RDD ID":18,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"32\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[17],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":16,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"37\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[15],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":17,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"33\",\"name\":\"WholeStageCodegen (2)\"}","Callsite":"count at <console>:23","Parent IDs":[16],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[7],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039528404,"Completion Time":1642039528438,"Accumulables":[{"ID":245,"Name":"local blocks read","Value":"50","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":248,"Name":"local bytes read","Value":"2600","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":249,"Name":"fetch wait time","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":250,"Name":"records read","Value":"258","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":286,"Name":"data size","Value":"160","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":295,"Name":"shuffle bytes written","Value":"590","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":296,"Name":"shuffle records written","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":297,"Name":"shuffle write time","Value":"7187123","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":298,"Name":"duration","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":299,"Name":"number of output rows","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":302,"Name":"time in aggregation build","Value":"7","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":305,"Name":"internal.metrics.executorDeserializeTime","Value":39,"Internal":true,"Count Failed Values":true},{"ID":306,"Name":"internal.metrics.executorDeserializeCpuTime","Value":20047000,"Internal":true,"Count Failed Values":true},{"ID":307,"Name":"internal.metrics.executorRunTime","Value":100,"Internal":true,"Count Failed Values":true},{"ID":308,"Name":"internal.metrics.executorCpuTime","Value":52889000,"Internal":true,"Count Failed Values":true},{"ID":309,"Name":"internal.metrics.resultSize","Value":28480,"Internal":true,"Count Failed Values":true},{"ID":316,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Value":0,"Internal":true,"Count Failed Values":true},{"ID":317,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Value":50,"Internal":true,"Count Failed Values":true},{"ID":318,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Value":0,"Internal":true,"Count Failed Values":true},{"ID":319,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Value":0,"Internal":true,"Count Failed Values":true},{"ID":320,"Name":"internal.metrics.shuffle.read.localBytesRead","Value":2600,"Internal":true,"Count Failed Values":true},{"ID":321,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Value":0,"Internal":true,"Count Failed Values":true},{"ID":322,"Name":"internal.metrics.shuffle.read.recordsRead","Value":258,"Internal":true,"Count Failed Values":true},{"ID":323,"Name":"internal.metrics.shuffle.write.bytesWritten","Value":590,"Internal":true,"Count Failed Values":true},{"ID":324,"Name":"internal.metrics.shuffle.write.recordsWritten","Value":10,"Internal":true,"Count Failed Values":true},{"ID":325,"Name":"internal.metrics.shuffle.write.writeTime","Value":7187123,"Internal":true,"Count Failed Values":true}],"Resource Profile Id":0}}
{"Event":"SparkListenerJobEnd","Job ID":4,"Completion Time":1642039528439,"Job Result":{"Result":"JobSucceeded"}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerSQLAdaptiveExecutionUpdate","executionId":1,"physicalPlanDescription":"== Physical Plan ==\nAdaptiveSparkPlan (13)\n+- == Final Plan ==\n   * HashAggregate (8)\n   +- ShuffleQueryStage (7)\n      +- Exchange (6)\n         +- * HashAggregate (5)\n            +- ShuffleQueryStage (4)\n               +- Exchange (3)\n                  +- * Project (2)\n                     +- * Range (1)\n+- == Initial Plan ==\n   HashAggregate (12)\n   +- Exchange (11)\n      +- HashAggregate (10)\n         +- Exchange (9)\n            +- Project (2)\n               +- Range (1)\n\n\n(1) Range [codegen id : 1]\nOutput [1]: [id#10L]\nArguments: Range (0, 3347, step=13, splits=Some(5))\n\n(2) Project [codegen id : 1]\nOutput: []\nInput [1]: [id#10L]\n\n(3) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#89]\n\n(4) ShuffleQueryStage\nOutput: []\nArguments: 0\n\n(5) HashAggregate [codegen id : 2]\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#18L]\nResults [1]: [count#19L]\n\n(6) Exchange\nInput [1]: [count#19L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#110]\n\n(7) ShuffleQueryStage\nOutput [1]: [count#19L]\nArguments: 1\n\n(8) HashAggregate [codegen id : 3]\nInput [1]: [count#19L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#15L]\nResults [1]: [count(1)#15L AS count#16L]\n\n(9) Exchange\nInput: []\nArguments: RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#76]\n\n(10) HashAggregate\nInput: []\nKeys: []\nFunctions [1]: [partial_count(1)]\nAggregate Attributes [1]: [count#18L]\nResults [1]: [count#19L]\n\n(11) Exchange\nInput [1]: [count#19L]\nArguments: SinglePartition, ENSURE_REQUIREMENTS, [id=#80]\n\n(12) HashAggregate\nInput [1]: [count#19L]\nKeys: []\nFunctions [1]: [count(1)]\nAggregate Attributes [1]: [count(1)#15L]\nResults [1]: [count(1)#15L AS count#16L]\n\n(13) AdaptiveSparkPlan\nOutput [1]: [count#16L]\nArguments: isFinalPlan=true\n\n","sparkPlanInfo":{"nodeName":"AdaptiveSparkPlan","simpleString":"AdaptiveSparkPlan isFinalPlan=true","children":[{"nodeName":"WholeStageCodegen (3)","simpleString":"WholeStageCodegen (3)","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[count(1)])","children":[{"nodeName":"InputAdapter","simpleString":"InputAdapter","children":[{"nodeName":"ShuffleQueryStage","simpleString":"ShuffleQueryStage 1","children":[{"nodeName":"Exchange","simpleString":"Exchange SinglePartition, ENSURE_REQUIREMENTS, [id=#110]","children":[{"nodeName":"WholeStageCodegen (2)","simpleString":"WholeStageCodegen (2)","children":[{"nodeName":"HashAggregate","simpleString":"HashAggregate(keys=[], functions=[partial_count(1)])","children":[{"nodeName":"InputAdapter","simpleString":"InputAdapter","children":[{"nodeName":"ShuffleQueryStage","simpleString":"ShuffleQueryStage 0","children":[{"nodeName":"Exchange","simpleString":"Exchange RoundRobinPartitioning(10), REPARTITION_BY_NUM, [id=#89]","children":[{"nodeName":"WholeStageCodegen (1)","simpleString":"WholeStageCodegen (1)","children":[{"nodeName":"Project","simpleString":"Project","children":[{"nodeName":"Range","simpleString":"Range (0, 3347, step=13, splits=5)","children":[],"metadata":{},"metrics":[{"name":"number of output rows","accumulatorId":217,"metricType":"sum"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":254,"metricType":"timing"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":252,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":253,"metricType":"nsTiming"},{"name":"records read","accumulatorId":250,"metricType":"sum"},{"name":"local bytes read","accumulatorId":248,"metricType":"size"},{"name":"fetch wait time","accumulatorId":249,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":246,"metricType":"size"},{"name":"local blocks read","accumulatorId":245,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":244,"metricType":"sum"},{"name":"data size","accumulatorId":242,"metricType":"size"},{"name":"number of partitions","accumulatorId":243,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":247,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":251,"metricType":"size"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":301,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":302,"metricType":"timing"},{"name":"peak memory","accumulatorId":300,"metricType":"size"},{"name":"number of output rows","accumulatorId":299,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":304,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":303,"metricType":"average"}]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":298,"metricType":"timing"}]}],"metadata":{},"metrics":[{"name":"shuffle records written","accumulatorId":296,"metricType":"sum"},{"name":"shuffle write time","accumulatorId":297,"metricType":"nsTiming"},{"name":"records read","accumulatorId":294,"metricType":"sum"},{"name":"local bytes read","accumulatorId":292,"metricType":"size"},{"name":"fetch wait time","accumulatorId":293,"metricType":"timing"},{"name":"remote bytes read","accumulatorId":290,"metricType":"size"},{"name":"local blocks read","accumulatorId":289,"metricType":"sum"},{"name":"remote blocks read","accumulatorId":288,"metricType":"sum"},{"name":"data size","accumulatorId":286,"metricType":"size"},{"name":"number of partitions","accumulatorId":287,"metricType":"sum"},{"name":"remote bytes read to disk","accumulatorId":291,"metricType":"size"},{"name":"shuffle bytes written","accumulatorId":295,"metricType":"size"}]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[]}],"metadata":{},"metrics":[{"name":"spill size","accumulatorId":333,"metricType":"size"},{"name":"time in aggregation build","accumulatorId":334,"metricType":"timing"},{"name":"peak memory","accumulatorId":332,"metricType":"size"},{"name":"number of output rows","accumulatorId":331,"metricType":"sum"},{"name":"number of sort fallback tasks","accumulatorId":336,"metricType":"sum"},{"name":"avg hash probe bucket list iters","accumulatorId":335,"metricType":"average"}]}],"metadata":{},"metrics":[{"name":"duration","accumulatorId":330,"metricType":"timing"}]}],"metadata":{},"metrics":[]}}
{"Event":"SparkListenerJobStart","Job ID":5,"Submission Time":1642039528461,"Stage Infos":[{"Stage ID":9,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":5,"RDD Info":[{"RDD ID":15,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[14],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":14,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"28\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[13],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":12,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[11],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":11,"Name":"ParallelCollectionRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":13,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"29\",\"name\":\"WholeStageCodegen (1)\"}","Callsite":"count at <console>:23","Parent IDs":[12],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"DETERMINATE","Number of Partitions":5,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0},{"Stage ID":10,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":10,"RDD Info":[{"RDD ID":18,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"32\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[17],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":16,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"37\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[15],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":17,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"33\",\"name\":\"WholeStageCodegen (2)\"}","Callsite":"count at <console>:23","Parent IDs":[16],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":10,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[9],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0},{"Stage ID":11,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":1,"RDD Info":[{"RDD ID":21,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"43\",\"name\":\"mapPartitionsInternal\"}","Callsite":"count at <console>:23","Parent IDs":[20],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":20,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"38\",\"name\":\"WholeStageCodegen (3)\"}","Callsite":"count at <console>:23","Parent IDs":[19],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":19,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"42\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[18],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[10],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Accumulables":[],"Resource Profile Id":0}],"Stage IDs":[9,10,11],"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","__fetch_continuous_blocks_in_batch_enabled":"true","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"44\",\"name\":\"collect\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"1","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerStageSubmitted","Stage Info":{"Stage ID":11,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":1,"RDD Info":[{"RDD ID":21,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"43\",\"name\":\"mapPartitionsInternal\"}","Callsite":"count at <console>:23","Parent IDs":[20],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":20,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"38\",\"name\":\"WholeStageCodegen (3)\"}","Callsite":"count at <console>:23","Parent IDs":[19],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":19,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"42\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[18],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[10],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039528463,"Accumulables":[],"Resource Profile Id":0},"Properties":{"spark.sql.warehouse.dir":"file:/Users/<USER>/Code/stczwd/spark/dist/spark-warehouse","spark.executor.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"*************","spark.eventLog.enabled":"true","spark.driver.port":"61038","__fetch_continuous_blocks_in_batch_enabled":"true","spark.repl.class.uri":"spark://*************:61038/classes","spark.jars":"","spark.repl.class.outputDir":"/private/var/folders/dm/1vhcj_l97j146n6mgr2cm9rw0000gp/T/spark-501ae231-0cb9-4ba2-845f-3fc3cb053141/repl-054c2c94-f7a2-4f4b-9f12-96a1cdb15bc6","spark.app.name":"Spark shell","spark.rdd.scope":"{\"id\":\"44\",\"name\":\"collect\"}","spark.rdd.scope.noOverride":"true","spark.submit.pyFiles":"","spark.ui.showConsoleProgress":"true","spark.app.startTime":"1642039450519","spark.executor.id":"driver","spark.driver.extraJavaOptions":"-XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"client","spark.master":"local[*]","spark.home":"/Users/<USER>/Code/stczwd/spark/dist","spark.eventLog.dir":"/Users/<USER>/Code/stczwd/spark/dist/eventLog","spark.sql.execution.id":"1","spark.sql.catalogImplementation":"hive","spark.app.id":"local-1642039451826"}}
{"Event":"SparkListenerTaskStart","Stage ID":11,"Stage Attempt ID":0,"Task Info":{"Task ID":34,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039528469,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":11,"Stage Attempt ID":0,"Task Type":"ResultTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":34,"Index":0,"Attempt":0,"Partition ID":0,"Launch Time":1642039528469,"Executor ID":"driver","Host":"*************","Locality":"NODE_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1642039528478,"Failed":false,"Killed":false,"Accumulables":[{"ID":289,"Name":"local blocks read","Update":"10","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":292,"Name":"local bytes read","Update":"590","Value":"590","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":293,"Name":"fetch wait time","Update":"0","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":294,"Name":"records read","Update":"10","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":330,"Name":"duration","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":331,"Name":"number of output rows","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":334,"Name":"time in aggregation build","Update":"1","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":337,"Name":"internal.metrics.executorDeserializeTime","Update":2,"Value":2,"Internal":true,"Count Failed Values":true},{"ID":338,"Name":"internal.metrics.executorDeserializeCpuTime","Update":1866000,"Value":1866000,"Internal":true,"Count Failed Values":true},{"ID":339,"Name":"internal.metrics.executorRunTime","Update":3,"Value":3,"Internal":true,"Count Failed Values":true},{"ID":340,"Name":"internal.metrics.executorCpuTime","Update":3912000,"Value":3912000,"Internal":true,"Count Failed Values":true},{"ID":341,"Name":"internal.metrics.resultSize","Update":2656,"Value":2656,"Internal":true,"Count Failed Values":true},{"ID":348,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":349,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Update":10,"Value":10,"Internal":true,"Count Failed Values":true},{"ID":350,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":351,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":352,"Name":"internal.metrics.shuffle.read.localBytesRead","Update":590,"Value":590,"Internal":true,"Count Failed Values":true},{"ID":353,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Update":0,"Value":0,"Internal":true,"Count Failed Values":true},{"ID":354,"Name":"internal.metrics.shuffle.read.recordsRead","Update":10,"Value":10,"Internal":true,"Count Failed Values":true}]},"Task Executor Metrics":{"JVMHeapMemory":0,"JVMOffHeapMemory":0,"OnHeapExecutionMemory":0,"OffHeapExecutionMemory":0,"OnHeapStorageMemory":0,"OffHeapStorageMemory":0,"OnHeapUnifiedMemory":0,"OffHeapUnifiedMemory":0,"DirectPoolMemory":0,"MappedPoolMemory":0,"ProcessTreeJVMVMemory":0,"ProcessTreeJVMRSSMemory":0,"ProcessTreePythonVMemory":0,"ProcessTreePythonRSSMemory":0,"ProcessTreeOtherVMemory":0,"ProcessTreeOtherRSSMemory":0,"MinorGCCount":0,"MinorGCTime":0,"MajorGCCount":0,"MajorGCTime":0,"TotalGCTime":0},"Task Metrics":{"Executor Deserialize Time":2,"Executor Deserialize CPU Time":1866000,"Executor Run Time":3,"Executor CPU Time":3912000,"Peak Execution Memory":0,"Result Size":2656,"JVM GC Time":0,"Result Serialization Time":0,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":10,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":590,"Total Records Read":10},"Shuffle Write Metrics":{"Shuffle Bytes Written":0,"Shuffle Write Time":0,"Shuffle Records Written":0},"Input Metrics":{"Bytes Read":0,"Records Read":0},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerStageCompleted","Stage Info":{"Stage ID":11,"Stage Attempt ID":0,"Stage Name":"count at <console>:23","Number of Tasks":1,"RDD Info":[{"RDD ID":21,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"43\",\"name\":\"mapPartitionsInternal\"}","Callsite":"count at <console>:23","Parent IDs":[20],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":20,"Name":"MapPartitionsRDD","Scope":"{\"id\":\"38\",\"name\":\"WholeStageCodegen (3)\"}","Callsite":"count at <console>:23","Parent IDs":[19],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0},{"RDD ID":19,"Name":"ShuffledRowRDD","Scope":"{\"id\":\"42\",\"name\":\"Exchange\"}","Callsite":"count at <console>:23","Parent IDs":[18],"Storage Level":{"Use Disk":false,"Use Memory":false,"Deserialized":false,"Replication":1},"Barrier":false,"DeterministicLevel":"UNORDERED","Number of Partitions":1,"Number of Cached Partitions":0,"Memory Size":0,"Disk Size":0}],"Parent IDs":[10],"Details":"org.apache.spark.sql.Dataset.count(Dataset.scala:3130)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:23)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line16.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:29)\n$line16.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:31)\n$line16.$read$$iw$$iw$$iw$$iw.<init>(<console>:33)\n$line16.$read$$iw$$iw$$iw.<init>(<console>:35)\n$line16.$read$$iw$$iw.<init>(<console>:37)\n$line16.$read$$iw.<init>(<console>:39)\n$line16.$read.<init>(<console>:41)\n$line16.$read$.<init>(<console>:45)\n$line16.$read$.<clinit>(<console>)\n$line16.$eval$.$print$lzycompute(<console>:7)\n$line16.$eval$.$print(<console>:6)\n$line16.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:747)","Submission Time":1642039528463,"Completion Time":1642039528479,"Accumulables":[{"ID":289,"Name":"local blocks read","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":292,"Name":"local bytes read","Value":"590","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":293,"Name":"fetch wait time","Value":"0","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":294,"Name":"records read","Value":"10","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":330,"Name":"duration","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":331,"Name":"number of output rows","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":334,"Name":"time in aggregation build","Value":"1","Internal":true,"Count Failed Values":true,"Metadata":"sql"},{"ID":337,"Name":"internal.metrics.executorDeserializeTime","Value":2,"Internal":true,"Count Failed Values":true},{"ID":338,"Name":"internal.metrics.executorDeserializeCpuTime","Value":1866000,"Internal":true,"Count Failed Values":true},{"ID":339,"Name":"internal.metrics.executorRunTime","Value":3,"Internal":true,"Count Failed Values":true},{"ID":340,"Name":"internal.metrics.executorCpuTime","Value":3912000,"Internal":true,"Count Failed Values":true},{"ID":341,"Name":"internal.metrics.resultSize","Value":2656,"Internal":true,"Count Failed Values":true},{"ID":348,"Name":"internal.metrics.shuffle.read.remoteBlocksFetched","Value":0,"Internal":true,"Count Failed Values":true},{"ID":349,"Name":"internal.metrics.shuffle.read.localBlocksFetched","Value":10,"Internal":true,"Count Failed Values":true},{"ID":350,"Name":"internal.metrics.shuffle.read.remoteBytesRead","Value":0,"Internal":true,"Count Failed Values":true},{"ID":351,"Name":"internal.metrics.shuffle.read.remoteBytesReadToDisk","Value":0,"Internal":true,"Count Failed Values":true},{"ID":352,"Name":"internal.metrics.shuffle.read.localBytesRead","Value":590,"Internal":true,"Count Failed Values":true},{"ID":353,"Name":"internal.metrics.shuffle.read.fetchWaitTime","Value":0,"Internal":true,"Count Failed Values":true},{"ID":354,"Name":"internal.metrics.shuffle.read.recordsRead","Value":10,"Internal":true,"Count Failed Values":true}],"Resource Profile Id":0}}
{"Event":"SparkListenerJobEnd","Job ID":5,"Completion Time":1642039528479,"Job Result":{"Result":"JobSucceeded"}}
{"Event":"org.apache.spark.sql.execution.ui.SparkListenerSQLExecutionEnd","executionId":1,"time":1642039528481}
{"Event":"SparkListenerApplicationEnd","Timestamp":1642039536564}
