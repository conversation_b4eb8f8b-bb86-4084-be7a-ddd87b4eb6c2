{"status": "COMPLETE", "stageId": 0, "attemptId": 0, "numTasks": 10, "numActiveTasks": 0, "numCompleteTasks": 10, "numFailedTasks": 4, "numKilledTasks": 0, "numCompletedIndices": 10, "submissionTime": "2018-01-18T18:33:12.658GMT", "firstTaskLaunchedTime": "2018-01-18T18:33:12.816GMT", "completionTime": "2018-01-18T18:33:15.279GMT", "executorDeserializeTime": 3679, "executorDeserializeCpuTime": 1029819716, "executorRunTime": 5080, "executorCpuTime": 1163210819, "resultSize": 10824, "jvmGcTime": 370, "resultSerializationTime": 5, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputBytes": 0, "inputRecords": 0, "outputBytes": 0, "outputRecords": 0, "shuffleRemoteBlocksFetched": 0, "shuffleLocalBlocksFetched": 0, "shuffleFetchWaitTime": 0, "shuffleRemoteBytesRead": 0, "shuffleRemoteBytesReadToDisk": 0, "shuffleLocalBytesRead": 0, "shuffleReadBytes": 0, "shuffleCorruptMergedBlockChunks": 0, "shuffleMergedFetchFallbackCount": 0, "shuffleMergedRemoteBlocksFetched": 0, "shuffleMergedLocalBlocksFetched": 0, "shuffleMergedRemoteChunksFetched": 0, "shuffleMergedLocalChunksFetched": 0, "shuffleMergedRemoteBytesRead": 0, "shuffleMergedLocalBytesRead": 0, "shuffleRemoteReqsDuration": 0, "shuffleMergedRemoteReqsDuration": 0, "isShufflePushEnabled": false, "shuffleMergersCount": 0, "shuffleReadRecords": 0, "shuffleWriteBytes": 1461, "shuffleWriteTime": 33251697, "shuffleWriteRecords": 30, "name": "map at <console>:27", "details": "org.apache.spark.rdd.RDD.map(RDD.scala:370)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:27)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:35)\n$line15.$read$$iw$$iw$$iw$$iw$$iw$$iw.<init>(<console>:37)\n$line15.$read$$iw$$iw$$iw$$iw$$iw.<init>(<console>:39)\n$line15.$read$$iw$$iw$$iw$$iw.<init>(<console>:41)\n$line15.$read$$iw$$iw$$iw.<init>(<console>:43)\n$line15.$read$$iw$$iw.<init>(<console>:45)\n$line15.$read$$iw.<init>(<console>:47)\n$line15.$read.<init>(<console>:49)\n$line15.$read$.<init>(<console>:53)\n$line15.$read$.<clinit>(<console>)\n$line15.$eval$.$print$lzycompute(<console>:7)\n$line15.$eval$.$print(<console>:6)\n$line15.$eval.$print(<console>)\nsun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nsun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\nsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\njava.lang.reflect.Method.invoke(Method.java:498)\nscala.tools.nsc.interpreter.IMain$ReadEvalPrint.call(IMain.scala:786)", "schedulingPool": "default", "rddIds": [1, 0], "accumulatorUpdates": [], "tasks": {"0": {"taskId": 0, "index": 0, "attempt": 0, "partitionId": -1, "launchTime": "2018-01-18T18:33:12.816GMT", "duration": 2064, "executorId": "1", "host": "apiros-3.gce.test.com", "status": "SUCCESS", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "taskMetrics": {"executorDeserializeTime": 1081, "executorDeserializeCpuTime": 353981050, "executorRunTime": 914, "executorCpuTime": 368865439, "resultSize": 1134, "jvmGcTime": 75, "resultSerializationTime": 1, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 144, "writeTime": 3662221, "recordsWritten": 3}}, "executorLogs": {"stdout": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000002/attilapiros/stdout?start=-4096", "stderr": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000002/attilapiros/stderr?start=-4096"}, "schedulerDelay": 68, "gettingResultTime": 0}, "5": {"taskId": 5, "index": 5, "attempt": 0, "partitionId": -1, "launchTime": "2018-01-18T18:33:14.320GMT", "duration": 73, "executorId": "5", "host": "apiros-2.gce.test.com", "status": "FAILED", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "errorMessage": "java.lang.RuntimeException: Bad executor\n\tat $line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$anonfun$2.apply(<console>:28)\n\tat $line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$anonfun$2.apply(<console>:27)\n\tat scala.collection.Iterator$$anon$11.next(Iterator.scala:409)\n\tat org.apache.spark.util.collection.ExternalSorter.insertAll(ExternalSorter.scala:193)\n\tat org.apache.spark.shuffle.sort.SortShuffleWriter.write(SortShuffleWriter.scala:63)\n\tat org.apache.spark.scheduler.ShuffleMapTask.runTask(ShuffleMapTask.scala:96)\n\tat org.apache.spark.scheduler.ShuffleMapTask.runTask(ShuffleMapTask.scala:53)\n\tat org.apache.spark.scheduler.Task.run(Task.scala:109)\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:345)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:748)\n", "taskMetrics": {"executorDeserializeTime": 0, "executorDeserializeCpuTime": 0, "executorRunTime": 27, "executorCpuTime": 0, "resultSize": 0, "jvmGcTime": 0, "resultSerializationTime": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 0, "writeTime": 191901, "recordsWritten": 0}}, "executorLogs": {"stdout": "http://apiros-2.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000007/attilapiros/stdout?start=-4096", "stderr": "http://apiros-2.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000007/attilapiros/stderr?start=-4096"}, "schedulerDelay": 46, "gettingResultTime": 0}, "10": {"taskId": 10, "index": 1, "attempt": 1, "partitionId": -1, "launchTime": "2018-01-18T18:33:15.069GMT", "duration": 132, "executorId": "2", "host": "apiros-3.gce.test.com", "status": "SUCCESS", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "taskMetrics": {"executorDeserializeTime": 5, "executorDeserializeCpuTime": 4598966, "executorRunTime": 76, "executorCpuTime": 20826337, "resultSize": 1091, "jvmGcTime": 0, "resultSerializationTime": 1, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 144, "writeTime": 301705, "recordsWritten": 3}}, "executorLogs": {"stdout": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000003/attilapiros/stdout?start=-4096", "stderr": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000003/attilapiros/stderr?start=-4096"}, "schedulerDelay": 50, "gettingResultTime": 0}, "1": {"taskId": 1, "index": 1, "attempt": 0, "partitionId": -1, "launchTime": "2018-01-18T18:33:12.832GMT", "duration": 1506, "executorId": "5", "host": "apiros-2.gce.test.com", "status": "FAILED", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "errorMessage": "java.lang.RuntimeException: Bad executor\n\tat $line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$anonfun$2.apply(<console>:28)\n\tat $line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$anonfun$2.apply(<console>:27)\n\tat scala.collection.Iterator$$anon$11.next(Iterator.scala:409)\n\tat org.apache.spark.util.collection.ExternalSorter.insertAll(ExternalSorter.scala:193)\n\tat org.apache.spark.shuffle.sort.SortShuffleWriter.write(SortShuffleWriter.scala:63)\n\tat org.apache.spark.scheduler.ShuffleMapTask.runTask(ShuffleMapTask.scala:96)\n\tat org.apache.spark.scheduler.ShuffleMapTask.runTask(ShuffleMapTask.scala:53)\n\tat org.apache.spark.scheduler.Task.run(Task.scala:109)\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:345)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:748)\n", "taskMetrics": {"executorDeserializeTime": 0, "executorDeserializeCpuTime": 0, "executorRunTime": 1332, "executorCpuTime": 0, "resultSize": 0, "jvmGcTime": 33, "resultSerializationTime": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 0, "writeTime": 3075188, "recordsWritten": 0}}, "executorLogs": {"stdout": "http://apiros-2.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000007/attilapiros/stdout?start=-4096", "stderr": "http://apiros-2.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000007/attilapiros/stderr?start=-4096"}, "schedulerDelay": 174, "gettingResultTime": 0}, "6": {"taskId": 6, "index": 6, "attempt": 0, "partitionId": -1, "launchTime": "2018-01-18T18:33:14.323GMT", "duration": 67, "executorId": "4", "host": "apiros-2.gce.test.com", "status": "FAILED", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "errorMessage": "java.lang.RuntimeException: Bad executor\n\tat $line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$anonfun$2.apply(<console>:28)\n\tat $line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$anonfun$2.apply(<console>:27)\n\tat scala.collection.Iterator$$anon$11.next(Iterator.scala:409)\n\tat org.apache.spark.util.collection.ExternalSorter.insertAll(ExternalSorter.scala:193)\n\tat org.apache.spark.shuffle.sort.SortShuffleWriter.write(SortShuffleWriter.scala:63)\n\tat org.apache.spark.scheduler.ShuffleMapTask.runTask(ShuffleMapTask.scala:96)\n\tat org.apache.spark.scheduler.ShuffleMapTask.runTask(ShuffleMapTask.scala:53)\n\tat org.apache.spark.scheduler.Task.run(Task.scala:109)\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:345)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:748)\n", "taskMetrics": {"executorDeserializeTime": 0, "executorDeserializeCpuTime": 0, "executorRunTime": 51, "executorCpuTime": 0, "resultSize": 0, "jvmGcTime": 0, "resultSerializationTime": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 0, "writeTime": 183718, "recordsWritten": 0}}, "executorLogs": {"stdout": "http://apiros-2.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000005/attilapiros/stdout?start=-4096", "stderr": "http://apiros-2.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000005/attilapiros/stderr?start=-4096"}, "schedulerDelay": 16, "gettingResultTime": 0}, "9": {"taskId": 9, "index": 4, "attempt": 1, "partitionId": -1, "launchTime": "2018-01-18T18:33:14.973GMT", "duration": 96, "executorId": "2", "host": "apiros-3.gce.test.com", "status": "SUCCESS", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "taskMetrics": {"executorDeserializeTime": 5, "executorDeserializeCpuTime": 4793905, "executorRunTime": 48, "executorCpuTime": 25678331, "resultSize": 1091, "jvmGcTime": 0, "resultSerializationTime": 1, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 147, "writeTime": 366050, "recordsWritten": 3}}, "executorLogs": {"stdout": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000003/attilapiros/stdout?start=-4096", "stderr": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000003/attilapiros/stderr?start=-4096"}, "schedulerDelay": 42, "gettingResultTime": 0}, "13": {"taskId": 13, "index": 9, "attempt": 0, "partitionId": -1, "launchTime": "2018-01-18T18:33:15.200GMT", "duration": 76, "executorId": "2", "host": "apiros-3.gce.test.com", "status": "SUCCESS", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "taskMetrics": {"executorDeserializeTime": 25, "executorDeserializeCpuTime": 5860574, "executorRunTime": 25, "executorCpuTime": 20585619, "resultSize": 1048, "jvmGcTime": 0, "resultSerializationTime": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 147, "writeTime": 369513, "recordsWritten": 3}}, "executorLogs": {"stdout": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000003/attilapiros/stdout?start=-4096", "stderr": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000003/attilapiros/stderr?start=-4096"}, "schedulerDelay": 26, "gettingResultTime": 0}, "2": {"taskId": 2, "index": 2, "attempt": 0, "partitionId": -1, "launchTime": "2018-01-18T18:33:12.832GMT", "duration": 1774, "executorId": "3", "host": "apiros-2.gce.test.com", "status": "SUCCESS", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "taskMetrics": {"executorDeserializeTime": 1206, "executorDeserializeCpuTime": 263386625, "executorRunTime": 493, "executorCpuTime": 278399617, "resultSize": 1134, "jvmGcTime": 78, "resultSerializationTime": 1, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 144, "writeTime": 3322956, "recordsWritten": 3}}, "executorLogs": {"stdout": "http://apiros-2.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000004/attilapiros/stdout?start=-4096", "stderr": "http://apiros-2.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000004/attilapiros/stderr?start=-4096"}, "schedulerDelay": 74, "gettingResultTime": 0}, "12": {"taskId": 12, "index": 8, "attempt": 0, "partitionId": -1, "launchTime": "2018-01-18T18:33:15.165GMT", "duration": 60, "executorId": "1", "host": "apiros-3.gce.test.com", "status": "SUCCESS", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "taskMetrics": {"executorDeserializeTime": 4, "executorDeserializeCpuTime": 4010338, "executorRunTime": 34, "executorCpuTime": 21657558, "resultSize": 1048, "jvmGcTime": 0, "resultSerializationTime": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 147, "writeTime": 319101, "recordsWritten": 3}}, "executorLogs": {"stdout": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000002/attilapiros/stdout?start=-4096", "stderr": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000002/attilapiros/stderr?start=-4096"}, "schedulerDelay": 22, "gettingResultTime": 0}, "7": {"taskId": 7, "index": 5, "attempt": 1, "partitionId": -1, "launchTime": "2018-01-18T18:33:14.859GMT", "duration": 115, "executorId": "2", "host": "apiros-3.gce.test.com", "status": "SUCCESS", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "taskMetrics": {"executorDeserializeTime": 11, "executorDeserializeCpuTime": 10894331, "executorRunTime": 84, "executorCpuTime": 28283110, "resultSize": 1048, "jvmGcTime": 0, "resultSerializationTime": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 147, "writeTime": 377601, "recordsWritten": 3}}, "executorLogs": {"stdout": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000003/attilapiros/stdout?start=-4096", "stderr": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000003/attilapiros/stderr?start=-4096"}, "schedulerDelay": 20, "gettingResultTime": 0}, "3": {"taskId": 3, "index": 3, "attempt": 0, "partitionId": -1, "launchTime": "2018-01-18T18:33:12.833GMT", "duration": 2027, "executorId": "2", "host": "apiros-3.gce.test.com", "status": "SUCCESS", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "taskMetrics": {"executorDeserializeTime": 1282, "executorDeserializeCpuTime": 365807898, "executorRunTime": 681, "executorCpuTime": 349920830, "resultSize": 1134, "jvmGcTime": 102, "resultSerializationTime": 1, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 147, "writeTime": 3587839, "recordsWritten": 3}}, "executorLogs": {"stdout": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000003/attilapiros/stdout?start=-4096", "stderr": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000003/attilapiros/stderr?start=-4096"}, "schedulerDelay": 63, "gettingResultTime": 0}, "11": {"taskId": 11, "index": 7, "attempt": 0, "partitionId": -1, "launchTime": "2018-01-18T18:33:15.072GMT", "duration": 93, "executorId": "1", "host": "apiros-3.gce.test.com", "status": "SUCCESS", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "taskMetrics": {"executorDeserializeTime": 4, "executorDeserializeCpuTime": 4239884, "executorRunTime": 77, "executorCpuTime": 21689428, "resultSize": 1048, "jvmGcTime": 0, "resultSerializationTime": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 147, "writeTime": 323898, "recordsWritten": 3}}, "executorLogs": {"stdout": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000002/attilapiros/stdout?start=-4096", "stderr": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000002/attilapiros/stderr?start=-4096"}, "schedulerDelay": 12, "gettingResultTime": 0}, "8": {"taskId": 8, "index": 6, "attempt": 1, "partitionId": -1, "launchTime": "2018-01-18T18:33:14.879GMT", "duration": 194, "executorId": "1", "host": "apiros-3.gce.test.com", "status": "SUCCESS", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "taskMetrics": {"executorDeserializeTime": 56, "executorDeserializeCpuTime": 12246145, "executorRunTime": 54, "executorCpuTime": 27304550, "resultSize": 1048, "jvmGcTime": 0, "resultSerializationTime": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 147, "writeTime": 311940, "recordsWritten": 3}}, "executorLogs": {"stdout": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000002/attilapiros/stdout?start=-4096", "stderr": "http://apiros-3.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000002/attilapiros/stderr?start=-4096"}, "schedulerDelay": 84, "gettingResultTime": 0}, "4": {"taskId": 4, "index": 4, "attempt": 0, "partitionId": -1, "launchTime": "2018-01-18T18:33:12.833GMT", "duration": 1522, "executorId": "4", "host": "apiros-2.gce.test.com", "status": "FAILED", "taskLocality": "PROCESS_LOCAL", "speculative": false, "accumulatorUpdates": [], "errorMessage": "java.lang.RuntimeException: Bad executor\n\tat $line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$anonfun$2.apply(<console>:28)\n\tat $line15.$read$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$iw$$anonfun$2.apply(<console>:27)\n\tat scala.collection.Iterator$$anon$11.next(Iterator.scala:409)\n\tat org.apache.spark.util.collection.ExternalSorter.insertAll(ExternalSorter.scala:193)\n\tat org.apache.spark.shuffle.sort.SortShuffleWriter.write(SortShuffleWriter.scala:63)\n\tat org.apache.spark.scheduler.ShuffleMapTask.runTask(ShuffleMapTask.scala:96)\n\tat org.apache.spark.scheduler.ShuffleMapTask.runTask(ShuffleMapTask.scala:53)\n\tat org.apache.spark.scheduler.Task.run(Task.scala:109)\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:345)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:748)\n", "taskMetrics": {"executorDeserializeTime": 0, "executorDeserializeCpuTime": 0, "executorRunTime": 1184, "executorCpuTime": 0, "resultSize": 0, "jvmGcTime": 82, "resultSerializationTime": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "peakExecutionMemory": 0, "inputMetrics": {"bytesRead": 0, "recordsRead": 0}, "outputMetrics": {"bytesWritten": 0, "recordsWritten": 0}, "shuffleReadMetrics": {"remoteBlocksFetched": 0, "localBlocksFetched": 0, "fetchWaitTime": 0, "remoteBytesRead": 0, "remoteBytesReadToDisk": 0, "localBytesRead": 0, "recordsRead": 0, "shufflePushReadMetrics": {"corruptMergedBlockChunks": 0, "mergedFetchFallbackCount": 0, "localMergedBytesRead": 0, "localMergedBlocksFetched": 0, "localMergedChunksFetched": 0, "remoteMergedBytesRead": 0, "remoteMergedBlocksFetched": 0, "remoteMergedChunksFetched": 0, "remoteMergedReqsDuration": 0}, "remoteReqsDuration": 0}, "shuffleWriteMetrics": {"bytesWritten": 0, "writeTime": 16858066, "recordsWritten": 0}}, "executorLogs": {"stdout": "http://apiros-2.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000005/attilapiros/stdout?start=-4096", "stderr": "http://apiros-2.gce.test.com:8042/node/containerlogs/container_1516285256255_0012_01_000005/attilapiros/stderr?start=-4096"}, "schedulerDelay": 338, "gettingResultTime": 0}}, "executorSummary": {"4": {"taskTime": 1589, "failedTasks": 2, "succeededTasks": 0, "killedTasks": 0, "inputBytes": 0, "inputRecords": 0, "outputBytes": 0, "outputRecords": 0, "shuffleRead": 0, "shuffleReadRecords": 0, "shuffleWrite": 0, "shuffleWriteRecords": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "isBlacklistedForStage": true, "peakMemoryMetrics": {"JVMHeapMemory": 0, "JVMOffHeapMemory": 0, "OnHeapExecutionMemory": 0, "OffHeapExecutionMemory": 0, "OnHeapStorageMemory": 0, "OffHeapStorageMemory": 0, "OnHeapUnifiedMemory": 0, "OffHeapUnifiedMemory": 0, "DirectPoolMemory": 0, "MappedPoolMemory": 0, "ProcessTreeJVMVMemory": 0, "ProcessTreeJVMRSSMemory": 0, "ProcessTreePythonVMemory": 0, "ProcessTreePythonRSSMemory": 0, "ProcessTreeOtherVMemory": 0, "ProcessTreeOtherRSSMemory": 0, "MinorGCCount": 0, "MinorGCTime": 0, "MajorGCCount": 0, "MajorGCTime": 0, "TotalGCTime": 0}, "isExcludedForStage": true}, "5": {"taskTime": 1579, "failedTasks": 2, "succeededTasks": 0, "killedTasks": 0, "inputBytes": 0, "inputRecords": 0, "outputBytes": 0, "outputRecords": 0, "shuffleRead": 0, "shuffleReadRecords": 0, "shuffleWrite": 0, "shuffleWriteRecords": 0, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "isBlacklistedForStage": true, "peakMemoryMetrics": {"JVMHeapMemory": 0, "JVMOffHeapMemory": 0, "OnHeapExecutionMemory": 0, "OffHeapExecutionMemory": 0, "OnHeapStorageMemory": 0, "OffHeapStorageMemory": 0, "OnHeapUnifiedMemory": 0, "OffHeapUnifiedMemory": 0, "DirectPoolMemory": 0, "MappedPoolMemory": 0, "ProcessTreeJVMVMemory": 0, "ProcessTreeJVMRSSMemory": 0, "ProcessTreePythonVMemory": 0, "ProcessTreePythonRSSMemory": 0, "ProcessTreeOtherVMemory": 0, "ProcessTreeOtherRSSMemory": 0, "MinorGCCount": 0, "MinorGCTime": 0, "MajorGCCount": 0, "MajorGCTime": 0, "TotalGCTime": 0}, "isExcludedForStage": true}, "1": {"taskTime": 2411, "failedTasks": 0, "succeededTasks": 4, "killedTasks": 0, "inputBytes": 0, "inputRecords": 0, "outputBytes": 0, "outputRecords": 0, "shuffleRead": 0, "shuffleReadRecords": 0, "shuffleWrite": 585, "shuffleWriteRecords": 12, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "isBlacklistedForStage": false, "peakMemoryMetrics": {"JVMHeapMemory": 0, "JVMOffHeapMemory": 0, "OnHeapExecutionMemory": 0, "OffHeapExecutionMemory": 0, "OnHeapStorageMemory": 0, "OffHeapStorageMemory": 0, "OnHeapUnifiedMemory": 0, "OffHeapUnifiedMemory": 0, "DirectPoolMemory": 0, "MappedPoolMemory": 0, "ProcessTreeJVMVMemory": 0, "ProcessTreeJVMRSSMemory": 0, "ProcessTreePythonVMemory": 0, "ProcessTreePythonRSSMemory": 0, "ProcessTreeOtherVMemory": 0, "ProcessTreeOtherRSSMemory": 0, "MinorGCCount": 0, "MinorGCTime": 0, "MajorGCCount": 0, "MajorGCTime": 0, "TotalGCTime": 0}, "isExcludedForStage": false}, "2": {"taskTime": 2446, "failedTasks": 0, "succeededTasks": 5, "killedTasks": 0, "inputBytes": 0, "inputRecords": 0, "outputBytes": 0, "outputRecords": 0, "shuffleRead": 0, "shuffleReadRecords": 0, "shuffleWrite": 732, "shuffleWriteRecords": 15, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "isBlacklistedForStage": false, "peakMemoryMetrics": {"JVMHeapMemory": 0, "JVMOffHeapMemory": 0, "OnHeapExecutionMemory": 0, "OffHeapExecutionMemory": 0, "OnHeapStorageMemory": 0, "OffHeapStorageMemory": 0, "OnHeapUnifiedMemory": 0, "OffHeapUnifiedMemory": 0, "DirectPoolMemory": 0, "MappedPoolMemory": 0, "ProcessTreeJVMVMemory": 0, "ProcessTreeJVMRSSMemory": 0, "ProcessTreePythonVMemory": 0, "ProcessTreePythonRSSMemory": 0, "ProcessTreeOtherVMemory": 0, "ProcessTreeOtherRSSMemory": 0, "MinorGCCount": 0, "MinorGCTime": 0, "MajorGCCount": 0, "MajorGCTime": 0, "TotalGCTime": 0}, "isExcludedForStage": false}, "3": {"taskTime": 1774, "failedTasks": 0, "succeededTasks": 1, "killedTasks": 0, "inputBytes": 0, "inputRecords": 0, "outputBytes": 0, "outputRecords": 0, "shuffleRead": 0, "shuffleReadRecords": 0, "shuffleWrite": 144, "shuffleWriteRecords": 3, "memoryBytesSpilled": 0, "diskBytesSpilled": 0, "isBlacklistedForStage": true, "peakMemoryMetrics": {"JVMHeapMemory": 0, "JVMOffHeapMemory": 0, "OnHeapExecutionMemory": 0, "OffHeapExecutionMemory": 0, "OnHeapStorageMemory": 0, "OffHeapStorageMemory": 0, "OnHeapUnifiedMemory": 0, "OffHeapUnifiedMemory": 0, "DirectPoolMemory": 0, "MappedPoolMemory": 0, "ProcessTreeJVMVMemory": 0, "ProcessTreeJVMRSSMemory": 0, "ProcessTreePythonVMemory": 0, "ProcessTreePythonRSSMemory": 0, "ProcessTreeOtherVMemory": 0, "ProcessTreeOtherRSSMemory": 0, "MinorGCCount": 0, "MinorGCTime": 0, "MajorGCCount": 0, "MajorGCTime": 0, "TotalGCTime": 0}, "isExcludedForStage": true}}, "killedTasksSummary": {}, "resourceProfileId": 0, "peakExecutorMetrics": {"JVMHeapMemory": 0, "JVMOffHeapMemory": 0, "OnHeapExecutionMemory": 0, "OffHeapExecutionMemory": 0, "OnHeapStorageMemory": 0, "OffHeapStorageMemory": 0, "OnHeapUnifiedMemory": 0, "OffHeapUnifiedMemory": 0, "DirectPoolMemory": 0, "MappedPoolMemory": 0, "ProcessTreeJVMVMemory": 0, "ProcessTreeJVMRSSMemory": 0, "ProcessTreePythonVMemory": 0, "ProcessTreePythonRSSMemory": 0, "ProcessTreeOtherVMemory": 0, "ProcessTreeOtherRSSMemory": 0, "MinorGCCount": 0, "MinorGCTime": 0, "MajorGCCount": 0, "MajorGCTime": 0, "TotalGCTime": 0}}