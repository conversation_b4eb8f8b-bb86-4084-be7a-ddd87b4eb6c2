{"runtime": {"javaVersion": "1.8.0_92 (Oracle Corporation)", "javaHome": "/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre", "scalaVersion": "version 2.11.8"}, "sparkProperties": [["spark.app.id", "app-20161116163331-0000"], ["spark.app.name", "Spark shell"], ["spark.blacklist.application.maxFailedExecutorsPerNode", "2"], ["spark.blacklist.application.maxFailedTasksPerExecutor", "1"], ["spark.blacklist.enabled", "TRUE"], ["spark.blacklist.stage.maxFailedExecutorsPerNode", "3"], ["spark.blacklist.stage.maxFailedTasksPerExecutor", "3"], ["spark.blacklist.task.maxTaskAttemptsPerExecutor", "3"], ["spark.blacklist.task.maxTaskAttemptsPerNode", "3"], ["spark.blacklist.timeout", "1000000"], ["spark.driver.host", "************"], ["spark.driver.port", "51459"], ["spark.eventLog.compress", "FALSE"], ["spark.eventLog.dir", "/Users/<USER>/logs"], ["spark.eventLog.enabled", "TRUE"], ["spark.eventLog.overwrite", "TRUE"], ["spark.executor.id", "driver"], ["spark.home", "/Users/<USER>/IdeaProjects/spark"], ["spark.jars", ""], ["spark.master", "local-cluster[4,4,1024]"], ["spark.repl.class.outputDir", "/private/var/folders/l4/d46wlzj16593f3d812vk49tw0000gp/T/spark-1cbc97d0-7fe6-4c9f-8c2c-f6fe51ee3cf2/repl-39929169-ac4c-4c6d-b116-f648e4dd62ed"], ["spark.repl.class.uri", "spark://************:51459/classes"], ["spark.scheduler.mode", "FIFO"], ["spark.sql.catalogImplementation", "in-memory"], ["spark.submit.deployMode", "client"], ["spark.task.maxFailures", "4"]], "hadoopProperties": [["mapreduce.jobhistory.client.thread-count", "10"], ["mapreduce.jobtracker.address", "local"], ["yarn.resourcemanager.scheduler.monitor.policies", "org.apache.hadoop.yarn.server.resourcemanager.monitor.capacity.ProportionalCapacityPreemptionPolicy"]], "systemProperties": [["SPARK_SUBMIT", "true"], ["awt.toolkit", "sun.lwawt.macosx.LWCToolkit"], ["file.encoding", "UTF-8"], ["file.encoding.pkg", "sun.io"], ["file.separator", "/"], ["ftp.nonProxyHosts", "local|*.local|169.254/16|*.169.254/16"], ["gopherProxySet", "false"], ["http.nonProxyHosts", "local|*.local|169.254/16|*.169.254/16"], ["io.netty.maxDirectMemory", "0"], ["java.awt.graphicsenv", "sun.awt.CGraphicsEnvironment"], ["java.awt.printerjob", "sun.lwawt.macosx.CPrinterJob"], ["java.class.version", "52.0"], ["java.endorsed.dirs", "/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/lib/endorsed"], ["java.ext.dirs", "/Users/<USER>/Library/Java/Extensions:/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/lib/ext:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java"], ["java.home", "/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre"], ["java.io.tmpdir", "/var/folders/l4/d46wlzj16593f3d812vk49tw0000gp/T/"], ["java.library.path", "/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."], ["java.runtime.name", "Java(TM) SE Runtime Environment"], ["java.runtime.version", "1.8.0_92-b14"], ["java.specification.name", "Java Platform API Specification"], ["java.specification.vendor", "Oracle Corporation"], ["java.specification.version", "1.8"], ["java.vendor", "Oracle Corporation"], ["java.vendor.url", "http://java.oracle.com/"], ["java.vendor.url.bug", "http://bugreport.sun.com/bugreport/"], ["java.version", "1.8.0_92"], ["java.vm.info", "mixed mode"], ["java.vm.name", "Java HotSpot(TM) 64-Bit Server VM"], ["java.vm.specification.name", "Java Virtual Machine Specification"], ["java.vm.specification.vendor", "Oracle Corporation"], ["java.vm.specification.version", "1.8"], ["java.vm.vendor", "Oracle Corporation"], ["java.vm.version", "25.92-b14"], ["line.separator", "\n"], ["os.arch", "x86_64"], ["os.name", "Mac OS X"], ["os.version", "10.11.6"], ["path.separator", ":"], ["scala.usejavacp", "true"], ["socksNonProxyHosts", "local|*.local|169.254/16|*.169.254/16"], ["sun.arch.data.model", "64"], ["sun.boot.class.path", "/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/lib/resources.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/lib/rt.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/lib/sunrsasign.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/lib/jsse.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/lib/jce.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/lib/charsets.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/lib/jfr.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/classes"], ["sun.boot.library.path", "/Library/Java/JavaVirtualMachines/jdk1.8.0_92.jdk/Contents/Home/jre/lib"], ["sun.cpu.endian", "little"], ["sun.cpu.isalist", ""], ["sun.io.unicode.encoding", "UnicodeBig"], ["sun.java.command", "org.apache.spark.deploy.SparkSubmit --master local-cluster[4,4,1024] --conf spark.blacklist.enabled=TRUE --conf spark.blacklist.timeout=1000000 --conf spark.blacklist.application.maxFailedTasksPerExecutor=1 --conf spark.eventLog.overwrite=TRUE --conf spark.blacklist.task.maxTaskAttemptsPerNode=3 --conf spark.blacklist.stage.maxFailedTasksPerExecutor=3 --conf spark.blacklist.task.maxTaskAttemptsPerExecutor=3 --conf spark.eventLog.compress=FALSE --conf spark.blacklist.stage.maxFailedExecutorsPerNode=3 --conf spark.eventLog.enabled=TRUE --conf spark.eventLog.dir=/Users/<USER>/logs --conf spark.blacklist.application.maxFailedExecutorsPerNode=2 --conf spark.task.maxFailures=4 --class org.apache.spark.repl.Main --name Spark shell spark-shell -i /Users/<USER>/dev/jose-utils/blacklist/test-blacklist.scala"], ["sun.java.launcher", "SUN_STANDARD"], ["sun.jnu.encoding", "UTF-8"], ["sun.management.compiler", "HotSpot 64-Bit Tiered Compilers"], ["sun.nio.ch.bugLevel", ""], ["sun.os.patch.level", "unknown"], ["user.country", "US"], ["user.dir", "/Users/<USER>/IdeaProjects/spark"], ["user.home", "/Users/<USER>"], ["user.language", "en"], ["user.name", "jose"], ["user.timezone", "America/Chicago"]], "metricsProperties": [["*.sink.servlet.class", "org.apache.spark.metrics.sink.MetricsServlet"], ["*.sink.servlet.path", "/metrics/json"], ["applications.sink.servlet.path", "/metrics/applications/json"], ["master.sink.servlet.path", "/metrics/master/json"]], "classpathEntries": [["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/RoaringBitmap-0.5.11.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/antlr4-runtime-4.5.3.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/aopalliance-1.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/aopalliance-repackaged-2.4.0-b34.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/arpack_combined_all-0.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/avro-1.7.7.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/avro-ipc-1.7.7-tests.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/avro-ipc-1.7.7.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/avro-mapred-1.7.7-hadoop2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/breeze-macros_2.11-0.12.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/breeze_2.11-0.12.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/cglib-2.2.1-v20090111.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/chill-java-0.8.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/chill_2.11-0.8.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-beanutils-1.7.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-beanutils-core-1.8.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-cli-1.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-codec-1.10.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-collections-3.2.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-compiler-3.0.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-compress-1.4.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-configuration-1.6.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-crypto-1.0.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-digester-1.8.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-httpclient-3.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-io-2.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-lang-2.5.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-lang3-3.5.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-math-2.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-math3-3.4.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/commons-net-3.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/compress-lzf-1.0.3.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/core-1.1.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/curator-client-2.4.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/curator-framework-2.4.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/curator-recipes-2.4.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/guava-14.0.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/guice-3.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-annotations-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-auth-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-client-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-common-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-hdfs-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-mapreduce-client-app-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-mapreduce-client-common-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-mapreduce-client-core-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-mapreduce-client-jobclient-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-mapreduce-client-shuffle-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-yarn-api-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-yarn-client-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-yarn-common-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hadoop-yarn-server-common-2.2.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hk2-api-2.4.0-b34.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hk2-locator-2.4.0-b34.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/hk2-utils-2.4.0-b34.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/ivy-2.4.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jackson-annotations-2.6.5.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jackson-core-2.6.5.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jackson-core-asl-1.9.13.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jackson-databind-2.6.5.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jackson-mapper-asl-1.9.13.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jackson-module-paranamer-2.6.5.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jackson-module-scala_2.11-2.6.5.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/janino-3.0.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/javassist-3.18.1-GA.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/javax.annotation-api-1.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/javax.inject-1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/javax.inject-2.4.0-b34.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/javax.servlet-api-3.1.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/javax.ws.rs-api-2.0.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jcl-over-slf4j-1.7.16.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jersey-client-2.22.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jersey-common-2.22.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jersey-container-servlet-2.22.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jersey-container-servlet-core-2.22.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jersey-guava-2.22.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jersey-media-jaxb-2.22.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jersey-server-2.22.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jets3t-0.7.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-client-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-continuation-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-http-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-io-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-jndi-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-plus-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-proxy-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-security-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-server-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-servlet-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-servlets-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-util-6.1.26.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-util-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-webapp-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jetty-xml-9.2.16.v20160414.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jline-2.12.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/json4s-ast_2.11-3.2.11.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/json4s-core_2.11-3.2.11.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/json4s-jackson_2.11-3.2.11.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jsr305-1.3.9.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jtransforms-2.4.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/jul-to-slf4j-1.7.16.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/kryo-shaded-3.0.3.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/leveldbjni-all-1.8.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/log4j-1.2.17.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/lz4-1.3.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/metrics-core-3.1.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/metrics-graphite-3.1.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/metrics-json-3.1.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/metrics-jvm-3.1.2.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/minlog-1.3.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/netty-3.8.0.Final.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/netty-all-4.0.41.Final.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/objenesis-2.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/opencsv-2.3.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/oro-2.0.8.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/osgi-resource-locator-1.0.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/paranamer-2.6.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/parquet-column-1.8.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/parquet-common-1.8.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/parquet-encoding-1.8.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/parquet-format-2.3.0-incubating.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/parquet-hadoop-1.8.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/parquet-jackson-1.8.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/pmml-model-1.2.15.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/pmml-schema-1.2.15.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/protobuf-java-2.5.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/py4j-0.10.4.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/pyrolite-4.13.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/scala-compiler-2.11.8.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/scala-library-2.11.8.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/scala-parser-combinators_2.11-1.0.4.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/scala-reflect-2.11.8.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/scala-xml_2.11-1.0.4.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/scalap-2.11.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/scalatest_2.11-2.2.6.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/shapeless_2.11-2.0.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/slf4j-api-1.7.16.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/slf4j-log4j12-1.7.16.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/snappy-java-1.1.2.6.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-assembly_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-catalyst_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-core_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-graphx_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-launcher_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-mllib-local_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-mllib_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-network-common_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-network-shuffle_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-repl_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-sketch_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-sql_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-streaming_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-tags_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spark-unsafe_2.11-2.1.0-SNAPSHOT.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spire-macros_2.11-0.7.4.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/spire_2.11-0.7.4.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/stream-2.7.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/univocity-parsers-2.2.1.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/unused-1.0.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/validation-api-1.1.0.Final.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/xbean-asm5-shaded-4.4.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/xmlenc-0.52.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/xz-1.0.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/assembly/target/scala-2.11/jars/zookeeper-3.4.5.jar", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/common/network-common/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/common/network-shuffle/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/common/network-yarn/target/scala-2.11/classes", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/common/sketch/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/common/tags/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/common/unsafe/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/conf/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/core/target/jars/*", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/core/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/examples/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/graphx/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/launcher/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/mllib/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/repl/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/sql/catalyst/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/sql/core/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/sql/hive-thriftserver/target/scala-2.11/classes", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/sql/hive/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/streaming/target/scala-2.11/classes/", "System Classpath"], ["/Users/<USER>/IdeaProjects/spark/yarn/target/scala-2.11/classes", "System Classpath"]], "resourceProfiles": []}