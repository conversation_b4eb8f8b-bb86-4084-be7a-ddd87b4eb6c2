{"Event":"SparkListenerLogStart","Spark Version":"3.4.0-SNAPSHOT"}
{"Event":"SparkListenerResourceProfileAdded","Resource Profile Id":0,"Executor Resource Requests":{"cores":{"Resource Name":"cores","Amount":1,"Discovery Script":"","Vendor":""},"memory":{"Resource Name":"memory","Amount":1024,"Discovery Script":"","Vendor":""},"offHeap":{"Resource Name":"offHeap","Amount":0,"Discovery Script":"","Vendor":""}},"Task Resource Requests":{"cpus":{"Resource Name":"cpus","Amount":1.0}}}
{"Event":"SparkListenerBlockManagerAdded","Block Manager ID":{"Executor ID":"driver","Host":"***************","Port":43289},"Maximum Memory":384093388,"Timestamp":1656322544350,"Maximum Onheap Memory":384093388,"Maximum Offheap Memory":0}
{"Event":"SparkListenerEnvironmentUpdate","JVM Information":{"Java Home":"/usr/lib/jvm/java-8-openjdk-amd64/jre","Java Version":"1.8.0_312 (Private Build)","Scala Version":"version 2.12.16"},"Spark Properties":{"spark.executor.extraJavaOptions":"-Djava.net.preferIPv6Addresses=false -XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.driver.host":"***************","spark.serializer.objectStreamReset":"100","spark.eventLog.enabled":"true","spark.ui.port":"0","spark.driver.port":"45865","spark.rdd.compress":"True","spark.app.attempt.id":"2","spark.executorEnv.PYTHONPATH":"{{PWD}}/pyspark.zip<CPS>{{PWD}}/py4j-********-src.zip","spark.yarn.app.id":"application_1656321732247_0006","spark.app.name":"PythonPi","spark.scheduler.mode":"FIFO","spark.submit.pyFiles":"","spark.app.submitTime":"1656322521818","spark.app.startTime":"1656322543203","spark.executor.id":"driver","spark.yarn.app.container.log.dir":"/home/<USER>/Projects/hadoop/logs/userlogs/application_1656321732247_0006/container_1656321732247_0006_02_000001","spark.driver.extraJavaOptions":"-Djava.net.preferIPv6Addresses=false -XX:+IgnoreUnrecognizedVMOptions --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED","spark.submit.deployMode":"cluster","spark.master":"yarn","spark.ui.filters":"org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter","spark.eventLog.dir":"/home/<USER>/Projects/spark-events","spark.yarn.isPython":"true","spark.org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter.param.PROXY_HOSTS":"kuwii-computer","spark.org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter.param.PROXY_URI_BASES":"http://kuwii-computer:8088/proxy/application_1656321732247_0006","spark.app.id":"application_1656321732247_0006"},"Hadoop Properties":{"hadoop.service.shutdown.timeout":"30s","yarn.resourcemanager.amlauncher.thread-count":"50","yarn.sharedcache.enabled":"false","fs.s3a.connection.maximum":"96","yarn.nodemanager.numa-awareness.numactl.cmd":"/usr/bin/numactl","fs.viewfs.overload.scheme.target.o3fs.impl":"org.apache.hadoop.fs.ozone.OzoneFileSystem","fs.s3a.impl":"org.apache.hadoop.fs.s3a.S3AFileSystem","yarn.app.mapreduce.am.scheduler.heartbeat.interval-ms":"1000","yarn.timeline-service.timeline-client.number-of-async-entities-to-merge":"10","hadoop.security.kms.client.timeout":"60","hadoop.http.authentication.kerberos.principal":"HTTP/_HOST@LOCALHOST","mapreduce.jobhistory.loadedjob.tasks.max":"-1","yarn.resourcemanager.application-tag-based-placement.enable":"false","mapreduce.framework.name":"yarn","yarn.sharedcache.uploader.server.thread-count":"50","yarn.nodemanager.log-aggregation.roll-monitoring-interval-seconds.min":"3600","yarn.nodemanager.linux-container-executor.nonsecure-mode.user-pattern":"^[_.A-Za-z0-9][-@_.A-Za-z0-9]{0,255}?[$]?$","tfile.fs.output.buffer.size":"262144","yarn.app.mapreduce.am.job.task.listener.thread-count":"30","yarn.nodemanager.node-attributes.resync-interval-ms":"120000","yarn.nodemanager.container-log-monitor.interval-ms":"60000","hadoop.security.groups.cache.background.reload.threads":"3","yarn.resourcemanager.webapp.cross-origin.enabled":"false","fs.AbstractFileSystem.ftp.impl":"org.apache.hadoop.fs.ftp.FtpFs","fs.viewfs.overload.scheme.target.gs.impl":"com.google.cloud.hadoop.fs.gcs.GoogleHadoopFS","hadoop.registry.secure":"false","hadoop.shell.safely.delete.limit.num.files":"100","mapreduce.job.acl-view-job":" ","fs.s3a.s3guard.ddb.background.sleep":"25ms","fs.s3a.retry.limit":"7","mapreduce.jobhistory.loadedjobs.cache.size":"5","fs.s3a.s3guard.ddb.table.create":"false","fs.viewfs.overload.scheme.target.s3a.impl":"org.apache.hadoop.fs.s3a.S3AFileSystem","yarn.nodemanager.amrmproxy.enabled":"false","yarn.timeline-service.entity-group-fs-store.with-user-dir":"false","mapreduce.shuffle.pathcache.expire-after-access-minutes":"5","mapreduce.input.fileinputformat.split.minsize":"0","yarn.resourcemanager.container.liveness-monitor.interval-ms":"600000","yarn.resourcemanager.client.thread-count":"50","io.seqfile.compress.blocksize":"1000000","yarn.nodemanager.runtime.linux.docker.allowed-container-runtimes":"runc","fs.viewfs.overload.scheme.target.http.impl":"org.apache.hadoop.fs.http.HttpFileSystem","yarn.resourcemanager.nodemanagers.heartbeat-interval-slowdown-factor":"1.0","yarn.sharedcache.checksum.algo.impl":"org.apache.hadoop.yarn.sharedcache.ChecksumSHA256Impl","yarn.nodemanager.amrmproxy.interceptor-class.pipeline":"org.apache.hadoop.yarn.server.nodemanager.amrmproxy.DefaultRequestInterceptor","dfs.datanode.data.dir":"file:/home/<USER>/Projects/hadoop/data/dfs/data","dfs.replication":"1","yarn.timeline-service.entity-group-fs-store.leveldb-cache-read-cache-size":"10485760","mapreduce.reduce.shuffle.fetch.retry.interval-ms":"1000","mapreduce.task.profile.maps":"0-2","yarn.scheduler.include-port-in-node-name":"false","yarn.nodemanager.admin-env":"MALLOC_ARENA_MAX=$MALLOC_ARENA_MAX","yarn.resourcemanager.node-removal-untracked.timeout-ms":"60000","mapreduce.am.max-attempts":"2","hadoop.security.kms.client.failover.sleep.base.millis":"100","mapreduce.jobhistory.webapp.https.address":"0.0.0.0:19890","yarn.node-labels.fs-store.impl.class":"org.apache.hadoop.yarn.nodelabels.FileSystemNodeLabelsStore","yarn.nodemanager.collector-service.address":"${yarn.nodemanager.hostname}:8048","fs.trash.checkpoint.interval":"0","mapreduce.job.map.output.collector.class":"org.apache.hadoop.mapred.MapTask$MapOutputBuffer","yarn.resourcemanager.node-ip-cache.expiry-interval-secs":"-1","hadoop.http.authentication.signature.secret.file":"*********(redacted)","hadoop.jetty.logs.serve.aliases":"true","yarn.resourcemanager.placement-constraints.handler":"disabled","yarn.timeline-service.handler-thread-count":"10","yarn.resourcemanager.max-completed-applications":"1000","yarn.nodemanager.aux-services.manifest.enabled":"false","yarn.resourcemanager.placement-constraints.algorithm.class":"org.apache.hadoop.yarn.server.resourcemanager.scheduler.constraint.algorithm.DefaultPlacementAlgorithm","yarn.sharedcache.webapp.address":"0.0.0.0:8788","fs.s3a.select.input.csv.quote.escape.character":"\\\\","yarn.resourcemanager.delegation.token.renew-interval":"*********(redacted)","yarn.sharedcache.nm.uploader.replication.factor":"10","hadoop.security.groups.negative-cache.secs":"30","yarn.app.mapreduce.task.container.log.backups":"0","mapreduce.reduce.skip.proc-count.auto-incr":"true","fs.viewfs.overload.scheme.target.swift.impl":"org.apache.hadoop.fs.swift.snative.SwiftNativeFileSystem","hadoop.security.group.mapping.ldap.posix.attr.gid.name":"gidNumber","rpc.engine.org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolPB":"org.apache.hadoop.ipc.ProtobufRpcEngine2","ipc.client.fallback-to-simple-auth-allowed":"false","yarn.nodemanager.resource.memory.enforced":"true","yarn.resourcemanager.system-metrics-publisher.timeline-server-v1.enable-batch":"false","yarn.client.failover-proxy-provider":"org.apache.hadoop.yarn.client.ConfiguredRMFailoverProxyProvider","yarn.timeline-service.http-authentication.simple.anonymous.allowed":"true","ha.health-monitor.check-interval.ms":"1000","yarn.nodemanager.runtime.linux.runc.host-pid-namespace.allowed":"false","hadoop.metrics.jvm.use-thread-mxbean":"false","ipc.[port_number].faircallqueue.multiplexer.weights":"8,4,2,1","yarn.acl.reservation-enable":"false","yarn.resourcemanager.store.class":"org.apache.hadoop.yarn.server.resourcemanager.recovery.FileSystemRMStateStore","yarn.app.mapreduce.am.hard-kill-timeout-ms":"10000","fs.s3a.etag.checksum.enabled":"false","yarn.nodemanager.container-metrics.enable":"true","ha.health-monitor.rpc.connect.max.retries":"1","yarn.timeline-service.client.fd-clean-interval-secs":"60","yarn.resourcemanager.nodemanagers.heartbeat-interval-scaling-enable":"false","yarn.resourcemanager.nodemanagers.heartbeat-interval-ms":"1000","hadoop.common.configuration.version":"3.0.0","fs.s3a.s3guard.ddb.table.capacity.read":"0","yarn.nodemanager.remote-app-log-dir-suffix":"logs","yarn.nodemanager.container-log-monitor.dir-size-limit-bytes":"**********","yarn.nodemanager.windows-container.cpu-limit.enabled":"false","yarn.nodemanager.runtime.linux.docker.privileged-containers.allowed":"false","file.blocksize":"67108864","hadoop.http.idle_timeout.ms":"60000","hadoop.registry.zk.retry.ceiling.ms":"60000","mapreduce.reduce.env":"HADOOP_MAPRED_HOME=/home/<USER>/Projects/hadoop","yarn.scheduler.configuration.leveldb-store.path":"${hadoop.tmp.dir}/yarn/system/confstore","yarn.sharedcache.store.in-memory.initial-delay-mins":"10","mapreduce.jobhistory.principal":"jhs/<EMAIL>","mapreduce.map.skip.proc-count.auto-incr":"true","fs.s3a.committer.name":"file","mapreduce.task.profile.reduces":"0-2","hadoop.zk.num-retries":"1000","yarn.webapp.xfs-filter.enabled":"true","fs.viewfs.overload.scheme.target.hdfs.impl":"org.apache.hadoop.hdfs.DistributedFileSystem","seq.io.sort.mb":"100","yarn.scheduler.configuration.max.version":"100","yarn.timeline-service.webapp.https.address":"${yarn.timeline-service.hostname}:8190","yarn.resourcemanager.scheduler.address":"${yarn.resourcemanager.hostname}:8030","yarn.node-labels.enabled":"false","yarn.resourcemanager.webapp.ui-actions.enabled":"true","mapreduce.task.timeout":"600000","yarn.sharedcache.client-server.thread-count":"50","hadoop.security.groups.shell.command.timeout":"0s","hadoop.security.crypto.cipher.suite":"AES/CTR/NoPadding","yarn.nodemanager.elastic-memory-control.oom-handler":"org.apache.hadoop.yarn.server.nodemanager.containermanager.linux.resources.DefaultOOMHandler","yarn.resourcemanager.connect.max-wait.ms":"900000","fs.defaultFS":"hdfs://localhost:9000","yarn.minicluster.use-rpc":"false","yarn.app.mapreduce.am.env":"HADOOP_MAPRED_HOME=/home/<USER>/Projects/hadoop","ipc.[port_number].decay-scheduler.decay-factor":"0.5","fs.har.impl.disable.cache":"true","yarn.webapp.ui2.enable":"false","io.compression.codec.bzip2.library":"system-native","yarn.webapp.filter-invalid-xml-chars":"false","yarn.nodemanager.runtime.linux.runc.layer-mounts-interval-secs":"600","fs.s3a.select.input.csv.record.delimiter":"\\n","fs.s3a.change.detection.source":"etag","ipc.[port_number].backoff.enable":"false","yarn.nodemanager.distributed-scheduling.enabled":"false","mapreduce.shuffle.connection-keep-alive.timeout":"5","yarn.resourcemanager.webapp.https.address":"${yarn.resourcemanager.hostname}:8090","yarn.webapp.enable-rest-app-submissions":"true","mapreduce.jobhistory.address":"0.0.0.0:10020","yarn.resourcemanager.nm-tokens.master-key-rolling-interval-secs":"*********(redacted)","yarn.is.minicluster":"false","yarn.nodemanager.address":"${yarn.nodemanager.hostname}:0","fs.abfss.impl":"org.apache.hadoop.fs.azurebfs.SecureAzureBlobFileSystem","fs.AbstractFileSystem.s3a.impl":"org.apache.hadoop.fs.s3a.S3A","mapreduce.task.combine.progress.records":"10000","yarn.resourcemanager.epoch.range":"0","yarn.resourcemanager.am.max-attempts":"2","yarn.nodemanager.runtime.linux.runc.image-toplevel-dir":"/runc-root","yarn.nodemanager.linux-container-executor.cgroups.hierarchy":"/hadoop-yarn","fs.AbstractFileSystem.wasbs.impl":"org.apache.hadoop.fs.azure.Wasbs","yarn.timeline-service.entity-group-fs-store.cache-store-class":"org.apache.hadoop.yarn.server.timeline.MemoryTimelineStore","yarn.nodemanager.runtime.linux.runc.allowed-container-networks":"host,none,bridge","fs.ftp.transfer.mode":"BLOCK_TRANSFER_MODE","ipc.server.log.slow.rpc":"false","ipc.server.reuseaddr":"true","fs.ftp.timeout":"0","yarn.resourcemanager.node-labels.provider.fetch-interval-ms":"1800000","yarn.router.webapp.https.address":"0.0.0.0:8091","yarn.nodemanager.webapp.cross-origin.enabled":"false","fs.wasb.impl":"org.apache.hadoop.fs.azure.NativeAzureFileSystem","yarn.resourcemanager.auto-update.containers":"false","yarn.app.mapreduce.am.job.committer.cancel-timeout":"60000","yarn.scheduler.configuration.zk-store.parent-path":"/confstore","yarn.nodemanager.default-container-executor.log-dirs.permissions":"710","yarn.app.attempt.diagnostics.limit.kc":"64","fs.viewfs.overload.scheme.target.swebhdfs.impl":"org.apache.hadoop.hdfs.web.SWebHdfsFileSystem","yarn.client.failover-no-ha-proxy-provider":"org.apache.hadoop.yarn.client.DefaultNoHARMFailoverProxyProvider","fs.s3a.change.detection.mode":"server","ftp.bytes-per-checksum":"512","yarn.nodemanager.resource.memory-mb":"8192","fs.AbstractFileSystem.abfs.impl":"org.apache.hadoop.fs.azurebfs.Abfs","yarn.timeline-service.writer.flush-interval-seconds":"60","fs.s3a.fast.upload.active.blocks":"4","yarn.resourcemanager.submission-preprocessor.enabled":"false","hadoop.security.credential.clear-text-fallback":"true","yarn.nodemanager.collector-service.thread-count":"5","ipc.[port_number].scheduler.impl":"org.apache.hadoop.ipc.DefaultRpcScheduler","fs.azure.secure.mode":"false","mapreduce.jobhistory.joblist.cache.size":"20000","fs.ftp.host":"0.0.0.0","yarn.timeline-service.writer.async.queue.capacity":"100","yarn.resourcemanager.fs.state-store.num-retries":"0","yarn.resourcemanager.nodemanager-connect-retries":"10","yarn.nodemanager.log-aggregation.num-log-files-per-app":"30","hadoop.security.kms.client.encrypted.key.cache.low-watermark":"0.3f","fs.s3a.committer.magic.enabled":"true","yarn.timeline-service.client.max-retries":"30","dfs.ha.fencing.ssh.connect-timeout":"30000","yarn.log-aggregation-enable":"false","yarn.system-metrics-publisher.enabled":"false","mapreduce.reduce.markreset.buffer.percent":"0.0","fs.AbstractFileSystem.viewfs.impl":"org.apache.hadoop.fs.viewfs.ViewFs","yarn.resourcemanager.nodemanagers.heartbeat-interval-speedup-factor":"1.0","mapreduce.task.io.sort.factor":"10","yarn.nodemanager.amrmproxy.client.thread-count":"25","ha.failover-controller.new-active.rpc-timeout.ms":"60000","yarn.nodemanager.container-localizer.java.opts":"-Xmx256m","mapreduce.jobhistory.datestring.cache.size":"200000","mapreduce.job.acl-modify-job":" ","yarn.nodemanager.windows-container.memory-limit.enabled":"false","yarn.timeline-service.webapp.address":"${yarn.timeline-service.hostname}:8188","yarn.app.mapreduce.am.job.committer.commit-window":"10000","yarn.nodemanager.container-manager.thread-count":"20","yarn.minicluster.fixed.ports":"false","hadoop.tags.system":"YARN,HDFS,NAMENODE,DATANODE,REQUIRED,SECURITY,KERBEROS,PERFORMANCE,CLIENT\n      ,SERVER,DEBUG,DEPRECATED,COMMON,OPTIONAL","yarn.cluster.max-application-priority":"0","yarn.timeline-service.ttl-enable":"true","mapreduce.jobhistory.recovery.store.fs.uri":"${hadoop.tmp.dir}/mapred/history/recoverystore","hadoop.caller.context.signature.max.size":"40","ipc.[port_number].decay-scheduler.backoff.responsetime.enable":"false","yarn.client.load.resource-types.from-server":"false","ha.zookeeper.session-timeout.ms":"10000","ipc.[port_number].decay-scheduler.metrics.top.user.count":"10","tfile.io.chunk.size":"1048576","fs.s3a.s3guard.ddb.table.capacity.write":"0","yarn.dispatcher.print-events-info.threshold":"5000","mapreduce.job.speculative.slowtaskthreshold":"1.0","io.serializations":"org.apache.hadoop.io.serializer.WritableSerialization, org.apache.hadoop.io.serializer.avro.AvroSpecificSerialization, org.apache.hadoop.io.serializer.avro.AvroReflectSerialization","hadoop.security.kms.client.failover.sleep.max.millis":"2000","hadoop.security.group.mapping.ldap.directory.search.timeout":"10000","yarn.scheduler.configuration.store.max-logs":"1000","yarn.nodemanager.node-attributes.provider.fetch-interval-ms":"600000","fs.swift.impl":"org.apache.hadoop.fs.swift.snative.SwiftNativeFileSystem","yarn.nodemanager.local-cache.max-files-per-directory":"8192","hadoop.http.cross-origin.enabled":"false","hadoop.zk.acl":"world:anyone:rwcda","yarn.nodemanager.runtime.linux.runc.image-tag-to-manifest-plugin.num-manifests-to-cache":"10","mapreduce.map.sort.spill.percent":"0.80","yarn.timeline-service.entity-group-fs-store.scan-interval-seconds":"60","yarn.node-attribute.fs-store.impl.class":"org.apache.hadoop.yarn.server.resourcemanager.nodelabels.FileSystemNodeAttributeStore","fs.s3a.retry.interval":"500ms","yarn.timeline-service.client.best-effort":"false","yarn.resourcemanager.webapp.delegation-token-auth-filter.enabled":"*********(redacted)","hadoop.security.group.mapping.ldap.posix.attr.uid.name":"uidNumber","fs.AbstractFileSystem.swebhdfs.impl":"org.apache.hadoop.fs.SWebHdfs","yarn.nodemanager.elastic-memory-control.timeout-sec":"5","fs.s3a.select.enabled":"true","mapreduce.ifile.readahead":"true","yarn.timeline-service.leveldb-timeline-store.ttl-interval-ms":"300000","yarn.timeline-service.reader.webapp.address":"${yarn.timeline-service.webapp.address}","yarn.resourcemanager.placement-constraints.algorithm.pool-size":"1","yarn.timeline-service.hbase.coprocessor.jar.hdfs.location":"/hbase/coprocessor/hadoop-yarn-server-timelineservice.jar","hadoop.security.kms.client.encrypted.key.cache.num.refill.threads":"2","yarn.resourcemanager.scheduler.class":"org.apache.hadoop.yarn.server.resourcemanager.scheduler.capacity.CapacityScheduler","yarn.app.mapreduce.am.command-opts":"-Xmx1024m","fs.s3a.metadatastore.fail.on.write.error":"true","hadoop.http.sni.host.check.enabled":"false","mapreduce.cluster.local.dir":"${hadoop.tmp.dir}/mapred/local","io.mapfile.bloom.error.rate":"0.005","fs.client.resolve.topology.enabled":"false","yarn.nodemanager.runtime.linux.allowed-runtimes":"default","yarn.sharedcache.store.class":"org.apache.hadoop.yarn.server.sharedcachemanager.store.InMemorySCMStore","ha.failover-controller.graceful-fence.rpc-timeout.ms":"5000","ftp.replication":"3","fs.getspaceused.jitterMillis":"60000","hadoop.security.uid.cache.secs":"14400","mapreduce.job.maxtaskfailures.per.tracker":"3","fs.s3a.metadatastore.impl":"org.apache.hadoop.fs.s3a.s3guard.NullMetadataStore","io.skip.checksum.errors":"false","yarn.app.mapreduce.client-am.ipc.max-retries-on-timeouts":"3","yarn.timeline-service.webapp.xfs-filter.xframe-options":"SAMEORIGIN","fs.s3a.connection.timeout":"200000","yarn.app.mapreduce.am.webapp.https.enabled":"false","mapreduce.job.max.split.locations":"15","yarn.resourcemanager.nm-container-queuing.max-queue-length":"15","yarn.resourcemanager.delegation-token.always-cancel":"*********(redacted)","hadoop.registry.zk.session.timeout.ms":"60000","yarn.federation.cache-ttl.secs":"300","mapreduce.jvm.system-properties-to-log":"os.name,os.version,java.home,java.runtime.version,java.vendor,java.version,java.vm.name,java.class.path,java.io.tmpdir,user.dir,user.name","yarn.resourcemanager.opportunistic-container-allocation.nodes-used":"10","yarn.timeline-service.entity-group-fs-store.active-dir":"/tmp/entity-file-history/active","mapreduce.shuffle.transfer.buffer.size":"131072","yarn.timeline-service.client.retry-interval-ms":"1000","yarn.timeline-service.flowname.max-size":"0","yarn.http.policy":"HTTP_ONLY","fs.s3a.socket.send.buffer":"8192","fs.AbstractFileSystem.abfss.impl":"org.apache.hadoop.fs.azurebfs.Abfss","yarn.sharedcache.uploader.server.address":"0.0.0.0:8046","yarn.resourcemanager.delegation-token.max-conf-size-bytes":"*********(redacted)","hadoop.http.authentication.token.validity":"*********(redacted)","mapreduce.shuffle.max.connections":"0","yarn.minicluster.yarn.nodemanager.resource.memory-mb":"4096","mapreduce.job.emit-timeline-data":"false","yarn.nodemanager.resource.system-reserved-memory-mb":"-1","hadoop.kerberos.min.seconds.before.relogin":"60","mapreduce.jobhistory.move.thread-count":"3","yarn.resourcemanager.admin.client.thread-count":"1","yarn.dispatcher.drain-events.timeout":"300000","ipc.[port_number].decay-scheduler.backoff.responsetime.thresholds":"10s,20s,30s,40s","fs.s3a.buffer.dir":"${hadoop.tmp.dir}/s3a","hadoop.ssl.enabled.protocols":"TLSv1.2","mapreduce.jobhistory.admin.address":"0.0.0.0:10033","yarn.log-aggregation-status.time-out.ms":"600000","fs.s3a.accesspoint.required":"false","mapreduce.shuffle.port":"13562","yarn.resourcemanager.max-log-aggregation-diagnostics-in-memory":"10","yarn.nodemanager.health-checker.interval-ms":"600000","yarn.resourcemanager.proxy.connection.timeout":"60000","yarn.router.clientrm.interceptor-class.pipeline":"org.apache.hadoop.yarn.server.router.clientrm.DefaultClientRequestInterceptor","yarn.resourcemanager.zk-appid-node.split-index":"0","ftp.blocksize":"67108864","yarn.nodemanager.runtime.linux.sandbox-mode.local-dirs.permissions":"read","yarn.router.rmadmin.interceptor-class.pipeline":"org.apache.hadoop.yarn.server.router.rmadmin.DefaultRMAdminRequestInterceptor","yarn.nodemanager.log-container-debug-info.enabled":"true","yarn.resourcemanager.activities-manager.app-activities.max-queue-length":"100","yarn.resourcemanager.application-https.policy":"NONE","yarn.client.max-cached-nodemanagers-proxies":"0","yarn.nodemanager.linux-container-executor.cgroups.delete-delay-ms":"20","yarn.nodemanager.delete.debug-delay-sec":"0","yarn.nodemanager.pmem-check-enabled":"true","yarn.nodemanager.disk-health-checker.max-disk-utilization-per-disk-percentage":"90.0","mapreduce.app-submission.cross-platform":"false","yarn.resourcemanager.work-preserving-recovery.scheduling-wait-ms":"10000","yarn.nodemanager.container-retry-minimum-interval-ms":"1000","hadoop.security.groups.cache.secs":"300","yarn.federation.enabled":"false","yarn.workflow-id.tag-prefix":"workflowid:","fs.azure.local.sas.key.mode":"false","ipc.maximum.data.length":"134217728","fs.s3a.endpoint":"s3.amazonaws.com","mapreduce.shuffle.max.threads":"0","yarn.router.pipeline.cache-max-size":"25","yarn.resourcemanager.nm-container-queuing.load-comparator":"QUEUE_LENGTH","yarn.resourcemanager.resource-tracker.nm.ip-hostname-check":"false","hadoop.security.authorization":"false","mapreduce.job.complete.cancel.delegation.tokens":"*********(redacted)","fs.s3a.paging.maximum":"5000","nfs.exports.allowed.hosts":"* rw","yarn.nodemanager.amrmproxy.ha.enable":"false","fs.AbstractFileSystem.gs.impl":"com.google.cloud.hadoop.fs.gcs.GoogleHadoopFS","mapreduce.jobhistory.http.policy":"HTTP_ONLY","yarn.sharedcache.store.in-memory.check-period-mins":"720","hadoop.security.group.mapping.ldap.ssl":"false","fs.s3a.downgrade.syncable.exceptions":"true","yarn.client.application-client-protocol.poll-interval-ms":"200","yarn.scheduler.configuration.leveldb-store.compaction-interval-secs":"86400","yarn.timeline-service.writer.class":"org.apache.hadoop.yarn.server.timelineservice.storage.HBaseTimelineWriterImpl","ha.zookeeper.parent-znode":"/hadoop-ha","yarn.resourcemanager.submission-preprocessor.file-refresh-interval-ms":"60000","yarn.nodemanager.log-aggregation.policy.class":"org.apache.hadoop.yarn.server.nodemanager.containermanager.logaggregation.AllContainerLogAggregationPolicy","mapreduce.reduce.shuffle.merge.percent":"0.66","hadoop.security.group.mapping.ldap.search.filter.group":"(objectClass=group)","yarn.resourcemanager.placement-constraints.scheduler.pool-size":"1","yarn.resourcemanager.activities-manager.cleanup-interval-ms":"5000","yarn.nodemanager.resourcemanager.minimum.version":"NONE","mapreduce.job.speculative.speculative-cap-running-tasks":"0.1","yarn.admin.acl":"*","ipc.[port_number].identity-provider.impl":"org.apache.hadoop.ipc.UserIdentityProvider","yarn.nodemanager.recovery.supervised":"false","yarn.sharedcache.admin.thread-count":"1","yarn.resourcemanager.ha.automatic-failover.enabled":"true","yarn.nodemanager.container-log-monitor.total-size-limit-bytes":"**********0","mapreduce.reduce.skip.maxgroups":"0","mapreduce.reduce.shuffle.connect.timeout":"180000","yarn.nodemanager.health-checker.scripts":"script","yarn.resourcemanager.address":"${yarn.resourcemanager.hostname}:8032","ipc.client.ping":"true","mapreduce.task.local-fs.write-limit.bytes":"-1","fs.adl.oauth2.access.token.provider.type":"*********(redacted)","mapreduce.shuffle.ssl.file.buffer.size":"65536","yarn.resourcemanager.ha.automatic-failover.embedded":"true","yarn.nodemanager.resource-plugins.gpu.docker-plugin":"nvidia-docker-v1","fs.s3a.s3guard.consistency.retry.interval":"2s","fs.s3a.multipart.purge":"false","yarn.scheduler.configuration.store.class":"file","yarn.resourcemanager.nm-container-queuing.queue-limit-stdev":"1.0f","mapreduce.job.end-notification.max.attempts":"5","mapreduce.output.fileoutputformat.compress.codec":"org.apache.hadoop.io.compress.DefaultCodec","yarn.nodemanager.container-monitor.procfs-tree.smaps-based-rss.enabled":"false","ipc.client.bind.wildcard.addr":"false","yarn.resourcemanager.webapp.rest-csrf.enabled":"false","ha.health-monitor.connect-retry-interval.ms":"1000","yarn.nodemanager.keytab":"/etc/krb5.keytab","mapreduce.jobhistory.keytab":"/etc/security/keytab/jhs.service.keytab","fs.s3a.threads.max":"64","yarn.nodemanager.runtime.linux.docker.image-update":"false","mapreduce.reduce.shuffle.input.buffer.percent":"0.70","fs.viewfs.overload.scheme.target.abfss.impl":"org.apache.hadoop.fs.azurebfs.SecureAzureBlobFileSystem","yarn.dispatcher.cpu-monitor.samples-per-min":"60","hadoop.security.token.service.use_ip":"*********(redacted)","yarn.nodemanager.runtime.linux.docker.allowed-container-networks":"host,none,bridge","yarn.nodemanager.node-labels.resync-interval-ms":"120000","hadoop.tmp.dir":"file:/home/<USER>/Projects/hadoop/data","mapreduce.job.maps":"2","mapreduce.jobhistory.webapp.rest-csrf.custom-header":"X-XSRF-Header","mapreduce.job.end-notification.max.retry.interval":"5000","yarn.log-aggregation.retain-check-interval-seconds":"-1","yarn.resourcemanager.resource-tracker.client.thread-count":"50","yarn.nodemanager.containers-launcher.class":"org.apache.hadoop.yarn.server.nodemanager.containermanager.launcher.ContainersLauncher","yarn.rm.system-metrics-publisher.emit-container-events":"false","yarn.timeline-service.leveldb-timeline-store.start-time-read-cache-size":"10000","yarn.resourcemanager.ha.automatic-failover.zk-base-path":"/yarn-leader-election","io.seqfile.local.dir":"${hadoop.tmp.dir}/io/local","fs.s3a.s3guard.ddb.throttle.retry.interval":"100ms","fs.AbstractFileSystem.wasb.impl":"org.apache.hadoop.fs.azure.Wasb","mapreduce.client.submit.file.replication":"10","mapreduce.jobhistory.minicluster.fixed.ports":"false","fs.s3a.multipart.threshold":"128M","yarn.resourcemanager.webapp.xfs-filter.xframe-options":"SAMEORIGIN","mapreduce.jobhistory.done-dir":"${yarn.app.mapreduce.am.staging-dir}/history/done","ipc.server.purge.interval":"15","dfs.namenode.name.dir":"file:/home/<USER>/Projects/hadoop/data/dfs/name","ipc.client.idlethreshold":"4000","yarn.nodemanager.linux-container-executor.cgroups.strict-resource-usage":"false","mapreduce.reduce.input.buffer.percent":"0.0","yarn.nodemanager.runtime.linux.docker.userremapping-gid-threshold":"1","yarn.nodemanager.webapp.rest-csrf.enabled":"false","fs.ftp.host.port":"21","ipc.ping.interval":"60000","yarn.resourcemanager.history-writer.multi-threaded-dispatcher.pool-size":"10","yarn.resourcemanager.admin.address":"${yarn.resourcemanager.hostname}:8033","file.client-write-packet-size":"65536","ipc.client.kill.max":"10","mapreduce.reduce.speculative":"true","hadoop.security.key.default.bitlength":"128","mapreduce.job.reducer.unconditional-preempt.delay.sec":"300","yarn.nodemanager.disk-health-checker.interval-ms":"120000","yarn.nodemanager.log.deletion-threads-count":"4","fs.s3a.committer.abort.pending.uploads":"true","yarn.webapp.filter-entity-list-by-user":"false","yarn.resourcemanager.activities-manager.app-activities.ttl-ms":"600000","ipc.client.connection.maxidletime":"10000","mapreduce.task.io.sort.mb":"100","yarn.nodemanager.localizer.client.thread-count":"5","io.erasurecode.codec.rs.rawcoders":"rs_native,rs_java","io.erasurecode.codec.rs-legacy.rawcoders":"rs-legacy_java","yarn.sharedcache.admin.address":"0.0.0.0:8047","yarn.resourcemanager.placement-constraints.algorithm.iterator":"SERIAL","yarn.nodemanager.localizer.cache.cleanup.interval-ms":"600000","hadoop.security.crypto.codec.classes.aes.ctr.nopadding":"org.apache.hadoop.crypto.OpensslAesCtrCryptoCodec, org.apache.hadoop.crypto.JceAesCtrCryptoCodec","mapreduce.job.cache.limit.max-resources-mb":"0","fs.s3a.connection.ssl.enabled":"true","yarn.nodemanager.process-kill-wait.ms":"5000","mapreduce.job.hdfs-servers":"${fs.defaultFS}","yarn.app.mapreduce.am.webapp.https.client.auth":"false","hadoop.workaround.non.threadsafe.getpwuid":"true","fs.df.interval":"60000","ipc.[port_number].decay-scheduler.thresholds":"13,25,50","fs.s3a.multiobjectdelete.enable":"true","yarn.sharedcache.cleaner.resource-sleep-ms":"0","yarn.nodemanager.disk-health-checker.min-healthy-disks":"0.25","hadoop.shell.missing.defaultFs.warning":"false","io.file.buffer.size":"65536","fs.viewfs.overload.scheme.target.wasb.impl":"org.apache.hadoop.fs.azure.NativeAzureFileSystem","hadoop.security.group.mapping.ldap.search.attr.member":"member","hadoop.security.random.device.file.path":"/dev/urandom","hadoop.security.sensitive-config-keys":"*********(redacted)","fs.s3a.s3guard.ddb.max.retries":"9","fs.viewfs.overload.scheme.target.file.impl":"org.apache.hadoop.fs.LocalFileSystem","hadoop.rpc.socket.factory.class.default":"org.apache.hadoop.net.StandardSocketFactory","yarn.intermediate-data-encryption.enable":"false","yarn.resourcemanager.connect.retry-interval.ms":"30000","yarn.nodemanager.container.stderr.pattern":"{*stderr*,*STDERR*}","yarn.scheduler.minimum-allocation-mb":"512","yarn.app.mapreduce.am.staging-dir":"/tmp/hadoop-yarn/staging","mapreduce.reduce.shuffle.read.timeout":"180000","hadoop.http.cross-origin.max-age":"1800","io.erasurecode.codec.xor.rawcoders":"xor_native,xor_java","fs.s3a.s3guard.consistency.retry.limit":"7","fs.s3a.connection.establish.timeout":"5000","mapreduce.job.running.map.limit":"0","yarn.minicluster.control-resource-monitoring":"false","hadoop.ssl.require.client.cert":"false","hadoop.kerberos.kinit.command":"kinit","yarn.federation.state-store.class":"org.apache.hadoop.yarn.server.federation.store.impl.MemoryFederationStateStore","mapreduce.reduce.log.level":"INFO","hadoop.security.dns.log-slow-lookups.threshold.ms":"1000","mapreduce.job.ubertask.enable":"false","adl.http.timeout":"-1","yarn.resourcemanager.placement-constraints.retry-attempts":"3","hadoop.caller.context.enabled":"false","hadoop.security.group.mapping.ldap.num.attempts":"3","yarn.nodemanager.vmem-pmem-ratio":"2.1","hadoop.rpc.protection":"authentication","ha.health-monitor.rpc-timeout.ms":"45000","yarn.nodemanager.remote-app-log-dir":"/tmp/logs","hadoop.zk.timeout-ms":"10000","fs.s3a.s3guard.cli.prune.age":"86400000","yarn.nodemanager.resource.pcores-vcores-multiplier":"1.0","yarn.nodemanager.runtime.linux.sandbox-mode":"disabled","yarn.app.mapreduce.am.containerlauncher.threadpool-initial-size":"10","fs.viewfs.overload.scheme.target.webhdfs.impl":"org.apache.hadoop.hdfs.web.WebHdfsFileSystem","fs.s3a.committer.threads":"8","hadoop.zk.retry-interval-ms":"1000","hadoop.security.crypto.buffer.size":"8192","yarn.nodemanager.node-labels.provider.fetch-interval-ms":"600000","mapreduce.jobhistory.recovery.store.leveldb.path":"${hadoop.tmp.dir}/mapred/history/recoverystore","yarn.client.failover-retries-on-socket-timeouts":"0","fs.s3a.ssl.channel.mode":"default_jsse","yarn.nodemanager.resource.memory.enabled":"false","fs.azure.authorization.caching.enable":"true","hadoop.security.instrumentation.requires.admin":"false","yarn.nodemanager.delete.thread-count":"4","mapreduce.job.finish-when-all-reducers-done":"true","hadoop.registry.jaas.context":"Client","yarn.timeline-service.leveldb-timeline-store.path":"${hadoop.tmp.dir}/yarn/timeline","io.map.index.interval":"128","yarn.resourcemanager.nm-container-queuing.max-queue-wait-time-ms":"100","fs.abfs.impl":"org.apache.hadoop.fs.azurebfs.AzureBlobFileSystem","mapreduce.job.counters.max":"120","mapreduce.jobhistory.webapp.rest-csrf.enabled":"false","yarn.timeline-service.store-class":"org.apache.hadoop.yarn.server.timeline.LeveldbTimelineStore","mapreduce.jobhistory.move.interval-ms":"180000","fs.s3a.change.detection.version.required":"true","yarn.nodemanager.localizer.fetch.thread-count":"4","yarn.resourcemanager.scheduler.client.thread-count":"50","hadoop.ssl.hostname.verifier":"DEFAULT","yarn.timeline-service.leveldb-state-store.path":"${hadoop.tmp.dir}/yarn/timeline","mapreduce.job.classloader":"false","mapreduce.task.profile.map.params":"${mapreduce.task.profile.params}","ipc.client.connect.timeout":"20000","hadoop.security.auth_to_local.mechanism":"hadoop","yarn.timeline-service.app-collector.linger-period.ms":"60000","yarn.nm.liveness-monitor.expiry-interval-ms":"600000","yarn.resourcemanager.reservation-system.planfollower.time-step":"1000","yarn.resourcemanager.proxy.timeout.enabled":"true","yarn.resourcemanager.activities-manager.scheduler-activities.ttl-ms":"600000","yarn.nodemanager.runtime.linux.docker.enable-userremapping.allowed":"true","yarn.webapp.api-service.enable":"false","yarn.nodemanager.recovery.enabled":"false","mapreduce.job.end-notification.retry.interval":"1000","fs.du.interval":"600000","fs.ftp.impl":"org.apache.hadoop.fs.ftp.FTPFileSystem","yarn.nodemanager.container.stderr.tail.bytes":"4096","yarn.nodemanager.disk-health-checker.disk-free-space-threshold.enabled":"true","hadoop.security.group.mapping.ldap.read.timeout.ms":"60000","mapreduce.map.env":"HADOOP_MAPRED_HOME=/home/<USER>/Projects/hadoop","hadoop.security.groups.cache.warn.after.ms":"5000","file.bytes-per-checksum":"512","mapreduce.outputcommitter.factory.scheme.s3a":"org.apache.hadoop.fs.s3a.commit.S3ACommitterFactory","hadoop.security.groups.cache.background.reload":"false","yarn.nodemanager.container-monitor.enabled":"true","yarn.nodemanager.elastic-memory-control.enabled":"false","net.topology.script.number.args":"100","mapreduce.task.merge.progress.records":"10000","yarn.nodemanager.localizer.address":"${yarn.nodemanager.hostname}:8040","yarn.timeline-service.keytab":"/etc/krb5.keytab","mapreduce.reduce.shuffle.fetch.retry.timeout-ms":"30000","yarn.resourcemanager.rm.container-allocation.expiry-interval-ms":"600000","yarn.nodemanager.container-executor.exit-code-file.timeout-ms":"2000","mapreduce.fileoutputcommitter.algorithm.version":"1","yarn.resourcemanager.work-preserving-recovery.enabled":"true","mapreduce.map.skip.maxrecords":"0","yarn.sharedcache.root-dir":"/sharedcache","fs.s3a.retry.throttle.limit":"20","hadoop.http.authentication.type":"simple","fs.viewfs.overload.scheme.target.oss.impl":"org.apache.hadoop.fs.aliyun.oss.AliyunOSSFileSystem","mapreduce.job.cache.limit.max-resources":"0","mapreduce.task.userlog.limit.kb":"0","ipc.[port_number].weighted-cost.handler":"1","yarn.resourcemanager.scheduler.monitor.enable":"false","ipc.client.connect.max.retries":"10","hadoop.registry.zk.retry.times":"5","yarn.nodemanager.resource-monitor.interval-ms":"3000","yarn.nodemanager.resource-plugins.gpu.allowed-gpu-devices":"auto","mapreduce.job.sharedcache.mode":"disabled","yarn.nodemanager.webapp.rest-csrf.custom-header":"X-XSRF-Header","mapreduce.shuffle.listen.queue.size":"128","yarn.scheduler.configuration.mutation.acl-policy.class":"org.apache.hadoop.yarn.server.resourcemanager.scheduler.DefaultConfigurationMutationACLPolicy","mapreduce.map.cpu.vcores":"1","yarn.log-aggregation.file-formats":"TFile","yarn.timeline-service.client.fd-retain-secs":"300","fs.s3a.select.output.csv.field.delimiter":",","yarn.nodemanager.health-checker.timeout-ms":"1200000","hadoop.user.group.static.mapping.overrides":"dr.who=;","fs.azure.sas.expiry.period":"90d","fs.s3a.select.output.csv.record.delimiter":"\\n","mapreduce.jobhistory.recovery.store.class":"org.apache.hadoop.mapreduce.v2.hs.HistoryServerFileSystemStateStoreService","fs.viewfs.overload.scheme.target.https.impl":"org.apache.hadoop.fs.http.HttpsFileSystem","fs.s3a.s3guard.ddb.table.sse.enabled":"false","yarn.resourcemanager.fail-fast":"${yarn.fail-fast}","yarn.resourcemanager.proxy-user-privileges.enabled":"false","yarn.router.webapp.interceptor-class.pipeline":"org.apache.hadoop.yarn.server.router.webapp.DefaultRequestInterceptorREST","yarn.nodemanager.resource.memory.cgroups.soft-limit-percentage":"90.0","mapreduce.job.reducer.preempt.delay.sec":"0","hadoop.util.hash.type":"murmur","yarn.nodemanager.disk-validator":"basic","yarn.app.mapreduce.client.job.max-retries":"3","fs.viewfs.overload.scheme.target.ftp.impl":"org.apache.hadoop.fs.ftp.FTPFileSystem","mapreduce.reduce.shuffle.retry-delay.max.ms":"60000","hadoop.security.group.mapping.ldap.connection.timeout.ms":"60000","mapreduce.task.profile.params":"-agentlib:hprof=cpu=samples,heap=sites,force=n,thread=y,verbose=n,file=%s","yarn.app.mapreduce.shuffle.log.backups":"0","yarn.nodemanager.container-diagnostics-maximum-size":"10000","hadoop.registry.zk.retry.interval.ms":"1000","yarn.nodemanager.linux-container-executor.cgroups.delete-timeout-ms":"1000","fs.AbstractFileSystem.file.impl":"org.apache.hadoop.fs.local.LocalFs","yarn.nodemanager.log-aggregation.roll-monitoring-interval-seconds":"-1","mapreduce.jobhistory.cleaner.interval-ms":"86400000","hadoop.registry.zk.quorum":"localhost:2181","yarn.nodemanager.runtime.linux.runc.allowed-container-runtimes":"runc","mapreduce.output.fileoutputformat.compress":"false","yarn.resourcemanager.am-rm-tokens.master-key-rolling-interval-secs":"*********(redacted)","fs.s3a.assumed.role.session.duration":"30m","hadoop.security.group.mapping.ldap.conversion.rule":"none","hadoop.ssl.server.conf":"ssl-server.xml","fs.s3a.retry.throttle.interval":"100ms","seq.io.sort.factor":"100","fs.viewfs.overload.scheme.target.ofs.impl":"org.apache.hadoop.fs.ozone.RootedOzoneFileSystem","yarn.sharedcache.cleaner.initial-delay-mins":"10","mapreduce.client.completion.pollinterval":"5000","hadoop.ssl.keystores.factory.class":"org.apache.hadoop.security.ssl.FileBasedKeyStoresFactory","yarn.app.mapreduce.am.resource.cpu-vcores":"1","yarn.timeline-service.enabled":"false","yarn.nodemanager.runtime.linux.docker.capabilities":"CHOWN,DAC_OVERRIDE,FSETID,FOWNER,MKNOD,NET_RAW,SETGID,SETUID,SETFCAP,SETPCAP,NET_BIND_SERVICE,SYS_CHROOT,KILL,AUDIT_WRITE","yarn.acl.enable":"false","yarn.timeline-service.entity-group-fs-store.done-dir":"/tmp/entity-file-history/done/","hadoop.security.group.mapping.ldap.num.attempts.before.failover":"3","mapreduce.task.profile":"false","hadoop.prometheus.endpoint.enabled":"false","yarn.resourcemanager.fs.state-store.uri":"${hadoop.tmp.dir}/yarn/system/rmstore","mapreduce.jobhistory.always-scan-user-dir":"false","fs.s3a.metadatastore.metadata.ttl":"15m","yarn.nodemanager.opportunistic-containers-use-pause-for-preemption":"false","yarn.nodemanager.linux-container-executor.nonsecure-mode.local-user":"nobody","yarn.timeline-service.reader.class":"org.apache.hadoop.yarn.server.timelineservice.storage.HBaseTimelineReaderImpl","yarn.resourcemanager.configuration.provider-class":"org.apache.hadoop.yarn.LocalConfigurationProvider","yarn.nodemanager.runtime.linux.docker.userremapping-uid-threshold":"1","yarn.resourcemanager.configuration.file-system-based-store":"/yarn/conf","mapreduce.job.cache.limit.max-single-resource-mb":"0","yarn.nodemanager.runtime.linux.docker.stop.grace-period":"10","yarn.resourcemanager.resource-profiles.source-file":"resource-profiles.json","mapreduce.job.dfs.storage.capacity.kill-limit-exceed":"false","yarn.nodemanager.resource.percentage-physical-cpu-limit":"100","mapreduce.jobhistory.client.thread-count":"10","tfile.fs.input.buffer.size":"262144","mapreduce.client.progressmonitor.pollinterval":"1000","yarn.nodemanager.log-dirs":"${yarn.log.dir}/userlogs","yarn.resourcemanager.opportunistic.max.container-allocation.per.am.heartbeat":"-1","fs.automatic.close":"true","yarn.resourcemanager.delegation-token-renewer.thread-retry-interval":"*********(redacted)","fs.s3a.select.input.csv.quote.character":"\"","yarn.nodemanager.hostname":"127.0.0.1","ipc.[port_number].cost-provider.impl":"org.apache.hadoop.ipc.DefaultCostProvider","yarn.nodemanager.runtime.linux.runc.manifest-to-resources-plugin":"org.apache.hadoop.yarn.server.nodemanager.containermanager.linux.runtime.runc.HdfsManifestToResourcesPlugin","yarn.nodemanager.remote-app-log-dir-include-older":"true","yarn.nodemanager.resource.memory.cgroups.swappiness":"0","ftp.stream-buffer-size":"4096","yarn.fail-fast":"false","yarn.nodemanager.runtime.linux.runc.layer-mounts-to-keep":"100","yarn.timeline-service.app-aggregation-interval-secs":"15","hadoop.security.group.mapping.ldap.search.filter.user":"(&(objectClass=user)(sAMAccountName={0}))","ipc.[port_number].weighted-cost.lockshared":"10","yarn.nodemanager.container-localizer.log.level":"INFO","yarn.timeline-service.address":"${yarn.timeline-service.hostname}:10200","mapreduce.job.ubertask.maxmaps":"9","fs.s3a.threads.keepalivetime":"60","mapreduce.jobhistory.webapp.rest-csrf.methods-to-ignore":"GET,OPTIONS,HEAD","mapreduce.task.files.preserve.failedtasks":"false","yarn.app.mapreduce.client.job.retry-interval":"2000","ha.failover-controller.graceful-fence.connection.retries":"1","fs.s3a.select.output.csv.quote.escape.character":"\\\\","yarn.resourcemanager.delegation.token.max-lifetime":"*********(redacted)","hadoop.kerberos.keytab.login.autorenewal.enabled":"false","yarn.timeline-service.client.drain-entities.timeout.ms":"2000","yarn.nodemanager.resource-plugins.fpga.vendor-plugin.class":"org.apache.hadoop.yarn.server.nodemanager.containermanager.resourceplugin.fpga.IntelFpgaOpenclPlugin","yarn.resourcemanager.nodemanagers.heartbeat-interval-min-ms":"1000","yarn.timeline-service.entity-group-fs-store.summary-store":"org.apache.hadoop.yarn.server.timeline.LeveldbTimelineStore","mapreduce.reduce.cpu.vcores":"1","mapreduce.job.encrypted-intermediate-data.buffer.kb":"128","fs.client.resolve.remote.symlinks":"true","yarn.nodemanager.webapp.https.address":"0.0.0.0:8044","hadoop.http.cross-origin.allowed-origins":"*","mapreduce.job.encrypted-intermediate-data":"false","yarn.nodemanager.disk-health-checker.disk-utilization-threshold.enabled":"true","fs.s3a.executor.capacity":"16","yarn.timeline-service.entity-group-fs-store.retain-seconds":"604800","yarn.resourcemanager.metrics.runtime.buckets":"60,300,1440","yarn.timeline-service.generic-application-history.max-applications":"10000","yarn.nodemanager.local-dirs":"${hadoop.tmp.dir}/nm-local-dir","mapreduce.shuffle.connection-keep-alive.enable":"false","yarn.node-labels.configuration-type":"centralized","fs.s3a.path.style.access":"false","yarn.nodemanager.aux-services.mapreduce_shuffle.class":"org.apache.hadoop.mapred.ShuffleHandler","yarn.sharedcache.store.in-memory.staleness-period-mins":"10080","fs.adl.impl":"org.apache.hadoop.fs.adl.AdlFileSystem","yarn.resourcemanager.application.max-tags":"10","hadoop.domainname.resolver.impl":"org.apache.hadoop.net.DNSDomainNameResolver","yarn.resourcemanager.nodemanager.minimum.version":"NONE","mapreduce.jobhistory.webapp.xfs-filter.xframe-options":"SAMEORIGIN","yarn.app.mapreduce.am.staging-dir.erasurecoding.enabled":"false","net.topology.impl":"org.apache.hadoop.net.NetworkTopology","io.map.index.skip":"0","yarn.timeline-service.reader.webapp.https.address":"${yarn.timeline-service.webapp.https.address}","fs.ftp.data.connection.mode":"ACTIVE_LOCAL_DATA_CONNECTION_MODE","mapreduce.job.local-fs.single-disk-limit.check.kill-limit-exceed":"true","fs.azure.buffer.dir":"${hadoop.tmp.dir}/abfs","yarn.scheduler.maximum-allocation-vcores":"4","hadoop.http.cross-origin.allowed-headers":"X-Requested-With,Content-Type,Accept,Origin","yarn.nodemanager.log-aggregation.compression-type":"none","yarn.timeline-service.version":"1.0f","yarn.ipc.rpc.class":"org.apache.hadoop.yarn.ipc.HadoopYarnProtoRPC","mapreduce.reduce.maxattempts":"4","yarn.resourcemanager.system-metrics-publisher.timeline-server-v1.batch-size":"1000","hadoop.security.dns.log-slow-lookups.enabled":"false","mapreduce.job.committer.setup.cleanup.needed":"true","hadoop.security.secure.random.impl":"org.apache.hadoop.crypto.random.OpensslSecureRandom","mapreduce.job.running.reduce.limit":"0","fs.s3a.select.errors.include.sql":"false","fs.s3a.connection.request.timeout":"0","ipc.maximum.response.length":"134217728","yarn.resourcemanager.webapp.rest-csrf.methods-to-ignore":"GET,OPTIONS,HEAD","mapreduce.job.token.tracking.ids.enabled":"*********(redacted)","hadoop.caller.context.max.size":"128","yarn.nodemanager.runtime.linux.docker.host-pid-namespace.allowed":"false","yarn.nodemanager.runtime.linux.docker.delayed-removal.allowed":"false","hadoop.registry.system.acls":"sasl:yarn@, sasl:mapred@, sasl:hdfs@","yarn.nodemanager.recovery.dir":"${hadoop.tmp.dir}/yarn-nm-recovery","fs.s3a.fast.upload.buffer":"disk","mapreduce.jobhistory.intermediate-done-dir":"${yarn.app.mapreduce.am.staging-dir}/history/done_intermediate","yarn.app.mapreduce.shuffle.log.separate":"true","yarn.log-aggregation.debug.filesize":"104857600","fs.s3a.max.total.tasks":"32","fs.s3a.readahead.range":"64K","hadoop.http.authentication.simple.anonymous.allowed":"true","fs.s3a.attempts.maximum":"20","hadoop.registry.zk.connection.timeout.ms":"15000","yarn.resourcemanager.delegation-token-renewer.thread-count":"*********(redacted)","yarn.resourcemanager.delegation-token-renewer.thread-timeout":"*********(redacted)","yarn.timeline-service.leveldb-timeline-store.start-time-write-cache-size":"10000","yarn.nodemanager.aux-services.manifest.reload-ms":"0","yarn.nodemanager.emit-container-events":"true","yarn.resourcemanager.resource-profiles.enabled":"false","yarn.timeline-service.hbase-schema.prefix":"prod.","fs.azure.authorization":"false","mapreduce.map.log.level":"INFO","ha.failover-controller.active-standby-elector.zk.op.retries":"3","yarn.resourcemanager.decommissioning-nodes-watcher.poll-interval-secs":"20","mapreduce.output.fileoutputformat.compress.type":"RECORD","yarn.resourcemanager.leveldb-state-store.path":"${hadoop.tmp.dir}/yarn/system/rmstore","yarn.timeline-service.webapp.rest-csrf.custom-header":"X-XSRF-Header","mapreduce.ifile.readahead.bytes":"4194304","yarn.sharedcache.app-checker.class":"org.apache.hadoop.yarn.server.sharedcachemanager.RemoteAppChecker","yarn.nodemanager.linux-container-executor.nonsecure-mode.limit-users":"true","yarn.nodemanager.resource.detect-hardware-capabilities":"false","mapreduce.cluster.acls.enabled":"false","mapreduce.job.speculative.retry-after-no-speculate":"1000","fs.viewfs.overload.scheme.target.abfs.impl":"org.apache.hadoop.fs.azurebfs.AzureBlobFileSystem","hadoop.security.group.mapping.ldap.search.group.hierarchy.levels":"0","yarn.resourcemanager.fs.state-store.retry-interval-ms":"1000","file.stream-buffer-size":"4096","yarn.resourcemanager.application-timeouts.monitor.interval-ms":"3000","mapreduce.map.output.compress.codec":"org.apache.hadoop.io.compress.DefaultCodec","mapreduce.map.speculative":"true","yarn.nodemanager.runtime.linux.runc.image-tag-to-manifest-plugin.hdfs-hash-file":"/runc-root/image-tag-to-hash","mapreduce.job.speculative.retry-after-speculate":"15000","yarn.nodemanager.linux-container-executor.cgroups.mount":"false","yarn.app.mapreduce.am.container.log.backups":"0","yarn.app.mapreduce.am.log.level":"INFO","yarn.nodemanager.runtime.linux.runc.image-tag-to-manifest-plugin":"org.apache.hadoop.yarn.server.nodemanager.containermanager.linux.runtime.runc.ImageTagToManifestPlugin","io.bytes.per.checksum":"512","mapreduce.job.reduce.slowstart.completedmaps":"0.05","yarn.timeline-service.http-authentication.type":"simple","hadoop.security.group.mapping.ldap.search.attr.group.name":"cn","yarn.nodemanager.resource-plugins.fpga.allowed-fpga-devices":"auto","yarn.timeline-service.client.internal-timers-ttl-secs":"420","fs.s3a.select.output.csv.quote.character":"\"","hadoop.http.logs.enabled":"true","fs.s3a.block.size":"32M","yarn.sharedcache.client-server.address":"0.0.0.0:8045","yarn.nodemanager.logaggregation.threadpool-size-max":"100","yarn.resourcemanager.hostname":"0.0.0.0","yarn.resourcemanager.delegation.key.update-interval":"86400000","mapreduce.reduce.shuffle.fetch.retry.enabled":"${yarn.nodemanager.recovery.enabled}","mapreduce.map.memory.mb":"-1","mapreduce.task.skip.start.attempts":"2","fs.AbstractFileSystem.hdfs.impl":"org.apache.hadoop.fs.Hdfs","yarn.nodemanager.disk-health-checker.enable":"true","fs.s3a.select.output.csv.quote.fields":"always","ipc.client.tcpnodelay":"true","ipc.client.rpc-timeout.ms":"0","yarn.nodemanager.webapp.rest-csrf.methods-to-ignore":"GET,OPTIONS,HEAD","yarn.resourcemanager.delegation-token-renewer.thread-retry-max-attempts":"*********(redacted)","ipc.client.low-latency":"false","mapreduce.input.lineinputformat.linespermap":"1","yarn.router.interceptor.user.threadpool-size":"5","ipc.client.connect.max.retries.on.timeouts":"45","yarn.timeline-service.leveldb-timeline-store.read-cache-size":"104857600","fs.AbstractFileSystem.har.impl":"org.apache.hadoop.fs.HarFs","mapreduce.job.split.metainfo.maxsize":"10000000","yarn.am.liveness-monitor.expiry-interval-ms":"600000","yarn.resourcemanager.container-tokens.master-key-rolling-interval-secs":"*********(redacted)","yarn.timeline-service.entity-group-fs-store.app-cache-size":"10","yarn.nodemanager.runtime.linux.runc.hdfs-manifest-to-resources-plugin.stat-cache-timeout-interval-secs":"360","fs.s3a.socket.recv.buffer":"8192","rpc.metrics.timeunit":"MILLISECONDS","yarn.resourcemanager.resource-tracker.address":"${yarn.resourcemanager.hostname}:8031","yarn.nodemanager.node-labels.provider.fetch-timeout-ms":"1200000","mapreduce.job.heap.memory-mb.ratio":"0.8","yarn.resourcemanager.leveldb-state-store.compaction-interval-secs":"3600","yarn.resourcemanager.webapp.rest-csrf.custom-header":"X-XSRF-Header","yarn.nodemanager.pluggable-device-framework.enabled":"false","yarn.scheduler.configuration.fs.path":"file://${hadoop.tmp.dir}/yarn/system/schedconf","mapreduce.client.output.filter":"FAILED","hadoop.http.filter.initializers":"org.apache.hadoop.http.lib.StaticUserWebFilter","mapreduce.reduce.memory.mb":"-1","yarn.timeline-service.hostname":"0.0.0.0","file.replication":"1","yarn.nodemanager.container-metrics.unregister-delay-ms":"10000","yarn.nodemanager.container-metrics.period-ms":"-1","mapreduce.fileoutputcommitter.task.cleanup.enabled":"false","yarn.nodemanager.log.retain-seconds":"10800","yarn.timeline-service.entity-group-fs-store.cleaner-interval-seconds":"3600","ipc.[port_number].callqueue.impl":"java.util.concurrent.LinkedBlockingQueue","yarn.resourcemanager.keytab":"/etc/krb5.keytab","hadoop.security.group.mapping.providers.combined":"true","mapreduce.reduce.merge.inmem.threshold":"1000","yarn.timeline-service.recovery.enabled":"false","fs.azure.saskey.usecontainersaskeyforallaccess":"true","yarn.sharedcache.nm.uploader.thread-count":"20","yarn.resourcemanager.nodemanager-graceful-decommission-timeout-secs":"3600","ipc.[port_number].weighted-cost.lockfree":"1","mapreduce.shuffle.ssl.enabled":"false","yarn.timeline-service.hbase.coprocessor.app-final-value-retention-milliseconds":"259200000","yarn.nodemanager.opportunistic-containers-max-queue-length":"0","yarn.resourcemanager.state-store.max-completed-applications":"${yarn.resourcemanager.max-completed-applications}","mapreduce.job.speculative.minimum-allowed-tasks":"10","fs.s3a.aws.credentials.provider":"\n    org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider,\n    org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider,\n    com.amazonaws.auth.EnvironmentVariableCredentialsProvider,\n    org.apache.hadoop.fs.s3a.auth.IAMInstanceCredentialsProvider\n  ","yarn.log-aggregation.retain-seconds":"-1","yarn.nodemanager.disk-health-checker.min-free-space-per-disk-mb":"0","mapreduce.jobhistory.max-age-ms":"604800000","hadoop.http.cross-origin.allowed-methods":"GET,POST,HEAD","yarn.resourcemanager.opportunistic-container-allocation.enabled":"false","mapreduce.jobhistory.webapp.address":"0.0.0.0:19888","hadoop.system.tags":"YARN,HDFS,NAMENODE,DATANODE,REQUIRED,SECURITY,KERBEROS,PERFORMANCE,CLIENT\n      ,SERVER,DEBUG,DEPRECATED,COMMON,OPTIONAL","yarn.log-aggregation.file-controller.TFile.class":"org.apache.hadoop.yarn.logaggregation.filecontroller.tfile.LogAggregationTFileController","yarn.client.nodemanager-connect.max-wait-ms":"180000","yarn.resourcemanager.webapp.address":"${yarn.resourcemanager.hostname}:8088","mapreduce.jobhistory.recovery.enable":"false","mapreduce.reduce.shuffle.parallelcopies":"5","fs.AbstractFileSystem.webhdfs.impl":"org.apache.hadoop.fs.WebHdfs","fs.trash.interval":"0","yarn.app.mapreduce.client.max-retries":"3","hadoop.security.authentication":"simple","mapreduce.task.profile.reduce.params":"${mapreduce.task.profile.params}","yarn.app.mapreduce.am.resource.mb":"1536","mapreduce.input.fileinputformat.list-status.num-threads":"1","yarn.nodemanager.container-executor.class":"org.apache.hadoop.yarn.server.nodemanager.DefaultContainerExecutor","io.mapfile.bloom.size":"1048576","yarn.timeline-service.ttl-ms":"604800000","yarn.resourcemanager.nm-container-queuing.min-queue-length":"5","yarn.nodemanager.resource.cpu-vcores":"4","mapreduce.job.reduces":"1","fs.s3a.multipart.size":"64M","fs.s3a.select.input.csv.comment.marker":"#","yarn.scheduler.minimum-allocation-vcores":"1","mapreduce.job.speculative.speculative-cap-total-tasks":"0.01","hadoop.ssl.client.conf":"ssl-client.xml","mapreduce.job.queuename":"default","mapreduce.job.encrypted-intermediate-data-key-size-bits":"128","fs.s3a.metadatastore.authoritative":"false","ipc.[port_number].weighted-cost.response":"1","yarn.nodemanager.webapp.xfs-filter.xframe-options":"SAMEORIGIN","ha.health-monitor.sleep-after-disconnect.ms":"1000","yarn.app.mapreduce.shuffle.log.limit.kb":"0","hadoop.security.group.mapping":"org.apache.hadoop.security.JniBasedUnixGroupsMappingWithFallback","yarn.client.application-client-protocol.poll-timeout-ms":"-1","mapreduce.jobhistory.jhist.format":"binary","mapreduce.task.stuck.timeout-ms":"600000","yarn.resourcemanager.application.max-tag.length":"100","yarn.resourcemanager.ha.enabled":"false","dfs.client.ignore.namenode.default.kms.uri":"false","hadoop.http.staticuser.user":"dr.who","mapreduce.task.exit.timeout.check-interval-ms":"20000","mapreduce.jobhistory.intermediate-user-done-dir.permissions":"770","mapreduce.task.exit.timeout":"60000","yarn.nodemanager.linux-container-executor.resources-handler.class":"org.apache.hadoop.yarn.server.nodemanager.util.DefaultLCEResourcesHandler","mapreduce.reduce.shuffle.memory.limit.percent":"0.25","yarn.resourcemanager.reservation-system.enable":"false","mapreduce.map.output.compress":"false","ha.zookeeper.acl":"world:anyone:rwcda","ipc.server.max.connections":"0","yarn.nodemanager.aux-services":"mapreduce_shuffle","yarn.nodemanager.runtime.linux.docker.default-container-network":"host","yarn.router.webapp.address":"0.0.0.0:8089","yarn.scheduler.maximum-allocation-mb":"8192","yarn.resourcemanager.scheduler.monitor.policies":"org.apache.hadoop.yarn.server.resourcemanager.monitor.capacity.ProportionalCapacityPreemptionPolicy","yarn.sharedcache.cleaner.period-mins":"1440","yarn.nodemanager.resource-plugins.gpu.docker-plugin.nvidia-docker-v1.endpoint":"http://localhost:3476/v1.0/docker/cli","yarn.app.mapreduce.am.container.log.limit.kb":"0","ipc.client.connect.retry.interval":"1000","yarn.timeline-service.http-cross-origin.enabled":"false","fs.wasbs.impl":"org.apache.hadoop.fs.azure.NativeAzureFileSystem$Secure","yarn.resourcemanager.nodemanagers.heartbeat-interval-max-ms":"1000","yarn.federation.subcluster-resolver.class":"org.apache.hadoop.yarn.server.federation.resolver.DefaultSubClusterResolverImpl","yarn.resourcemanager.zk-state-store.parent-path":"/rmstore","fs.s3a.select.input.csv.field.delimiter":",","mapreduce.jobhistory.cleaner.enable":"true","yarn.timeline-service.client.fd-flush-interval-secs":"10","hadoop.security.kms.client.encrypted.key.cache.expiry":"43200000","yarn.client.nodemanager-client-async.thread-pool-max-size":"500","mapreduce.map.maxattempts":"4","yarn.resourcemanager.nm-container-queuing.sorting-nodes-interval-ms":"1000","fs.s3a.committer.staging.tmp.path":"tmp/staging","yarn.nodemanager.sleep-delay-before-sigkill.ms":"250","yarn.resourcemanager.nm-container-queuing.min-queue-wait-time-ms":"10","mapreduce.job.end-notification.retry.attempts":"0","yarn.nodemanager.resource.count-logical-processors-as-cores":"false","hadoop.registry.zk.root":"/registry","adl.feature.ownerandgroup.enableupn":"false","yarn.resourcemanager.zk-max-znode-size.bytes":"1048576","mapreduce.job.reduce.shuffle.consumer.plugin.class":"org.apache.hadoop.mapreduce.task.reduce.Shuffle","yarn.resourcemanager.delayed.delegation-token.removal-interval-ms":"*********(redacted)","yarn.nodemanager.localizer.cache.target-size-mb":"10240","fs.s3a.committer.staging.conflict-mode":"append","mapreduce.client.libjars.wildcard":"true","fs.s3a.committer.staging.unique-filenames":"true","yarn.nodemanager.node-attributes.provider.fetch-timeout-ms":"1200000","fs.s3a.list.version":"2","ftp.client-write-packet-size":"65536","ipc.[port_number].weighted-cost.lockexclusive":"100","fs.AbstractFileSystem.adl.impl":"org.apache.hadoop.fs.adl.Adl","yarn.nodemanager.container-log-monitor.enable":"false","hadoop.security.key.default.cipher":"AES/CTR/NoPadding","yarn.client.failover-retries":"0","fs.s3a.multipart.purge.age":"86400","mapreduce.job.local-fs.single-disk-limit.check.interval-ms":"5000","net.topology.node.switch.mapping.impl":"org.apache.hadoop.net.ScriptBasedMapping","yarn.nodemanager.amrmproxy.address":"0.0.0.0:8049","ipc.server.listen.queue.size":"256","ipc.[port_number].decay-scheduler.period-ms":"5000","yarn.nodemanager.runtime.linux.runc.image-tag-to-manifest-plugin.cache-refresh-interval-secs":"60","map.sort.class":"org.apache.hadoop.util.QuickSort","fs.viewfs.rename.strategy":"SAME_MOUNTPOINT","hadoop.security.kms.client.authentication.retry-count":"1","fs.permissions.umask-mode":"022","fs.s3a.assumed.role.credentials.provider":"org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider","yarn.nodemanager.runtime.linux.runc.privileged-containers.allowed":"false","yarn.nodemanager.vmem-check-enabled":"true","yarn.nodemanager.numa-awareness.enabled":"false","yarn.nodemanager.recovery.compaction-interval-secs":"3600","yarn.app.mapreduce.client-am.ipc.max-retries":"3","yarn.resourcemanager.system-metrics-publisher.timeline-server-v1.interval-seconds":"60","yarn.federation.registry.base-dir":"yarnfederation/","yarn.nodemanager.health-checker.run-before-startup":"false","mapreduce.job.max.map":"-1","mapreduce.job.local-fs.single-disk-limit.bytes":"-1","mapreduce.shuffle.pathcache.concurrency-level":"16","mapreduce.job.ubertask.maxreduces":"1","mapreduce.shuffle.pathcache.max-weight":"10485760","hadoop.security.kms.client.encrypted.key.cache.size":"500","hadoop.security.java.secure.random.algorithm":"SHA1PRNG","ha.failover-controller.cli-check.rpc-timeout.ms":"20000","mapreduce.jobhistory.jobname.limit":"50","fs.s3a.select.input.compression":"none","yarn.client.nodemanager-connect.retry-interval-ms":"10000","ipc.[port_number].scheduler.priority.levels":"4","yarn.timeline-service.state-store-class":"org.apache.hadoop.yarn.server.timeline.recovery.LeveldbTimelineStateStore","yarn.nodemanager.env-whitelist":"JAVA_HOME,HADOOP_COMMON_HOME,HADOOP_HDFS_HOME,HADOOP_CONF_DIR,CLASSPATH_PREPEND_DISTCACHE,HADOOP_YARN_HOME,HADOOP_HOME,PATH,LANG,TZ","yarn.sharedcache.nested-level":"3","yarn.timeline-service.webapp.rest-csrf.methods-to-ignore":"GET,OPTIONS,HEAD","fs.azure.user.agent.prefix":"unknown","yarn.resourcemanager.zk-delegation-token-node.split-index":"*********(redacted)","yarn.nodemanager.numa-awareness.read-topology":"false","yarn.nodemanager.webapp.address":"${yarn.nodemanager.hostname}:8042","rpc.metrics.quantile.enable":"false","yarn.registry.class":"org.apache.hadoop.registry.client.impl.FSRegistryOperationsService","mapreduce.jobhistory.admin.acl":"*","yarn.resourcemanager.system-metrics-publisher.dispatcher.pool-size":"10","yarn.scheduler.queue-placement-rules":"user-group","hadoop.http.authentication.kerberos.keytab":"${user.home}/hadoop.keytab","yarn.resourcemanager.recovery.enabled":"false","fs.s3a.select.input.csv.header":"none","yarn.nodemanager.runtime.linux.runc.hdfs-manifest-to-resources-plugin.stat-cache-size":"500","yarn.timeline-service.webapp.rest-csrf.enabled":"false","yarn.nodemanager.disk-health-checker.min-free-space-per-disk-watermark-high-mb":"0"},"System Properties":{"java.io.tmpdir":"/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/tmp","line.separator":"\n","path.separator":":","sun.management.compiler":"HotSpot 64-Bit Tiered Compilers","sun.cpu.endian":"little","java.specification.version":"1.8","java.vm.specification.name":"Java Virtual Machine Specification","java.vendor":"Private Build","java.vm.specification.version":"1.8","user.home":"/home/<USER>","file.encoding.pkg":"sun.io","sun.arch.data.model":"64","sun.boot.library.path":"/usr/lib/jvm/java-8-openjdk-amd64/jre/lib/amd64","user.dir":"/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001","java.library.path":"/usr/java/packages/lib/amd64:/usr/lib/x86_64-linux-gnu/jni:/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu:/usr/lib/jni:/lib:/usr/lib","sun.cpu.isalist":"","os.arch":"amd64","java.vm.version":"25.312-b07","jetty.git.hash":"bc17a0369a11ecf40bb92c839b9ef0a8ac50ea18","java.endorsed.dirs":"/usr/lib/jvm/java-8-openjdk-amd64/jre/lib/endorsed","java.runtime.version":"1.8.0_312-8u312-b07-0ubuntu1~20.04-b07","java.vm.info":"mixed mode","java.ext.dirs":"/usr/lib/jvm/java-8-openjdk-amd64/jre/lib/ext:/usr/java/packages/lib/ext","java.runtime.name":"OpenJDK Runtime Environment","file.separator":"/","java.net.preferIPv6Addresses":"false","java.class.version":"52.0","java.specification.name":"Java Platform API Specification","sun.boot.class.path":"/usr/lib/jvm/java-8-openjdk-amd64/jre/lib/resources.jar:/usr/lib/jvm/java-8-openjdk-amd64/jre/lib/rt.jar:/usr/lib/jvm/java-8-openjdk-amd64/jre/lib/sunrsasign.jar:/usr/lib/jvm/java-8-openjdk-amd64/jre/lib/jsse.jar:/usr/lib/jvm/java-8-openjdk-amd64/jre/lib/jce.jar:/usr/lib/jvm/java-8-openjdk-amd64/jre/lib/charsets.jar:/usr/lib/jvm/java-8-openjdk-amd64/jre/lib/jfr.jar:/usr/lib/jvm/java-8-openjdk-amd64/jre/classes","file.encoding":"UTF-8","user.timezone":"Asia/Shanghai","java.specification.vendor":"Oracle Corporation","sun.java.launcher":"SUN_STANDARD","os.version":"5.13.0-51-generic","sun.os.patch.level":"unknown","java.vm.specification.vendor":"Oracle Corporation","user.country":"US","sun.jnu.encoding":"UTF-8","user.language":"en","java.vendor.url":"http://java.oracle.com/","java.awt.printerjob":"sun.print.PSPrinterJob","java.awt.graphicsenv":"sun.awt.X11GraphicsEnvironment","awt.toolkit":"sun.awt.X11.XToolkit","os.name":"Linux","java.vm.vendor":"Private Build","java.vendor.url.bug":"http://bugreport.sun.com/bugreport/","user.name":"kuwii","java.vm.name":"OpenJDK 64-Bit Server VM","sun.java.command":"org.apache.spark.deploy.yarn.ApplicationMaster --class org.apache.spark.deploy.PythonRunner --primary-py-file pi.py --arg 100 --properties-file /home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_conf__/__spark_conf__.properties --dist-cache-conf /home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_conf__/__spark_dist_cache__.properties","java.home":"/usr/lib/jvm/java-8-openjdk-amd64/jre","java.version":"1.8.0_312","sun.io.unicode.encoding":"UnicodeLittle"},"Metrics Properties":{"*.sink.servlet.class":"org.apache.spark.metrics.sink.MetricsServlet","*.sink.servlet.path":"/metrics/json","applications.sink.servlet.path":"/metrics/applications/json","master.sink.servlet.path":"/metrics/master/json"},"Classpath Entries":{"/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/kryo-shaded-4.0.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-core_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/protobuf-java-2.5.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jakarta.servlet-api-4.0.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/parquet-encoding-1.12.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/oro-2.0.8.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/scala-parser-combinators_2.12-1.1.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-graphx_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/RoaringBitmap-0.9.28.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-transport-native-epoll-4.1.77.Final-linux-x86_64.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/zookeeper-jute-3.6.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jaxb-runtime-2.3.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jakarta.ws.rs-api-2.1.6.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/core-1.1.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jackson-databind-2.13.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/curator-framework-2.13.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/metrics-jmx-4.2.7.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-compiler-3.0.16.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/arpack_combined_all-0.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-handler-4.1.77.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jakarta.validation-api-2.0.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/univocity-parsers-2.9.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jcl-over-slf4j-1.7.32.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/metrics-jvm-4.2.7.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/hk2-api-2.6.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-compress-1.21.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/rocksdbjni-7.3.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-network-common_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-mllib-local_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/lapack-2.2.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/arrow-memory-core-8.0.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/log4j-api-2.17.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/scala-library-2.12.16.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-transport-classes-epoll-4.1.77.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/xbean-asm9-shaded-4.20.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/stream-2.9.6.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-text-1.9.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/shapeless_2.12-2.3.7.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/py4j-********.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/chill_2.12-0.10.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/hadoop-shaded-guava-1.1.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/lz4-java-1.8.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-streaming_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-io-2.11.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/janino-3.0.16.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-transport-native-kqueue-4.1.77.Final-osx-aarch_64.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/zstd-jni-1.5.2-3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-transport-native-epoll-4.1.77.Final-linux-aarch_64.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-resolver-4.1.77.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/arrow-memory-netty-8.0.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/paranamer-2.8.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/arpack-2.2.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jersey-container-servlet-2.35.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-network-shuffle_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/scala-compiler-2.12.16.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-mllib_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/gson-2.8.6.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/shims-0.9.28.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/snappy-java-1.1.8.4.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/algebra_2.12-2.0.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/avro-mapred-1.11.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/audience-annotations-0.5.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/xz-1.8.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jackson-module-scala_2.12-2.13.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/antlr4-runtime-4.8.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/json4s-scalap_2.12-3.7.0-M11.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/curator-recipes-2.13.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/threeten-extra-1.5.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/parquet-common-1.12.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spire-util_2.12-0.17.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jersey-client-2.35.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/avro-ipc-1.11.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-yarn_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/hadoop-client-api-3.3.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-lang3-3.12.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/istack-commons-runtime-3.0.8.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-all-4.1.77.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/tink-1.6.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jersey-common-2.35.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-common-4.1.77.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/avro-1.11.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/pickle-1.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/JLargeArrays-1.5.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/javassist-3.25.0-GA.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/orc-shims-1.7.5.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/minlog-1.3.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_conf__/__hadoop_conf__":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/hadoop-yarn-server-web-proxy-3.3.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-collections-3.2.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/scala-xml_2.12-1.2.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-math3-3.6.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-transport-classes-kqueue-4.1.77.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/aopalliance-repackaged-2.6.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/orc-core-1.7.5.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/scala-collection-compat_2.12-2.1.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/orc-mapreduce-1.7.5.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/breeze-macros_2.12-1.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/compress-lzf-1.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/parquet-jackson-1.12.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/ivy-2.5.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-codec-1.15.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/parquet-hadoop-1.12.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/json4s-core_2.12-3.7.0-M11.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spire-platform_2.12-0.17.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jakarta.xml.bind-api-2.3.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/guava-14.0.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/blas-2.2.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-transport-4.1.77.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jersey-server-2.35.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jersey-container-servlet-core-2.35.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/aircompressor-0.21.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/breeze_2.12-1.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/JTransforms-3.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-tcnative-classes-2.0.52.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jackson-annotations-2.13.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-repl_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-logging-1.1.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/slf4j-api-1.7.32.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-crypto-1.1.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/cats-kernel_2.12-2.1.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-collections4-4.4.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-unsafe_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/curator-client-2.13.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/opencsv-2.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/chill-java-0.10.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jul-to-slf4j-1.7.32.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/hk2-locator-2.6.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/annotations-17.0.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-tags_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spire_2.12-0.17.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/objenesis-3.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/log4j-1.2-api-2.17.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/metrics-json-4.2.7.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/commons-lang-2.6.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/scala-reflect-2.12.16.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-transport-native-unix-common-4.1.77.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/json4s-ast_2.12-3.7.0-M11.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-kvstore_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jsr305-3.0.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/parquet-format-structures-1.12.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/osgi-resource-locator-1.0.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/metrics-core-4.2.7.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/hk2-utils-2.6.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/log4j-slf4j-impl-2.17.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-launcher_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_conf__":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/json4s-jackson_2.12-3.7.0-M11.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jersey-hk2-2.35.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jakarta.inject-2.6.1.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-codec-4.1.77.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/log4j-core-2.17.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/parquet-column-1.12.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/hadoop-client-runtime-3.3.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-sketch_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/leveldbjni-all-1.8.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/flatbuffers-java-1.12.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/arrow-format-8.0.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jackson-datatype-jsr310-2.13.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jakarta.annotation-api-1.3.5.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/metrics-graphite-4.2.7.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-catalyst_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-transport-native-kqueue-4.1.77.Final-osx-x86_64.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/zookeeper-3.6.2.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spark-sql_2.12-3.4.0-SNAPSHOT.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/hive-storage-api-2.7.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/jackson-core-2.13.3.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/spire-macros_2.12-0.17.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/arrow-vector-8.0.0.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/netty-buffer-4.1.77.Final.jar":"System Classpath","/home/<USER>/Projects/hadoop/data/nm-local-dir/usercache/kuwii/appcache/application_1656321732247_0006/container_1656321732247_0006_02_000001/__spark_libs__/activation-1.1.1.jar":"System Classpath"}}
{"Event":"SparkListenerApplicationStart","App Name":"PythonPi","App ID":"application_1656321732247_0006","Timestamp":1656322543203,"User":"kuwii","App Attempt ID":"2","Driver Logs":{"stdout":"http://localhost:8042/node/containerlogs/container_1656321732247_0006_02_000001/kuwii/stdout?start=-4096","stderr":"http://localhost:8042/node/containerlogs/container_1656321732247_0006_02_000001/kuwii/stderr?start=-4096"},"Driver Attributes":{"NM_HTTP_ADDRESS":"localhost:8042","USER":"kuwii","LOG_FILES":"stderr,stdout","NM_HTTP_PORT":"8042","CLUSTER_ID":"","NM_PORT":"35379","HTTP_SCHEME":"http://","NM_HOST":"localhost","CONTAINER_ID":"container_1656321732247_0006_02_000001"}}
{"Event":"SparkListenerExecutorAdded","Timestamp":1656322550105,"Executor ID":"1","Executor Info":{"Host":"localhost","Total Cores":1,"Log Urls":{"stdout":"http://localhost:8042/node/containerlogs/container_1656321732247_0006_02_000002/kuwii/stdout?start=-4096","stderr":"http://localhost:8042/node/containerlogs/container_1656321732247_0006_02_000002/kuwii/stderr?start=-4096"},"Attributes":{"NM_HTTP_ADDRESS":"localhost:8042","USER":"kuwii","LOG_FILES":"stderr,stdout","NM_HTTP_PORT":"8042","CLUSTER_ID":"","NM_PORT":"35379","HTTP_SCHEME":"http://","NM_HOST":"localhost","CONTAINER_ID":"container_1656321732247_0006_02_000002"},"Resources":{},"Resource Profile Id":0,"Registration Time":1656322550105}}
{"Event":"SparkListenerBlockManagerAdded","Block Manager ID":{"Executor ID":"1","Host":"localhost","Port":41059},"Maximum Memory":384093388,"Timestamp":1656322550218,"Maximum Onheap Memory":384093388,"Maximum Offheap Memory":0}
{"Event":"SparkListenerExecutorAdded","Timestamp":1656322552392,"Executor ID":"2","Executor Info":{"Host":"localhost","Total Cores":1,"Log Urls":{"stdout":"http://localhost:8042/node/containerlogs/container_1656321732247_0006_02_000003/kuwii/stdout?start=-4096","stderr":"http://localhost:8042/node/containerlogs/container_1656321732247_0006_02_000003/kuwii/stderr?start=-4096"},"Attributes":{"NM_HTTP_ADDRESS":"localhost:8042","USER":"kuwii","LOG_FILES":"stderr,stdout","NM_HTTP_PORT":"8042","CLUSTER_ID":"","NM_PORT":"35379","HTTP_SCHEME":"http://","NM_HOST":"localhost","CONTAINER_ID":"container_1656321732247_0006_02_000003"},"Resources":{},"Resource Profile Id":0,"Registration Time":1656322552392}}
{"Event":"SparkListenerBlockManagerAdded","Block Manager ID":{"Executor ID":"2","Host":"localhost","Port":42333},"Maximum Memory":384093388,"Timestamp":1656322552495,"Maximum Onheap Memory":384093388,"Maximum Offheap Memory":0}
