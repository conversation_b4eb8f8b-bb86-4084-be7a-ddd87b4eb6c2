# Guidelines

To throw a standardized user-facing error or exception, developers should specify the error class
and message parameters rather than an arbitrary error message.

## Usage

1. Check if the error is an internal error.
   Internal errors are bugs in the code that we do not expect users to encounter; this does not include unsupported operations.
   If true, use the error class `INTERNAL_ERROR` and skip to step 4.
2. Check if an appropriate error class already exists in `error-classes.json`.
   If true, use the error class and skip to step 4.
3. Add a new class to `error-classes.json`; keep in mind the invariants below.
4. Check if the exception type already extends `SparkThrowable`.
   If true, skip to step 6.
5. Mix `SparkThrowable` into the exception.
6. Throw the exception with the error class and message parameters. If the same exception is thrown in several places, create an util function in a central place such as `QueryCompilationErrors.scala` to instantiate the exception.

### Before

Throw with arbitrary error message:

    throw new TestException("Problem A because B")

### After

`error-classes.json`

    "PROBLEM_BECAUSE": {
      "message": ["Problem <problem> because <cause>"],
      "sqlState": "XXXXX"
    }

`SparkException.scala`

    class SparkTestException(
        errorClass: String,
        messageParameters: Map[String, String])
      extends TestException(SparkThrowableHelper.getMessage(errorClass, messageParameters))
        with SparkThrowable {
        
      override def getMessageParameters: java.util.Map[String, String] = messageParameters.asJava

      override def getErrorClass: String = errorClass
    }

Throw with error class and message parameters:

    throw new SparkTestException("PROBLEM_BECAUSE", Map("problem" -> "A", "cause" -> "B"))

## Access fields

To access error fields, catch exceptions that extend `org.apache.spark.SparkThrowable` and access
  - Error class with `getErrorClass`
  - SQLSTATE with `getSqlState`


    try {
        ...
    } catch {
        case e: SparkThrowable if Option(e.getSqlState).forall(_.startsWith("42")) =>
            warn("Syntax error")
    }

## Fields

### Error class

Error classes are a succinct, human-readable representation of the error category.

An uncategorized errors can be assigned to a legacy error class with the prefix `_LEGACY_ERROR_TEMP_` and an unused sequential number, for instance `_LEGACY_ERROR_TEMP_0053`.

#### Invariants

- Unique
- Consistent across releases
- Sorted alphabetically

### Message

Error messages provide a descriptive, human-readable representation of the error.
The message format accepts string parameters via the C-style printf syntax.

The quality of the error message should match the
[guidelines](https://spark.apache.org/error-message-guidelines.html).

#### Invariants

- Unique

### SQLSTATE

SQLSTATE is an optional portable error identifier across SQL engines.
SQLSTATE comprises a 2-character class value followed by a 3-character subclass value.
Spark prefers to re-use existing SQLSTATEs, preferably used by multiple vendors.
For extension Spark claims the 'K**' subclass range.
If a new class is needed it will also claim the 'K0' class.

#### Invariants

- Consistent across releases

#### ANSI/ISO standard

The following SQLSTATEs are collated from:
- SQL2016
- DB2 zOS
- PostgreSQL 15
- Oracle 12 (last published)
- SQL Server
- Redshift.

<!-- SQLSTATE table start -->
|SQLSTATE |Class|Condition                                         |Subclass|Subcondition                                                |Origin         |Standard|Used By                                                                     |
|---------|-----|--------------------------------------------------|--------|------------------------------------------------------------|---------------|--------|----------------------------------------------------------------------------|
|00000    |00   |successful completion                             |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|01000    |01   |warning                                           |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation SQL/PSM SQL/XML SQL/JRT PostgreSQL Redshift Oracle SQL Server|
|01001    |01   |warning                                           |001     |cursor operation conflict                                   |SQL/Foundation |Y       |SQL/Foundation Oracle SQL Server                                            |
|01002    |01   |warning                                           |002     |disconnect error                                            |SQL/Foundation |Y       |SQL/Foundation Oracle SQL Server                                            |
|01003    |01   |warning                                           |003     |null value eliminated in set function                       |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|01004    |01   |warning                                           |004     |string data, right truncation                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Redshift Oracle SQL Server           |
|01005    |01   |warning                                           |005     |insufficient item descriptor areas                          |SQL/Foundation |Y       |SQL/Foundation DB2 Oracle                                                   |
|01006    |01   |warning                                           |006     |privilege not revoked                                       |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|01007    |01   |warning                                           |007     |privilege not granted                                       |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|01008    |01   |Warning                                           |008     |implicit_zero_bit_padding                                   |PostgreSQL     |N       |PostgreSQL Redshift Oracle                                                  |
|01009    |01   |warning                                           |009     |search condition too long for information schema            |SQL/Foundation |Y       |SQL/Foundation Oracle                                                       |
|0100A    |01   |warning                                           |00A     |query expression too long for information schema            |SQL/Foundation |Y       |SQL/Foundation Oracle                                                       |
|0100B    |01   |warning                                           |00B     |default value too long for information schema               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0100C    |01   |warning                                           |00C     |result sets returned                                        |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|0100D    |01   |warning                                           |00D     |additional result sets returned                             |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0100E    |01   |warning                                           |00E     |attempt to return too many result sets                      |SQL/Foundation |Y       |SQL/Foundation DB2                                                          |
|0100F    |01   |warning                                           |00F     |statement too long for information schema                   |SQL/Foundation |Y       |SQL/Foundation                                                              |
|01010    |01   |warning                                           |010     |column cannot be mapped to XML                              |SQL/XML        |Y       |SQL/XML                                                                     |
|01011    |01   |warning                                           |011     |SQL-Java path too long for infor- mation schema             |SQL/JRT        |Y       |SQL/JRT DB2                                                                 |
|01012    |01   |warning                                           |012     |invalid number of conditions                                |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0102F    |01   |warning                                           |02F     |array data, right truncation                                |SQL/Foundation |Y       |SQL/Foundation                                                              |
|01503    |01   |Warning                                           |503     |The number of result columns is larger than the number of variables provided.|DB2            |N       |DB2                                                                         |
|01504    |01   |Warning                                           |504     |The UPDATE or DELETE statement does not include a WHERE clause.|DB2            |N       |DB2                                                                         |
|01505    |01   |Warning                                           |505     |The statement was not executed because it is unacceptable in this environment.|DB2            |N       |DB2                                                                         |
|01506    |01   |Warning                                           |506     |An adjustment was made to a DATE or TIMESTAMP value to correct an invalid date resulting from an arithmetic operation.|DB2            |N       |DB2                                                                         |
|01507    |01   |Warning                                           |507     |One or more non-zero digits were eliminated from the fractional part of a number used as the operand of a multiply or divide operation.|DB2            |N       |DB2                                                                         |
|01514    |01   |Warning                                           |514     |The tablespace has been placed in the check-pending state.  |DB2            |N       |DB2                                                                         |
|01515    |01   |Warning                                           |515     |The null value has been assigned to a variable, because the non-null value of the column is not within the range of the variable.|DB2            |N       |DB2                                                                         |
|01516    |01   |Warning                                           |516     |An inapplicable WITH GRANT OPTION has been ignored.         |DB2            |N       |DB2                                                                         |
|01517    |01   |Warning                                           |517     |A character that could not be converted was replaced with a substitute character.|DB2            |N       |DB2                                                                         |
|01519    |01   |Warning                                           |519     |The null value has been assigned to a variable, because a numeric value is out of range.|DB2            |N       |DB2                                                                         |
|01520    |01   |Warning                                           |520     |The null value has been assigned to a variable, because the characters cannot be converted.|DB2            |N       |DB2                                                                         |
|01521    |01   |Warning                                           |521     |A specified server-name is undefined but is not needed until the statement is executed or the alias is used.|DB2            |N       |DB2                                                                         |
|01522    |01   |Warning                                           |522     |The local table or view name used in the CREATE ALIAS statement is undefined.|DB2            |N       |DB2                                                                         |
|01523    |01   |Warning                                           |523     |ALL was interpreted to exclude ALTER, INDEX, REFERENCES, and TRIGGER, because these privileges cannot be granted to a remote user.|DB2            |N       |DB2                                                                         |
|01524    |01   |Warning                                           |524     |The result of an aggregate function does not include the null values that were caused by evaluating the arithmetic expression implied by the column of the view.|DB2            |N       |DB2                                                                         |
|01525    |01   |Warning                                           |525     |The number of INSERT values is not the same as the number of columns.|DB2            |N       |DB2                                                                         |
|01527    |01   |Warning                                           |527     |A SET statement references a special register that does not exist at the AS.|DB2            |N       |DB2                                                                         |
|01528    |01   |Warning                                           |528     |WHERE NOT NULL is ignored, because the index key cannot contain null values.|DB2            |N       |DB2                                                                         |
|01530    |01   |Warning                                           |530     |Definition change may require a corresponding change on the read-only systems.|DB2            |N       |DB2                                                                         |
|01532    |01   |Warning                                           |532     |An undefined object name was detected.                      |DB2            |N       |DB2                                                                         |
|01533    |01   |Warning                                           |533     |An undefined column name was detected.                      |DB2            |N       |DB2                                                                         |
|01537    |01   |Warning                                           |537     |An SQL statement cannot be EXPLAINed, because it references a remote object.|DB2            |N       |DB2                                                                         |
|01538    |01   |Warning                                           |538     |The table cannot be subsequently defined as a dependent, because it has the maximum number of columns.|DB2            |N       |DB2                                                                         |
|01539    |01   |Warning                                           |539     |Connection is successful but only SBCS characters should be used.|DB2            |N       |DB2                                                                         |
|01540    |01   |Warning                                           |540     |A limit key has been truncated to 40 bytes.                 |DB2            |N       |DB2                                                                         |
|01542    |01   |Warning                                           |542     |Authorization ID does not have the privilege to perform the operation as specified.|DB2            |N       |DB2                                                                         |
|01543    |01   |Warning                                           |543     |A duplicate constraint has been ignored.                    |DB2            |N       |DB2                                                                         |
|01545    |01   |Warning                                           |545     |An unqualified column name has been interpreted as a correlated reference.|DB2            |N       |DB2                                                                         |
|01546    |01   |Warning                                           |546     |A column of the explanation table is improperly defined.    |DB2            |N       |DB2                                                                         |
|01548    |01   |Warning                                           |548     |The authorization ID does not have the privilege to perform the specified operation on the identified object.|DB2            |N       |DB2                                                                         |
|01551    |01   |Warning                                           |551     |A table in a partitioned tablespace is not available, because its partitioned index has not been created.|DB2            |N       |DB2                                                                         |
|01552    |01   |Warning                                           |552     |An ambiguous qualified column name was resolved to the first of the duplicate names in the FROM clause.|DB2            |N       |DB2                                                                         |
|01553    |01   |Warning                                           |553     |Isolation level RR conflicts with a tablespace locksize of page.|DB2            |N       |DB2                                                                         |
|01554    |01   |Warning                                           |554     |Decimal multiplication may cause overflow.                  |DB2            |N       |DB2                                                                         |
|01558    |01   |Warning                                           |558     |A distribution protocol has been violated.                  |DB2            |N       |DB2                                                                         |
|01560    |01   |Warning                                           |560     |A redundant GRANT has been ignored.                         |DB2            |N       |DB2                                                                         |
|01561    |01   |Warning                                           |561     |An update to a data capture table was not signaled to the originating subsystem.|DB2            |N       |DB2                                                                         |
|01565    |01   |Warning                                           |565     |The null value has been assigned to a variable, because a miscellaneous data exception occurred. For example, the character value for the CAST, DECIMAL, FLOAT, or INTEGER scalar function is invalid; a floating-point NAN (not a number) was detected; invalid data in a packed decimal field was detected; or a mask mapping error was detected.|DB2            |N       |DB2                                                                         |
|01566    |01   |Warning                                           |566     |The object has been placed in a pending state.              |DB2            |N       |DB2                                                                         |
|01568    |01   |Warning                                           |568     |The dynamic SQL statement ends with a semicolon.            |DB2            |N       |DB2                                                                         |
|01578    |01   |Warning                                           |578     |The bind process detected operands of an operator that are not compatible.|DB2            |N       |DB2                                                                         |
|01590    |01   |Warning                                           |590     |Type 2 indexes do not have subpages.                        |DB2            |N       |DB2                                                                         |
|01591    |01   |Warning                                           |591     |The result of the positioned UPDATE or DELETE may depend on the order of the rows.|DB2            |N       |DB2                                                                         |
|01594    |01   |Warning                                           |594     |Insufficient number of entries in an SQLDA for ALL information (i.e. not enough descriptors to return the distinct name).|DB2            |N       |DB2                                                                         |
|01596    |01   |Warning                                           |596     |Comparison functions were not created for a distinct type based on a long string data type.|DB2            |N       |DB2                                                                         |
|01597    |01   |Warning                                           |597     |Specific and non-specific volume IDs are not allowed in a storage group.|DB2            |N       |DB2                                                                         |
|01599    |01   |Warning                                           |599     |Bind options were ignored on REBIND.                        |DB2            |N       |DB2                                                                         |
|01600    |01   |Warning                                           |600     |SUBPAGES ignored on alter of catalog index.                 |DB2            |N       |DB2                                                                         |
|01602    |01   |Warning                                           |602     |Optimization processing encountered a restriction that might have caused it to produce a sub-optimal result.|DB2            |N       |DB2                                                                         |
|01604    |01   |Warning                                           |604     |The SQL statement was explained and not executed.           |DB2            |N       |DB2                                                                         |
|01605    |01   |Warning                                           |605     |A recursive common table expression may contain an infinite loop.|DB2            |N       |DB2                                                                         |
|01608    |01   |Warning                                           |608     |An unsupported value has been replaced.                     |DB2            |N       |DB2                                                                         |
|01614    |01   |Warning                                           |614     |There are fewer locators than the number of result sets.    |DB2            |N       |DB2                                                                         |
|01615    |01   |Warning                                           |615     |A bind option was ignored.                                  |DB2            |N       |DB2                                                                         |
|01616    |01   |Warning                                           |616     |The estimated CPU cost exceeds the resource limit.          |DB2            |N       |DB2                                                                         |
|01624    |01   |Warning                                           |624     |The GBPCACHE specification is ignored because the buffer pool does not allow caching.|DB2            |N       |DB2                                                                         |
|01625    |01   |Warning                                           |625     |The schema name appears more than once in the CURRENT PATH. |DB2            |N       |DB2                                                                         |
|01628    |01   |Warning                                           |628     |The user-specified access path hints are invalid. The access path hints are ignored.|DB2            |N       |DB2                                                                         |
|01629    |01   |Warning                                           |629     |User-specified access path hints were used during access path selection.|DB2            |N       |DB2                                                                         |
|01640    |01   |Warning                                           |640     |ROLLBACK TO SAVEPOINT occurred when there were uncommitted INSERTs or DELETEs that cannot be rolled back.|DB2            |N       |DB2                                                                         |
|01643    |01   |Warning                                           |643     |Assignment to SQLCODE or SQLSTATE variable does not signal a warning or error.|DB2            |N       |DB2                                                                         |
|01644    |01   |Warning                                           |644     |DEFINE NO is not applicable for a lob space or data sets using the VCAT option.|DB2            |N       |DB2                                                                         |
|01656    |01   |Warning                                           |656     |ROLLBACK TO savepoint caused a NOT LOGGED table space to be placed in the LPL.|DB2            |N       |DB2                                                                         |
|01658    |01   |Warning                                           |658     |Binary data is invalid for DECRYPT_CHAR and DECYRYPT_DB.    |DB2            |N       |DB2                                                                         |
|01659    |01   |Warning                                           |659     |A non-atomic statement successfully processed all requested rows with one or more warning conditions.|DB2            |N       |DB2                                                                         |
|01663    |01   |Warning                                           |663     |NOT PADDED clause is ignored for indexes created on auxiliary tables.|DB2            |N       |DB2                                                                         |
|01664    |01   |Warning                                           |664     |Option not specified following the ALTER PARTITION CLAUSE.  |DB2            |N       |DB2                                                                         |
|01665    |01   |Warning                                           |665     |A name or label was truncated.                              |DB2            |N       |DB2                                                                         |
|01666    |01   |Warning                                           |666     |The last partition's limit key value is set to the highest or lowest possible value.|DB2            |N       |DB2                                                                         |
|01668    |01   |Warning                                           |668     |A rowset FETCH statement returned one or more rows of data, with one or more bind out processing error conditions. Use GET DIAGNOSTICS for more information.|DB2            |N       |DB2                                                                         |
|01676    |01   |Warning                                           |676     |Transfer operation ignored since the authorization ID is already the owner of the database object.|DB2            |N       |DB2                                                                         |
|01679    |01   |Warning                                           |679     |A trusted connection cannot be established for the specified system authorization ID.|DB2            |N       |DB2                                                                         |
|01680    |01   |Warning                                           |680     |The option is not supported in the context in which it was specified.|DB2            |N       |DB2                                                                         |
|01681    |01   |Warning                                           |681     |The trusted context is no longer defined to be used by specific attribute value.|DB2            |N       |DB2                                                                         |
|01682    |01   |Warning                                           |682     |The ability to use the trusted context was removed from some but not all authorization IDs specified in statement.|DB2            |N       |DB2                                                                         |
|01683    |01   |Warning                                           |683     |A SELECT containing a non-ATOMIC data change statement successfully returned some rows, but one or more warnings or errors occurred.|DB2            |N       |DB2                                                                         |
|0168B    |01   |Warning                                           |68B     |An operation was partially successful and partially unsuccessful. Use GET DIAGNOSTICS for more information.|DB2            |N       |DB2                                                                         |
|0168C    |01   |Warning                                           |68C     |A decimal float operation produced an inexact result.       |DB2            |N       |DB2                                                                         |
|0168D    |01   |Warning                                           |68D     |A decimal floating point operation was invalid.             |DB2            |N       |DB2                                                                         |
|0168E    |01   |Warning                                           |68E     |A decimal float operation produced an overflow or underflow.|DB2            |N       |DB2                                                                         |
|0168F    |01   |Warning                                           |68F     |A decimal float operation produced division by zero.        |DB2            |N       |DB2                                                                         |
|0168G    |01   |Warning                                           |68G     |A decimal float operation produced a subnormal number.      |DB2            |N       |DB2                                                                         |
|0168L    |01   |Warning                                           |68L     |No routine was found with the specified name and compatible arguments.|DB2            |N       |DB2                                                                         |
|0168T    |01   |Warning                                           |68T     |WITH ROW CHANGE COLUMNS ALWAYS DISTINCT was specified, but the database manager is unable to return distinct row change columns.|DB2            |N       |DB2                                                                         |
|0168X    |01   |Warning                                           |68X     |The combination of target namespace and schema location hint is not unique in the XML schema repository.|DB2            |N       |DB2                                                                         |
|0168Z    |01   |Warning                                           |68Z     |The statement was successfully prepared, but cannot be executed.|DB2            |N       |DB2                                                                         |
|01694    |01   |Warning                                           |694     |A deprecated feature has been ignored.                      |DB2            |N       |DB2                                                                         |
|01695    |01   |Warning                                           |695     |Adjustment made to a value for a period as a result of a data change operation.|DB2            |N       |DB2                                                                         |
|0169A    |01   |Warning                                           |69A     |A configuration parameter was overridden.                   |DB2            |N       |DB2                                                                         |
|0169B    |01   |Warning                                           |69B     |The operation was successful on the Db2 server, but may not have been successful on the accelerator server.|DB2            |N       |DB2                                                                         |
|0169D    |01   |Warning                                           |69D     |The accelerator does not exist.                             |DB2            |N       |DB2                                                                         |
|01H54    |01   |Warning                                           |H54     |The procedure has returned successfully but encountered an error in the format or content of a parameter. Information about the error in the parameter value is returned in an output parameter.|DB2            |N       |DB2                                                                         |
|01H55    |01   |Warning                                           |H55     |The procedure has returned successfully but encountered an internal processing error. Information about the internal error situation is returned in an output parameter.|DB2            |N       |DB2                                                                         |
|01H56    |01   |Warning                                           |H56     |The procedure has returned successfully but supports a higher version for a parameter than the one that was specified.|DB2            |N       |DB2                                                                         |
|01H57    |01   |Warning                                           |H57     |The procedure has returned output in an alternate locale instead of the locale specified.|DB2            |N       |DB2                                                                         |
|01Hxx    |01   |Warning                                           |Hxx     |Valid warning SQLSTATEs returned by a user-defined function, external procedure CALL, or command invocation.|DB2            |N       |DB2                                                                         |
|01P01    |01   |Warning                                           |P01     |deprecated_feature                                          |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|01S00    |01   |Warning                                           |S00     |Invalid connection string attribute                         |SQL Server     |N       |SQL Server                                                                  |
|01S01    |01   |Warning                                           |S01     |Error in row                                                |SQL Server     |N       |SQL Server                                                                  |
|01S02    |01   |Warning                                           |S02     |Option value changed                                        |SQL Server     |N       |SQL Server                                                                  |
|01S06    |01   |Warning                                           |S06     |Attempt to fetch before the result set returned the first rowset|SQL Server     |N       |SQL Server                                                                  |
|01S07    |01   |Warning                                           |S07     |Fractional truncation                                       |SQL Server     |N       |SQL Server                                                                  |
|01S08    |01   |Warning                                           |S08     |Error saving File DSN                                       |SQL Server     |N       |SQL Server                                                                  |
|01S09    |01   |Warning                                           |S09     |Invalid keyword                                             |SQL Server     |N       |SQL Server                                                                  |
|02000    |02   |no data                                           |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle                               |
|02001    |02   |no data                                           |001     |no additional result sets returned                          |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|02502    |02   |No Data                                           |502     |Delete or update hole detected.                             |DB2            |N       |DB2                                                                         |
|02504    |02   |No Data                                           |504     |FETCH PRIOR ROWSET returned a partial rowset.               |DB2            |N       |DB2                                                                         |
|03000    |03   |SQL Statement Not Yet Complete                    |000     |sql_statement_not_yet_complete                              |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|07000    |07   |dynamic SQL error                                 |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation Oracle                                                       |
|07001    |07   |dynamic SQL error                                 |001     |using clause does not match dynamic parameter specifications|SQL/Foundation |Y       |SQL/Foundation DB2 Oracle SQL Server                                        |
|07002    |07   |dynamic SQL error                                 |002     |using clause does not match target specifications           |SQL/Foundation |Y       |SQL/Foundation DB2 Oracle SQL Server                                        |
|07003    |07   |dynamic SQL error                                 |003     |cursor specification cannot be executed                     |SQL/Foundation |Y       |SQL/Foundation DB2 Oracle                                                   |
|07004    |07   |dynamic SQL error                                 |004     |using clause required for dynamic parameters                |SQL/Foundation |Y       |SQL/Foundation Oracle                                                       |
|07005    |07   |dynamic SQL error                                 |005     |prepared statement not a cursor specification               |SQL/Foundation |Y       |SQL/Foundation DB2 Oracle SQL Server                                        |
|07006    |07   |dynamic SQL error                                 |006     |restricted data type attribute violation                    |SQL/Foundation |Y       |SQL/Foundation Oracle SQL Server                                            |
|07007    |07   |dynamic SQL error                                 |007     |using clause required for result fields                     |SQL/Foundation |Y       |SQL/Foundation Oracle                                                       |
|07008    |07   |dynamic SQL error                                 |008     |invalid descriptor count                                    |SQL/Foundation |Y       |SQL/Foundation Oracle                                                       |
|07009    |07   |dynamic SQL error                                 |009     |invalid descriptor index                                    |SQL/Foundation |Y       |SQL/Foundation Oracle SQL Server                                            |
|0700B    |07   |dynamic SQL error                                 |00B     |data type transform function violation                      |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0700C    |07   |dynamic SQL error                                 |00C     |undefined DATA value                                        |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0700D    |07   |dynamic SQL error                                 |00D     |invalid DATA target                                         |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0700E    |07   |dynamic SQL error                                 |00E     |invalid LEVEL value                                         |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0700F    |07   |dynamic SQL error                                 |00F     |invalid DATETIME_INTERVAL_CODE                              |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0700G    |07   |dynamic SQL error                                 |00G     |invalid pass-through surrogate value                        |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0700H    |07   |dynamic SQL error                                 |00H     |PIPE ROW not during PTF execution                           |SQL/Foundation |Y       |SQL/Foundation                                                              |
|07501    |07   |Dynamic SQL Error                                 |501     |The option specified on PREPARE or EXECUTE is not valid.    |DB2            |N       |DB2                                                                         |
|07S01    |07   |dynamic SQL error                                 |S01     |Invalid use of default parameter                            |SQL Server     |N       |SQL Server                                                                  |
|08000    |08   |connection exception                              |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|08001    |08   |connection exception                              |001     |SQL-client unable to establish SQL-connection               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|08002    |08   |connection exception                              |002     |connection name in use                                      |SQL/Foundation |Y       |SQL/Foundation DB2 Oracle SQL Server                                        |
|08003    |08   |connection exception                              |003     |connection does not exist                                   |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|08004    |08   |connection exception                              |004     |SQL-server rejected establishment of SQL-connection         |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|08006    |08   |connection exception                              |006     |connection failure                                          |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|08007    |08   |connection exception                              |007     |transaction resolution unknown                              |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|08P01    |08   |Connection Exception                              |P01     |protocol_violation                                          |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|08S01    |08   |connection exception                              |S01     |Communication link failure                                  |SQL Server     |N       |SQL Server                                                                  |
|09000    |09   |triggered action exception                        |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|0A000    |0A   |feature not supported                             |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Redshift Oracle                          |
|0A001    |0A   |feature not supported                             |001     |multiple server transactions                                |SQL/Foundation |Y       |SQL/Foundation DB2 Oracle                                                   |
|0AKD0    |0A   |Cross catalog or schema operation not supported   |KD0     |Renaming a <type> across schemas is not allowed.            |Spark          |Y       |Spark                                                                       |
|0B000    |0B   |Invalid Transaction Initiation                    |000     |invalid_transaction_initiation                              |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|0D000    |0D   |invalid target type specification                 |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0E000    |0E   |invalid schema name list specification            |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0F000    |0F   |locator exception                                 |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|0F001    |0F   |locator exception                                 |001     |invalid specification                                       |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|0K000    |0K   |resignal when handler not active                  |000     |(no subclass)                                               |SQL/PSM        |Y       |SQL/PSM DB2                                                                 |
|0L000    |0L   |invalid grantor                                   |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|0LP01    |0L   |Invalid Grantor                                   |P01     |invalid_grant_operation                                     |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|0M000    |0M   |invalid SQL-invoked procedure reference           |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0N000    |0N   |SQL/XML mapping error                             |000     |(no subclass)                                               |SQL/XML        |Y       |SQL/XML                                                                     |
|0N001    |0N   |SQL/XML mapping error                             |001     |unmappable XML Name                                         |SQL/XML        |Y       |SQL/XML                                                                     |
|0N002    |0N   |SQL/XML mapping error                             |002     |invalid XML character                                       |SQL/XML        |Y       |SQL/XML DB2                                                                 |
|0P000    |0P   |invalid role specification                        |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|0S000    |0S   |invalid transform group name specification        |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0T000    |0T   |target table disagrees with cursor specification  |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0U000    |0U   |attempt to assign to non-updatable column         |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0V000    |0V   |attempt to assign to ordering column              |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0W000    |0W   |prohibited statement encountered during trigger execution|000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0W001    |0W   |prohibited statement encountered during trigger execution|001     |modify table modified by data change delta table            |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0X000    |0X   |invalid foreign server specification              |000     |(no subclass)                                               |SQL/MED        |Y       |SQL/MED                                                                     |
|0Y000    |0Y   |pass-through specific condition                   |000     |(no subclass)                                               |SQL/MED        |Y       |SQL/MED                                                                     |
|0Y001    |0Y   |pass-through specific condition                   |001     |invalid cursor option                                       |SQL/MED        |Y       |SQL/MED                                                                     |
|0Y002    |0Y   |pass-through specific condition                   |002     |invalid cursor allocation                                   |SQL/MED        |Y       |SQL/MED                                                                     |
|0Z000    |0Z   |diagnostics exception                             |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|0Z001    |0Z   |diagnostics exception                             |001     |maximum number of stacked diagnostics areas exceeded        |SQL/Foundation |Y       |SQL/Foundation                                                              |
|0Z002    |0Z   |diagnostics exception                             |002     |stacked diagnostics accessed without active handler         |SQL/PSM        |Y       |SQL/PSM PostgreSQL Redshift                                                 |
|10000    |10   |XQuery error                                      |000     |(no subclass)                                               |SQL/XML        |Y       |SQL/XML                                                                     |
|10501    |10   |XQuery Error                                      |501     |An XQuery expression is missing the assignment of a static or dynamic context component.|DB2            |N       |DB2                                                                         |
|10502    |10   |XQuery Error                                      |502     |An error was encountered in the prolog of an XQuery expression.|DB2            |N       |DB2                                                                         |
|10503    |10   |XQuery Error                                      |503     |A duplicate name was defined in an XQuery or XPath expression.|DB2            |N       |DB2                                                                         |
|10504    |10   |XQuery Error                                      |504     |An XQuery namespace declaration specified an invalid URI.   |DB2            |N       |DB2                                                                         |
|10505    |10   |XQuery Error                                      |505     |A character, token or clause is missing or invalid in an XQuery expression.|DB2            |N       |DB2                                                                         |
|10506    |10   |XQuery Error                                      |506     |An XQuery expression references a name that is not defined. |DB2            |N       |DB2                                                                         |
|10507    |10   |XQuery Error                                      |507     |A type error was encountered processing an XPath or XQuery expression.|DB2            |N       |DB2                                                                         |
|10509    |10   |XQuery Error                                      |509     |An unsupported XQuery language feature is specified.        |DB2            |N       |DB2                                                                         |
|10601    |10   |XQuery Error                                      |601     |An arithmetic error was encountered processing an XQuery function or operator.|DB2            |N       |DB2                                                                         |
|10602    |10   |XQuery Error                                      |602     |A casting error was encountered processing an XQuery function or operator.|DB2            |N       |DB2                                                                         |
|10606    |10   |XQuery Error                                      |606     |There is no context item for processing an XQuery function or operator.|DB2            |N       |DB2                                                                         |
|10608    |10   |XQuery Error                                      |608     |An error was encountered in the argument of an XQuery function or operator.|DB2            |N       |DB2                                                                         |
|10609    |10   |XQuery Error                                      |609     |A regular expression error was encountered processing an XQuery function or operator.|DB2            |N       |DB2                                                                         |
|10703    |10   |XQuery Error                                      |703     |The target node of an XQuery basic updating expression is not valid.|DB2            |N       |DB2                                                                         |
|11000    |11   |prohibited column reference encountered during trigger execution|000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|20000    |20   |case not found for case statement                 |000     |(no subclass)                                               |SQL/PSM        |Y       |SQL/PSM PostgreSQL DB2 Redshift                                             |
|21000    |21   |cardinality violation                             |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle                               |
|21501    |21   |Cardinality Violation                             |501     |A multiple-row INSERT into a self-referencing table is invalid.|DB2            |N       |DB2                                                                         |
|21502    |21   |Cardinality Violation                             |502     |A multiple-row UPDATE of a primary key is invalid.          |DB2            |N       |DB2                                                                         |
|21S01    |21   |cardinality violation                             |S01     |Insert value list does not match column list                |SQL Server     |N       |SQL Server                                                                  |
|21S02    |21   |cardinality violation                             |S02     |Degree of derived table does not match column list          |SQL Server     |N       |SQL Server                                                                  |
|22000    |22   |data exception                                    |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|22001    |22   |data exception                                    |001     |string data, right truncation                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|22002    |22   |data exception                                    |002     |null value, no indicator parameter                          |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|22003    |22   |data exception                                    |003     |numeric value out of range                                  |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|22004    |22   |data exception                                    |004     |null value not allowed                                      |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|22005    |22   |data exception                                    |005     |error in assignment                                         |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|22006    |22   |data exception                                    |006     |invalid interval format                                     |SQL/Foundation |Y       |SQL/Foundation                                                              |
|22007    |22   |data exception                                    |007     |invalid datetime format                                     |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|22008    |22   |data exception                                    |008     |datetime field overflow                                     |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|22009    |22   |data exception                                    |009     |invalid time zone displacement value                        |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|2200B    |22   |data exception                                    |00B     |escape character conflict                                   |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2200C    |22   |data exception                                    |00C     |invalid use of escape character                             |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2200D    |22   |data exception                                    |00D     |invalid escape octet                                        |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2200E    |22   |data exception                                    |00E     |null value in array target                                  |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2200F    |22   |data exception                                    |00F     |zero-length character string                                |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2200G    |22   |data exception                                    |00G     |most specific type mismatch                                 |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2200H    |22   |data exception                                    |00H     |sequence generator limit exceeded                           |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2200J    |22   |data exception                                    |00J     |nonidentical notations with the same name                   |SQL/XML        |Y       |SQL/XML                                                                     |
|2200K    |22   |data exception                                    |00K     |nonidentical unparsed entities with the same name           |SQL/XML        |Y       |SQL/XML                                                                     |
|2200L    |22   |data exception                                    |00L     |not an XML document                                         |SQL/XML        |Y       |SQL/XML PostgreSQL DB2                                                      |
|2200M    |22   |data exception                                    |00M     |invalid XML document                                        |SQL/XML        |Y       |SQL/XML PostgreSQL DB2                                                      |
|2200N    |22   |data exception                                    |00N     |invalid XML content                                         |SQL/XML        |Y       |SQL/XML PostgreSQL                                                          |
|2200P    |22   |data exception                                    |00P     |interval value out of range                                 |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2200Q    |22   |data exception                                    |00Q     |multiset value overflow                                     |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2200R    |22   |data exception                                    |00R     |XML value overflow                                          |SQL/XML        |Y       |SQL/XML                                                                     |
|2200S    |22   |data exception                                    |00S     |invalid comment                                             |SQL/XML        |Y       |SQL/XML PostgreSQL DB2                                                      |
|2200T    |22   |data exception                                    |00T     |invalid processing instruction                              |SQL/XML        |Y       |SQL/XML PostgreSQL DB2                                                      |
|2200U    |22   |data exception                                    |00U     |not an XQuery document node                                 |SQL/XML        |Y       |SQL/XML                                                                     |
|2200V    |22   |data exception                                    |00V     |invalid XQuery context item                                 |SQL/XML        |Y       |SQL/XML DB2                                                                 |
|2200W    |22   |data exception                                    |00W     |XQuery serialization error                                  |SQL/XML        |Y       |SQL/XML DB2                                                                 |
|22010    |22   |data exception                                    |010     |invalid indicator parameter value                           |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|22011    |22   |data exception                                    |011     |substring error                                             |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle                               |
|22012    |22   |data exception                                    |012     |division by zero                                            |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|22013    |22   |data exception                                    |013     |invalid preceding or following size in window function      |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|22014    |22   |data exception                                    |014     |invalid argument for NTILE function                         |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2                                               |
|22015    |22   |data exception                                    |015     |interval field overflow                                     |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|22016    |22   |data exception                                    |016     |invalid argument for NTH_VALUE function                     |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2                                               |
|22017    |22   |data exception                                    |017     |invalid data specified for datalink                         |SQL/MED        |Y       |SQL/MED                                                                     |
|22018    |22   |data exception                                    |018     |invalid character value for cast                            |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|22019    |22   |data exception                                    |019     |invalid escape character                                    |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|2201A    |22   |data exception                                    |01A     |null argument passed to datalink constructor                |SQL/MED        |Y       |SQL/MED                                                                     |
|2201B    |22   |data exception                                    |01B     |invalid regular expression                                  |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2201C    |22   |data exception                                    |01C     |null row not permitted in table                             |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2201D    |22   |data exception                                    |01D     |datalink value exceeds maximum length                       |SQL/MED        |Y       |SQL/MED                                                                     |
|2201E    |22   |data exception                                    |01E     |invalid argument for natural logarithm                      |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2201F    |22   |data exception                                    |01F     |invalid argument for power function                         |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2201G    |22   |data exception                                    |01G     |invalid argument for width bucket function                  |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2201H    |22   |data exception                                    |01H     |invalid row version                                         |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2201J    |22   |data exception                                    |01J     |XQuery sequence cannot be vali- dated                       |SQL/XML        |Y       |SQL/XML                                                                     |
|2201K    |22   |data exception                                    |01K     |XQuery document node cannot be validated                    |SQL/XML        |Y       |SQL/XML                                                                     |
|2201L    |22   |data exception                                    |01L     |no XML schema found                                         |SQL/XML        |Y       |SQL/XML                                                                     |
|2201M    |22   |data exception                                    |01M     |element namespace not declared                              |SQL/XML        |Y       |SQL/XML                                                                     |
|2201N    |22   |data exception                                    |01N     |global element not declared                                 |SQL/XML        |Y       |SQL/XML                                                                     |
|2201P    |22   |data exception                                    |01P     |no XML element with the specified QName                     |SQL/XML        |Y       |SQL/XML                                                                     |
|2201Q    |22   |data exception                                    |01Q     |no XML element with the specified namespace                 |SQL/XML        |Y       |SQL/XML                                                                     |
|2201R    |22   |data exception                                    |01R     |validation failure                                          |SQL/XML        |Y       |SQL/XML DB2                                                                 |
|2201S    |22   |data exception                                    |01S     |invalid XQuery regular expression                           |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2201T    |22   |data exception                                    |01T     |invalid XQuery option flag                                  |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2201U    |22   |data exception                                    |01U     |attempt to replace a zero-length string                     |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2201V    |22   |data exception                                    |01V     |invalid XQuery replacement string                           |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2201W    |22   |data exception                                    |01W     |invalid row count in fetch first clause                     |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2201X    |22   |data exception                                    |01X     |invalid row count in result offset clause                   |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2201Y    |22   |data exception                                    |01Y     |zero-length binary string                                   |SQL/Foundation |Y       |SQL/Foundation                                                              |
|22020    |22   |data exception                                    |020     |invalid period value                                        |SQL/Foundation |Y       |SQL/Foundation                                                              |
|22021    |22   |data exception                                    |021     |character not in repertoire                                 |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle                               |
|22022    |22   |data exception                                    |022     |indicator overflow                                          |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|22023    |22   |data exception                                    |023     |invalid parameter value                                     |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle                               |
|22024    |22   |data exception                                    |024     |unterminated C string                                       |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle                               |
|22025    |22   |data exception                                    |025     |invalid escape sequence                                     |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|22026    |22   |data exception                                    |026     |string data, length mismatch                                |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|22027    |22   |data exception                                    |027     |trim error                                                  |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|22029    |22   |data exception                                    |029     |noncharacter in UCS string                                  |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2202A    |22   |data exception                                    |02A     |null value in field reference                               |SQL/PSM        |Y       |SQL/PSM                                                                     |
|2202D    |22   |data exception                                    |02D     |null value substituted for mutator subject parameter        |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2202E    |22   |data exception                                    |02E     |array element error                                         |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2202F    |22   |data exception                                    |02F     |array data, right truncation                                |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2202G    |22   |data exception                                    |02G     |invalid repeat argument in a sample clause                  |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2202H    |22   |data exception                                    |02H     |invalid sample size                                         |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2202J    |22   |data exception                                    |02J     |invalid argument for row pattern navigation operation       |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2202K    |22   |data exception                                    |02K     |skip to non-existent row                                    |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2202L    |22   |data exception                                    |02L     |skip to first row of match                                  |SQL/Foundation |Y       |SQL/Foundation                                                              |
|22030    |22   |data exception                                    |030     |duplicate JSON object key value                             |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|22031    |22   |data exception                                    |031     |invalid argument for SQL/JSON datetime function             |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|22032    |22   |data exception                                    |032     |invalid JSON text                                           |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|22033    |22   |data exception                                    |033     |invalid SQL/JSON subscript                                  |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|22034    |22   |data exception                                    |034     |more than one SQL/JSON item                                 |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|22035    |22   |data exception                                    |035     |no SQL/JSON item                                            |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|22036    |22   |data exception                                    |036     |non-numeric SQL/JSON item                                   |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|22037    |22   |data exception                                    |037     |non-unique keys in a JSON object                            |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|22038    |22   |data exception                                    |038     |singleton SQL/JSON item required                            |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|22039    |22   |data exception                                    |039     |SQL/JSON array not found                                    |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2203A    |22   |data exception                                    |03A     |SQL/JSON member not found                                   |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2203B    |22   |data exception                                    |03B     |SQL/JSON number not found                                   |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2203C    |22   |data exception                                    |03C     |SQL/JSON object not found                                   |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2203D    |22   |data exception                                    |03D     |too many JSON array elements                                |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2203E    |22   |data exception                                    |03E     |too many JSON object members                                |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2203F    |22   |data exception                                    |03F     |SQL/JSON scalar required                                    |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|2203G    |22   |Data Exception                                    |03G     |sql_json_item_cannot_be_cast_to_target_type                 |PostgreSQL     |N       |PostgreSQL                                                                  |
|2203J    |22   |data exception                                    |03J     |MD-array null limit in subset                               |SQL/MD         |Y       |SQL/MD                                                                      |
|2203K    |22   |data exception                                    |03K     |MD-array null limit in MD-extent                            |SQL/MD         |Y       |SQL/MD                                                                      |
|2203L    |22   |data exception                                    |03L     |MD-array subset not within MD- extent                       |SQL/MD         |Y       |SQL/MD                                                                      |
|2203M    |22   |data exception                                    |03M     |MD-array duplicate coordinate in query constructor          |SQL/MD         |Y       |SQL/MD                                                                      |
|2203N    |22   |data exception                                    |03N     |MD-array null coordinate in query constructor               |SQL/MD         |Y       |SQL/MD                                                                      |
|2203P    |22   |data exception                                    |03P     |MD-array coordinate not within specified MD-extent          |SQL/MD         |Y       |SQL/MD                                                                      |
|2203Q    |22   |data exception                                    |03Q     |MD-array source MD-extent not strictly within maximum target MD-extent|SQL/MD         |Y       |SQL/MD                                                                      |
|2203R    |22   |data exception                                    |03R     |MD-array operands with non- matching MD-extents             |SQL/MD         |Y       |SQL/MD                                                                      |
|2203T    |22   |data exception                                    |03T     |MD-array invalid MD-axis                                    |SQL/MD         |Y       |SQL/MD                                                                      |
|2203U    |22   |data exception                                    |03U     |MD-array lower limit greater than upper limit               |SQL/MD         |Y       |SQL/MD                                                                      |
|2203V    |22   |data exception                                    |03V     |MD-array axis name not unique in MD-extent                  |SQL/MD         |Y       |SQL/MD                                                                      |
|2203X    |22   |data exception                                    |03X     |MD-array element error                                      |SQL/MD         |Y       |SQL/MD                                                                      |
|2203Y    |22   |data exception                                    |03Y     |MD-array decoding error                                     |SQL/MD         |Y       |SQL/MD                                                                      |
|2203Z    |22   |data exception                                    |03Z     |MD-array encoding error                                     |SQL/MD         |Y       |SQL/MD                                                                      |
|22040    |22   |data exception                                    |040     |MD-array element reference not within MD-extent             |SQL/MD         |Y       |SQL/MD                                                                      |
|22041    |22   |data exception                                    |041     |MD-array null value in MD-array target                      |SQL/MD         |Y       |SQL/MD                                                                      |
|22042    |22   |data exception                                    |042     |MD-array source MD-extent not strictly within target MD-extent|SQL/MD         |Y       |SQL/MD                                                                      |
|22043    |22   |data exception                                    |043     |MD-array target MD-extent not strictly within maximum MD extent|SQL/MD         |Y       |SQL/MD                                                                      |
|22044    |22   |data exception                                    |044     |MD-array limit in MD-extent out of bounds                   |SQL/MD         |Y       |SQL/MD                                                                      |
|22501    |22   |Data Exception                                    |501     |The length control field of a variable length string is negative or greater than the maximum.|DB2            |N       |DB2                                                                         |
|22502    |22   |Data Exception                                    |502     |Signalling NaN was encountered.                             |DB2            |N       |DB2                                                                         |
|22503    |22   |Data Exception                                    |503     |The string representation of a name is invalid.             |DB2            |N       |DB2                                                                         |
|22504    |22   |Data Exception                                    |504     |A mixed data value is invalid.                              |DB2            |N       |DB2                                                                         |
|22505    |22   |Data Exception                                    |505     |The local date or time length has been increased, but the executing program relies on the old length.|DB2            |N       |DB2                                                                         |
|22506    |22   |Data Exception                                    |506     |A reference to a datetime special register is invalid, because the clock is malfunctioning or the operating system time zone parameter is out of range.|DB2            |N       |DB2                                                                         |
|22508    |22   |Data Exception                                    |508     |CURRENT PACKAGESET is blank.                                |DB2            |N       |DB2                                                                         |
|22511    |22   |Data Exception                                    |511     |The value for a ROWID or reference column is not valid.     |DB2            |N       |DB2                                                                         |
|22512    |22   |Data Exception                                    |512     |A variable in a predicate is invalid, because its indicator variable is negative.|DB2            |N       |DB2                                                                         |
|22522    |22   |Data Exception                                    |522     |A CCSID value is not valid at all, not valid for the data type or subtype, or not valid for the encoding scheme.|DB2            |N       |DB2                                                                         |
|22525    |22   |Data Exception                                    |525     |Partitioning key value is not valid.                        |DB2            |N       |DB2                                                                         |
|22527    |22   |Data Exception                                    |527     |Invalid input data detected for a multiple-row insert.      |DB2            |N       |DB2                                                                         |
|22528    |22   |Data Exception                                    |528     |Binary data is invalid for DECRYPT_CHAR and DECYRYPT_DB.    |DB2            |N       |DB2                                                                         |
|22529    |22   |Data Exception                                    |529     |A non-atomic statement successfully completed for at least one row, but one or more errors occurred.|DB2            |N       |DB2                                                                         |
|22530    |22   |Data Exception                                    |530     |A non-atomic statement attempted to process multiple rows of data, but no row was inserted and one or more errors occurred.|DB2            |N       |DB2                                                                         |
|22531    |22   |Data Exception                                    |531     |The argument of a built-in or system provided routine resulted in an error.|DB2            |N       |DB2                                                                         |
|22532    |22   |Data Exception                                    |532     |An XSROBJECT is not found in the XML schema repository.     |DB2            |N       |DB2                                                                         |
|22533    |22   |Data Exception                                    |533     |A unique XSROBJECT could not be found in the XML schema repository.|DB2            |N       |DB2                                                                         |
|22534    |22   |Data Exception                                    |534     |An XML schema document is not connected to the other XML schema documents using an include or redefine.|DB2            |N       |DB2                                                                         |
|22537    |22   |Data Exception                                    |537     |A rowset FETCH statement returned one or more rows of data, with one or more non-terminating error conditions. Use GET DIAGNOSTICS for more information.|DB2            |N       |DB2                                                                         |
|22539    |22   |Data Exception                                    |539     |Invalid use of extended indicator parameter value.          |DB2            |N       |DB2                                                                         |
|22541    |22   |Data Exception                                    |541     |The binary XML value contains unrecognized data.            |DB2            |N       |DB2                                                                         |
|22542    |22   |Data Exception                                    |542     |The INSERT or UPDATE in not allowed because a resulting row does not satisfy row permissions.|DB2            |N       |DB2                                                                         |
|22544    |22   |Data Exception                                    |544     |The binary XML value contains a version that is not supported.|DB2            |N       |DB2                                                                         |
|22546    |22   |Data Exception                                    |546     |The value for a routine argument is not valid.              |DB2            |N       |DB2                                                                         |
|22547    |22   |Data Exception                                    |547     |Multiple result values cannot be returned from the scalar function.|DB2            |N       |DB2                                                                         |
|225DE    |22   |Data Exception                                    |5DE     |An XML schema cannot be enabled for decomposition.          |DB2            |N       |DB2                                                                         |
|22P01    |22   |Data Exception                                    |P01     |floating_point_exception                                    |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|22P02    |22   |Data Exception                                    |P02     |invalid_text_representation                                 |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|22P03    |22   |Data Exception                                    |P03     |invalid_binary_representation                               |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|22P04    |22   |Data Exception                                    |P04     |bad_copy_file_format                                        |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|22P05    |22   |Data Exception                                    |P05     |untranslatable_character                                    |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|22P06    |22   |Data Exception                                    |P06     |nonstandard_use_of_escape_character                         |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|23000    |23   |integrity constraint violation                    |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|23001    |23   |integrity constraint violation                    |001     |restrict violation                                          |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|23502    |23   |Constraint Violation                              |502     |An insert or update value is null, but the column cannot contain null values.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|23503    |23   |Constraint Violation                              |503     |The insert or update value of a foreign key is invalid.     |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|23504    |23   |Constraint Violation                              |504     |The update or delete of a parent key is prevented by a NO ACTION update or delete rule.|DB2            |N       |DB2                                                                         |
|23505    |23   |Constraint Violation                              |505     |A violation of the constraint imposed by a unique index or a unique constraint occurred.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|23506    |23   |Constraint Violation                              |506     |A violation of a constraint imposed by an edit or validation procedure occurred.|DB2            |N       |DB2                                                                         |
|23507    |23   |Constraint Violation                              |507     |A violation of a constraint imposed by a field procedure occurred.|DB2            |N       |DB2                                                                         |
|23508    |23   |Constraint Violation                              |508     |A violation of a constraint imposed by the DDL Registration Facility occurred.|DB2            |N       |DB2                                                                         |
|23509    |23   |Constraint Violation                              |509     |The owner of the package has constrained its use to environments which do not include that of the application process.|DB2            |N       |DB2                                                                         |
|23510    |23   |Constraint Violation                              |510     |A violation of a constraint on the use of the command imposed by the RLST table occurred.|DB2            |N       |DB2                                                                         |
|23511    |23   |Constraint Violation                              |511     |A parent row cannot be deleted, because the check constraint restricts the deletion.|DB2            |N       |DB2                                                                         |
|23512    |23   |Constraint Violation                              |512     |The check constraint cannot be added, because the table contains rows that do not satisfy the constraint definition.|DB2            |N       |DB2                                                                         |
|23513    |23   |Constraint Violation                              |513     |The resulting row of the INSERT or UPDATE does not conform to the check constraint definition.|DB2            |N       |DB2                                                                         |
|23514    |23   |Integrity Constraint Violation                    |514     |check_violation                                             |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|23515    |23   |Constraint Violation                              |515     |The unique index could not be created or unique constraint added, because the table contains duplicate values of the specified key.|DB2            |N       |DB2                                                                         |
|23522    |23   |Constraint Violation                              |522     |The range of values for the identity column or sequence is exhausted.|DB2            |N       |DB2                                                                         |
|23523    |23   |Constraint Violation                              |523     |An invalid value has been provided for the SECURITY LABEL column.|DB2            |N       |DB2                                                                         |
|23525    |23   |Constraint Violation                              |525     |A violation of a constraint imposed by an XML values index occurred.|DB2            |N       |DB2                                                                         |
|23526    |23   |Constraint Violation                              |526     |An XML values index could not be created because the table data contains values that violate a constraint imposed by the index.|DB2            |N       |DB2                                                                         |
|23P01    |23   |Integrity Constraint Violation                    |P01     |exclusion_violation                                         |PostgreSQL     |N       |PostgreSQL                                                                  |
|24000    |24   |invalid cursor state                              |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|24501    |24   |Invalid Cursor State                              |501     |The identified cursor is not open.                          |DB2            |N       |DB2                                                                         |
|24502    |24   |Invalid Cursor State                              |502     |The cursor identified in an OPEN statement is already open. |DB2            |N       |DB2                                                                         |
|24504    |24   |Invalid Cursor State                              |504     |The cursor identified in the UPDATE, DELETE, SET, or GET statement is not positioned on a row.|DB2            |N       |DB2                                                                         |
|24506    |24   |Invalid Cursor State                              |506     |The statement identified in the PREPARE is the statement of an open cursor.|DB2            |N       |DB2                                                                         |
|24510    |24   |Invalid Cursor State                              |510     |An UPDATE or DELETE operation was attempted against a delete or update hole|DB2            |N       |DB2                                                                         |
|24512    |24   |Invalid Cursor State                              |512     |The result table does not agree with the base table.        |DB2            |N       |DB2                                                                         |
|24513    |24   |Invalid Cursor State                              |513     |FETCH NEXT, PRIOR, CURRENT, or RELATIVE is not allowed, because the cursor position is not known.|DB2            |N       |DB2                                                                         |
|24516    |24   |Invalid Cursor State                              |516     |A cursor has already been assigned to a result set.         |DB2            |N       |DB2                                                                         |
|24517    |24   |Invalid Cursor State                              |517     |A cursor was left open by a function or method.             |DB2            |N       |DB2                                                                         |
|24518    |24   |Invalid Cursor State                              |518     |A cursor is not defined to handle row sets, but a rowset was requested.|DB2            |N       |DB2                                                                         |
|24519    |24   |Invalid Cursor State                              |519     |A hole was detected on a multiple-row FETCH statement, but indicator variables were not provided.|DB2            |N       |DB2                                                                         |
|24520    |24   |Invalid Cursor State                              |520     |The cursor identified in the UPDATE or DELETE statement is not positioned on a rowset.|DB2            |N       |DB2                                                                         |
|24521    |24   |Invalid Cursor State                              |521     |A positioned DELETE or UPDATE statement specified a row of a rowset, but the row is not contained within the current rowset.|DB2            |N       |DB2                                                                         |
|24522    |24   |Invalid Cursor State                              |522     |The fetch orientation is inconsistent with the definition of the cursor and whether rowsets are supported for the cursor.|DB2            |N       |DB2                                                                         |
|24524    |24   |Invalid Cursor State                              |524     |A FETCH CURRENT CONTINUE was requested, but there is no truncated LOB or XML data to return.|DB2            |N       |DB2                                                                         |
|25000    |25   |invalid transaction state                         |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|25001    |25   |invalid transaction state                         |001     |active SQL-transaction                                      |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|25002    |25   |invalid transaction state                         |002     |branch transaction already active                           |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|25003    |25   |invalid transaction state                         |003     |inappropriate access mode for branch transaction            |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|25004    |25   |invalid transaction state                         |004     |inappropriate isolation level for branch transaction        |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|25005    |25   |invalid transaction state                         |005     |no active SQL-transaction for branch transaction            |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|25006    |25   |invalid transaction state                         |006     |read-only SQL-transaction                                   |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|25007    |25   |invalid transaction state                         |007     |schema and data statement mixing not supported              |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|25008    |25   |invalid transaction state                         |008     |held cursor requires same isolation level                   |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|25P01    |25   |Invalid Transaction State                         |P01     |no_active_sql_transaction                                   |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|25P02    |25   |Invalid Transaction State                         |P02     |in_failed_sql_transaction                                   |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|25P03    |25   |Invalid Transaction State                         |P03     |idle_in_transaction_session_timeout                         |PostgreSQL     |N       |PostgreSQL                                                                  |
|25S01    |25   |invalid transaction state                         |S01     |Transaction state                                           |SQL Server     |N       |SQL Server                                                                  |
|25S02    |25   |invalid transaction state                         |S02     |Transaction is still active                                 |SQL Server     |N       |SQL Server                                                                  |
|25S03    |25   |invalid transaction state                         |S03     |Transaction is rolled back                                  |SQL Server     |N       |SQL Server                                                                  |
|26000    |26   |invalid SQL statement name                        |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|26501    |26   |Invalid SQL Statement Identifier                  |501     |The statement identified does not exist.                    |DB2            |N       |DB2                                                                         |
|27000    |27   |triggered data change violation                   |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Oracle                                            |
|27001    |27   |triggered data change violation                   |001     |modify table modified by data change delta table            |SQL/Foundation |Y       |SQL/Foundation                                                              |
|28000    |28   |invalid authorization specification               |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|28P01    |28   |Invalid Authorization Specification               |P01     |invalid_password                                            |PostgreSQL     |N       |PostgreSQL                                                                  |
|2A000    |2A   |direct SQL syntax error or access rule violation  |000     |direct SQL syntax error or access rule violation            |Oracle         |N       |Oracle                                                                      |
|2B000    |2B   |dependent privilege descriptors still exist       |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|2BP01    |2B   |Dependent Privilege Descriptors Still Exist       |P01     |dependent_objects_still_exist                               |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|2C000    |2C   |invalid character set name                        |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation Oracle                                                       |
|2C001    |2C   |invalid character set name                        |001     |cannot drop SQL-session default character set               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|2D000    |2D   |invalid transaction termination                   |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle                                   |
|2D521    |2D   |Invalid Transaction Termination                   |521     |SQL COMMIT or ROLLBACK are invalid in the current operating environment.|DB2            |N       |DB2                                                                         |
|2D528    |2D   |Invalid Transaction Termination                   |528     |Dynamic COMMIT or COMMIT ON RETURN procedure is invalid for the application execution environment|DB2            |N       |DB2                                                                         |
|2D529    |2D   |Invalid Transaction Termination                   |529     |Dynamic ROLLBACK is invalid for the application execution environment.|DB2            |N       |DB2                                                                         |
|2E000    |2E   |invalid connection name                           |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation Oracle                                                       |
|2F000    |2F   |SQL routine exception                             |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2F002    |2F   |SQL routine exception                             |002     |modifying SQL-data not permitted                            |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2F003    |2F   |SQL routine exception                             |003     |prohibited SQL-statement attempted                          |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2F004    |2F   |SQL routine exception                             |004     |reading SQL-data not permitted                              |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2F005    |2F   |SQL routine exception                             |005     |function executed no return statement                       |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|2H000    |2H   |invalid collation name                            |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|30000    |30   |invalid SQL statement identifier                  |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|33000    |33   |invalid SQL descriptor name                       |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation Oracle                                                       |
|34000    |34   |invalid cursor name                               |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|35000    |35   |invalid condition number                          |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation DB2 Oracle                                                   |
|36000    |36   |cursor sensitivity exception                      |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation                                                              |
|36001    |36   |cursor sensitivity exception                      |001     |request rejected                                            |SQL/Foundation |Y       |SQL/Foundation DB2                                                          |
|36002    |36   |cursor sensitivity exception                      |002     |request failed                                              |SQL/Foundation |Y       |SQL/Foundation                                                              |
|37000    |37   |dynamic SQL syntax error or access rule violation |000     |dynamic SQL syntax error or access rule violation           |Oracle         |N       |Oracle                                                                      |
|38000    |38   |external routine exception                        |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|38001    |38   |external routine exception                        |001     |containing SQL not permitted                                |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|38002    |38   |external routine exception                        |002     |modifying SQL-data not permitted                            |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|38003    |38   |external routine exception                        |003     |prohibited SQL-statement attempted                          |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|38004    |38   |external routine exception                        |004     |reading SQL-data not permitted                              |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|38503    |38   |External Function Exception                       |503     |A user-defined function or procedure has abnormally terminated (abend).|DB2            |N       |DB2                                                                         |
|38504    |38   |External Function Exception                       |504     |A routine, trigger, or anonymous block has been interrupted by the user.|DB2            |N       |DB2                                                                         |
|38505    |38   |External Function Exception                       |505     |An SQL statement is not allowed in a routine on a FINAL CALL.|DB2            |N       |DB2                                                                         |
|38H01    |38   |External Function Exception                       |H01     |An IBM® MQ function failed to initialize.                   |DB2            |N       |DB2                                                                         |
|38H02    |38   |External Function Exception                       |H02     |IBM MQ Application Messaging Interface failed to terminate the session.|DB2            |N       |DB2                                                                         |
|38H03    |38   |External Function Exception                       |H03     |IBM MQ Application Messaging Interface failed to properly process a message.|DB2            |N       |DB2                                                                         |
|38H04    |38   |External Function Exception                       |H04     |IBM MQ Application Messaging Interface failed in sending a message.|DB2            |N       |DB2                                                                         |
|38H05    |38   |External Function Exception                       |H05     |IBM MQ Application Messaging Interface failed to read/receive a message.|DB2            |N       |DB2                                                                         |
|38H06    |38   |External Function Exception                       |H06     |An IBM MQ Application Messaging Interface message was truncated.|DB2            |N       |DB2                                                                         |
|38H10    |38   |External Function Exception                       |H10     |Error occurred during text search processing.               |DB2            |N       |DB2                                                                         |
|38H11    |38   |External Function Exception                       |H11     |Text search support is not available.                       |DB2            |N       |DB2                                                                         |
|38H12    |38   |External Function Exception                       |H12     |Text search is not allowed on a column because a text search index does not exist on the column.|DB2            |N       |DB2                                                                         |
|38H13    |38   |External Function Exception                       |H13     |A conflicting search administration procedure or command is running on the same text search index.|DB2            |N       |DB2                                                                         |
|38H14    |38   |External Function Exception                       |H14     |Text search administration procedure error.                 |DB2            |N       |DB2                                                                         |
|39000    |39   |external routine invocation exception             |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift                                          |
|39001    |39   |External Routine Invocation Exception             |001     |invalid_sqlstate_returned                                   |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|39004    |39   |external routine invocation exception             |004     |null value not allowed                                      |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift                                      |
|39501    |39   |External Function Call Exception                  |501     |An output argument value returned from a function or a procedure was too long.|DB2            |N       |DB2                                                                         |
|39P01    |39   |External Routine Invocation Exception             |P01     |trigger_protocol_violated                                   |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|39P02    |39   |External Routine Invocation Exception             |P02     |srf_protocol_violated                                       |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|39P03    |39   |External Routine Invocation Exception             |P03     |event_trigger_protocol_violated                             |PostgreSQL     |N       |PostgreSQL                                                                  |
|3B000    |3B   |savepoint exception                               |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL                                                   |
|3B001    |3B   |savepoint exception                               |001     |invalid specification                                       |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2                                               |
|3B002    |3B   |savepoint exception                               |002     |too many                                                    |SQL/Foundation |Y       |SQL/Foundation                                                              |
|3B501    |3B   |Savepoint Exception                               |501     |A duplicate savepoint name was detected.                    |DB2            |N       |DB2                                                                         |
|3B502    |3B   |Savepoint Exception                               |502     |A RELEASE or ROLLBACK TO SAVEPOINT was specified, but a savepoint does not exist.|DB2            |N       |DB2                                                                         |
|3B503    |3B   |Savepoint Exception                               |503     |A SAVEPOINT, RELEASE SAVEPOINT, or ROLLBACK TO SAVEPOINT is not allowed in a trigger, function, or global transaction.|DB2            |N       |DB2                                                                         |
|3C000    |3C   |ambiguous cursor name                             |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation DB2 Oracle SQL Server                                        |
|3D000    |3D   |invalid catalog name                              |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|3F000    |3F   |invalid schema name                               |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|40000    |40   |transaction rollback                              |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Oracle                                            |
|40001    |40   |transaction rollback                              |001     |serialization failure                                       |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Oracle SQL Server                             |
|40002    |40   |transaction rollback                              |002     |integrity constraint violation                              |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Oracle SQL Server                                 |
|40003    |40   |transaction rollback                              |003     |statement completion unknown                                |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Oracle SQL Server                                 |
|40004    |40   |transaction rollback                              |004     |triggered action exception                                  |SQL/Foundation |Y       |SQL/Foundation                                                              |
|40P01    |40   |Transaction Rollback                              |P01     |deadlock_detected                                           |PostgreSQL     |N       |PostgreSQL                                                                  |
|42000    |42   |syntax error or access rule violation             |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL Redshift Oracle SQL Server                        |
|42501    |42   |Syntax Error or Access Rule Violation             |501     |The authorization ID does not have the privilege to perform the specified operation on the identified object.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42502    |42   |Syntax Error or Access Rule Violation             |502     |The authorization ID does not have the privilege to perform the operation as specified.|DB2            |N       |DB2                                                                         |
|42503    |42   |Syntax Error or Access Rule Violation             |503     |The specified authorization ID or one of the authorization IDs of the application process is not allowed.|DB2            |N       |DB2                                                                         |
|42504    |42   |Syntax Error or Access Rule Violation             |504     |A specified privilege, security label, or exemption cannot be revoked from a specified authorization-name.|DB2            |N       |DB2                                                                         |
|42505    |42   |Syntax Error or Access Rule Violation             |505     |Connection authorization failure occurred.                  |DB2            |N       |DB2                                                                         |
|42506    |42   |Syntax Error or Access Rule Violation             |506     |Owner authorization failure occurred.                       |DB2            |N       |DB2                                                                         |
|42509    |42   |Syntax Error or Access Rule Violation             |509     |SQL statement is not authorized, because of the DYNAMICRULES option.|DB2            |N       |DB2                                                                         |
|42510    |42   |Syntax Error or Access Rule Violation             |510     |The authorization ID does not have the privilege to create functions or procedures in the WLM environment.|DB2            |N       |DB2                                                                         |
|42512    |42   |Syntax Error or Access Rule Violation             |512     |The authorization ID does not have security to the protected column.|DB2            |N       |DB2                                                                         |
|42513    |42   |Syntax Error or Access Rule Violation             |513     |The authorization ID does not have the MLS WRITE-DOWN privilege.|DB2            |N       |DB2                                                                         |
|42517    |42   |Syntax Error or Access Rule Violation             |517     |The specified authorization ID is not allowed to use the trusted context.|DB2            |N       |DB2                                                                         |
|42601    |42   |Syntax Error or Access Rule Violation             |601     |A character, token, or clause is invalid or missing.        |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42602    |42   |Syntax Error or Access Rule Violation             |602     |A character that is invalid in a name has been detected.    |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42603    |42   |Syntax Error or Access Rule Violation             |603     |An unterminated string constant has been detected.          |DB2            |N       |DB2                                                                         |
|42604    |42   |Syntax Error or Access Rule Violation             |604     |An invalid numeric or string constant has been detected.    |DB2            |N       |DB2                                                                         |
|42605    |42   |Syntax Error or Access Rule Violation             |605     |The number of arguments specified for a scalar function is invalid.|DB2            |N       |DB2                                                                         |
|42606    |42   |Syntax Error or Access Rule Violation             |606     |An invalid hexadecimal constant has been detected.          |DB2            |N       |DB2                                                                         |
|42607    |42   |Syntax Error or Access Rule Violation             |607     |An operand of an aggregate function or CONCAT operator is invalid.|DB2            |N       |DB2                                                                         |
|42608    |42   |Syntax Error or Access Rule Violation             |608     |The use of NULL or DEFAULT in VALUES or an assignment statement is invalid.|DB2            |N       |DB2                                                                         |
|42609    |42   |Syntax Error or Access Rule Violation             |609     |All operands of an operator or predicate are parameter markers.|DB2            |N       |DB2                                                                         |
|42610    |42   |Syntax Error or Access Rule Violation             |610     |A parameter marker or the null value is not allowed.        |DB2            |N       |DB2                                                                         |
|42611    |42   |Syntax Error or Access Rule Violation             |611     |The column, argument, parameter, or global variable definition is invalid.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42612    |42   |Syntax Error or Access Rule Violation             |612     |The statement string is an SQL statement that is not acceptable in the context in which it is presented.|DB2            |N       |DB2                                                                         |
|42613    |42   |Syntax Error or Access Rule Violation             |613     |Clauses are mutually exclusive.                             |DB2            |N       |DB2                                                                         |
|42614    |42   |Syntax Error or Access Rule Violation             |614     |A duplicate keyword or clause is invalid.                   |DB2            |N       |DB2                                                                         |
|42615    |42   |Syntax Error or Access Rule Violation             |615     |An invalid alternative was detected.                        |DB2            |N       |DB2                                                                         |
|42617    |42   |Syntax Error or Access Rule Violation             |617     |The statement string is blank or empty.                     |DB2            |N       |DB2                                                                         |
|42618    |42   |Syntax Error or Access Rule Violation             |618     |A variable is not allowed.                                  |DB2            |N       |DB2                                                                         |
|42620    |42   |Syntax Error or Access Rule Violation             |620     |Read-only SCROLL was specified with the UPDATE clause.      |DB2            |N       |DB2                                                                         |
|42621    |42   |Syntax Error or Access Rule Violation             |621     |The check constraint or generated column expression is invalid.|DB2            |N       |DB2                                                                         |
|42622    |42   |Syntax Error or Access Rule Violation             |622     |A name or label is too long.                                |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42623    |42   |Syntax Error or Access Rule Violation             |623     |A DEFAULT clause cannot be specified.                       |DB2            |N       |DB2                                                                         |
|42625    |42   |Syntax Error or Access Rule Violation             |625     |A CASE expression is invalid.                               |DB2            |N       |DB2                                                                         |
|42626    |42   |Syntax Error or Access Rule Violation             |626     |A column specification is not allowed for a CREATE INDEX that is built on an auxiliary table.|DB2            |N       |DB2                                                                         |
|42629    |42   |Syntax Error or Access Rule Violation             |629     |Parameter names must be specified for SQL routines.         |DB2            |N       |DB2                                                                         |
|42630    |42   |Syntax Error or Access Rule Violation             |630     |An SQLSTATE or SQLCODE variable is not valid in this context.|DB2            |N       |DB2                                                                         |
|42631    |42   |Syntax Error or Access Rule Violation             |631     |An expression must be specified on a RETURN statement in an SQL function.|DB2            |N       |DB2                                                                         |
|42633    |42   |Syntax Error or Access Rule Violation             |633     |An AS clause is required for an argument of XMLATTRIBUTES or XMLFOREST.|DB2            |N       |DB2                                                                         |
|42634    |42   |Syntax Error or Access Rule Violation             |634     |The XML name is not valid.                                  |DB2            |N       |DB2                                                                         |
|42701    |42   |Syntax Error or Access Rule Violation             |701     |The same target is specified more than once for assignment in the same SQL statement.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42702    |42   |Syntax Error or Access Rule Violation             |702     |A column reference is ambiguous, because of duplicate names.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42703    |42   |Syntax Error or Access Rule Violation             |703     |An undefined column or parameter name was detected.         |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42704    |42   |Syntax Error or Access Rule Violation             |704     |An undefined object or constraint name was detected.        |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42705    |42   |Syntax Error or Access Rule Violation             |705     |An undefined server-name was detected.                      |DB2            |N       |DB2                                                                         |
|42707    |42   |Syntax Error or Access Rule Violation             |707     |A column name in ORDER BY does not identify a column of the result table.|DB2            |N       |DB2                                                                         |
|42708    |42   |Syntax Error or Access Rule Violation             |708     |The locale specified in a SET LOCALE or locale sensitive function was not found.|DB2            |N       |DB2                                                                         |
|42709    |42   |Syntax Error or Access Rule Violation             |709     |A duplicate column name was specified in a key column list. |DB2            |N       |DB2                                                                         |
|42710    |42   |Syntax Error or Access Rule Violation             |710     |A duplicate object or constraint name was detected.         |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42711    |42   |Syntax Error or Access Rule Violation             |711     |A duplicate column name was detected in the object definition or ALTER TABLE statement.|DB2            |N       |DB2                                                                         |
|42712    |42   |Syntax Error or Access Rule Violation             |712     |A duplicate table designator was detected in the FROM clause or REFERENCING clause of a CREATE TRIGGER statement.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42713    |42   |Syntax Error or Access Rule Violation             |713     |A duplicate object was detected in a list or is the same as an existing object.|DB2            |N       |DB2                                                                         |
|42714    |42   |Syntax Error or Access Rule Violation             |714     |A host variable can be defined only once.                   |DB2            |N       |DB2                                                                         |
|42718    |42   |Syntax Error or Access Rule Violation             |718     |The local server name is not defined.                       |DB2            |N       |DB2                                                                         |
|42721    |42   |Syntax Error or Access Rule Violation             |721     |The special register name is unknown at the server.         |DB2            |N       |DB2                                                                         |
|42723    |42   |Syntax Error or Access Rule Violation             |723     |A routine with the same signature already exists in the schema, module, or compound block where it is defined.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42724    |42   |Syntax Error or Access Rule Violation             |724     |Unable to access an external program used for a user-defined function or a procedure.|DB2            |N       |DB2                                                                         |
|42725    |42   |Syntax Error or Access Rule Violation             |725     |A routine was referenced directly (not by either signature or by specific instance name), but there is more than one specific instance of that routine.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42726    |42   |Syntax Error or Access Rule Violation             |726     |Duplicate names for common table expressions were detected. |DB2            |N       |DB2                                                                         |
|42732    |42   |Syntax Error or Access Rule Violation             |732     |A duplicate schema name in a special register was detected. |DB2            |N       |DB2                                                                         |
|42734    |42   |Syntax Error or Access Rule Violation             |734     |A duplicate parameter-name, SQL variable name, label, or condition-name was detected.|DB2            |N       |DB2                                                                         |
|42736    |42   |Syntax Error or Access Rule Violation             |736     |The label specified on the GOTO, ITERATE, or LEAVE statement is not found or not valid.|DB2            |N       |DB2                                                                         |
|42737    |42   |Syntax Error or Access Rule Violation             |737     |The condition specified is not defined.                     |DB2            |N       |DB2                                                                         |
|42749    |42   |Syntax Error or Access Rule Violation             |749     |An XML schema document with the same target namespace and schema location already exists for the XML schema.|DB2            |N       |DB2                                                                         |
|4274A    |42   |Syntax Error or Access Rule Violation             |74A     |An XSROBJECT is not found in the XML schema repository.     |DB2            |N       |DB2                                                                         |
|4274B    |42   |Syntax Error or Access Rule Violation             |74B     |A unique XSROBJECT could not be found in the XML schema repository.|DB2            |N       |DB2                                                                         |
|4274C    |42   |Syntax Error or Access Rule Violation             |74C     |The specified attribute was not found in the trusted context.|DB2            |N       |DB2                                                                         |
|4274D    |42   |Syntax Error or Access Rule Violation             |74D     |The specified attribute already exists in the trusted context.|DB2            |N       |DB2                                                                         |
|4274E    |42   |Syntax Error or Access Rule Violation             |74E     |The specified attribute is not supported in the trusted context.|DB2            |N       |DB2                                                                         |
|4274M    |42   |Syntax Error or Access Rule Violation             |74M     |An undefined period name was detected.                      |DB2            |N       |DB2                                                                         |
|42801    |42   |Syntax Error or Access Rule Violation             |801     |Isolation level UR is invalid, because the result table is not read-only.|DB2            |N       |DB2                                                                         |
|42802    |42   |Syntax Error or Access Rule Violation             |802     |The number of target values is not the same as the number of source values.|DB2            |N       |DB2                                                                         |
|42803    |42   |Syntax Error or Access Rule Violation             |803     |A column reference in the SELECT or HAVING clause is invalid, because it is not a grouping column; or a column reference in the GROUP BY clause is invalid.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42804    |42   |Syntax Error or Access Rule Violation             |804     |The result expressions in a CASE expression are not compatible.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42805    |42   |Syntax Error or Access Rule Violation             |805     |An integer in the ORDER BY clause does not identify a column of the result table.|DB2            |N       |DB2                                                                         |
|42806    |42   |Syntax Error or Access Rule Violation             |806     |A value cannot be assigned to a variable, because the data types are not compatible.|DB2            |N       |DB2                                                                         |
|42807    |42   |Syntax Error or Access Rule Violation             |807     |The data-change statement is not permitted on this object.  |DB2            |N       |DB2                                                                         |
|42808    |42   |Syntax Error or Access Rule Violation             |808     |A column identified in the INSERT or UPDATE operation is not updatable.|DB2            |N       |DB2                                                                         |
|42809    |42   |Syntax Error or Access Rule Violation             |809     |The identified object is not the type of object to which the statement applies.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42810    |42   |Syntax Error or Access Rule Violation             |810     |A base table is not identified in a FOREIGN KEY clause.     |DB2            |N       |DB2                                                                         |
|42811    |42   |Syntax Error or Access Rule Violation             |811     |The number of columns specified is not the same as the number of columns in the SELECT clause.|DB2            |N       |DB2                                                                         |
|42813    |42   |Syntax Error or Access Rule Violation             |813     |WITH CHECK OPTION cannot be used for the specified view.    |DB2            |N       |DB2                                                                         |
|42814    |42   |Syntax Error or Access Rule Violation             |814     |The column cannot be dropped because it is the only column in the table.|DB2            |N       |DB2                                                                         |
|42815    |42   |Syntax Error or Access Rule Violation             |815     |The data type, length, scale, value, or CCSID is invalid.   |DB2            |N       |DB2                                                                         |
|42816    |42   |Syntax Error or Access Rule Violation             |816     |A datetime value or duration in an expression is invalid.   |DB2            |N       |DB2                                                                         |
|42817    |42   |Syntax Error or Access Rule Violation             |817     |The column cannot be dropped because a view or constraint is dependent on the column, the column is part of a partitioning key, or the column is a security label column.|DB2            |N       |DB2                                                                         |
|42818    |42   |Syntax Error or Access Rule Violation             |818     |The operands of an operator or function are not compatible or comparable.|DB2            |N       |DB2                                                                         |
|42819    |42   |Syntax Error or Access Rule Violation             |819     |An operand of an arithmetic operation or an operand of a function that requires a number is invalid.|DB2            |N       |DB2                                                                         |
|42820    |42   |Syntax Error or Access Rule Violation             |820     |A numeric constant is too long, or it has a value that is not within the range of its data type.|DB2            |N       |DB2                                                                         |
|42821    |42   |Syntax Error or Access Rule Violation             |821     |A data type for an assignment to a column or variable is not compatible with the data type.|DB2            |N       |DB2                                                                         |
|42822    |42   |Syntax Error or Access Rule Violation             |822     |An expression in the ORDER BY clause or GROUP BY clause is not valid.|DB2            |N       |DB2                                                                         |
|42823    |42   |Syntax Error or Access Rule Violation             |823     |Multiple columns are returned from a subquery that only allows one column.|DB2            |N       |DB2                                                                         |
|42824    |42   |Syntax Error or Access Rule Violation             |824     |An operand of LIKE is not a string, or the first operand is not a column.|DB2            |N       |DB2                                                                         |
|42825    |42   |Syntax Error or Access Rule Violation             |825     |The rows of UNION, INTERSECT, EXCEPT, or VALUES do not have compatible columns.|DB2            |N       |DB2                                                                         |
|42826    |42   |Syntax Error or Access Rule Violation             |826     |The rows of UNION, INTERSECT, EXCEPT, or VALUES do not have the same number of columns.|DB2            |N       |DB2                                                                         |
|42827    |42   |Syntax Error or Access Rule Violation             |827     |The table identified in the UPDATE or DELETE is not the same table designated by the cursor.|DB2            |N       |DB2                                                                         |
|42828    |42   |Syntax Error or Access Rule Violation             |828     |The table designated by the cursor of the UPDATE or DELETE statement cannot be modified, or the cursor is read-only.|DB2            |N       |DB2                                                                         |
|42829    |42   |Syntax Error or Access Rule Violation             |829     |FOR UPDATE OF is invalid, because the result table designated by the cursor cannot be modified.|DB2            |N       |DB2                                                                         |
|42830    |42   |Syntax Error or Access Rule Violation             |830     |The foreign key does not conform to the description of the parent key.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42831    |42   |Syntax Error or Access Rule Violation             |831     |Null values are not allowed in a column of a primary key, a column of a unique key, a ROWID column, a row change timestamp column, a row-begin column, a row-end column, or a column of an application period.|DB2            |N       |DB2                                                                         |
|42832    |42   |Syntax Error or Access Rule Violation             |832     |The operation is not allowed on system objects.             |DB2            |N       |DB2                                                                         |
|42834    |42   |Syntax Error or Access Rule Violation             |834     |SET NULL cannot be specified, because no column of the foreign key can be assigned the null value.|DB2            |N       |DB2                                                                         |
|42835    |42   |Syntax Error or Access Rule Violation             |835     |Cyclic references cannot be specified between named derived tables.|DB2            |N       |DB2                                                                         |
|42836    |42   |Syntax Error or Access Rule Violation             |836     |The specification of a recursive, named derived table is invalid.|DB2            |N       |DB2                                                                         |
|42837    |42   |Syntax Error or Access Rule Violation             |837     |The column cannot be altered, because its attributes are not compatible with the current column attributes.|DB2            |N       |DB2                                                                         |
|42842    |42   |Syntax Error or Access Rule Violation             |842     |A column or parameter definition is invalid, because a specified option is inconsistent with the column description.|DB2            |N       |DB2                                                                         |
|42845    |42   |Syntax Error or Access Rule Violation             |845     |An invalid use of a NOT DETERMINISTIC or EXTERNAL ACTION function was detected.|DB2            |N       |DB2                                                                         |
|42846    |42   |Syntax Error or Access Rule Violation             |846     |Cast from source type to target type is not supported.      |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42849    |42   |Syntax Error or Access Rule Violation             |849     |The specified option is not supported for the routine type. |DB2            |N       |DB2                                                                         |
|42852    |42   |Syntax Error or Access Rule Violation             |852     |The privileges specified in GRANT or REVOKE are invalid or inconsistent. (For example, GRANT ALTER on a view.)|DB2            |N       |DB2                                                                         |
|42855    |42   |Syntax Error or Access Rule Violation             |855     |The assignment of the LOB or XML to this variable is not allowed. The target variable for all fetches of a LOB or XML value for this cursor must be the same for all FETCHes.|DB2            |N       |DB2                                                                         |
|42856    |42   |Syntax Error or Access Rule Violation             |856     |The alter of a CCSID to the specified CCSID is not valid.   |DB2            |N       |DB2                                                                         |
|42866    |42   |Syntax Error or Access Rule Violation             |866     |The data type in either the RETURNS clause or the CAST FROM clause in the CREATE FUNCTION statement is not appropriate for the data type returned from the sourced function or RETURN statement in the function body.|DB2            |N       |DB2                                                                         |
|42872    |42   |Syntax Error or Access Rule Violation             |872     |FETCH statement clauses are incompatible with the cursor definition.|DB2            |N       |DB2                                                                         |
|42873    |42   |Syntax Error or Access Rule Violation             |873     |An invalid number of rows was specified in a multiple-row FETCH or multiple-row INSERT.|DB2            |N       |DB2                                                                         |
|42877    |42   |Syntax Error or Access Rule Violation             |877     |The column name cannot be qualified.                        |DB2            |N       |DB2                                                                         |
|42878    |42   |Syntax Error or Access Rule Violation             |878     |An invalid function or procedure name was used with the EXTERNAL keyword.|DB2            |N       |DB2                                                                         |
|42879    |42   |Syntax Error or Access Rule Violation             |879     |The data type of one or more input parameters in the CREATE FUNCTION statement is not appropriate for the corresponding data type in the source function.|DB2            |N       |DB2                                                                         |
|42880    |42   |Syntax Error or Access Rule Violation             |880     |The CAST TO and CAST FROM data types are incompatible, or would always result in truncation of a fixed string.|DB2            |N       |DB2                                                                         |
|42882    |42   |Syntax Error or Access Rule Violation             |882     |The specific instance name qualifier is not equal to the function name qualifier.|DB2            |N       |DB2                                                                         |
|42883    |42   |Syntax Error or Access Rule Violation             |883     |No routine was found with a matching signature.             |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42884    |42   |Syntax Error or Access Rule Violation             |884     |No routine was found with the specified name and compatible arguments.|DB2            |N       |DB2                                                                         |
|42885    |42   |Syntax Error or Access Rule Violation             |885     |The number of input parameters specified on a CREATE FUNCTION statement does not match the number provided by the function named in the SOURCE clause.|DB2            |N       |DB2                                                                         |
|42886    |42   |Syntax Error or Access Rule Violation             |886     |The IN, OUT, or INOUT parameter attributes do not match.    |DB2            |N       |DB2                                                                         |
|42887    |42   |Syntax Error or Access Rule Violation             |887     |The function or table-reference is not valid in the context where it occurs.|DB2            |N       |DB2                                                                         |
|42888    |42   |Syntax Error or Access Rule Violation             |888     |The table does not have a primary key.                      |DB2            |N       |DB2                                                                         |
|42889    |42   |Syntax Error or Access Rule Violation             |889     |The table already has a primary key.                        |DB2            |N       |DB2                                                                         |
|42890    |42   |Syntax Error or Access Rule Violation             |890     |A column list was specified in the references clause, but the identified parent table does not have a unique constraint with the specified column names.|DB2            |N       |DB2                                                                         |
|42891    |42   |Syntax Error or Access Rule Violation             |891     |A duplicate constraint already exists.                      |DB2            |N       |DB2                                                                         |
|42893    |42   |Syntax Error or Access Rule Violation             |893     |The object or constraint cannot be dropped, altered, or transferred or authorities cannot be revoked from the object, because other objects are dependent on it.|DB2            |N       |DB2                                                                         |
|42894    |42   |Syntax Error or Access Rule Violation             |894     |The value of a column or sequence attribute is invalid.     |DB2            |N       |DB2                                                                         |
|42895    |42   |Syntax Error or Access Rule Violation             |895     |For static SQL, an input variable cannot be used, because its data type is not compatible with the parameter of a procedure or user-defined function.|DB2            |N       |DB2                                                                         |
|42898    |42   |Syntax Error or Access Rule Violation             |898     |An invalid correlated reference or transition table was detected in a trigger.|DB2            |N       |DB2                                                                         |
|42899    |42   |Syntax Error or Access Rule Violation             |899     |Correlated references and column names are not allowed for triggered actions with the FOR EACH STATEMENT clause.|DB2            |N       |DB2                                                                         |
|428A1    |42   |Syntax Error or Access Rule Violation             |8A1     |Unable to access a file referenced by a file reference variable.|DB2            |N       |DB2                                                                         |
|428B0    |42   |Syntax Error or Access Rule Violation             |8B0     |Nesting not valid in ROLLUP, CUBE, or GROUPING SETs.        |DB2            |N       |DB2                                                                         |
|428B3    |42   |Syntax Error or Access Rule Violation             |8B3     |An invalid SQLSTATE was specified.                          |DB2            |N       |DB2                                                                         |
|428B4    |42   |Syntax Error or Access Rule Violation             |8B4     |The part clause of a LOCK TABLE statement is not valid.     |DB2            |N       |DB2                                                                         |
|428B7    |42   |Syntax Error or Access Rule Violation             |8B7     |A number specified in an SQL statement is out of the valid range.|DB2            |N       |DB2                                                                         |
|428C1    |42   |Syntax Error or Access Rule Violation             |8C1     |The data type or attribute of a column can only be specified once for a table.|DB2            |N       |DB2                                                                         |
|428C2    |42   |Syntax Error or Access Rule Violation             |8C2     |Examination of the function body indicates that the given clause should have been specified on the CREATE FUNCTION statement.|DB2            |N       |DB2                                                                         |
|428C4    |42   |Syntax Error or Access Rule Violation             |8C4     |The number of elements on each side of the predicate operator is not the same.|DB2            |N       |DB2                                                                         |
|428C7    |42   |Syntax Error or Access Rule Violation             |8C7     |A ROWID or reference column specification is not valid or used in an invalid context.|DB2            |N       |DB2                                                                         |
|428C9    |42   |Syntax Error or Access Rule Violation             |8C9     |A column defined as GENERATED ALWAYS cannot be specified as the target column of an insert or update operation.|DB2            |N       |PostgreSQL DB2                                                              |
|428D2    |42   |Syntax Error or Access Rule Violation             |8D2     |AS LOCATOR cannot be specified for a non-LOB parameter.     |DB2            |N       |DB2                                                                         |
|428D3    |42   |Syntax Error or Access Rule Violation             |8D3     |GENERATED is not allowed for the specified data type or attribute of a column.|DB2            |N       |DB2                                                                         |
|428D4    |42   |Syntax Error or Access Rule Violation             |8D4     |A cursor specified in a FOR statement cannot be referenced in an OPEN, CLOSE, or FETCH statement.|DB2            |N       |DB2                                                                         |
|428D5    |42   |Syntax Error or Access Rule Violation             |8D5     |The ending label does not match the beginning label.        |DB2            |N       |DB2                                                                         |
|428D6    |42   |Syntax Error or Access Rule Violation             |8D6     |UNDO is not allowed for NOT ATOMIC compound statements.     |DB2            |N       |DB2                                                                         |
|428D7    |42   |Syntax Error or Access Rule Violation             |8D7     |The condition value is not allowed.                         |DB2            |N       |DB2                                                                         |
|428D8    |42   |Syntax Error or Access Rule Violation             |8D8     |The sqlcode or sqlstate variable declaration is not valid.  |DB2            |N       |DB2                                                                         |
|428EC    |42   |Syntax Error or Access Rule Violation             |8EC     |The fullselect specified for the materialized query table is not valid.|DB2            |N       |DB2                                                                         |
|428EK    |42   |Syntax Error or Access Rule Violation             |8EK     |The schema qualifier is not valid.                          |DB2            |N       |DB2                                                                         |
|428EW    |42   |Syntax Error or Access Rule Violation             |8EW     |The table cannot be converted to or from a materialized query table.|DB2            |N       |DB2                                                                         |
|428F2    |42   |Syntax Error or Access Rule Violation             |8F2     |An integer expression must be specified on a RETURN statement in an SQL procedure.|DB2            |N       |DB2                                                                         |
|428F4    |42   |Syntax Error or Access Rule Violation             |8F4     |The SENSITIVITY specified on FETCH is not allowed for the cursor.|DB2            |N       |DB2                                                                         |
|428F5    |42   |Syntax Error or Access Rule Violation             |8F5     |The invocation of a routine is ambiguous.                   |DB2            |N       |DB2                                                                         |
|428F9    |42   |Syntax Error or Access Rule Violation             |8F9     |A sequence expression cannot be specified in this context.  |DB2            |N       |DB2                                                                         |
|428FA    |42   |Syntax Error or Access Rule Violation             |8FA     |The scale of the decimal number must be zero.               |DB2            |N       |DB2                                                                         |
|428FB    |42   |Syntax Error or Access Rule Violation             |8FB     |Sequence-name must not be a sequence generated by the system.|DB2            |N       |DB2                                                                         |
|428FC    |42   |Syntax Error or Access Rule Violation             |8FC     |The length of the encryption password is not valid.         |DB2            |N       |DB2                                                                         |
|428FE    |42   |Syntax Error or Access Rule Violation             |8FE     |The data is not a result of the ENCRYPT function.           |DB2            |N       |DB2                                                                         |
|428FJ    |42   |Syntax Error or Access Rule Violation             |8FJ     |ORDER BY or FETCH FIRST is not allowed in the outer fullselect of a view or materialized query table.|DB2            |N       |DB2                                                                         |
|428FL    |42   |Syntax Error or Access Rule Violation             |8FL     |A data change statement is not allowed in the context in which it was specified.|DB2            |N       |DB2                                                                         |
|428FM    |42   |Syntax Error or Access Rule Violation             |8FM     |An SQL data change statement within a SELECT specified a view which is not a symmetric view.|DB2            |N       |DB2                                                                         |
|428FP    |42   |Syntax Error or Access Rule Violation             |8FP     |Only one INSTEAD OF trigger is allowed for each kind of operation on a view.|DB2            |N       |DB2                                                                         |
|428FQ    |42   |Syntax Error or Access Rule Violation             |8FQ     |An INSTEAD OF trigger cannot be created because of how the view is defined.|DB2            |N       |DB2                                                                         |
|428FR    |42   |Syntax Error or Access Rule Violation             |8FR     |A column cannot be altered as specified.                    |DB2            |N       |DB2                                                                         |
|428FS    |42   |Syntax Error or Access Rule Violation             |8FS     |A column cannot be added to an index.                       |DB2            |N       |DB2                                                                         |
|428FT    |42   |Syntax Error or Access Rule Violation             |8FT     |The partitioning clause specified on CREATE or ALTER is not valid.|DB2            |N       |DB2                                                                         |
|428FY    |42   |Syntax Error or Access Rule Violation             |8FY     |A column cannot be added, dropped, or altered in a materialized query table.|DB2            |N       |DB2                                                                         |
|428G3    |42   |Syntax Error or Access Rule Violation             |8G3     |FINAL TABLE is not valid when the target view of the SQL data change statement in a fullselect has an INSTEAD OF trigger defined.|DB2            |N       |DB2                                                                         |
|428G4    |42   |Syntax Error or Access Rule Violation             |8G4     |Invalid use of INPUT SEQUENCE ordering.                     |DB2            |N       |DB2                                                                         |
|428G5    |42   |Syntax Error or Access Rule Violation             |8G5     |The assignment clause of the UPDATE statement must specify at least one column that is not an INCLUDE column.|DB2            |N       |DB2                                                                         |
|428G8    |42   |Syntax Error or Access Rule Violation             |8G8     |The view cannot be enabled for query optimization.          |DB2            |N       |DB2                                                                         |
|428GB    |42   |Syntax Error or Access Rule Violation             |8GB     |A character could not be converted and substitution characters are not allowed.|DB2            |N       |DB2                                                                         |
|428GC    |42   |Syntax Error or Access Rule Violation             |8GC     |An invalid string unit was specified for a function.        |DB2            |N       |DB2                                                                         |
|428GH    |42   |Syntax Error or Access Rule Violation             |8GH     |The data type of one or more parameters specified in the ADD VERSION clause does not match the corresponding data type in the routine being altered.|DB2            |N       |DB2                                                                         |
|428GI    |42   |Syntax Error or Access Rule Violation             |8GI     |An XML schema is not complete because an XML schema document is missing.|DB2            |N       |DB2                                                                         |
|428GJ    |42   |Syntax Error or Access Rule Violation             |8GJ     |The table cannot be truncated because DELETE triggers exist for the table or the table is a parent table of a referential constraint that would be affected by the statement.|DB2            |N       |DB2                                                                         |
|428GK    |42   |Syntax Error or Access Rule Violation             |8GK     |An ALTER TRUSTED CONTEXT attempted to remove one or more of the minimum required attributes.|DB2            |N       |DB2                                                                         |
|428GL    |42   |Syntax Error or Access Rule Violation             |8GL     |The system authorization ID specified for a trusted context is already specified in another trusted context.|DB2            |N       |DB2                                                                         |
|428GM    |42   |Syntax Error or Access Rule Violation             |8GM     |The trusted context is already defined to be used by this authorization ID or PUBLIC.|DB2            |N       |DB2                                                                         |
|428GN    |42   |Syntax Error or Access Rule Violation             |8GN     |The specified authorization ID or PUBLIC is not defined in the specified trusted context.|DB2            |N       |DB2                                                                         |
|428GU    |42   |Syntax Error or Access Rule Violation             |8GU     |A table must include at least one column that is not implicitly hidden.|DB2            |N       |DB2                                                                         |
|428H2    |42   |Syntax Error or Access Rule Violation             |8H2     |Data type is not supported in the context where it is being used.|DB2            |N       |DB2                                                                         |
|428H8    |42   |Syntax Error or Access Rule Violation             |8H8     |The object must be defined as secure because another object depends on it for row-level or column-level access control.|DB2            |N       |DB2                                                                         |
|428H9    |42   |Syntax Error or Access Rule Violation             |8H9     |PERMISSION or MASK cannot be altered.                       |DB2            |N       |DB2                                                                         |
|428HA    |42   |Syntax Error or Access Rule Violation             |8HA     |An argument of a user-defined function must not reference a column for which a column mask is defined.|DB2            |N       |DB2                                                                         |
|428HB    |42   |Syntax Error or Access Rule Violation             |8HB     |A permission or mask cannot be created on the specified object.|DB2            |N       |DB2                                                                         |
|428HC    |42   |Syntax Error or Access Rule Violation             |8HC     |A column mask is already defined for the specified column.  |DB2            |N       |DB2                                                                         |
|428HD    |42   |Syntax Error or Access Rule Violation             |8HD     |The statement cannot be processed because a column mask cannot be applied or the definition of the mask conflicts with the statement.|DB2            |N       |DB2                                                                         |
|428HJ    |42   |Syntax Error or Access Rule Violation             |8HJ     |The organization clause specified on CREATE or ALTER is not valid.|DB2            |N       |DB2                                                                         |
|428HK    |42   |Syntax Error or Access Rule Violation             |8HK     |The specified hash space is not valid for the implicitly created table space.|DB2            |N       |DB2                                                                         |
|428HL    |42   |Syntax Error or Access Rule Violation             |8HL     |Another version of the routine exists and is defined with an incompatible option.|DB2            |N       |DB2                                                                         |
|428HM    |42   |Syntax Error or Access Rule Violation             |8HM     |The table cannot be used as a system-period temporal table or an archive-enabled table.|DB2            |N       |DB2                                                                         |
|428HN    |42   |Syntax Error or Access Rule Violation             |8HN     |The period specification is not valid.                      |DB2            |N       |DB2                                                                         |
|428HW    |42   |Syntax Error or Access Rule Violation             |8HW     |The period specification or period clause in an index or constraint is not valid.|DB2            |N       |DB2                                                                         |
|428HX    |42   |Syntax Error or Access Rule Violation             |8HX     |The table is not valid for a history table or archive table.|DB2            |N       |DB2                                                                         |
|428HY    |42   |Syntax Error or Access Rule Violation             |8HY     |The period specification or period condition is not valid.  |DB2            |N       |DB2                                                                         |
|428HZ    |42   |Syntax Error or Access Rule Violation             |8HZ     |The temporal attribute of the table was not valid for the specified ALTER operation.|DB2            |N       |DB2                                                                         |
|428I1    |42   |Syntax Error or Access Rule Violation             |8I1     |The columns updated by the XMLMODIFY function were not specified in the UPDATE SET clause.|DB2            |N       |DB2                                                                         |
|428I4    |42   |Syntax Error or Access Rule Violation             |8I4     |The combination of UNNEST arguments are not valid.          |DB2            |N       |DB2                                                                         |
|428I5    |42   |Syntax Error or Access Rule Violation             |8I5     |The attributes of an object at one location do not match the attributes of the same object at another location.|DB2            |N       |DB2                                                                         |
|428I6    |42   |Syntax Error or Access Rule Violation             |8I6     |The archive enabled table is not allowed in this context.   |DB2            |N       |DB2                                                                         |
|428IC    |42   |Syntax Error or Access Rule Violation             |8IC     |An Invalid combination of replication overrides is in use for a data change operation.|DB2            |N       |DB2                                                                         |
|428ID    |42   |Syntax Error or Access Rule Violation             |8ID     |Model columns specified in an SQL Data Insights function could not be determined or are not usable.|DB2            |N       |DB2                                                                         |
|42902    |42   |Syntax Error or Access Rule Violation             |902     |The object of the INSERT, UPDATE, or DELETE is also identified (possibly implicitly through a view) in a FROM clause.|DB2            |N       |DB2                                                                         |
|42903    |42   |Syntax Error or Access Rule Violation             |903     |Invalid use of an aggregate function or OLAP function.      |DB2            |N       |DB2                                                                         |
|42905    |42   |Syntax Error or Access Rule Violation             |905     |DISTINCT is specified more than once in a subselect.        |DB2            |N       |DB2                                                                         |
|42906    |42   |Syntax Error or Access Rule Violation             |906     |An aggregate function in a subquery of a HAVING clause includes an expression that applies an operator to a correlated reference.|DB2            |N       |DB2                                                                         |
|42907    |42   |Syntax Error or Access Rule Violation             |907     |The string is too long in the context it was specified.     |DB2            |N       |DB2                                                                         |
|42908    |42   |Syntax Error or Access Rule Violation             |908     |The statement does not include a required column list.      |DB2            |N       |DB2                                                                         |
|42909    |42   |Syntax Error or Access Rule Violation             |909     |CREATE VIEW includes an operator or operand that is not valid for views.|DB2            |N       |DB2                                                                         |
|42911    |42   |Syntax Error or Access Rule Violation             |911     |A decimal divide operation is invalid, because the result would have a negative scale.|DB2            |N       |DB2                                                                         |
|42912    |42   |Syntax Error or Access Rule Violation             |912     |A column cannot be updated, because it is not identified in the UPDATE clause of the select-statement of the cursor.|DB2            |N       |DB2                                                                         |
|42914    |42   |Syntax Error or Access Rule Violation             |914     |The DELETE is invalid, because a table referenced in a subquery can be affected by the operation.|DB2            |N       |DB2                                                                         |
|42915    |42   |Syntax Error or Access Rule Violation             |915     |An invalid referential constraint has been detected.        |DB2            |N       |DB2                                                                         |
|42917    |42   |Syntax Error or Access Rule Violation             |917     |The object cannot be explicitly dropped, altered, or replaced.|DB2            |N       |DB2                                                                         |
|42918    |42   |Syntax Error or Access Rule Violation             |918     |A user-defined data type cannot be created with a system-defined data type name (for example, INTEGER).|DB2            |N       |DB2                                                                         |
|42924    |42   |Syntax Error or Access Rule Violation             |924     |An alias resolved to another alias rather than a table or view at the remote location.|DB2            |N       |DB2                                                                         |
|42925    |42   |Syntax Error or Access Rule Violation             |925     |Recursive named derived tables cannot specify SELECT DISTINCT and must specify UNION ALL.|DB2            |N       |DB2                                                                         |
|42927    |42   |Syntax Error or Access Rule Violation             |927     |The function cannot be altered to NOT DETERMINISTIC or EXTERNAL ACTION because it is referenced by one or more existing views.|DB2            |N       |DB2                                                                         |
|42932    |42   |Syntax Error or Access Rule Violation             |932     |The program preparation assumptions are incorrect.          |DB2            |N       |DB2                                                                         |
|42939    |42   |Syntax Error or Access Rule Violation             |939     |The name cannot be used, because the specified identifier is reserved for system use.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|42945    |42   |Syntax Error or Access Rule Violation             |945     |ALTER CCSID is not allowed on a table space or database that contains a view.|DB2            |N       |DB2                                                                         |
|42961    |42   |Syntax Error or Access Rule Violation             |961     |The server name specified does not match the current server.|DB2            |N       |DB2                                                                         |
|42962    |42   |Syntax Error or Access Rule Violation             |962     |The column type is not allowed in an index, a key, generated column, or a constraint.|DB2            |N       |DB2                                                                         |
|42963    |42   |Syntax Error or Access Rule Violation             |963     |Invalid specification of a security label column.           |DB2            |N       |DB2                                                                         |
|42969    |42   |Syntax Error or Access Rule Violation             |969     |The package was not created.                                |DB2            |N       |DB2                                                                         |
|42972    |42   |Syntax Error or Access Rule Violation             |972     |An expression in a join-condition or ON clause of a MERGE statement references columns in more than one of the operand tables.|DB2            |N       |DB2                                                                         |
|42986    |42   |Syntax Error or Access Rule Violation             |986     |The source table of a rename operation is referenced in a context where is it not supported.|DB2            |N       |DB2                                                                         |
|42987    |42   |Syntax Error or Access Rule Violation             |987     |The statement or routine is not allowed in a trigger.       |DB2            |N       |DB2                                                                         |
|42988    |42   |Syntax Error or Access Rule Violation             |988     |The operation is not allowed with mixed ASCII data.         |DB2            |N       |DB2                                                                         |
|42993    |42   |Syntax Error or Access Rule Violation             |993     |The column, as defined, is too large to be logged.          |DB2            |N       |DB2                                                                         |
|42995    |42   |Syntax Error or Access Rule Violation             |995     |The requested function does not apply to global temporary tables.|DB2            |N       |DB2                                                                         |
|42996    |42   |Syntax Error or Access Rule Violation             |996     |A specified column may not be used in a partition key.      |DB2            |N       |DB2                                                                         |
|42997    |42   |Syntax Error or Access Rule Violation             |997     |Capability is not supported by this version of the Db2 application requester, Db2 application server, or the combination of the two.|DB2            |N       |DB2                                                                         |
|429B1    |42   |Syntax Error or Access Rule Violation             |9B1     |A procedure specifying COMMIT ON RETURN cannot be the target of a nested CALL statement.|DB2            |N       |DB2                                                                         |
|429BB    |42   |Syntax Error or Access Rule Violation             |9BB     |The data type of a column, parameter, or SQL variable is not supported.|DB2            |N       |DB2                                                                         |
|429BD    |42   |Syntax Error or Access Rule Violation             |9BD     |RETURN must be the last SQL statement of the atomic compound statement within an SQL row or table function.|DB2            |N       |DB2                                                                         |
|429BI    |42   |Syntax Error or Access Rule Violation             |9BI     |The condition area is full and cannot handle more errors for a NOT ATOMIC statement.|DB2            |N       |DB2                                                                         |
|429BN    |42   |Syntax Error or Access Rule Violation             |9BN     |A CREATE statement cannot be processed when the value of CURRENT SCHEMA differs from CURRENT SQLID.|DB2            |N       |DB2                                                                         |
|429BQ    |42   |Syntax Error or Access Rule Violation             |9BQ     |The specified alter of the data type or attribute is not allowed.|DB2            |N       |DB2                                                                         |
|429BS    |42   |Syntax Error or Access Rule Violation             |9BS     |Invalid index definition involving an XMLPATTERN clause or a column defined with a data type of XML.|DB2            |N       |DB2                                                                         |
|429BV    |42   |Syntax Error or Access Rule Violation             |9BV     |Invalid specification of a ROW CHANGE TIMESTAMP column.     |DB2            |N       |DB2                                                                         |
|429BW    |42   |Syntax Error or Access Rule Violation             |9BW     |The statement cannot be processed due to related implicitly created objects.|DB2            |N       |DB2                                                                         |
|429BX    |42   |Syntax Error or Access Rule Violation             |9BX     |The expression for an index key is not valid.               |DB2            |N       |DB2                                                                         |
|429BY    |42   |Syntax Error or Access Rule Violation             |9BY     |The statement is not allowed when using a trusted connection.|DB2            |N       |DB2                                                                         |
|429C1    |42   |Syntax Error or Access Rule Violation             |9C1     |A data type cannot be determined for an untyped expression. |DB2            |N       |DB2                                                                         |
|429CB    |42   |Syntax Error or Access Rule Violation             |9CB     |The attributes of the table or column are not supported for the table type.|DB2            |N       |DB2                                                                         |
|42K01    |42   |Syntax Error or Access Rule Violation             |K01     |data type not fully specified                               |Spark          |N       |Spark                                                                       |
|42K02    |42   |Syntax Error or Access Rule Violation             |K02     |data source not found                                       |Spark          |N       |Spark                                                                       |
|42K03    |42   |Syntax Error or Access Rule Violation             |K03     |File not found                                              |Spark          |N       |Spark                                                                       |
|42K04    |42   |Syntax Error or Access Rule Violation             |K04     |Duplicate file                                              |Spark          |N       |Spark                                                                       |
|42K05    |42   |Syntax Error or Access Rule Violation             |K05     |Name is not valid                                           |Spark          |N       |Spark                                                                       |
|42K06    |42   |Syntax Error or Access Rule Violation             |K06     |Invalid type for options                                    |Spark          |N       |Spark                                                                       |
|42K07    |42   |Syntax Error or Access Rule Violation             |K07     |Not a valid schema literal                                  |Spark          |N       |Spark                                                                       |
|42K08    |42   |Syntax Error or Access Rule Violation             |K08     |Not a constant                                              |Spark          |N       |Spark                                                                       |
|42K09    |42   |Syntax Error or Access Rule Violation             |K09     |Data type mismatch                                          |Spark          |N       |Spark                                                                       |
|42K0A    |42   |Syntax error or Access Rule violation             |K0A     |Invalid UNPIVOT clause                                      |Spark          |N       |Spark                                                                       |
|42K0B    |42   |Syntax error or Access Rule violation             |K0B     |Legacy feature blocked                                      |Spark          |N       |Spark                                                                       |
|42KD0    |42   |Syntax error or Access Rule violation             |KD0     |Ambiguous name reference.                                   |Databricks     |N       |Databricks                                                                  |
|42KD1    |42   |Syntax error or Access Rule violation             |KD1     |Operation not supported in READ ONLY session mode.          |Databricks     |N       |Databricks                                                                  |
|42KD2    |42   |Syntax error or Access Rule violation             |KD2     |The source and target table names of a SYNC operaton must be the same.|Databricks     |N       |Databricks                                                                  |
|42KD3    |42   |Syntax error or Access Rule violation             |KD3     |A column can not be added as specified.                     |Databricks     |N       |Databricks                                                                  |
|42KD4    |42   |Syntax error or Access Rule violation             |KD4     |Operation not supported because table schema has changed.   |Databricks     |N       |Databricks                                                                  |
|42KD5    |42   |Syntax error or Access Rule violation             |KD5     |Cannot create file or path.                                 |Databricks     |N       |Databricks                                                                  |
|42KD6    |42   |Syntax error or Access Rule violation             |KD6     |No partition information found.                             |Databricks     |N       |Databricks                                                                  |
|42KD7    |42   |Syntax error or Access Rule violation             |KD7     |Table signature mismatch.                                   |Databricks     |N       |Databricks                                                                  |
|42KD8    |42   |Syntax error or Access Rule violation             |KD8     |Column position out of range.                               |Databricks     |N       |Databricks                                                                  |
|42KD9    |42   |Syntax error or Access Rule violation             |KD9     |Cannot infer table schema.                                  |Databricks     |N       |Databricks                                                                  |
|42KDA    |42   |Syntax error or Access Rule violation             |KDA     |Failed to merge file into table schema.                     |Databricks     |N       |Databricks                                                                  |
|42P01    |42   |Syntax error or Access Rule violation             |P01     |undefined_table                                             |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P02    |42   |Syntax Error or Access Rule Violation             |P02     |undefined_parameter                                         |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P03    |42   |Syntax Error or Access Rule Violation             |P03     |duplicate_cursor                                            |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P04    |42   |Syntax Error or Access Rule Violation             |P04     |duplicate_database                                          |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P05    |42   |Syntax Error or Access Rule Violation             |P05     |duplicate_prepared_statement                                |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P06    |42   |Syntax Error or Access Rule Violation             |P06     |duplicate_schema                                            |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P07    |42   |Syntax Error or Access Rule Violation             |P07     |duplicate_table                                             |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P08    |42   |Syntax Error or Access Rule Violation             |P08     |ambiguous_parameter                                         |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P09    |42   |Syntax Error or Access Rule Violation             |P09     |ambiguous_alias                                             |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P10    |42   |Syntax Error or Access Rule Violation             |P10     |invalid_column_reference                                    |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P11    |42   |Syntax Error or Access Rule Violation             |P11     |invalid_cursor_definition                                   |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P12    |42   |Syntax Error or Access Rule Violation             |P12     |invalid_database_definition                                 |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P13    |42   |Syntax Error or Access Rule Violation             |P13     |invalid_function_definition                                 |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P14    |42   |Syntax Error or Access Rule Violation             |P14     |invalid_prepared_statement_definition                       |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P15    |42   |Syntax Error or Access Rule Violation             |P15     |invalid_schema_definition                                   |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P16    |42   |Syntax Error or Access Rule Violation             |P16     |invalid_table_definition                                    |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P17    |42   |Syntax Error or Access Rule Violation             |P17     |invalid_object_definition                                   |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P18    |42   |Syntax Error or Access Rule Violation             |P18     |indeterminate_datatype                                      |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|42P19    |42   |Syntax Error or Access Rule Violation             |P19     |invalid_recursion                                           |PostgreSQL     |N       |PostgreSQL                                                                  |
|42P20    |42   |Syntax Error or Access Rule Violation             |P20     |windowing_error                                             |PostgreSQL     |N       |PostgreSQL                                                                  |
|42P21    |42   |Syntax Error or Access Rule Violation             |P21     |collation_mismatch                                          |PostgreSQL     |N       |PostgreSQL                                                                  |
|42P22    |42   |Syntax Error or Access Rule Violation             |P22     |indeterminate_collation                                     |PostgreSQL     |N       |PostgreSQL                                                                  |
|42S01    |42   |Syntax error or Access rule violation             |S01     |Base table or view already exists                           |SQL Server     |N       |SQL Server                                                                  |
|42S02    |42   |Syntax error or Access rule violation             |S02     |Base table or view not found                                |SQL Server     |N       |SQL Server                                                                  |
|42S11    |42   |Syntax error or Access rule violation             |S11     |Index already exists                                        |SQL Server     |N       |SQL Server                                                                  |
|42S12    |42   |Syntax error or Access rule violation             |S12     |Index not found                                             |SQL Server     |N       |SQL Server                                                                  |
|42S21    |42   |Syntax error or Access rule violation             |S21     |Column already exists                                       |SQL Server     |N       |SQL Server                                                                  |
|42S22    |42   |Syntax error or Access rule violation             |S22     |Column not found                                            |SQL Server     |N       |SQL Server                                                                  |
|44000    |44   |with check option violation                       |000     |(no subclass)                                               |SQL/Foundation |Y       |SQL/Foundation PostgreSQL DB2 Redshift Oracle SQL Server                    |
|45000    |45   |unhandled user-defined exception                  |000     |(no subclass)                                               |SQL/PSM        |Y       |SQL/PSM                                                                     |
|46000    |46   |Java DDL 1                                        |000     |(no subclass)                                               |SQL/JRT        |Y       |SQL/JRT SQL/JRT SQL/OLB                                                     |
|46001    |46   |Java DDL 1                                        |001     |invalid URL                                                 |SQL/JRT        |Y       |SQL/JRT DB2                                                                 |
|46002    |46   |Java DDL 1                                        |002     |invalid JAR name                                            |SQL/JRT        |Y       |SQL/JRT DB2                                                                 |
|46003    |46   |Java DDL 1                                        |003     |invalid class deletion                                      |SQL/JRT        |Y       |SQL/JRT DB2                                                                 |
|46005    |46   |Java DDL 1                                        |005     |invalid replacement                                         |SQL/JRT        |Y       |SQL/JRT                                                                     |
|46007    |46   |Java™ Errors                                      |007     |A Java function has a Java method with an invalid signature.|DB2            |N       |DB2                                                                         |
|46008    |46   |Java™ Errors                                      |008     |A Java function could not map to a single Java method.      |DB2            |N       |DB2                                                                         |
|4600A    |46   |Java DDL 1                                        |00A     |attempt to replace uninstalled JAR                          |SQL/JRT        |Y       |SQL/JRT                                                                     |
|4600B    |46   |Java DDL 1                                        |00B     |attempt to remove uninstalled JAR                           |SQL/JRT        |Y       |SQL/JRT                                                                     |
|4600C    |46   |Java DDL 1                                        |00C     |invalid JAR removal                                         |SQL/JRT        |Y       |SQL/JRT DB2                                                                 |
|4600D    |46   |Java DDL 1                                        |00D     |invalid path                                                |SQL/JRT        |Y       |SQL/JRT DB2                                                                 |
|4600E    |46   |Java DDL 1                                        |00E     |self-referencing path                                       |SQL/JRT        |Y       |SQL/JRT DB2                                                                 |
|46102    |46   |Java execution 1                                  |102     |invalid JAR name in path                                    |SQL/JRT        |Y       |SQL/JRT                                                                     |
|46103    |46   |Java execution 1                                  |103     |unresolved class name                                       |SQL/JRT        |Y       |SQL/JRT DB2                                                                 |
|46110    |46   |OLB-specific error                                |110     |unsupported feature                                         |SQL/OLB        |Y       |SQL/OLB                                                                     |
|46120    |46   |OLB-specific error                                |120     |invalid class declaration                                   |SQL/OLB        |Y       |SQL/OLB                                                                     |
|46121    |46   |OLB-specific error                                |121     |invalid column name                                         |SQL/OLB        |Y       |SQL/OLB                                                                     |
|46122    |46   |OLB-specific error                                |122     |invalid number of columns                                   |SQL/OLB        |Y       |SQL/OLB                                                                     |
|46130    |46   |OLB-specific error                                |130     |invalid profile state                                       |SQL/OLB        |Y       |SQL/OLB                                                                     |
|46501    |46   |Java™ Errors                                      |501     |The install or remove jar procedure specified the use of a deployment descriptor.|DB2            |N       |DB2                                                                         |
|46502    |46   |Java™ Errors                                      |502     |A user-defined procedure has returned a DYNAMIC RESULT SET of an invalid class. The parameter is not a Db2 result set.|DB2            |N       |DB2                                                                         |
|51002    |51   |Invalid Application State                         |002     |The package corresponding to an SQL statement execution request was not found.|DB2            |N       |DB2                                                                         |
|51003    |51   |Invalid Application State                         |003     |Consistency tokens do not match.                            |DB2            |N       |DB2                                                                         |
|51004    |51   |Invalid Application State                         |004     |An address in the SQLDA is invalid.                         |DB2            |N       |DB2                                                                         |
|51005    |51   |Invalid Application State                         |005     |The previous system error has disabled this function.       |DB2            |N       |DB2                                                                         |
|51006    |51   |Invalid Application State                         |006     |A valid connection has not been established.                |DB2            |N       |DB2                                                                         |
|51008    |51   |Invalid Application State                         |008     |The release number of the program or package is not valid.  |DB2            |N       |DB2                                                                         |
|51015    |51   |Invalid Application State                         |015     |An attempt was made to execute a section that was found to be in error at bind time.|DB2            |N       |DB2                                                                         |
|51021    |51   |Invalid Application State                         |021     |SQL statements cannot be executed until the application process executes a rollback operation.|DB2            |N       |DB2                                                                         |
|51024    |51   |Invalid Application State                         |024     |An object cannot be used, because it has been marked inoperative.|DB2            |N       |DB2                                                                         |
|51030    |51   |Invalid Application State                         |030     |The procedure referenced in a DESCRIBE PROCEDURE or ASSOCIATE LOCATOR statement has not yet been called within the application process.|DB2            |N       |DB2                                                                         |
|51032    |51   |Invalid Application State                         |032     |A valid CCSID has not yet been specified for this Db2 for z/OS® subsystem.|DB2            |N       |DB2                                                                         |
|51033    |51   |Invalid Application State                         |033     |The operation is not allowed because it operates on a result set that was not created by the current server.|DB2            |N       |DB2                                                                         |
|51034    |51   |Invalid Application State                         |034     |The routine defined with MODIFIES SQL DATA is not valid in the context in which it is invoked.|DB2            |N       |DB2                                                                         |
|51035    |51   |Invalid Application State                         |035     |A PREVIOUS VALUE expression cannot be used because a value has not been generated for the sequence yet in this session.|DB2            |N       |DB2                                                                         |
|51036    |51   |Invalid Application State                         |036     |An implicit connect to a remote server is not allowed because a savepoint is outstanding.|DB2            |N       |DB2                                                                         |
|51039    |51   |Invalid Application State                         |039     |The ENCRYPTION PASSWORD value is not set.                   |DB2            |N       |DB2                                                                         |
|51043    |51   |Invalid Application State                         |043     |Procedure cannot be called because the nested environment already called an autonomous procedure.|DB2            |N       |DB2                                                                         |
|53000    |53   |Insufficient Resources                            |000     |insufficient_resources                                      |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|53001    |53   |Invalid Operand or Inconsistent Specification     |001     |A clause is invalid, because the table space is a workfile. |DB2            |N       |DB2                                                                         |
|53004    |53   |Invalid Operand or Inconsistent Specification     |004     |DSNDB07 is the implicit workfile database.                  |DB2            |N       |DB2                                                                         |
|53014    |53   |Invalid Operand or Inconsistent Specification     |014     |The specified OBID is invalid.                              |DB2            |N       |DB2                                                                         |
|53022    |53   |Invalid Operand or Inconsistent Specification     |022     |Variable or parameter is not allowed.                       |DB2            |N       |DB2                                                                         |
|53035    |53   |Invalid Operand or Inconsistent Specification     |035     |Key limits must be specified in the CREATE or ALTER INDEX statement.|DB2            |N       |DB2                                                                         |
|53036    |53   |Invalid Operand or Inconsistent Specification     |036     |The number of PARTITION specifications is not the same as the number of partitions.|DB2            |N       |DB2                                                                         |
|53037    |53   |Invalid Operand or Inconsistent Specification     |037     |A partitioned index cannot be created on a table.           |DB2            |N       |DB2                                                                         |
|53038    |53   |Invalid Operand or Inconsistent Specification     |038     |The number of key limit values is zero or greater than the number of columns in the key.|DB2            |N       |DB2                                                                         |
|53039    |53   |Invalid Operand or Inconsistent Specification     |039     |The PARTITION clause of the ALTER statement is omitted or invalid.|DB2            |N       |DB2                                                                         |
|53040    |53   |Invalid Operand or Inconsistent Specification     |040     |The buffer pool cannot be changed as specified.             |DB2            |N       |DB2                                                                         |
|53041    |53   |Invalid Operand or Inconsistent Specification     |041     |The page size of the buffer pool is invalid.                |DB2            |N       |DB2                                                                         |
|53043    |53   |Invalid Operand or Inconsistent Specification     |043     |Columns with different field procedures cannot be compared. |DB2            |N       |DB2                                                                         |
|53044    |53   |Invalid Operand or Inconsistent Specification     |044     |The columns have a field procedure, but the field types are not compatible.|DB2            |N       |DB2                                                                         |
|53045    |53   |Invalid Operand or Inconsistent Specification     |045     |The data type of the key limit constant is not the same as the data type of the column.|DB2            |N       |DB2                                                                         |
|53088    |53   |Invalid Operand or Inconsistent Specification     |088     |LOCKMAX is inconsistent with the specified LOCKSIZE.        |DB2            |N       |DB2                                                                         |
|53089    |53   |Invalid Operand or Inconsistent Specification     |089     |The number of variable parameters for a stored procedure is not equal to the number of expected variable parameters.|DB2            |N       |DB2                                                                         |
|53090    |53   |Invalid Operand or Inconsistent Specification     |090     |Only data from one encoding scheme, either ASCII, EBCDIC or Unicode, can be referenced in the same SQL statement.|DB2            |N       |DB2                                                                         |
|53091    |53   |Invalid Operand or Inconsistent Specification     |091     |The encoding scheme specified is not the same as the encoding scheme currently in use for the containing table space.|DB2            |N       |DB2                                                                         |
|53092    |53   |Invalid Operand or Inconsistent Specification     |092     |Type 1 index cannot be created for a table using the ASCII encoding scheme.|DB2            |N       |DB2                                                                         |
|53093    |53   |Invalid Operand or Inconsistent Specification     |093     |The CCSID ASCII or UNICODE clause is not supported for this database or table space.|DB2            |N       |DB2                                                                         |
|53094    |53   |Invalid Operand or Inconsistent Specification     |094     |The PLAN_TABLE cannot be created with the FOR ASCII clause. |DB2            |N       |DB2                                                                         |
|53095    |53   |Invalid Operand or Inconsistent Specification     |095     |CREATE or ALTER statement cannot define an object with the specified encoding scheme.|DB2            |N       |DB2                                                                         |
|53096    |53   |Invalid Operand or Inconsistent Specification     |096     |The PARTITION clause was specified on CREATE AUXILIARY TABLE, but the base table is not partitioned.|DB2            |N       |DB2                                                                         |
|53098    |53   |Invalid Operand or Inconsistent Specification     |098     |The auxiliary table cannot be created because a column was specified that is not a LOB column.|DB2            |N       |DB2                                                                         |
|53099    |53   |Invalid Operand or Inconsistent Specification     |099     |A WLM ENVIRONMENT name must be specified on the CREATE FUNCTION statement.|DB2            |N       |DB2                                                                         |
|530A1    |53   |Invalid Operand or Inconsistent Specification     |0A1     |An ALTER TABLE statement specified FLOAT as the new data type for a column, but there is an existing index or constraint that restricts the use of FLOAT.|DB2            |N       |DB2                                                                         |
|530A2    |53   |Invalid Operand or Inconsistent Specification     |0A2     |The PARTITIONING clause is not allowed on the specified index.|DB2            |N       |DB2                                                                         |
|530A3    |53   |Invalid Operand or Inconsistent Specification     |0A3     |The specified option is not allowed for the internal representation of the routine specified|DB2            |N       |DB2                                                                         |
|530A4    |53   |Invalid Operand or Inconsistent Specification     |0A4     |The options specified on ALTER statement are not the same as those specified when the object was created.|DB2            |N       |DB2                                                                         |
|530A5    |53   |Invalid Operand or Inconsistent Specification     |0A5     |The REGENERATE option is only valid for an index with key expressions.|DB2            |N       |DB2                                                                         |
|530A7    |53   |Invalid Operand or Inconsistent Specification     |0A7     |EXCHANGE DATA is not allowed because the tables do not have a defined clone relationship.|DB2            |N       |DB2                                                                         |
|530A8    |53   |Invalid Operand or Inconsistent Specification     |0A8     |A system parameter is incompatible with the specified SQL statement.|DB2            |N       |DB2                                                                         |
|530A9    |53   |Invalid Operand or Inconsistent Specification     |0A9     |A temporal table is not allowed in this context.            |DB2            |N       |DB2                                                                         |
|53100    |53   |Insufficient Resources                            |100     |disk_full                                                   |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|53200    |53   |Insufficient Resources                            |200     |out_of_memory                                               |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|53300    |53   |Insufficient Resources                            |300     |too_many_connections                                        |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|53400    |53   |Insufficient Resources                            |400     |configuration_limit_exceeded                                |PostgreSQL     |N       |PostgreSQL                                                                  |
|54000    |54   |Program Limit Exceeded                            |000     |program_limit_exceeded                                      |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|54001    |54   |SQL or Product Limit Exceeded                     |001     |The statement is too long or too complex.                   |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|54002    |54   |SQL or Product Limit Exceeded                     |002     |A string constant is too long.                              |DB2            |N       |DB2                                                                         |
|54004    |54   |SQL or Product Limit Exceeded                     |004     |The statement has too many table names or too many items in a SELECT or INSERT list.|DB2            |N       |DB2                                                                         |
|54005    |54   |SQL or Product Limit Exceeded                     |005     |The sort key is too long, or has too many columns.          |DB2            |N       |DB2                                                                         |
|54006    |54   |SQL or Product Limit Exceeded                     |006     |The result string is too long.                              |DB2            |N       |DB2                                                                         |
|54008    |54   |SQL or Product Limit Exceeded                     |008     |The key is too long, a column of the key is too long, or the key has too many columns.|DB2            |N       |DB2                                                                         |
|54010    |54   |SQL or Product Limit Exceeded                     |010     |The record length of the table is too long.                 |DB2            |N       |DB2                                                                         |
|54011    |54   |SQL or Product Limit Exceeded                     |011     |Too many columns were specified for a table, view, or table function.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|54012    |54   |SQL or Product Limit Exceeded                     |012     |The literal is too long.                                    |DB2            |N       |DB2                                                                         |
|54023    |54   |Program Limit Exceeded                            |023     |too_many_arguments                                          |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|54024    |54   |SQL or Product Limit Exceeded                     |024     |The check constraint, generated column, or key expression is too long.|DB2            |N       |DB2                                                                         |
|54025    |54   |SQL or Product Limit Exceeded                     |025     |The table description exceeds the maximum size of the object descriptor.|DB2            |N       |DB2                                                                         |
|54027    |54   |SQL or Product Limit Exceeded                     |027     |The catalog has the maximum number of user-defined indexes. |DB2            |N       |DB2                                                                         |
|54035    |54   |SQL or Product Limit Exceeded                     |035     |An internal object limit exceeded.                          |DB2            |N       |DB2                                                                         |
|54038    |54   |SQL or Product Limit Exceeded                     |038     |Maximum depth of nested routines or triggers was exceeded.  |DB2            |N       |DB2                                                                         |
|54041    |54   |SQL or Product Limit Exceeded                     |041     |The maximum number of internal identifiers has been reached.|DB2            |N       |DB2                                                                         |
|54042    |54   |SQL or Product Limit Exceeded                     |042     |Only one index is allowed on an auxiliary table.            |DB2            |N       |DB2                                                                         |
|54051    |54   |SQL or Product Limit Exceeded                     |051     |Value specified on FETCH ABSOLUTE or RELATIVE is invalid.   |DB2            |N       |DB2                                                                         |
|54054    |54   |SQL or Product Limit Exceeded                     |054     |The number of data partitions is exceeded.                  |DB2            |N       |DB2                                                                         |
|54055    |54   |SQL or Product Limit Exceeded                     |055     |The maximum number of versions has been reached for a table or index.|DB2            |N       |DB2                                                                         |
|54058    |54   |SQL or Product Limit Exceeded                     |058     |The internal representation of an XML path is too long.     |DB2            |N       |DB2                                                                         |
|54065    |54   |SQL or Product Limit Exceeded                     |065     |The maximum of 99999 implicitly generated object names has been exceeded.|DB2            |N       |DB2                                                                         |
|54068    |54   |SQL or Product Limit Exceeded                     |068     |Seamless automatic client reroute retry limit exceeded.     |DB2            |N       |DB2                                                                         |
|55000    |55   |Object Not In Prerequisite State                  |000     |object_not_in_prerequisite_state                            |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|55002    |55   |Object Not in Prerequisite State                  |002     |The explanation table is not defined properly.              |DB2            |N       |DB2                                                                         |
|55003    |55   |Object Not in Prerequisite State                  |003     |The DDL registration table is not defined properly.         |DB2            |N       |DB2                                                                         |
|55004    |55   |Object Not in Prerequisite State                  |004     |The database cannot be accessed, because it is no longer a shared database.|DB2            |N       |DB2                                                                         |
|55006    |55   |Object Not in Prerequisite State                  |006     |The object cannot be dropped, because it is currently in use by the same application process.|DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|55007    |55   |Object Not in Prerequisite State                  |007     |The object cannot be altered, because it is currently in use by the same application process.|DB2            |N       |DB2                                                                         |
|55011    |55   |Object Not in Prerequisite State                  |011     |The operation is disallowed, because the workfile database is not in the stopped state.|DB2            |N       |DB2                                                                         |
|55012    |55   |Object Not in Prerequisite State                  |012     |A clustering index is not valid on the table.               |DB2            |N       |DB2                                                                         |
|55014    |55   |Object Not in Prerequisite State                  |014     |The table does not have an index to enforce the uniqueness of the primary key.|DB2            |N       |DB2                                                                         |
|55015    |55   |Object Not in Prerequisite State                  |015     |The ALTER statement cannot be executed, because the pageset is not in the stopped state.|DB2            |N       |DB2                                                                         |
|55016    |55   |Object Not in Prerequisite State                  |016     |The ALTER statement is invalid, because the pageset has user-managed data sets.|DB2            |N       |DB2                                                                         |
|55017    |55   |Object Not in Prerequisite State                  |017     |The table cannot be created in the table space, because it already contains a table.|DB2            |N       |DB2                                                                         |
|55019    |55   |Object Not in Prerequisite State                  |019     |The object is in an invalid state for the operation.        |DB2            |N       |DB2                                                                         |
|55020    |55   |Object Not in Prerequisite State                  |020     |A work file database is already defined for the member.     |DB2            |N       |DB2                                                                         |
|55023    |55   |Object Not in Prerequisite State                  |023     |An error occurred calling a procedure.                      |DB2            |N       |DB2                                                                         |
|55030    |55   |Object Not in Prerequisite State                  |030     |A package specified in a remote BIND REPLACE operation must not have a system list.|DB2            |N       |DB2                                                                         |
|55035    |55   |Object Not in Prerequisite State                  |035     |The table cannot be dropped, because it is protected.       |DB2            |N       |DB2                                                                         |
|55048    |55   |Object Not in Prerequisite State                  |048     |Encrypted data cannot be encrypted.                         |DB2            |N       |DB2                                                                         |
|55058    |55   |Object Not in Prerequisite State                  |058     |The DEBUG MODE cannot be changed for a routine that was created with DISABLE DEBUG MODE.|DB2            |N       |DB2                                                                         |
|55059    |55   |Object Not in Prerequisite State                  |059     |The currently active version for a routine cannot be dropped.|DB2            |N       |DB2                                                                         |
|55063    |55   |Object Not in Prerequisite State                  |063     |The XML schema is not in the correct state for the operation.|DB2            |N       |DB2                                                                         |
|55078    |55   |Object Not in Prerequisite State                  |078     |The table is already in the specified state.                |DB2            |N       |DB2                                                                         |
|55079    |55   |Object Not in Prerequisite State                  |079     |The operation cannot be performed because the XML column is not in the versioning format.|DB2            |N       |DB2                                                                         |
|55P02    |55   |Object Not In Prerequisite State                  |P02     |cant_change_runtime_param                                   |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|55P03    |55   |Object Not In Prerequisite State                  |P03     |lock_not_available                                          |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|55P04    |55   |Object Not In Prerequisite State                  |P04     |unsafe_new_enum_value_usage                                 |PostgreSQL     |N       |PostgreSQL                                                                  |
|56010    |56   |Miscellaneous SQL or Product Error                |010     |The subtype of a string variable is not the same as the subtype at bind time, and the difference cannot be resolved by character conversion.|DB2            |N       |DB2                                                                         |
|56016    |56   |Miscellaneous SQL or Product Error                |016     |The ranges specified for data partitions are not valid.     |DB2            |N       |DB2                                                                         |
|56018    |56   |Miscellaneous SQL or Product Error                |018     |A column cannot be added to the table, because it has an edit procedure.|DB2            |N       |DB2                                                                         |
|56023    |56   |Miscellaneous SQL or Product Error                |023     |An invalid reference to a remote object has been detected.  |DB2            |N       |DB2                                                                         |
|56025    |56   |Miscellaneous SQL or Product Error                |025     |An invalid use of AT ALL LOCATIONS in GRANT or REVOKE has been detected.|DB2            |N       |DB2                                                                         |
|56027    |56   |Miscellaneous SQL or Product Error                |027     |A nullable column of a foreign key with a delete rule of SET NULL cannot be part of the key of a partitioned index.|DB2            |N       |DB2                                                                         |
|56031    |56   |Miscellaneous SQL or Product Error                |031     |The clause or scalar function is invalid, because mixed and DBCS data are not supported on this system.|DB2            |N       |DB2                                                                         |
|56036    |56   |Miscellaneous SQL or Product Error                |036     |Specific and non-specific volume IDs are not allowed in a storage group.|DB2            |N       |DB2                                                                         |
|56038    |56   |Miscellaneous SQL or Product Error                |038     |The requested feature is not supported in this environment. |DB2            |N       |DB2                                                                         |
|56040    |56   |Miscellaneous SQL or Product Error                |040     |CURRENT SQLID cannot be used in a statement that references remote objects.|DB2            |N       |DB2                                                                         |
|56045    |56   |Miscellaneous SQL or Product Error                |045     |The application must issue a rollback operation to back out the change that was made at the read-only application server.|DB2            |N       |DB2                                                                         |
|56052    |56   |Miscellaneous SQL or Product Error                |052     |The remote requester tried to bind, rebind, or free a trigger package.|DB2            |N       |DB2                                                                         |
|56053    |56   |Miscellaneous SQL or Product Error                |053     |The parent of a table in a read-only shared database must also be a table in a read-only shared database.|DB2            |N       |DB2                                                                         |
|56054    |56   |Miscellaneous SQL or Product Error                |054     |User-defined data sets for objects in a shared database must be defined with SHAREOPTIONS(1,3).|DB2            |N       |DB2                                                                         |
|56055    |56   |Miscellaneous SQL or Product Error                |055     |The database is defined as SHARE READ, but the table space or indexspace has not been defined on the owning system.|DB2            |N       |DB2                                                                         |
|56056    |56   |Miscellaneous SQL or Product Error                |056     |The description of an object in a SHARE READ database must be consistent with its description in the OWNER system.|DB2            |N       |DB2                                                                         |
|56057    |56   |Miscellaneous SQL or Product Error                |057     |A database cannot be altered from SHARE READ to SHARE OWNER.|DB2            |N       |DB2                                                                         |
|56059    |56   |Miscellaneous SQL or Product Error                |059     |An error occurred when binding a triggered SQL statement.   |DB2            |N       |DB2                                                                         |
|56060    |56   |Miscellaneous SQL or Product Error                |060     |An LE function failed.                                      |DB2            |N       |DB2                                                                         |
|56062    |56   |Miscellaneous SQL or Product Error                |062     |A distributed operation is invalid, because the unit of work was started before DDF.|DB2            |N       |DB2                                                                         |
|56064    |56   |Miscellaneous SQL or Product Error                |064     |The bind operation is disallowed, because the program depends on functions of a release from which fallback has occurred.|DB2            |N       |DB2                                                                         |
|56065    |56   |Miscellaneous SQL or Product Error                |065     |The bind operation is disallowed, because the DBRM has been modified or was created for a different release.|DB2            |N       |DB2                                                                         |
|56066    |56   |Miscellaneous SQL or Product Error                |066     |The rebind operation is disallowed, because the plan or package depends on functions of a release from which fallback has occurred.|DB2            |N       |DB2                                                                         |
|56067    |56   |Miscellaneous SQL or Product Error                |067     |The rebind operation is disallowed, because the value of SYSPACKAGE.IBMREQD is invalid.|DB2            |N       |DB2                                                                         |
|56072    |56   |Miscellaneous SQL or Product Error                |072     |Execution failed due to the function not supported by a downlevel server that will not affect the execution of subsequent SQL statements.|DB2            |N       |DB2                                                                         |
|56073    |56   |Miscellaneous SQL or Product Error                |073     |Execution failed due to the function not supported by a downlevel server that will affect the execution of subsequent SQL statements.|DB2            |N       |DB2                                                                         |
|56080    |56   |Miscellaneous SQL or Product Error                |080     |The data type is not allowed in Db2 private protocol processing.|DB2            |N       |DB2                                                                         |
|56084    |56   |Miscellaneous SQL or Product Error                |084     |An unsupported SQLTYPE was encountered in a select list or input list.|DB2            |N       |DB2                                                                         |
|56088    |56   |Miscellaneous SQL or Product Error                |088     |ALTER FUNCTION failed because functions cannot modify data when they are processed in parallel.|DB2            |N       |DB2                                                                         |
|56089    |56   |Miscellaneous SQL or Product Error                |089     |Specified option requires type 2 indexes.                   |DB2            |N       |DB2                                                                         |
|56090    |56   |Miscellaneous SQL or Product Error                |090     |The alter of an index or table is not allowed.              |DB2            |N       |DB2                                                                         |
|56095    |56   |Miscellaneous SQL or Product Error                |095     |A bind option is invalid.                                   |DB2            |N       |DB2                                                                         |
|56096    |56   |Miscellaneous SQL or Product Error                |096     |Bind options are incompatible.                              |DB2            |N       |DB2                                                                         |
|560A1    |56   |Miscellaneous SQL or Product Error                |0A1     |The table space name is not valid.                          |DB2            |N       |DB2                                                                         |
|560A2    |56   |Miscellaneous SQL or Product Error                |0A2     |A LOB table and its associated base table space must be in the same database.|DB2            |N       |DB2                                                                         |
|560A3    |56   |Miscellaneous SQL or Product Error                |0A3     |The table is not compatible with the database.              |DB2            |N       |DB2                                                                         |
|560A4    |56   |Miscellaneous SQL or Product Error                |0A4     |The operation is not allowed on an auxiliary table.         |DB2            |N       |DB2                                                                         |
|560A5    |56   |Miscellaneous SQL or Product Error                |0A5     |An auxiliary table already exists for the specified column or partition.|DB2            |N       |DB2                                                                         |
|560A6    |56   |Miscellaneous SQL or Product Error                |0A6     |A table cannot have a LOB column unless it also has a ROWID column or cannot have an XML column unless it also has a DOCID.|DB2            |N       |DB2                                                                         |
|560A7    |56   |Miscellaneous SQL or Product Error                |0A7     |GBPCACHE NONE cannot be specified for a table space or index in GRECP.|DB2            |N       |DB2                                                                         |
|560A8    |56   |Miscellaneous SQL or Product Error                |0A8     |An 8K or 16K buffer pool pagesize is invalid for a WORKFILE object.|DB2            |N       |DB2                                                                         |
|560A9    |56   |Miscellaneous SQL or Product Error                |0A9     |A discontinued parameter, option, or clause was specified.  |DB2            |N       |DB2                                                                         |
|560AB    |56   |Miscellaneous SQL or Product Error                |0AB     |The data type is not supported in an SQL routine.           |DB2            |N       |DB2                                                                         |
|560AD    |56   |Miscellaneous SQL or Product Error                |0AD     |A view name was specified after LIKE in addition to the INCLUDING IDENTITY COLUMN ATTRIBUTES clause.|DB2            |N       |DB2                                                                         |
|560AE    |56   |Miscellaneous SQL or Product Error                |0AE     |The specified table or view is not allowed in a LIKE clause.|DB2            |N       |DB2                                                                         |
|560B1    |56   |Miscellaneous SQL or Product Error                |0B1     |Procedure failed because a result set was scrollable but the cursor was not positioned before the first row.|DB2            |N       |DB2                                                                         |
|560B2    |56   |Miscellaneous SQL or Product Error                |0B2     |Open failed because the cursor is scrollable but the client does not support scrollable cursors.|DB2            |N       |DB2                                                                         |
|560B3    |56   |Miscellaneous SQL or Product Error                |0B3     |Procedure failed because one or more result sets cannot be returned by the procedure to the calling application.|DB2            |N       |DB2                                                                         |
|560B5    |56   |Miscellaneous SQL or Product Error                |0B5     |Local special register is not valid as used.                |DB2            |N       |DB2                                                                         |
|560B8    |56   |Miscellaneous SQL or Product Error                |0B8     |The SQL statement cannot be executed because it was precompiled at a level that is incompatible with the current value of the ENCODING bind option or special register.|DB2            |N       |DB2                                                                         |
|560B9    |56   |Miscellaneous SQL or Product Error                |0B9     |Hexadecimal constant GX is not allowed.                     |DB2            |N       |DB2                                                                         |
|560BF    |56   |Miscellaneous SQL or Product Error                |0BF     |The cryptographic facility has not been installed.          |DB2            |N       |DB2                                                                         |
|560C3    |56   |Miscellaneous SQL or Product Error                |0C3     |An AFTER trigger cannot modify a row being inserted for an INSERT statement.|DB2            |N       |DB2                                                                         |
|560C5    |56   |Miscellaneous SQL or Product Error                |0C5     |The package must be bound or rebound to be successfully executed.|DB2            |N       |DB2                                                                         |
|560C7    |56   |Miscellaneous SQL or Product Error                |0C7     |ALTER VIEW failed.                                          |DB2            |N       |DB2                                                                         |
|560CC    |56   |Miscellaneous SQL or Product Error                |0CC     |ALTER INDEX failed.                                         |DB2            |N       |DB2                                                                         |
|560CG    |56   |Miscellaneous SQL or Product Error                |0CG     |An XML value contains a combination of XML nodes that causes an internal identifier limit to be exceeded.|DB2            |N       |DB2                                                                         |
|560CH    |56   |Miscellaneous SQL or Product Error                |0CH     |The maximum number of children nodes for an XML node in an XML value is exceeded.to be exceeded.|DB2            |N       |DB2                                                                         |
|560CK    |56   |Miscellaneous SQL or Product Error                |0CK     |Explain monitored statements failed.                        |DB2            |N       |DB2                                                                         |
|560CM    |56   |Miscellaneous SQL or Product Error                |0CM     |An error occurred in a key expression evaluation.           |DB2            |N       |DB2                                                                         |
|560CU    |56   |Miscellaneous SQL or Product Error                |0CU     |The VARCHAR option is not consistent with the option specified when the procedure was created.|DB2            |N       |DB2                                                                         |
|560CV    |56   |Miscellaneous SQL or Product Error                |0CV     |Invalid table reference for table locator.                  |DB2            |N       |DB2                                                                         |
|560CY    |56   |Miscellaneous SQL or Product Error                |0CY     |A period specification or period clause is not valid as specified.|DB2            |N       |DB2                                                                         |
|560D5    |56   |Miscellaneous SQL or Product Error                |0D5     |The statement cannot be executed by the query accelerator.  |DB2            |N       |DB2                                                                         |
|560DC    |56   |Miscellaneous SQL or Product Error                |0DC     |An error was detected while using the z/OS Unicode Services.|DB2            |N       |DB2                                                                         |
|57000    |57   |Operator Intervention                             |000     |operator_intervention                                       |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|57001    |57   |Resource Not Available or Operator Intervention   |001     |The table is unavailable, because it does not have a primary index.|DB2            |N       |DB2                                                                         |
|57002    |57   |Resource Not Available or Operator Intervention   |002     |GRANT and REVOKE are invalid, because authorization has been disabled.|DB2            |N       |DB2                                                                         |
|57003    |57   |Resource Not Available or Operator Intervention   |003     |The specified buffer pool has not been activated.           |DB2            |N       |DB2                                                                         |
|57004    |57   |Resource Not Available or Operator Intervention   |004     |The table is unavailable, because it lacks a partitioned index.|DB2            |N       |DB2                                                                         |
|57005    |57   |Resource Not Available or Operator Intervention   |005     |The statement cannot be executed, because a utility or a governor time limit was exceeded.|DB2            |N       |DB2                                                                         |
|57006    |57   |Resource Not Available or Operator Intervention   |006     |The object cannot be created, because a DROP or CREATE is pending.|DB2            |N       |DB2                                                                         |
|57007    |57   |Resource Not Available or Operator Intervention   |007     |The object cannot be used, because an operation is pending. |DB2            |N       |DB2                                                                         |
|57008    |57   |Resource Not Available or Operator Intervention   |008     |The date or time local format exit has not been installed.  |DB2            |N       |DB2                                                                         |
|57010    |57   |Resource Not Available or Operator Intervention   |010     |A field procedure could not be loaded.                      |DB2            |N       |DB2                                                                         |
|57011    |57   |Resource Not Available or Operator Intervention   |011     |Virtual storage or database resource is not available.      |DB2            |N       |DB2                                                                         |
|57012    |57   |Resource Not Available or Operator Intervention   |012     |A non-database resource is not available. This will not affect the successful execution of subsequent statements.|DB2            |N       |DB2                                                                         |
|57013    |57   |Resource Not Available or Operator Intervention   |013     |A non-database resource is not available. This will affect the successful execution of subsequent statements.|DB2            |N       |DB2                                                                         |
|57014    |57   |Resource Not Available or Operator Intervention   |014     |Processing was canceled as requested.                       |DB2            |N       |PostgreSQL DB2 Redshift                                                     |
|57015    |57   |Resource Not Available or Operator Intervention   |015     |Connection to the local Db2 not established.                |DB2            |N       |DB2                                                                         |
|57017    |57   |Resource Not Available or Operator Intervention   |017     |Character conversion is not defined.                        |DB2            |N       |DB2                                                                         |
|57018    |57   |Resource Not Available or Operator Intervention   |018     |A DDL registration table or its unique index does not exist.|DB2            |N       |DB2                                                                         |
|57023    |57   |Resource Not Available or Operator Intervention   |023     |The DDL statement cannot be executed, because a DROP is pending of a DDL registration table.|DB2            |N       |DB2                                                                         |
|57033    |57   |Resource Not Available or Operator Intervention   |033     |Deadlock or timeout occurred without automatic rollback.    |DB2            |N       |DB2                                                                         |
|57051    |57   |Resource Not Available or Operator Intervention   |051     |The estimated CPU cost exceeds the resource limit.          |DB2            |N       |DB2                                                                         |
|57053    |57   |Resource Not Available or Operator Intervention   |053     |A table is not available in a routine or trigger because of violated nested SQL statement rules.|DB2            |N       |DB2                                                                         |
|57054    |57   |Resource Not Available or Operator Intervention   |054     |A table is not available until the auxiliary tables and indexes for its externally stored columns have been created.|DB2            |N       |DB2                                                                         |
|57057    |57   |Resource Not Available or Operator Intervention   |057     |The SQL statement cannot be executed due to a prior condition in a DRDA chain of SQL statements.|DB2            |N       |DB2                                                                         |
|57062    |57   |Resource Not Available or Operator Intervention   |062     |Adjustment not allowed for a period as a result of a data change operation.|DB2            |N       |DB2                                                                         |
|57P01    |57   |Operator Intervention                             |P01     |admin_shutdown                                              |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|57P02    |57   |Operator Intervention                             |P02     |crash_shutdown                                              |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|57P03    |57   |Operator Intervention                             |P03     |cannot_connect_now                                          |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|57P04    |57   |Operator Intervention                             |P04     |database_dropped                                            |PostgreSQL     |N       |PostgreSQL                                                                  |
|57P05    |57   |Operator Intervention                             |P05     |idle_session_timeout                                        |PostgreSQL     |N       |PostgreSQL                                                                  |
|58000    |58   |System Error (error external to PostgreSQL itself)|000     |system_error                                                |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|58001    |58   |System Error                                      |001     |The database cannot be created, because the assigned DBID is a duplicate.|DB2            |N       |DB2                                                                         |
|58002    |58   |System Error                                      |002     |An exit has returned an error or invalid data.              |DB2            |N       |DB2                                                                         |
|58003    |58   |System Error                                      |003     |An invalid section number was detected.                     |DB2            |N       |DB2                                                                         |
|58004    |58   |System Error                                      |004     |A system error (that does not necessarily preclude the successful execution of subsequent SQL statements) occurred.|DB2            |N       |DB2                                                                         |
|58006    |58   |System Error                                      |006     |A system error occurred during connection.                  |DB2            |N       |DB2                                                                         |
|58008    |58   |System Error                                      |008     |Execution failed due to a distribution protocol error that will not affect the successful execution of subsequent DDM commands or SQL statements.|DB2            |N       |DB2                                                                         |
|58009    |58   |System Error                                      |009     |Execution failed due to a distribution protocol error that caused deallocation of the conversation.|DB2            |N       |DB2                                                                         |
|58010    |58   |System Error                                      |010     |Execution failed due to a distribution protocol error that will affect the successful execution of subsequent DDM commands or SQL statements.|DB2            |N       |DB2                                                                         |
|58011    |58   |System Error                                      |011     |The DDM command is invalid while the bind process in progress.|DB2            |N       |DB2                                                                         |
|58012    |58   |System Error                                      |012     |The bind process with the specified package name and consistency token is not active.|DB2            |N       |DB2                                                                         |
|58013    |58   |System Error                                      |013     |The SQLCODE is inconsistent with the reply message.         |DB2            |N       |DB2                                                                         |
|58014    |58   |System Error                                      |014     |The DDM command is not supported.                           |DB2            |N       |DB2                                                                         |
|58015    |58   |System Error                                      |015     |The DDM object is not supported.                            |DB2            |N       |DB2                                                                         |
|58016    |58   |System Error                                      |016     |The DDM parameter is not supported.                         |DB2            |N       |DB2                                                                         |
|58017    |58   |System Error                                      |017     |The DDM parameter value is not supported.                   |DB2            |N       |DB2                                                                         |
|58018    |58   |System Error                                      |018     |The DDM reply message is not supported.                     |DB2            |N       |DB2                                                                         |
|58026    |58   |System Error                                      |026     |The number of variables in the statement is not equal to the number of variables in SQLSTTVRB.|DB2            |N       |DB2                                                                         |
|58030    |58   |System Error (error external to PostgreSQL itself)|030     |io_error                                                    |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|58P01    |58   |System Error (error external to PostgreSQL itself)|P01     |undefined_file                                              |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|58P02    |58   |System Error (error external to PostgreSQL itself)|P02     |duplicate_file                                              |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|5UA01    |5U   |Common Utilities and Tools                        |A01     |The task cannot be removed because it is currently executing.|DB2            |N       |DB2                                                                         |
|60000    |60   |system error                                      |000     |system error                                                |Oracle         |N       |Oracle                                                                      |
|61000    |61   |shared server and detached process errors         |000     |shared server and detached process errors                   |Oracle         |N       |Oracle                                                                      |
|62000    |62   |shared server and detached process errors         |000     |shared server and detached process errors                   |Oracle         |N       |Oracle                                                                      |
|63000    |63   |Oracle*XA and two-task interface errors           |000     |Oracle*XA and two-task interface errors                     |Oracle         |N       |Oracle                                                                      |
|64000    |64   |control file, database file, and redo file errors; archival and media recovery errors|000     |control file, database file, and redo file errors; archival and media recovery errors|Oracle         |N       |Oracle                                                                      |
|65000    |65   |PL/SQL errors                                     |000     |PL/SQL errors                                               |Oracle         |N       |Oracle                                                                      |
|66000    |66   |Oracle Net driver errors                          |000     |Oracle Net driver errors                                    |Oracle         |N       |Oracle                                                                      |
|67000    |67   |licensing errors                                  |000     |licensing errors                                            |Oracle         |N       |Oracle                                                                      |
|69000    |69   |SQL*Connect errors                                |000     |SQL*Connect errors                                          |Oracle         |N       |Oracle                                                                      |
|72000    |72   |Snapshot Failure                                  |000     |snapshot_too_old                                            |PostgreSQL     |N       |PostgreSQL Oracle                                                           |
|82100    |82   |out of memory (could not allocate)                |100     |out of memory (could not allocate)                          |Oracle         |N       |Oracle                                                                      |
|82101    |82   |out of memory (could not allocate)                |101     |inconsistent cursor cache (UCE/CUC mismatch)                |Oracle         |N       |Oracle                                                                      |
|82102    |82   |out of memory (could not allocate)                |102     |inconsistent cursor cache (no CUC entry for UCE)            |Oracle         |N       |Oracle                                                                      |
|82103    |82   |out of memory (could not allocate)                |103     |inconsistent cursor cache (out-or-range CUC ref)            |Oracle         |N       |Oracle                                                                      |
|82104    |82   |out of memory (could not allocate)                |104     |inconsistent cursor cache (no CUC available)                |Oracle         |N       |Oracle                                                                      |
|82105    |82   |out of memory (could not allocate)                |105     |inconsistent cursor cache (no CUC entry in cache)           |Oracle         |N       |Oracle                                                                      |
|82106    |82   |out of memory (could not allocate)                |106     |inconsistent cursor cache (invalid cursor number)           |Oracle         |N       |Oracle                                                                      |
|82107    |82   |out of memory (could not allocate)                |107     |program too old for runtime library; re-precompile          |Oracle         |N       |Oracle                                                                      |
|82108    |82   |out of memory (could not allocate)                |108     |invalid descriptor passed to runtime library                |Oracle         |N       |Oracle                                                                      |
|82109    |82   |out of memory (could not allocate)                |109     |inconsistent host cache (out-or-range SIT ref)              |Oracle         |N       |Oracle                                                                      |
|82110    |82   |out of memory (could not allocate)                |110     |inconsistent host cache (invalid SQL type)                  |Oracle         |N       |Oracle                                                                      |
|82111    |82   |out of memory (could not allocate)                |111     |heap consistency error                                      |Oracle         |N       |Oracle                                                                      |
|82113    |82   |out of memory (could not allocate)                |113     |code generation internal consistency failed                 |Oracle         |N       |Oracle                                                                      |
|82114    |82   |out of memory (could not allocate)                |114     |reentrant code generator gave invalid context               |Oracle         |N       |Oracle                                                                      |
|82117    |82   |out of memory (could not allocate)                |117     |invalid OPEN or PREPARE for this connection                 |Oracle         |N       |Oracle                                                                      |
|82118    |82   |out of memory (could not allocate)                |118     |application context not found                               |Oracle         |N       |Oracle                                                                      |
|82119    |82   |out of memory (could not allocate)                |119     |unable to obtain error message text                         |Oracle         |N       |Oracle                                                                      |
|82120    |82   |out of memory (could not allocate)                |120     |Precompiler/SQLLIB version mismatch                         |Oracle         |N       |Oracle                                                                      |
|82121    |82   |out of memory (could not allocate)                |121     |NCHAR error; fetched number of bytes is odd                 |Oracle         |N       |Oracle                                                                      |
|82122    |82   |out of memory (could not allocate)                |122     |EXEC TOOLS interface not available                          |Oracle         |N       |Oracle                                                                      |
|82123    |82   |out of memory (could not allocate)                |123     |runtime context in use                                      |Oracle         |N       |Oracle                                                                      |
|82124    |82   |out of memory (could not allocate)                |124     |unable to allocate runtime context                          |Oracle         |N       |Oracle                                                                      |
|82125    |82   |out of memory (could not allocate)                |125     |unable to initialize process for use with threads           |Oracle         |N       |Oracle                                                                      |
|82126    |82   |out of memory (could not allocate)                |126     |invalid runtime context                                     |Oracle         |N       |Oracle                                                                      |
|F0000    |F0   |Configuration File Error                          |000     |config_file_error                                           |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|F0001    |F0   |Configuration File Error                          |001     |lock_file_exists                                            |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|HV000    |HV   |FDW-specific condition                            |000     |(no subclass)                                               |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV001    |HV   |FDW-specific condition                            |001     |memory allocation error                                     |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV002    |HV   |FDW-specific condition                            |002     |dynamic parameter value needed                              |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV004    |HV   |FDW-specific condition                            |004     |invalid data type                                           |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV005    |HV   |FDW-specific condition                            |005     |column name not found                                       |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV006    |HV   |FDW-specific condition                            |006     |invalid data type descriptors                               |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV007    |HV   |FDW-specific condition                            |007     |invalid column name                                         |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV008    |HV   |FDW-specific condition                            |008     |invalid column number                                       |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV009    |HV   |FDW-specific condition                            |009     |invalid use of null pointer                                 |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00A    |HV   |FDW-specific condition                            |00A     |invalid string format                                       |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00B    |HV   |FDW-specific condition                            |00B     |invalid handle                                              |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00C    |HV   |FDW-specific condition                            |00C     |invalid option index                                        |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00D    |HV   |FDW-specific condition                            |00D     |invalid option name                                         |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00J    |HV   |FDW-specific condition                            |00J     |option name not found                                       |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00K    |HV   |FDW-specific condition                            |00K     |reply handle                                                |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00L    |HV   |FDW-specific condition                            |00L     |unable to create execution                                  |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00M    |HV   |FDW-specific condition                            |00M     |unable to create reply                                      |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00N    |HV   |FDW-specific condition                            |00N     |unable to establish connection                              |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00P    |HV   |FDW-specific condition                            |00P     |no schemas                                                  |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00Q    |HV   |FDW-specific condition                            |00Q     |schema not found                                            |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV00R    |HV   |FDW-specific condition                            |00R     |table not found                                             |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV010    |HV   |FDW-specific condition                            |010     |function sequence error                                     |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV014    |HV   |FDW-specific condition                            |014     |limit on number of handles exceeded                         |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV021    |HV   |FDW-specific condition                            |021     |inconsistent descriptor informa- tion                       |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV024    |HV   |FDW-specific condition                            |024     |invalid attribute value                                     |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV090    |HV   |FDW-specific condition                            |090     |invalid string length or buffer length                      |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HV091    |HV   |FDW-specific condition                            |091     |invalid descriptor field identifier                         |SQL/MED        |Y       |SQL/MED PostgreSQL                                                          |
|HW000    |HW   |datalink exception                                |000     |(no subclass)                                               |SQL/MED        |Y       |SQL/MED                                                                     |
|HW001    |HW   |datalink exception                                |001     |external file not linked                                    |SQL/MED        |Y       |SQL/MED                                                                     |
|HW002    |HW   |datalink exception                                |002     |external file already linked                                |SQL/MED        |Y       |SQL/MED                                                                     |
|HW003    |HW   |datalink exception                                |003     |referenced file does not exist                              |SQL/MED        |Y       |SQL/MED                                                                     |
|HW004    |HW   |datalink exception                                |004     |invalid write token                                         |SQL/MED        |Y       |SQL/MED                                                                     |
|HW005    |HW   |datalink exception                                |005     |invalid datalink construction                               |SQL/MED        |Y       |SQL/MED                                                                     |
|HW006    |HW   |datalink exception                                |006     |invalid write permission for update                         |SQL/MED        |Y       |SQL/MED                                                                     |
|HW007    |HW   |datalink exception                                |007     |referenced file not valid                                   |SQL/MED        |Y       |SQL/MED                                                                     |
|HY000    |HY   |CLI-specific condition                            |000     |(no subclass)                                               |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY001    |HY   |CLI-specific condition                            |001     |memory allocation error                                     |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY003    |HY   |CLI-specific condition                            |003     |invalid data type in application descriptor                 |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY004    |HY   |CLI-specific condition                            |004     |invalid data type                                           |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY007    |HY   |CLI-specific condition                            |007     |associated statement is not pre- pared                      |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY008    |HY   |CLI-specific condition                            |008     |operation canceled                                          |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY009    |HY   |CLI-specific condition                            |009     |invalid use of null pointer                                 |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY010    |HY   |CLI-specific condition                            |010     |function sequence error                                     |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY011    |HY   |CLI-specific condition                            |011     |attribute cannot be set now                                 |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY012    |HY   |CLI-specific condition                            |012     |invalid transaction operation code                          |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY013    |HY   |CLI-specific condition                            |013     |memory management error                                     |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY014    |HY   |CLI-specific condition                            |014     |limit on number of handles exceeded                         |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY015    |HY   |CLI-specific condition                            |015     |No cursor name available                                    |SQL Server     |N       |SQL Server                                                                  |
|HY016    |HY   |CLI-specific condition                            |016     |Cannot modify an implementation row descriptor              |SQL Server     |N       |SQL Server                                                                  |
|HY017    |HY   |CLI-specific condition                            |017     |invalid use of automatically-allocated descriptor handle    |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY018    |HY   |CLI-specific condition                            |018     |server declined the cancellation request                    |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY019    |HY   |CLI-specific condition                            |019     |non-string data cannot be sent in pieces                    |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY020    |HY   |CLI-specific condition                            |020     |attempt to concatenate a null value                         |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY021    |HY   |CLI-specific condition                            |021     |inconsistent descriptor information                         |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY024    |HY   |CLI-specific condition                            |024     |invalid attribute value                                     |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY055    |HY   |CLI-specific condition                            |055     |non-string data cannot be used with string routine          |SQL/CLI        |Y       |SQL/CLI                                                                     |
|HY090    |HY   |CLI-specific condition                            |090     |invalid string length or buffer length                      |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY091    |HY   |CLI-specific condition                            |091     |invalid descriptor field identifier                         |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY092    |HY   |CLI-specific condition                            |092     |invalid attribute identifier                                |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY093    |HY   |CLI-specific condition                            |093     |invalid datalink value                                      |SQL/MED        |Y       |SQL/MED                                                                     |
|HY095    |HY   |CLI-specific condition                            |095     |invalid FunctionId specified                                |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY096    |HY   |CLI-specific condition                            |096     |invalid information type                                    |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY097    |HY   |CLI-specific condition                            |097     |column type out of range                                    |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY098    |HY   |CLI-specific condition                            |098     |scope out of range                                          |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY099    |HY   |CLI-specific condition                            |099     |nullable type out of range                                  |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY100    |HY   |CLI-specific condition                            |100     |Uniqueness option type out of range                         |SQL Server     |N       |SQL Server                                                                  |
|HY101    |HY   |CLI-specific condition                            |101     |Accuracy option type out of range                           |SQL Server     |N       |SQL Server                                                                  |
|HY103    |HY   |CLI-specific condition                            |103     |invalid retrieval code                                      |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY104    |HY   |CLI-specific condition                            |104     |invalid LengthPrecision value                               |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY105    |HY   |CLI-specific condition                            |105     |invalid parameter mode                                      |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY106    |HY   |CLI-specific condition                            |106     |invalid fetch orientation                                   |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY107    |HY   |CLI-specific condition                            |107     |row value out of range                                      |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY109    |HY   |CLI-specific condition                            |109     |invalid cursor position                                     |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HY110    |HY   |CLI-specific condition                            |110     |Invalid driver completion                                   |SQL Server     |N       |SQL Server                                                                  |
|HY111    |HY   |CLI-specific condition                            |111     |Invalid bookmark value                                      |SQL Server     |N       |SQL Server                                                                  |
|HYC00    |HY   |CLI-specific condition                            |C00     |optional feature not implemented                            |SQL/CLI        |Y       |SQL/CLI SQL Server                                                          |
|HYT00    |HY   |CLI-specific condition                            |T00     |Timeout expired                                             |SQL Server     |N       |SQL Server                                                                  |
|HYT01    |HY   |CLI-specific condition                            |T01     |Connection timeout expired                                  |SQL Server     |N       |SQL Server                                                                  |
|HZ000    |HZ   |RDA-specific condition                            |000     |(no subclass)                                               |RDA/SQL        |Y       |RDA/SQL Oracle                                                              |
|HZ301    |HZ   |RDA-specific condition                            |301     |attribute not permitted                                     |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ302    |HZ   |RDA-specific condition                            |302     |authentication failure                                      |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ303    |HZ   |RDA-specific condition                            |303     |duplicate request ident                                     |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ304    |HZ   |RDA-specific condition                            |304     |encoding not supported                                      |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ305    |HZ   |RDA-specific condition                            |305     |feature not supported – multiple server transactions        |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ306    |HZ   |RDA-specific condition                            |306     |invalid attribute type                                      |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ307    |HZ   |RDA-specific condition                            |307     |invalid fetch count                                         |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ308    |HZ   |RDA-specific condition                            |308     |invalid message type                                        |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ309    |HZ   |RDA-specific condition                            |309     |invalid operation sequence                                  |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ310    |HZ   |RDA-specific condition                            |310     |invalid transaction operation code                          |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ311    |HZ   |RDA-specific condition                            |311     |mismatch between descriptor and row                         |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ312    |HZ   |RDA-specific condition                            |312     |no connection handle available                              |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ313    |HZ   |RDA-specific condition                            |313     |number of values does not match number of item descriptors  |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ314    |HZ   |RDA-specific condition                            |314     |transaction cannot commit                                   |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ315    |HZ   |RDA-specific condition                            |315     |transaction state unknown                                   |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ316    |HZ   |RDA-specific condition                            |316     |transport failure                                           |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ317    |HZ   |RDA-specific condition                            |317     |unexpected parameter descriptor                             |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ318    |HZ   |RDA-specific condition                            |318     |unexpected row descriptor                                   |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ319    |HZ   |RDA-specific condition                            |319     |unexpected rows                                             |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ320    |HZ   |RDA-specific condition                            |320     |version not supported                                       |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ321    |HZ   |RDA-specific condition                            |321     |TCP/IP error                                                |RDA/SQL        |Y       |RDA/SQL                                                                     |
|HZ322    |HZ   |RDA-specific condition                            |322     |TLS alert                                                   |RDA/SQL        |Y       |RDA/SQL                                                                     |
|IM001    |IM   |ODBC driver                                       |001     |Driver does not support this function                       |SQL Server     |N       |SQL Server                                                                  |
|IM002    |IM   |ODBC driver                                       |002     |Data source name not found and no default driver specified  |SQL Server     |N       |SQL Server                                                                  |
|IM003    |IM   |ODBC driver                                       |003     |Specified driver could not be loaded                        |SQL Server     |N       |SQL Server                                                                  |
|IM004    |IM   |ODBC driver                                       |004     |Driver's SQLAllocHandle on SQL_HANDLE_ENV failed            |SQL Server     |N       |SQL Server                                                                  |
|IM005    |IM   |ODBC driver                                       |005     |Driver's SQLAllocHandle on SQL_HANDLE_DBC failed            |SQL Server     |N       |SQL Server                                                                  |
|IM006    |IM   |ODBC driver                                       |006     |Driver's SQLSetConnectAttr failed                           |SQL Server     |N       |SQL Server                                                                  |
|IM007    |IM   |ODBC driver                                       |007     |No data source or driver specified; dialog prohibited       |SQL Server     |N       |SQL Server                                                                  |
|IM008    |IM   |ODBC driver                                       |008     |Dialog failed                                               |SQL Server     |N       |SQL Server                                                                  |
|IM009    |IM   |ODBC driver                                       |009     |Unable to load translation DLL                              |SQL Server     |N       |SQL Server                                                                  |
|IM010    |IM   |ODBC driver                                       |010     |Data source name too long                                   |SQL Server     |N       |SQL Server                                                                  |
|IM011    |IM   |ODBC driver                                       |011     |Driver name too long                                        |SQL Server     |N       |SQL Server                                                                  |
|IM012    |IM   |ODBC driver                                       |012     |DRIVER keyword syntax error                                 |SQL Server     |N       |SQL Server                                                                  |
|IM013    |IM   |ODBC driver                                       |013     |Trace file error                                            |SQL Server     |N       |SQL Server                                                                  |
|IM014    |IM   |ODBC driver                                       |014     |Invalid name of File DSN                                    |SQL Server     |N       |SQL Server                                                                  |
|IM015    |IM   |ODBC driver                                       |015     |Corrupt file data source                                    |SQL Server     |N       |SQL Server                                                                  |
|P0000    |P0   |PL/pgSQL Error                                    |000     |plpgsql_error                                               |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|P0001    |P0   |PL/pgSQL Error                                    |001     |raise_exception                                             |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|P0002    |P0   |PL/pgSQL Error                                    |002     |no_data_found                                               |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|P0003    |P0   |PL/pgSQL Error                                    |003     |too_many_rows                                               |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|P0004    |P0   |PL/pgSQL Error                                    |004     |assert_failure                                              |PostgreSQL     |N       |PostgreSQL                                                                  |
|XX000    |XX   |Internal Error                                    |000     |internal_error                                              |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|XX001    |XX   |Internal Error                                    |001     |data_corrupted                                              |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
|XX002    |XX   |Internal Error                                    |002     |index_corrupted                                             |PostgreSQL     |N       |PostgreSQL Redshift                                                         |
<!-- SQLSTATE table stop -->
