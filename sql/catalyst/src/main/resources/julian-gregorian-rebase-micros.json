[{"tz": "Africa/Abidjan", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Accra", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Addis_Ababa", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Africa/Algiers", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2486595771, -**********], "diffs": [175668, 89268, 2868, -83532, -169932, -256332, -342732, -429132, -515532, -601932, -688332, -774732, -861132, 2868, 3039, 0]}, {"tz": "Africa/Asmara", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Africa/Asmera", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Africa/Bamako", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Bangui", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 0]}, {"tz": "Africa/Banjul", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Bissau", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [176540, 90140, 3740, -82660, -169060, -255460, -341860, -428260, -514660, -601060, -687460, -773860, -860260, 3740, 0]}, {"tz": "Africa/Blantyre", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172180, 85780, -620, -87020, -173420, -259820, -346220, -432620, -519020, -605420, -691820, -778220, -864620, -620, 0]}, {"tz": "Africa/Brazzaville", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 0]}, {"tz": "Africa/Bujumbura", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172180, 85780, -620, -87020, -173420, -259820, -346220, -432620, -519020, -605420, -691820, -778220, -864620, -620, 0]}, {"tz": "Africa/Cairo", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172491, 86091, -309, -86709, -173109, -259509, -345909, -432309, -518709, -605109, -691509, -777909, -864309, -309, 0]}, {"tz": "Africa/Casablanca", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174620, 88220, 1820, -84580, -170980, -257380, -343780, -430180, -516580, -602980, -689380, -775780, -862180, 1820, 0]}, {"tz": "Africa/Ceuta", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [177676, 91276, 4876, -81524, -167924, -254324, -340724, -427124, -513524, -599924, -686324, -772724, -859124, 4876, 0]}, {"tz": "Africa/Conakry", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Dakar", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Dar_es_Salaam", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Africa/Djibouti", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Africa/Douala", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 0]}, {"tz": "Africa/El_Aaiun", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175968, 89568, 3168, -83232, -169632, -256032, -342432, -428832, -515232, -601632, -688032, -774432, -860832, 3168, 0]}, {"tz": "Africa/Freetown", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Gaborone", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172180, 85780, -620, -87020, -173420, -259820, -346220, -432620, -519020, -605420, -691820, -778220, -864620, -620, 0]}, {"tz": "Africa/Harare", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172180, 85780, -620, -87020, -173420, -259820, -346220, -432620, -519020, -605420, -691820, -778220, -864620, -620, 0]}, {"tz": "Africa/Johannesburg", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2458174920, -**********], "diffs": [173280, 86880, 480, -85920, -172320, -258720, -345120, -431520, -517920, -604320, -690720, -777120, -863520, 480, 1800, 0]}, {"tz": "Africa/Juba", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172412, 86012, -388, -86788, -173188, -259588, -345988, -432388, -518788, -605188, -691588, -777988, -864388, -388, 0]}, {"tz": "Africa/Kampala", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Africa/Khartoum", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172192, 85792, -608, -87008, -173408, -259808, -346208, -432608, -519008, -605408, -691808, -778208, -864608, -608, 0]}, {"tz": "Africa/Kigali", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172180, 85780, -620, -87020, -173420, -259820, -346220, -432620, -519020, -605420, -691820, -778220, -864620, -620, 0]}, {"tz": "Africa/Kinshasa", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 0]}, {"tz": "Africa/Lagos", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 0]}, {"tz": "Africa/Libreville", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 0]}, {"tz": "Africa/Lome", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Luanda", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 0]}, {"tz": "Africa/Lubumbashi", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172180, 85780, -620, -87020, -173420, -259820, -346220, -432620, -519020, -605420, -691820, -778220, -864620, -620, 0]}, {"tz": "Africa/Lusaka", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172180, 85780, -620, -87020, -173420, -259820, -346220, -432620, -519020, -605420, -691820, -778220, -864620, -620, 0]}, {"tz": "Africa/Malabo", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 0]}, {"tz": "Africa/Maputo", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172180, 85780, -620, -87020, -173420, -259820, -346220, -432620, -519020, -605420, -691820, -778220, -864620, -620, 0]}, {"tz": "Africa/Maseru", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2458174920, -**********], "diffs": [173280, 86880, 480, -85920, -172320, -258720, -345120, -431520, -517920, -604320, -690720, -777120, -863520, 480, 1800, 0]}, {"tz": "Africa/Mbabane", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2458174920, -**********], "diffs": [173280, 86880, 480, -85920, -172320, -258720, -345120, -431520, -517920, -604320, -690720, -777120, -863520, 480, 1800, 0]}, {"tz": "Africa/Mogadishu", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Africa/Monrovia", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175388, 88988, 2588, -83812, -170212, -256612, -343012, -429412, -515812, -602212, -688612, -775012, -861412, 2588, 0]}, {"tz": "Africa/Nairobi", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Africa/Ndjamena", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [172788, 86388, -12, -86412, -172812, -259212, -345612, -432012, -518412, -604812, -691212, -777612, -864012, -12, 0]}, {"tz": "Africa/Niamey", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 0]}, {"tz": "Africa/Nouakchott", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Ouagadougou", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Porto-Novo", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 0]}, {"tz": "Africa/Sao_Tome", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2713914221, -**********], "diffs": [171184, 84784, -1616, -88016, -174416, -260816, -347216, -433616, -520016, -606416, -692816, -779216, -865616, -1616, 2205, 0]}, {"tz": "Africa/Timbuktu", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Africa/Tripoli", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [176836, 90436, 4036, -82364, -168764, -255164, -341564, -427964, -514364, -600764, -687164, -773564, -859964, 4036, 0]}, {"tz": "Africa/Tunis", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2797205483, -**********], "diffs": [173956, 87556, 1156, -85244, -171644, -258044, -344444, -430844, -517244, -603644, -690044, -776444, -862844, 1156, 3039, 0]}, {"tz": "Africa/Windhoek", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2458172304, -**********], "diffs": [175896, 89496, 3096, -83304, -169704, -256104, -342504, -428904, -515304, -601704, -688104, -774504, -860904, 3096, 1800, 0]}, {"tz": "America/Adak", "switches": [-62135733600, -59006412000, -55850652000, -52694892000, -46383372000, -43227612000, -40071852000, -33760332000, -30604572000, -27448812000, -21137292000, -17981532000, -14825772000, -12219256800, -3225230125, -**********], "diffs": [92798, 6398, -80002, -166402, -252802, -339202, -425602, -512002, -598402, -684802, -771202, -857602, -944002, -80002, 6398, 0]}, {"tz": "America/Anchorage", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400, -3225227303, -**********], "diffs": [89976, 3576, -82824, -169224, -255624, -342024, -428424, -514824, -601224, -687624, -774024, -860424, -946824, -82824, 3576, 0]}, {"tz": "America/Anguilla", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Antigua", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Araguaina", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173568, 87168, 768, -85632, -172032, -258432, -344832, -431232, -517632, -604032, -690432, -776832, -863232, 768, 0]}, {"tz": "America/Argentina/Buenos_Aires", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372102580, -**********], "diffs": [176028, 89628, 3228, -83172, -169572, -255972, -342372, -428772, -515172, -601572, -687972, -774372, -860772, 3228, 4608, 0]}, {"tz": "America/Argentina/Catamarca", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100820, -**********], "diffs": [177788, 91388, 4988, -81412, -167812, -254212, -340612, -427012, -513412, -599812, -686212, -772612, -859012, 4988, 4608, 0]}, {"tz": "America/Argentina/ComodRivadavia", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100820, -**********], "diffs": [177788, 91388, 4988, -81412, -167812, -254212, -340612, -427012, -513412, -599812, -686212, -772612, -859012, 4988, 4608, 0]}, {"tz": "America/Argentina/Cordoba", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [177408, 91008, 4608, -81792, -168192, -254592, -340992, -427392, -513792, -600192, -686592, -772992, -859392, 4608, 0]}, {"tz": "America/Argentina/Jujuy", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100936, -**********], "diffs": [177672, 91272, 4872, -81528, -167928, -254328, -340728, -427128, -513528, -599928, -686328, -772728, -859128, 4872, 4608, 0]}, {"tz": "America/Argentina/La_Rioja", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100564, -**********], "diffs": [178044, 91644, 5244, -81156, -167556, -253956, -340356, -426756, -513156, -599556, -685956, -772356, -858756, 5244, 4608, 0]}, {"tz": "America/Argentina/Mendoza", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100092, -**********], "diffs": [178516, 92116, 5716, -80684, -167084, -253484, -339884, -426284, -512684, -599084, -685484, -771884, -858284, 5716, 4608, 0]}, {"tz": "America/Argentina/Rio_Gallegos", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372099996, -**********], "diffs": [178612, 92212, 5812, -80588, -166988, -253388, -339788, -426188, -512588, -598988, -685388, -771788, -858188, 5812, 4608, 0]}, {"tz": "America/Argentina/Salta", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100908, -**********], "diffs": [177700, 91300, 4900, -81500, -167900, -254300, -340700, -427100, -513500, -599900, -686300, -772700, -859100, 4900, 4608, 0]}, {"tz": "America/Argentina/San_Juan", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100164, -**********], "diffs": [178444, 92044, 5644, -80756, -167156, -253556, -339956, -426356, -512756, -599156, -685556, -771956, -858356, 5644, 4608, 0]}, {"tz": "America/Argentina/San_Luis", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100684, -**********], "diffs": [177924, 91524, 5124, -81276, -167676, -254076, -340476, -426876, -513276, -599676, -686076, -772476, -858876, 5124, 4608, 0]}, {"tz": "America/Argentina/Tucuman", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100956, -**********], "diffs": [177652, 91252, 4852, -81548, -167948, -254348, -340748, -427148, -513548, -599948, -686348, -772748, -859148, 4852, 4608, 0]}, {"tz": "America/Argentina/Ushuaia", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100216, -**********], "diffs": [178392, 91992, 5592, -80808, -167208, -253608, -340008, -426408, -512808, -599208, -685608, -772008, -858408, 5592, 4608, 0]}, {"tz": "America/Aruba", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Asuncion", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [172240, 85840, -560, -86960, -173360, -259760, -346160, -432560, -518960, -605360, -691760, -778160, -864560, -560, 0]}, {"tz": "America/Atikokan", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2524503688, -**********], "diffs": [173888, 87488, 1088, -85312, -171712, -258112, -344512, -430912, -517312, -603712, -690112, -776512, -862912, 1088, 1176, 0]}, {"tz": "America/Atka", "switches": [-62135733600, -59006412000, -55850652000, -52694892000, -46383372000, -43227612000, -40071852000, -33760332000, -30604572000, -27448812000, -21137292000, -17981532000, -14825772000, -12219256800, -3225230125, -**********], "diffs": [92798, 6398, -80002, -166402, -252802, -339202, -425602, -512002, -598402, -684802, -771202, -857602, -944002, -80002, 6398, 0]}, {"tz": "America/Bahia", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [171244, 84844, -1556, -87956, -174356, -260756, -347156, -433556, -519956, -606356, -692756, -779156, -865556, -1556, 0]}, {"tz": "America/Bahia_Banderas", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [176460, 90060, 3660, -82740, -169140, -255540, -341940, -428340, -514740, -601140, -687540, -773940, -860340, 3660, 0]}, {"tz": "America/Barbados", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [172709, 86309, -91, -86491, -172891, -259291, -345691, -432091, -518491, -604891, -691291, -777691, -864091, -91, 0]}, {"tz": "America/Belem", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173636, 87236, 836, -85564, -171964, -258364, -344764, -431164, -517564, -603964, -690364, -776764, -863164, 836, 0]}, {"tz": "America/Belize", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [172368, 85968, -432, -86832, -173232, -259632, -346032, -432432, -518832, -605232, -691632, -778032, -864432, -432, 0]}, {"tz": "America/Blanc-Sablon", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Boa_Vista", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [172960, 86560, 160, -86240, -172640, -259040, -345440, -431840, -518240, -604640, -691040, -777440, -863840, 160, 0]}, {"tz": "America/Bogota", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [172576, 86176, -224, -86624, -173024, -259424, -345824, -432224, -518624, -605024, -691424, -777824, -864224, -224, 0]}, {"tz": "America/Boise", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -2717643600, -**********], "diffs": [175489, 89089, 2689, -83711, -170111, -256511, -342911, -429311, -515711, -602111, -688511, -774911, -861311, 2689, 3600, 0]}, {"tz": "America/Buenos_Aires", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372102580, -**********], "diffs": [176028, 89628, 3228, -83172, -169572, -255972, -342372, -428772, -515172, -601572, -687972, -774372, -860772, 3228, 4608, 0]}, {"tz": "America/Cambridge_Bay", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [147600, 61200, -25200, -111600, -198000, -284400, -370800, -457200, -543600, -630000, -716400, -802800, -889200, -25200, 0]}, {"tz": "America/Campo_Grande", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [171508, 85108, -1292, -87692, -174092, -260492, -346892, -433292, -519692, -606092, -692492, -778892, -865292, -1292, 0]}, {"tz": "America/Cancun", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [175624, 89224, 2824, -83576, -169976, -256376, -342776, -429176, -515576, -601976, -688376, -774776, -861176, 2824, 0]}, {"tz": "America/Caracas", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2524507196, -**********], "diffs": [174464, 88064, 1664, -84736, -171136, -257536, -343936, -430336, -516736, -603136, -689536, -775936, -862336, 1664, 1660, 0]}, {"tz": "America/Catamarca", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100820, -**********], "diffs": [177788, 91388, 4988, -81412, -167812, -254212, -340612, -427012, -513412, -599812, -686212, -772612, -859012, 4988, 4608, 0]}, {"tz": "America/Cayenne", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174560, 88160, 1760, -84640, -171040, -257440, -343840, -430240, -516640, -603040, -689440, -775840, -862240, 1760, 0]}, {"tz": "America/Cayman", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2524503688, -**********], "diffs": [173888, 87488, 1088, -85312, -171712, -258112, -344512, -430912, -517312, -603712, -690112, -776512, -862912, 1088, 1176, 0]}, {"tz": "America/Chicago", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2717647200], "diffs": [172236, 85836, -564, -86964, -173364, -259764, -346164, -432564, -518964, -605364, -691764, -778164, -864564, -564, 0]}, {"tz": "America/Chihuahua", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [173060, 86660, 260, -86140, -172540, -258940, -345340, -431740, -518140, -604540, -690940, -777340, -863740, 260, 0]}, {"tz": "America/Coral_Harbour", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2524503688, -**********], "diffs": [173888, 87488, 1088, -85312, -171712, -258112, -344512, -430912, -517312, -603712, -690112, -776512, -862912, 1088, 1176, 0]}, {"tz": "America/Cordoba", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [177408, 91008, 4608, -81792, -168192, -254592, -340992, -427392, -513792, -600192, -686592, -772992, -859392, 4608, 0]}, {"tz": "America/Costa_Rica", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [171373, 84973, -1427, -87827, -174227, -260627, -347027, -433427, -519827, -606227, -692627, -779027, -865427, -1427, 0]}, {"tz": "America/Creston", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -2717643600], "diffs": [174498, 88098, 1698, -84702, -171102, -257502, -343902, -430302, -516702, -603102, -689502, -775902, -862302, 1698, 0]}, {"tz": "America/Cuiaba", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [171860, 85460, -940, -87340, -173740, -260140, -346540, -432940, -519340, -605740, -692140, -778540, -864940, -940, 0]}, {"tz": "America/Curacao", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Danmarkshavn", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [177280, 90880, 4480, -81920, -168320, -254720, -341120, -427520, -513920, -600320, -686720, -773120, -859520, 4480, 0]}, {"tz": "America/Dawson", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [181060, 94660, 8260, -78140, -164540, -250940, -337340, -423740, -510140, -596540, -682940, -769340, -855740, 8260, 0]}, {"tz": "America/Dawson_Creek", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -2713885144, -**********], "diffs": [176456, 90056, 3656, -82744, -169144, -255544, -341944, -428344, -514744, -601144, -687544, -773944, -860344, 3656, 3600, 0]}, {"tz": "America/Denver", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -2717643600], "diffs": [172796, 86396, -4, -86404, -172804, -259204, -345604, -432004, -518404, -604804, -691204, -777604, -864004, -4, 0]}, {"tz": "America/Detroit", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [174731, 88331, 1931, -84469, -170869, -257269, -343669, -430069, -516469, -602869, -689269, -775669, -862069, 1931, 0]}, {"tz": "America/Dominica", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Edmonton", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [174832, 88432, 2032, -84368, -170768, -257168, -343568, -429968, -516368, -602768, -689168, -775568, -861968, 2032, 0]}, {"tz": "America/Eirunepe", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [171568, 85168, -1232, -87632, -174032, -260432, -346832, -433232, -519632, -606032, -692432, -778832, -865232, -1232, 0]}, {"tz": "America/El_Salvador", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [172608, 86208, -192, -86592, -172992, -259392, -345792, -432192, -518592, -604992, -691392, -777792, -864192, -192, 0]}, {"tz": "America/Ensenada", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000, -**********], "diffs": [172084, 85684, -716, -87116, -173516, -259916, -346316, -432716, -519116, -605516, -691916, -778316, -864716, -716, 0]}, {"tz": "America/Fort_Nelson", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -2713884553, -**********], "diffs": [177047, 90647, 4247, -82153, -168553, -254953, -341353, -427753, -514153, -600553, -686953, -773353, -859753, 4247, 3600, 0]}, {"tz": "America/Fort_Wayne", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175478, 89078, 2678, -83722, -170122, -256522, -342922, -429322, -515722, -602122, -688522, -774922, -861322, 2678, 3600, 0]}, {"tz": "America/Fortaleza", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [171240, 84840, -1560, -87960, -174360, -260760, -347160, -433560, -519960, -606360, -692760, -779160, -865560, -1560, 0]}, {"tz": "America/Glace_Bay", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [172788, 86388, -12, -86412, -172812, -259212, -345612, -432012, -518412, -604812, -691212, -777612, -864012, -12, 0]}, {"tz": "America/Godthab", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174416, 88016, 1616, -84784, -171184, -257584, -343984, -430384, -516784, -603184, -689584, -775984, -862384, 1616, 0]}, {"tz": "America/Goose_Bay", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2713894152, -**********], "diffs": [172900, 86500, 100, -86300, -172700, -259100, -345500, -431900, -518300, -604700, -691100, -777500, -863900, 100, -1748, 0]}, {"tz": "America/Grand_Turk", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2524504958, -**********], "diffs": [171872, 85472, -928, -87328, -173728, -260128, -346528, -432928, -519328, -605728, -692128, -778528, -864928, -928, 430, 0]}, {"tz": "America/Grenada", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Guadeloupe", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Guatemala", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [172924, 86524, 124, -86276, -172676, -259076, -345476, -431876, -518276, -604676, -691076, -777476, -863876, 124, 0]}, {"tz": "America/Guayaquil", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2524503280, -**********], "diffs": [173960, 87560, 1160, -85240, -171640, -258040, -344440, -430840, -517240, -603640, -690040, -776440, -862840, 1160, 840, 0]}, {"tz": "America/Guyana", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [172359, 85959, -441, -86841, -173241, -259641, -346041, -432441, -518841, -605241, -691641, -778041, -864441, -441, 0]}, {"tz": "America/Halifax", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [173664, 87264, 864, -85536, -171936, -258336, -344736, -431136, -517536, -603936, -690336, -776736, -863136, 864, 0]}, {"tz": "America/Havana", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2524503608, -**********], "diffs": [174568, 88168, 1768, -84632, -171032, -257432, -343832, -430232, -516632, -603032, -689432, -775832, -862232, 1768, 1776, 0]}, {"tz": "America/Hermosillo", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [174232, 87832, 1432, -84968, -171368, -257768, -344168, -430568, -516968, -603368, -689768, -776168, -862568, 1432, 0]}, {"tz": "America/Indiana/Indianapolis", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175478, 89078, 2678, -83722, -170122, -256522, -342922, -429322, -515722, -602122, -688522, -774922, -861322, 2678, 3600, 0]}, {"tz": "America/Indiana/Knox", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2717647200], "diffs": [171990, 85590, -810, -87210, -173610, -260010, -346410, -432810, -519210, -605610, -692010, -778410, -864810, -810, 0]}, {"tz": "America/Indiana/Marengo", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175523, 89123, 2723, -83677, -170077, -256477, -342877, -429277, -515677, -602077, -688477, -774877, -861277, 2723, 3600, 0]}, {"tz": "America/Indiana/Petersburg", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175747, 89347, 2947, -83453, -169853, -256253, -342653, -429053, -515453, -601853, -688253, -774653, -861053, 2947, 3600, 0]}, {"tz": "America/Indiana/Tell_City", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2717647200], "diffs": [172023, 85623, -777, -87177, -173577, -259977, -346377, -432777, -519177, -605577, -691977, -778377, -864777, -777, 0]}, {"tz": "America/Indiana/Vevay", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175216, 88816, 2416, -83984, -170384, -256784, -343184, -429584, -515984, -602384, -688784, -775184, -861584, 2416, 3600, 0]}, {"tz": "America/Indiana/Vincennes", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175807, 89407, 3007, -83393, -169793, -256193, -342593, -428993, -515393, -601793, -688193, -774593, -860993, 3007, 3600, 0]}, {"tz": "America/Indiana/Winamac", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175585, 89185, 2785, -83615, -170015, -256415, -342815, -429215, -515615, -602015, -688415, -774815, -861215, 2785, 3600, 0]}, {"tz": "America/Indianapolis", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175478, 89078, 2678, -83722, -170122, -256522, -342922, -429322, -515722, -602122, -688522, -774922, -861322, 2678, 3600, 0]}, {"tz": "America/Inuvik", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [147600, 61200, -25200, -111600, -198000, -284400, -370800, -457200, -543600, -630000, -716400, -802800, -889200, -25200, 0]}, {"tz": "America/Iqaluit", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [154800, 68400, -18000, -104400, -190800, -277200, -363600, -450000, -536400, -622800, -709200, -795600, -882000, -18000, 0]}, {"tz": "America/Jamaica", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [173230, 86830, 430, -85970, -172370, -258770, -345170, -431570, -517970, -604370, -690770, -777170, -863570, 430, 0]}, {"tz": "America/Jujuy", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100936, -**********], "diffs": [177672, 91272, 4872, -81528, -167928, -254328, -340728, -427128, -513528, -599928, -686328, -772728, -859128, 4872, 4608, 0]}, {"tz": "America/Juneau", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400, -3225223588, -**********], "diffs": [86261, -139, -86539, -172939, -259339, -345739, -432139, -518539, -604939, -691339, -777739, -864139, -950539, -86539, -139, 0]}, {"tz": "America/Kentucky/Louisville", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175382, 88982, 2582, -83818, -170218, -256618, -343018, -429418, -515818, -602218, -688618, -775018, -861418, 2582, 3600, 0]}, {"tz": "America/Kentucky/Monticello", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175164, 88764, 2364, -84036, -170436, -256836, -343236, -429636, -516036, -602436, -688836, -775236, -861636, 2364, 3600, 0]}, {"tz": "America/Knox_IN", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2717647200], "diffs": [171990, 85590, -810, -87210, -173610, -260010, -346410, -432810, -519210, -605610, -692010, -778410, -864810, -810, 0]}, {"tz": "America/Kralendijk", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/La_Paz", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [174756, 88356, 1956, -84444, -170844, -257244, -343644, -430044, -516444, -602844, -689244, -775644, -862044, 1956, 0]}, {"tz": "America/Lima", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2524503624, -**********], "diffs": [173292, 86892, 492, -85908, -172308, -258708, -345108, -431508, -517908, -604308, -690708, -777108, -863508, 492, 516, 0]}, {"tz": "America/Los_Angeles", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000, -2717640000], "diffs": [172378, 85978, -422, -86822, -173222, -259622, -346022, -432422, -518822, -605222, -691622, -778022, -864422, -422, 0]}, {"tz": "America/Louisville", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175382, 88982, 2582, -83818, -170218, -256618, -343018, -429418, -515818, -602218, -688618, -775018, -861418, 2582, 3600, 0]}, {"tz": "America/Lower_Princes", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Maceio", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [170572, 84172, -2228, -88628, -175028, -261428, -347828, -434228, -520628, -607028, -693428, -779828, -866228, -2228, 0]}, {"tz": "America/Managua", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2524500004, -**********], "diffs": [171908, 85508, -892, -87292, -173692, -260092, -346492, -432892, -519292, -605692, -692092, -778492, -864892, -892, -888, 0]}, {"tz": "America/Manaus", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [172804, 86404, 4, -86396, -172796, -259196, -345596, -431996, -518396, -604796, -691196, -777596, -863996, 4, 0]}, {"tz": "America/Marigot", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Martinique", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [173060, 86660, 260, -86140, -172540, -258940, -345340, -431740, -518140, -604540, -690940, -777340, -863740, 260, 0]}, {"tz": "America/Matamoros", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [175200, 88800, 2400, -84000, -170400, -256800, -343200, -429600, -516000, -602400, -688800, -775200, -861600, 2400, 0]}, {"tz": "America/Mazatlan", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [173140, 86740, 340, -86060, -172460, -258860, -345260, -431660, -518060, -604460, -690860, -777260, -863660, 340, 0]}, {"tz": "America/Mendoza", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2372100092, -**********], "diffs": [178516, 92116, 5716, -80684, -167084, -253484, -339884, -426284, -512684, -599084, -685484, -771884, -858284, 5716, 4608, 0]}, {"tz": "America/Menominee", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2659759773], "diffs": [172227, 85827, -573, -86973, -173373, -259773, -346173, -432573, -518973, -605373, -691773, -778173, -864573, -573, 0]}, {"tz": "America/Merida", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [172708, 86308, -92, -86492, -172892, -259292, -345692, -432092, -518492, -604892, -691292, -777692, -864092, -92, 0]}, {"tz": "America/Metlakatla", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400, -3225222905, -**********], "diffs": [85578, -822, -87222, -173622, -260022, -346422, -432822, -519222, -605622, -692022, -778422, -864822, -951222, -87222, -822, 0]}, {"tz": "America/Mexico_City", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [174996, 88596, 2196, -84204, -170604, -257004, -343404, -429804, -516204, -602604, -689004, -775404, -861804, 2196, 0]}, {"tz": "America/Miquelon", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175480, 89080, 2680, -83720, -170120, -256520, -342920, -429320, -515720, -602120, -688520, -774920, -861320, 2680, 0]}, {"tz": "America/Moncton", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2715885652, -**********], "diffs": [173948, 87548, 1148, -85252, -171652, -258052, -344452, -430852, -517252, -603652, -690052, -776452, -862852, 1148, 3600, 0]}, {"tz": "America/Monterrey", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [175276, 88876, 2476, -83924, -170324, -256724, -343124, -429524, -515924, -602324, -688724, -775124, -861524, 2476, 0]}, {"tz": "America/Montevideo", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175491, 89091, 2691, -83709, -170109, -256509, -342909, -429309, -515709, -602109, -688509, -774909, -861309, 2691, 0]}, {"tz": "America/Montreal", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2366736148], "diffs": [173852, 87452, 1052, -85348, -171748, -258148, -344548, -430948, -517348, -603748, -690148, -776548, -862948, 1052, 0]}, {"tz": "America/Montserrat", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Nassau", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2366736148], "diffs": [173852, 87452, 1052, -85348, -171748, -258148, -344548, -430948, -517348, -603748, -690148, -776548, -862948, 1052, 0]}, {"tz": "America/New_York", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800], "diffs": [172562, 86162, -238, -86638, -173038, -259438, -345838, -432238, -518638, -605038, -691438, -777838, -864238, -238, 0]}, {"tz": "America/Nipigon", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2366734016], "diffs": [175984, 89584, 3184, -83216, -169616, -256016, -342416, -428816, -515216, -601616, -688016, -774416, -860816, 3184, 0]}, {"tz": "America/Nome", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400, -3225231025, -**********], "diffs": [93698, 7298, -79102, -165502, -251902, -338302, -424702, -511102, -597502, -683902, -770302, -856702, -943102, -79102, 7298, 0]}, {"tz": "America/Noronha", "switches": [-62135762400, -59006440800, -55850680800, -52694920800, -46383400800, -43227640800, -40071880800, -33760360800, -30604600800, -27448840800, -21137320800, -17981560800, -14825800800, -12219285600, -**********], "diffs": [173380, 86980, 580, -85820, -172220, -258620, -345020, -431420, -517820, -604220, -690620, -777020, -863420, 580, 0]}, {"tz": "America/North_Dakota/Beulah", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2717647200, -**********], "diffs": [175627, 89227, 2827, -83573, -169973, -256373, -342773, -429173, -515573, -601973, -688373, -774773, -861173, 2827, 3600, 0]}, {"tz": "America/North_Dakota/Center", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2717647200, -**********], "diffs": [175512, 89112, 2712, -83688, -170088, -256488, -342888, -429288, -515688, -602088, -688488, -774888, -861288, 2712, 3600, 0]}, {"tz": "America/North_Dakota/New_Salem", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2717647200, -**********], "diffs": [175539, 89139, 2739, -83661, -170061, -256461, -342861, -429261, -515661, -602061, -688461, -774861, -861261, 2739, 3600, 0]}, {"tz": "America/Nuuk", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174416, 88016, 1616, -84784, -171184, -257584, -343984, -430384, -516784, -603184, -689584, -775984, -862384, 1616, 0]}, {"tz": "America/Ojinaga", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [172660, 86260, -140, -86540, -172940, -259340, -345740, -432140, -518540, -604940, -691340, -777740, -864140, -140, 0]}, {"tz": "America/Panama", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2524503688, -**********], "diffs": [173888, 87488, 1088, -85312, -171712, -258112, -344512, -430912, -517312, -603712, -690112, -776512, -862912, 1088, 1176, 0]}, {"tz": "America/Pangnirtung", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [154800, 68400, -18000, -104400, -190800, -277200, -363600, -450000, -536400, -622800, -709200, -795600, -882000, -18000, 0]}, {"tz": "America/Paramaribo", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175240, 88840, 2440, -83960, -170360, -256760, -343160, -429560, -515960, -602360, -688760, -775160, -861560, 2440, 0]}, {"tz": "America/Phoenix", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -2717643600], "diffs": [174498, 88098, 1698, -84702, -171102, -257502, -343902, -430302, -516702, -603102, -689502, -775902, -862302, 1698, 0]}, {"tz": "America/Port-au-Prince", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2524503580, -**********], "diffs": [172160, 85760, -640, -87040, -173440, -259840, -346240, -432640, -519040, -605440, -691840, -778240, -864640, -640, -660, 0]}, {"tz": "America/Port_of_Spain", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Porto_Acre", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [171072, 84672, -1728, -88128, -174528, -260928, -347328, -433728, -520128, -606528, -692928, -779328, -865728, -1728, 0]}, {"tz": "America/Porto_Velho", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [173736, 87336, 936, -85464, -171864, -258264, -344664, -431064, -517464, -603864, -690264, -776664, -863064, 936, 0]}, {"tz": "America/Puerto_Rico", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Punta_Arenas", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2524510745, -**********], "diffs": [179020, 92620, 6220, -80180, -166580, -252980, -339380, -425780, -512180, -598580, -684980, -771380, -857780, 6220, 6165, 0]}, {"tz": "America/Rainy_River", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2366732504], "diffs": [173896, 87496, 1096, -85304, -171704, -258104, -344504, -430904, -517304, -603704, -690104, -776504, -862904, 1096, 0]}, {"tz": "America/Rankin_Inlet", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [151200, 64800, -21600, -108000, -194400, -280800, -367200, -453600, -540000, -626400, -712800, -799200, -885600, -21600, 0]}, {"tz": "America/Recife", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [170376, 83976, -2424, -88824, -175224, -261624, -348024, -434424, -520824, -607224, -693624, -780024, -866424, -2424, 0]}, {"tz": "America/Regina", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [176316, 89916, 3516, -82884, -169284, -255684, -342084, -428484, -514884, -601284, -687684, -774084, -860484, 3516, 0]}, {"tz": "America/Resolute", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [151200, 64800, -21600, -108000, -194400, -280800, -367200, -453600, -540000, -626400, -712800, -799200, -885600, -21600, 0]}, {"tz": "America/Rio_Branco", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [171072, 84672, -1728, -88128, -174528, -260928, -347328, -433728, -520128, -606528, -692928, -779328, -865728, -1728, 0]}, {"tz": "America/Rosario", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [177408, 91008, 4608, -81792, -168192, -254592, -340992, -427392, -513792, -600192, -686592, -772992, -859392, 4608, 0]}, {"tz": "America/Santa_Isabel", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000, -**********], "diffs": [172084, 85684, -716, -87116, -173516, -259916, -346316, -432716, -519116, -605516, -691916, -778316, -864716, -716, 0]}, {"tz": "America/Santarem", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175128, 88728, 2328, -84072, -170472, -256872, -343272, -429672, -516072, -602472, -688872, -775272, -861672, 2328, 0]}, {"tz": "America/Santiago", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [175365, 88965, 2565, -83835, -170235, -256635, -343035, -429435, -515835, -602235, -688635, -775035, -861435, 2565, 0]}, {"tz": "America/Santo_Domingo", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2524507224, -**********], "diffs": [175176, 88776, 2376, -84024, -170424, -256824, -343224, -429624, -516024, -602424, -688824, -775224, -861624, 2376, 2400, 0]}, {"tz": "America/Sao_Paulo", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173188, 86788, 388, -86012, -172412, -258812, -345212, -431612, -518012, -604412, -690812, -777212, -863612, 388, 0]}, {"tz": "America/Scoresbysund", "switches": [-62135766000, -59006444400, -55850684400, -52694924400, -46383404400, -43227644400, -40071884400, -33760364400, -30604604400, -27448844400, -21137324400, -17981564400, -14825804400, -12219289200, -**********], "diffs": [174472, 88072, 1672, -84728, -171128, -257528, -343928, -430328, -516728, -603128, -689528, -775928, -862328, 1672, 0]}, {"tz": "America/Shiprock", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -2717643600], "diffs": [172796, 86396, -4, -86404, -172804, -259204, -345604, -432004, -518404, -604804, -691204, -777604, -864004, -4, 0]}, {"tz": "America/Sitka", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400, -3225223800, -**********], "diffs": [86473, 73, -86327, -172727, -259127, -345527, -431927, -518327, -604727, -691127, -777527, -863927, -950327, -86327, 73, 0]}, {"tz": "America/St_Barthelemy", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/St_Johns", "switches": [-62135757000, -59006435400, -55850675400, -52694915400, -46383395400, -43227635400, -40071875400, -33760355400, -30604595400, -27448835400, -21137315400, -17981555400, -14825795400, -12219280200, -**********], "diffs": [172852, 86452, 52, -86348, -172748, -259148, -345548, -431948, -518348, -604748, -691148, -777548, -863948, 52, 0]}, {"tz": "America/St_Kitts", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/St_Lucia", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/St_Thomas", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/St_Vincent", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Swift_Current", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [177080, 90680, 4280, -82120, -168520, -254920, -341320, -427720, -514120, -600520, -686920, -773320, -859720, 4280, 0]}, {"tz": "America/Tegucigalpa", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [172132, 85732, -668, -87068, -173468, -259868, -346268, -432668, -519068, -605468, -691868, -778268, -864668, -668, 0]}, {"tz": "America/Thule", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [174908, 88508, 2108, -84292, -170692, -257092, -343492, -429892, -516292, -602692, -689092, -775492, -861892, 2108, 0]}, {"tz": "America/Thunder_Bay", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2366737380, -**********], "diffs": [176220, 89820, 3420, -82980, -169380, -255780, -342180, -428580, -514980, -601380, -687780, -774180, -860580, 3420, 3600, 0]}, {"tz": "America/Tijuana", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000, -**********], "diffs": [172084, 85684, -716, -87116, -173516, -259916, -346316, -432716, -519116, -605516, -691916, -778316, -864716, -716, 0]}, {"tz": "America/Toronto", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2366736148], "diffs": [173852, 87452, 1052, -85348, -171748, -258148, -344548, -430948, -517348, -603748, -690148, -776548, -862948, 1052, 0]}, {"tz": "America/Tortola", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Vancouver", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000, -2713880852], "diffs": [173548, 87148, 748, -85652, -172052, -258452, -344852, -431252, -517652, -604052, -690452, -776852, -863252, 748, 0]}, {"tz": "America/Virgin", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -2233035335], "diffs": [174265, 87865, 1465, -84935, -171335, -257735, -344135, -430535, -516935, -603335, -689735, -776135, -862535, 1465, 0]}, {"tz": "America/Whitehorse", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [180012, 93612, 7212, -79188, -165588, -251988, -338388, -424788, -511188, -597588, -683988, -770388, -856788, 7212, 0]}, {"tz": "America/Winnipeg", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2602258284], "diffs": [174516, 88116, 1716, -84684, -171084, -257484, -343884, -430284, -516684, -603084, -689484, -775884, -862284, 1716, 0]}, {"tz": "America/Yakutat", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400, -3225224862, -**********], "diffs": [87535, 1135, -85265, -171665, -258065, -344465, -430865, -517265, -603665, -690065, -776465, -862865, -949265, -85265, 1135, 0]}, {"tz": "America/Yellowknife", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [147600, 61200, -25200, -111600, -198000, -284400, -370800, -457200, -543600, -630000, -716400, -802800, -889200, -25200, 0]}, {"tz": "Antarctica/Casey", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -**********], "diffs": [212400, 126000, 39600, -46800, -133200, -219600, -306000, -392400, -478800, -565200, -651600, -738000, -824400, 39600, 0]}, {"tz": "Antarctica/Davis", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [198000, 111600, 25200, -61200, -147600, -234000, -320400, -406800, -493200, -579600, -666000, -752400, -838800, 25200, 0]}, {"tz": "Antarctica/DumontDUrville", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2840176808, -2366790512], "diffs": [173480, 87080, 680, -85720, -172120, -258520, -344920, -431320, -517720, -604120, -690520, -776920, -863320, 680, 688, 0]}, {"tz": "Antarctica/Macquarie", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2214259200], "diffs": [208800, 122400, 36000, -50400, -136800, -223200, -309600, -396000, -482400, -568800, -655200, -741600, -828000, 36000, 0]}, {"tz": "Antarctica/Mawson", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [190800, 104400, 18000, -68400, -154800, -241200, -327600, -414000, -500400, -586800, -673200, -759600, -846000, 18000, 0]}, {"tz": "Antarctica/McMurdo", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -3192437344, -**********], "diffs": [174056, 87656, 1256, -85144, -171544, -257944, -344344, -430744, -517144, -603544, -689944, -776344, -862744, 1256, 1800, 0]}, {"tz": "Antarctica/Palmer", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [162000, 75600, -10800, -97200, -183600, -270000, -356400, -442800, -529200, -615600, -702000, -788400, -874800, -10800, 0]}, {"tz": "Antarctica/Rothera", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [162000, 75600, -10800, -97200, -183600, -270000, -356400, -442800, -529200, -615600, -702000, -788400, -874800, -10800, 0]}, {"tz": "Antarctica/South_Pole", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -3192437344, -**********], "diffs": [174056, 87656, 1256, -85144, -171544, -257944, -344344, -430744, -517144, -603544, -689944, -776344, -862744, 1256, 1800, 0]}, {"tz": "Antarctica/Syowa", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [172388, 85988, -412, -86812, -173212, -259612, -346012, -432412, -518812, -605212, -691612, -778012, -864412, -412, 0]}, {"tz": "Antarctica/Troll", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Antarctica/Vostok", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -**********], "diffs": [173380, 86980, 580, -85820, -172220, -258620, -345020, -431420, -517820, -604220, -690620, -777020, -863420, 580, 0]}, {"tz": "Arctic/Longyearbyen", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2422054408], "diffs": [173192, 86792, 392, -86008, -172408, -258808, -345208, -431608, -518008, -604408, -690808, -777208, -863608, 392, 0]}, {"tz": "Asia/Aden", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [172388, 85988, -412, -86812, -173212, -259612, -346012, -432412, -518812, -605212, -691612, -778012, -864412, -412, 0]}, {"tz": "Asia/Almaty", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -**********], "diffs": [175932, 89532, 3132, -83268, -169668, -256068, -342468, -428868, -515268, -601668, -688068, -774468, -860868, 3132, 0]}, {"tz": "Asia/Amman", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [171376, 84976, -1424, -87824, -174224, -260624, -347024, -433424, -519824, -606224, -692624, -779024, -865424, -1424, 0]}, {"tz": "Asia/Anadyr", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [173404, 87004, 604, -85796, -172196, -258596, -344996, -431396, -517796, -604196, -690596, -776996, -863396, 604, 0]}, {"tz": "Asia/Aqtau", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [178736, 92336, 5936, -80464, -166864, -253264, -339664, -426064, -512464, -598864, -685264, -771664, -858064, 5936, 0]}, {"tz": "Asia/Aqtobe", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [177080, 90680, 4280, -82120, -168520, -254920, -341320, -427720, -514120, -600520, -686920, -773320, -859720, 4280, 0]}, {"tz": "Asia/Ashgabat", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [176788, 90388, 3988, -82412, -168812, -255212, -341612, -428012, -514412, -600812, -687212, -773612, -860012, 3988, 0]}, {"tz": "Asia/Ashkhabad", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [176788, 90388, 3988, -82412, -168812, -255212, -341612, -428012, -514412, -600812, -687212, -773612, -860012, 3988, 0]}, {"tz": "Asia/Atyrau", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [178336, 91936, 5536, -80864, -167264, -253664, -340064, -426464, -512864, -599264, -685664, -772064, -858464, 5536, 0]}, {"tz": "Asia/Baghdad", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -2524532404, -**********], "diffs": [172940, 86540, 140, -86260, -172660, -259060, -345460, -431860, -518260, -604660, -691060, -777460, -863860, 140, 144, 0]}, {"tz": "Asia/Bahrain", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [171232, 84832, -1568, -87968, -174368, -260768, -347168, -433568, -519968, -606368, -692768, -779168, -865568, -1568, 0]}, {"tz": "Asia/Baku", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [175236, 88836, 2436, -83964, -170364, -256764, -343164, -429564, -515964, -602364, -688764, -775164, -861564, 2436, 0]}, {"tz": "Asia/Bangkok", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [173876, 87476, 1076, -85324, -171724, -258124, -344524, -430924, -517324, -603724, -690124, -776524, -862924, 1076, 0]}, {"tz": "Asia/Barnaul", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [177900, 91500, 5100, -81300, -167700, -254100, -340500, -426900, -513300, -599700, -686100, -772500, -858900, 5100, 0]}, {"tz": "Asia/Beirut", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2840149320], "diffs": [171480, 85080, -1320, -87720, -174120, -260520, -346920, -433320, -519720, -606120, -692520, -778920, -865320, -1320, 0]}, {"tz": "Asia/Bishkek", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -**********], "diffs": [176496, 90096, 3696, -82704, -169104, -255504, -341904, -428304, -514704, -601104, -687504, -773904, -860304, 3696, 0]}, {"tz": "Asia/Brunei", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [175120, 88720, 2320, -84080, -170480, -256880, -343280, -429680, -516080, -602480, -688880, -775280, -861680, 2320, 0]}, {"tz": "Asia/Calcutta", "switches": [-62135789400, -59006467800, -55850707800, -52694947800, -46383427800, -43227667800, -40071907800, -33760387800, -30604627800, -27448867800, -21137347800, -17981587800, -14825827800, -12219312600, -3645235808, -3155695330, -**********], "diffs": [171392, 84992, -1408, -87808, -174208, -260608, -347008, -433408, -519808, -606208, -692608, -779008, -865408, -1408, -1400, 530, 0]}, {"tz": "Asia/Chita", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [177968, 91568, 5168, -81232, -167632, -254032, -340432, -426832, -513232, -599632, -686032, -772432, -858832, 5168, 0]}, {"tz": "Asia/Choibalsan", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [174120, 87720, 1320, -85080, -171480, -257880, -344280, -430680, -517080, -603480, -689880, -776280, -862680, 1320, 0]}, {"tz": "Asia/Chongqing", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [172457, 86057, -343, -86743, -173143, -259543, -345943, -432343, -518743, -605143, -691543, -777943, -864343, -343, 0]}, {"tz": "Asia/Chungking", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [172457, 86057, -343, -86743, -173143, -259543, -345943, -432343, -518743, -605143, -691543, -777943, -864343, -343, 0]}, {"tz": "Asia/Colombo", "switches": [-62135789400, -59006467800, -55850707800, -52694947800, -46383427800, -43227667800, -40071907800, -33760387800, -30604627800, -27448867800, -21137347800, -17981587800, -14825827800, -12219312600, -2840160592, -**********], "diffs": [173436, 87036, 636, -85764, -172164, -258564, -344964, -431364, -517764, -604164, -690564, -776964, -863364, 636, 628, 0]}, {"tz": "Asia/Dacca", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -2524543700, -**********], "diffs": [172700, 86300, -100, -86500, -172900, -259300, -345700, -432100, -518500, -604900, -691300, -777700, -864100, -100, 400, 0]}, {"tz": "Asia/Damascus", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [171288, 84888, -1512, -87912, -174312, -260712, -347112, -433512, -519912, -606312, -692712, -779112, -865512, -1512, 0]}, {"tz": "Asia/Dhaka", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -2524543700, -**********], "diffs": [172700, 86300, -100, -86500, -172900, -259300, -345700, -432100, -518500, -604900, -691300, -777700, -864100, -100, 400, 0]}, {"tz": "Asia/Dili", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175060, 88660, 2260, -84140, -170540, -256940, -343340, -429740, -516140, -602540, -688940, -775340, -861740, 2260, 0]}, {"tz": "Asia/Dubai", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [173928, 87528, 1128, -85272, -171672, -258072, -344472, -430872, -517272, -603672, -690072, -776472, -862872, 1128, 0]}, {"tz": "Asia/Dushanbe", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [174288, 87888, 1488, -84912, -171312, -257712, -344112, -430512, -516912, -603312, -689712, -776112, -862512, 1488, 0]}, {"tz": "Asia/Famagusta", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [171852, 85452, -948, -87348, -173748, -260148, -346548, -432948, -519348, -605748, -692148, -778548, -864948, -948, 0]}, {"tz": "Asia/Gaza", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [171728, 85328, -1072, -87472, -173872, -260272, -346672, -433072, -519472, -605872, -692272, -778672, -865072, -1072, 0]}, {"tz": "Asia/Harbin", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [172457, 86057, -343, -86743, -173143, -259543, -345943, -432343, -518743, -605143, -691543, -777943, -864343, -343, 0]}, {"tz": "Asia/Hebron", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [171577, 85177, -1223, -87623, -174023, -260423, -346823, -433223, -519623, -606023, -692423, -778823, -865223, -1223, 0]}, {"tz": "Asia/Ho_Chi_Minh", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [172410, 86010, -390, -86790, -173190, -259590, -345990, -432390, -518790, -605190, -691590, -777990, -864390, -390, 0]}, {"tz": "Asia/Hong_Kong", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [174198, 87798, 1398, -85002, -171402, -257802, -344202, -430602, -517002, -603402, -689802, -776202, -862602, 1398, 0]}, {"tz": "Asia/Hovd", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [176004, 89604, 3204, -83196, -169596, -255996, -342396, -428796, -515196, -601596, -687996, -774396, -860796, 3204, 0]}, {"tz": "Asia/Irkutsk", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [176575, 90175, 3775, -82625, -169025, -255425, -341825, -428225, -514625, -601025, -687425, -773825, -860225, 3775, 0]}, {"tz": "Asia/Istanbul", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -2840151536, -**********], "diffs": [176648, 90248, 3848, -82552, -168952, -255352, -341752, -428152, -514552, -600952, -687352, -773752, -860152, 3848, 3784, 0]}, {"tz": "Asia/Jakarta", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [172368, 85968, -432, -86832, -173232, -259632, -346032, -432432, -518832, -605232, -691632, -778032, -864432, -432, 0]}, {"tz": "Asia/Jayapura", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [171432, 85032, -1368, -87768, -174168, -260568, -346968, -433368, -519768, -606168, -692568, -778968, -865368, -1368, 0]}, {"tz": "Asia/Jerusalem", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2840148014, -**********], "diffs": [171546, 85146, -1254, -87654, -174054, -260454, -346854, -433254, -519654, -606054, -692454, -778854, -865254, -1254, -1240, 0]}, {"tz": "Asia/Kabul", "switches": [-62135785800, -59006464200, -55850704200, -52694944200, -46383424200, -43227664200, -40071904200, -33760384200, -30604624200, -27448864200, -21137344200, -17981584200, -14825824200, -12219309000, -2524540008, -**********], "diffs": [172392, 85992, -408, -86808, -173208, -259608, -346008, -432408, -518808, -605208, -691608, -778008, -864408, -408, 1800, 0]}, {"tz": "Asia/Kamchatka", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [177924, 91524, 5124, -81276, -167676, -254076, -340476, -426876, -513276, -599676, -686076, -772476, -858876, 5124, 0]}, {"tz": "Asia/Karachi", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [174708, 88308, 1908, -84492, -170892, -257292, -343692, -430092, -516492, -602892, -689292, -775692, -862092, 1908, 0]}, {"tz": "Asia/Kashgar", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -**********], "diffs": [173380, 86980, 580, -85820, -172220, -258620, -345020, -431420, -517820, -604220, -690620, -777020, -863420, 580, 0]}, {"tz": "Asia/Kathmandu", "switches": [-62135790300, -59006468700, -55850708700, -52694948700, -46383428700, -43227668700, -40071908700, -33760388700, -30604628700, -27448868700, -21137348700, -17981588700, -14825828700, -12219313500, -**********], "diffs": [173024, 86624, 224, -86176, -172576, -258976, -345376, -431776, -518176, -604576, -690976, -777376, -863776, 224, 0]}, {"tz": "Asia/Katmandu", "switches": [-62135790300, -59006468700, -55850708700, -52694948700, -46383428700, -43227668700, -40071908700, -33760388700, -30604628700, -27448868700, -21137348700, -17981588700, -14825828700, -12219313500, -**********], "diffs": [173024, 86624, 224, -86176, -172576, -258976, -345376, -431776, -518176, -604576, -690976, -777376, -863776, 224, 0]}, {"tz": "Asia/Khandyga", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [172667, 86267, -133, -86533, -172933, -259333, -345733, -432133, -518533, -604933, -691333, -777733, -864133, -133, 0]}, {"tz": "Asia/Kolkata", "switches": [-62135789400, -59006467800, -55850707800, -52694947800, -46383427800, -43227667800, -40071907800, -33760387800, -30604627800, -27448867800, -21137347800, -17981587800, -14825827800, -12219312600, -3645235808, -3155695330, -**********], "diffs": [171392, 84992, -1408, -87808, -174208, -260608, -347008, -433408, -519808, -606208, -692608, -779008, -865408, -1408, -1400, 530, 0]}, {"tz": "Asia/Krasnoyarsk", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [175714, 89314, 2914, -83486, -169886, -256286, -342686, -429086, -515486, -601886, -688286, -774686, -861086, 2914, 0]}, {"tz": "Asia/Kuala_Lumpur", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [176675, 90275, 3875, -82525, -168925, -255325, -341725, -428125, -514525, -600925, -687325, -773725, -860125, 3875, 0]}, {"tz": "Asia/Kuching", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [175120, 88720, 2320, -84080, -170480, -256880, -343280, -429680, -516080, -602480, -688880, -775280, -861680, 2320, 0]}, {"tz": "Asia/Kuwait", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [172388, 85988, -412, -86812, -173212, -259612, -346012, -432412, -518812, -605212, -691612, -778012, -864412, -412, 0]}, {"tz": "Asia/Macao", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [174350, 87950, 1550, -84850, -171250, -257650, -344050, -430450, -516850, -603250, -689650, -776050, -862450, 1550, 0]}, {"tz": "Asia/Macau", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [174350, 87950, 1550, -84850, -171250, -257650, -344050, -430450, -516850, -603250, -689650, -776050, -862450, 1550, 0]}, {"tz": "Asia/Magadan", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -**********], "diffs": [176208, 89808, 3408, -82992, -169392, -255792, -342192, -428592, -514992, -601392, -687792, -774192, -860592, 3408, 0]}, {"tz": "Asia/Makassar", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [172944, 86544, 144, -86256, -172656, -259056, -345456, -431856, -518256, -604656, -691056, -777456, -863856, 144, 0]}, {"tz": "Asia/Manila", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -3944620800, -2229321840], "diffs": [258960, 172560, 86160, -240, -86640, -173040, -259440, -345840, -432240, -518640, -605040, -691440, -777840, 86160, -240, 0]}, {"tz": "Asia/Muscat", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [173928, 87528, 1128, -85272, -171672, -258072, -344472, -430872, -517272, -603672, -690072, -776472, -862872, 1128, 0]}, {"tz": "Asia/Nicosia", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [171992, 85592, -808, -87208, -173608, -260008, -346408, -432808, -519208, -605608, -692008, -778408, -864808, -808, 0]}, {"tz": "Asia/Novokuznetsk", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [177072, 90672, 4272, -82128, -168528, -254928, -341328, -427728, -514128, -600528, -686928, -773328, -859728, 4272, 0]}, {"tz": "Asia/Novosibirsk", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [178100, 91700, 5300, -81100, -167500, -253900, -340300, -426700, -513100, -599500, -685900, -772300, -858700, 5300, 0]}, {"tz": "Asia/Omsk", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -**********], "diffs": [176790, 90390, 3990, -82410, -168810, -255210, -341610, -428010, -514410, -600810, -687210, -773610, -860010, 3990, 0]}, {"tz": "Asia/Oral", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [178476, 92076, 5676, -80724, -167124, -253524, -339924, -426324, -512724, -599124, -685524, -771924, -858324, 5676, 0]}, {"tz": "Asia/Phnom_Penh", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [173876, 87476, 1076, -85324, -171724, -258124, -344524, -430924, -517324, -603724, -690124, -776524, -862924, 1076, 0]}, {"tz": "Asia/Pontianak", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [171760, 85360, -1040, -87440, -173840, -260240, -346640, -433040, -519440, -605840, -692240, -778640, -865040, -1040, 0]}, {"tz": "Asia/Pyongyang", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175020, 88620, 2220, -84180, -170580, -256980, -343380, -429780, -516180, -602580, -688980, -775380, -861780, 2220, 0]}, {"tz": "Asia/Qatar", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [171232, 84832, -1568, -87968, -174368, -260768, -347168, -433568, -519968, -606368, -692768, -779168, -865568, -1568, 0]}, {"tz": "Asia/Qostanay", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -**********], "diffs": [179132, 92732, 6332, -80068, -166468, -252868, -339268, -425668, -512068, -598468, -684868, -771268, -857668, 6332, 0]}, {"tz": "Asia/Qyzylorda", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [175088, 88688, 2288, -84112, -170512, -256912, -343312, -429712, -516112, -602512, -688912, -775312, -861712, 2288, 0]}, {"tz": "Asia/Rangoon", "switches": [-62135793000, -59006471400, -55850711400, -52694951400, -46383431400, -43227671400, -40071911400, -33760391400, -30604631400, -27448871400, -21137351400, -17981591400, -14825831400, -12219316200, -**********], "diffs": [173113, 86713, 313, -86087, -172487, -258887, -345287, -431687, -518087, -604487, -690887, -777287, -863687, 313, 0]}, {"tz": "Asia/Riyadh", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [172388, 85988, -412, -86812, -173212, -259612, -346012, -432412, -518812, -605212, -691612, -778012, -864412, -412, 0]}, {"tz": "Asia/Saigon", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [172410, 86010, -390, -86790, -173190, -259590, -345990, -432390, -518790, -605190, -691590, -777990, -864390, -390, 0]}, {"tz": "Asia/Sakhalin", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -**********], "diffs": [178152, 91752, 5352, -81048, -167448, -253848, -340248, -426648, -513048, -599448, -685848, -772248, -858648, 5352, 0]}, {"tz": "Asia/Samarkand", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [174727, 88327, 1927, -84473, -170873, -257273, -343673, -430073, -516473, -602873, -689273, -775673, -862073, 1927, 0]}, {"tz": "Asia/Seoul", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174728, 88328, 1928, -84472, -170872, -257272, -343672, -430072, -516472, -602872, -689272, -775672, -862072, 1928, 0]}, {"tz": "Asia/Shanghai", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [172457, 86057, -343, -86743, -173143, -259543, -345943, -432343, -518743, -605143, -691543, -777943, -864343, -343, 0]}, {"tz": "Asia/Singapore", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [176675, 90275, 3875, -82525, -168925, -255325, -341725, -428125, -514525, -600925, -687325, -773725, -860125, 3875, 0]}, {"tz": "Asia/Srednekolymsk", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -**********], "diffs": [175508, 89108, 2708, -83692, -170092, -256492, -342892, -429292, -515692, -602092, -688492, -774892, -861292, 2708, 0]}, {"tz": "Asia/Taipei", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -2335248360], "diffs": [172440, 86040, -360, -86760, -173160, -259560, -345960, -432360, -518760, -605160, -691560, -777960, -864360, -360, 0]}, {"tz": "Asia/Tashkent", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [174169, 87769, 1369, -85031, -171431, -257831, -344231, -430631, -517031, -603431, -689831, -776231, -862631, 1369, 0]}, {"tz": "Asia/Tbilisi", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [176449, 90049, 3649, -82751, -169151, -255551, -341951, -428351, -514751, -601151, -687551, -773951, -860351, 3649, 0]}, {"tz": "Asia/Tehran", "switches": [-62135782200, -59006460600, -55850700600, -52694940600, -46383420600, -43227660600, -40071900600, -33760380600, -30604620600, -27448860600, -21137340600, -17981580600, -14825820600, -12219305400, -**********], "diffs": [173056, 86656, 256, -86144, -172544, -258944, -345344, -431744, -518144, -604544, -690944, -777344, -863744, 256, 0]}, {"tz": "Asia/Tel_Aviv", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2840148014, -**********], "diffs": [171546, 85146, -1254, -87654, -174054, -260454, -346854, -433254, -519654, -606054, -692454, -778854, -865254, -1254, -1240, 0]}, {"tz": "Asia/Thimbu", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -**********], "diffs": [172884, 86484, 84, -86316, -172716, -259116, -345516, -431916, -518316, -604716, -691116, -777516, -863916, 84, 0]}, {"tz": "Asia/Thimphu", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -**********], "diffs": [172884, 86484, 84, -86316, -172716, -259116, -345516, -431916, -518316, -604716, -691116, -777516, -863916, 84, 0]}, {"tz": "Asia/Tokyo", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2587712400], "diffs": [171661, 85261, -1139, -87539, -173939, -260339, -346739, -433139, -519539, -605939, -692339, -778739, -865139, -1139, 0]}, {"tz": "Asia/Tomsk", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [177609, 91209, 4809, -81591, -167991, -254391, -340791, -427191, -513591, -599991, -686391, -772791, -859191, 4809, 0]}, {"tz": "Asia/Ujung_Pandang", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [172944, 86544, 144, -86256, -172656, -259056, -345456, -431856, -518256, -604656, -691056, -777456, -863856, 144, 0]}, {"tz": "Asia/Ulaanbaatar", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [175948, 89548, 3148, -83252, -169652, -256052, -342452, -428852, -515252, -601652, -688052, -774452, -860852, 3148, 0]}, {"tz": "Asia/Ulan_Bator", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [175948, 89548, 3148, -83252, -169652, -256052, -342452, -428852, -515252, -601652, -688052, -774452, -860852, 3148, 0]}, {"tz": "Asia/Urumqi", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -**********], "diffs": [173380, 86980, 580, -85820, -172220, -258620, -345020, -431420, -517820, -604220, -690620, -777020, -863420, 580, 0]}, {"tz": "Asia/Ust-Nera", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174426, 88026, 1626, -84774, -171174, -257574, -343974, -430374, -516774, -603174, -689574, -775974, -862374, 1626, 0]}, {"tz": "Asia/Vientiane", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [173876, 87476, 1076, -85324, -171724, -258124, -344524, -430924, -517324, -603724, -690124, -776524, -862924, 1076, 0]}, {"tz": "Asia/Vladivostok", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [177149, 90749, 4349, -82051, -168451, -254851, -341251, -427651, -514051, -600451, -686851, -773251, -859651, 4349, 0]}, {"tz": "Asia/Yakutsk", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174062, 87662, 1262, -85138, -171538, -257938, -344338, -430738, -517138, -603538, -689938, -776338, -862738, 1262, 0]}, {"tz": "Asia/Yangon", "switches": [-62135793000, -59006471400, -55850711400, -52694951400, -46383431400, -43227671400, -40071911400, -33760391400, -30604631400, -27448871400, -21137351400, -17981591400, -14825831400, -12219316200, -**********], "diffs": [173113, 86713, 313, -86087, -172487, -258887, -345287, -431687, -518087, -604487, -690887, -777287, -863687, 313, 0]}, {"tz": "Asia/Yekaterinburg", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [176247, 89847, 3447, -82953, -169353, -255753, -342153, -428553, -514953, -601353, -687753, -774153, -860553, 3447, 0]}, {"tz": "Asia/Yerevan", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [176520, 90120, 3720, -82680, -169080, -255480, -341880, -428280, -514680, -601080, -687480, -773880, -860280, 3720, 0]}, {"tz": "Atlantic/Azores", "switches": [-62135766000, -59006444400, -55850684400, -52694924400, -46383404400, -43227644400, -40071884400, -33760364400, -30604604400, -27448844400, -21137324400, -17981564400, -14825804400, -12219289200, -2713907512, -**********], "diffs": [175360, 88960, 2560, -83840, -170240, -256640, -343040, -429440, -515840, -602240, -688640, -775040, -861440, 2560, 3272, 0]}, {"tz": "Atlantic/Bermuda", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [173958, 87558, 1158, -85242, -171642, -258042, -344442, -430842, -517242, -603642, -690042, -776442, -862842, 1158, 0]}, {"tz": "Atlantic/Canary", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [176496, 90096, 3696, -82704, -169104, -255504, -341904, -428304, -514704, -601104, -687504, -773904, -860304, 3696, 0]}, {"tz": "Atlantic/Cape_Verde", "switches": [-62135766000, -59006444400, -55850684400, -52694924400, -46383404400, -43227644400, -40071884400, -33760364400, -30604604400, -27448844400, -21137324400, -17981564400, -14825804400, -12219289200, -**********], "diffs": [174844, 88444, 2044, -84356, -170756, -257156, -343556, -429956, -516356, -602756, -689156, -775556, -861956, 2044, 0]}, {"tz": "Atlantic/Faeroe", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174424, 88024, 1624, -84776, -171176, -257576, -343976, -430376, -516776, -603176, -689576, -775976, -862376, 1624, 0]}, {"tz": "Atlantic/Faroe", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174424, 88024, 1624, -84776, -171176, -257576, -343976, -430376, -516776, -603176, -689576, -775976, -862376, 1624, 0]}, {"tz": "Atlantic/<PERSON>_<PERSON>en", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2422054408], "diffs": [173192, 86792, 392, -86008, -172408, -258808, -345208, -431608, -518008, -604408, -690808, -777208, -863608, 392, 0]}, {"tz": "Atlantic/Madeira", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [176856, 90456, 4056, -82344, -168744, -255144, -341544, -427944, -514344, -600744, -687144, -773544, -859944, 4056, 0]}, {"tz": "Atlantic/Reykjavik", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Atlantic/South_Georgia", "switches": [-62135762400, -59006440800, -55850680800, -52694920800, -46383400800, -43227640800, -40071880800, -33760360800, -30604600800, -27448840800, -21137320800, -17981560800, -14825800800, -12219285600, -2524512832], "diffs": [174368, 87968, 1568, -84832, -171232, -257632, -344032, -430432, -516832, -603232, -689632, -776032, -862432, 1568, 0]}, {"tz": "Atlantic/St_Helena", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Atlantic/Stanley", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175884, 89484, 3084, -83316, -169716, -256116, -342516, -428916, -515316, -601716, -688116, -774516, -860916, 3084, 0]}, {"tz": "Australia/ACT", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [172508, 86108, -292, -86692, -173092, -259492, -345892, -432292, -518692, -605092, -691492, -777892, -864292, -292, 0]}, {"tz": "Australia/Adelaide", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********, -**********], "diffs": [173740, 87340, 940, -85460, -171860, -258260, -344660, -431060, -517460, -603860, -690260, -776660, -863060, 940, 1800, 0]}, {"tz": "Australia/Brisbane", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [172072, 85672, -728, -87128, -173528, -259928, -346328, -432728, -519128, -605528, -691928, -778328, -864728, -728, 0]}, {"tz": "Australia/Broken_Hill", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********, -**********, -**********], "diffs": [173052, 86652, 252, -86148, -172548, -258948, -345348, -431748, -518148, -604548, -690948, -777348, -863748, 252, -1800, 1800, 0]}, {"tz": "Australia/Canberra", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [172508, 86108, -292, -86692, -173092, -259492, -345892, -432292, -518692, -605092, -691492, -777892, -864292, -292, 0]}, {"tz": "Australia/Currie", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173444, 87044, 644, -85756, -172156, -258556, -344956, -431356, -517756, -604156, -690556, -776956, -863356, 644, 0]}, {"tz": "Australia/Darwin", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********, -**********], "diffs": [175600, 89200, 2800, -83600, -170000, -256400, -342800, -429200, -515600, -602000, -688400, -774800, -861200, 2800, 1800, 0]}, {"tz": "Australia/Eucla", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173372, 86972, 572, -85828, -172228, -258628, -345028, -431428, -517828, -604228, -690628, -777028, -863428, 572, 0]}, {"tz": "Australia/Hobart", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173444, 87044, 644, -85756, -172156, -258556, -344956, -431356, -517756, -604156, -690556, -776956, -863356, 644, 0]}, {"tz": "Australia/LHI", "switches": [-62135807400, -59006485800, -55850725800, -52694965800, -46383445800, -43227685800, -40071925800, -33760405800, -30604645800, -27448885800, -21137365800, -17981605800, -14825845800, -12219330600, -**********, -**********], "diffs": [172420, 86020, -380, -86780, -173180, -259580, -345980, -432380, -518780, -605180, -691580, -777980, -864380, -380, 1800, 0]}, {"tz": "Australia/Lindeman", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173044, 86644, 244, -86156, -172556, -258956, -345356, -431756, -518156, -604556, -690956, -777356, -863756, 244, 0]}, {"tz": "Australia/Lord_Howe", "switches": [-62135807400, -59006485800, -55850725800, -52694965800, -46383445800, -43227685800, -40071925800, -33760405800, -30604645800, -27448885800, -21137365800, -17981605800, -14825845800, -12219330600, -**********, -**********], "diffs": [172420, 86020, -380, -86780, -173180, -259580, -345980, -432380, -518780, -605180, -691580, -777980, -864380, -380, 1800, 0]}, {"tz": "Australia/Melbourne", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174008, 87608, 1208, -85192, -171592, -257992, -344392, -430792, -517192, -603592, -689992, -776392, -862792, 1208, 0]}, {"tz": "Australia/NSW", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [172508, 86108, -292, -86692, -173092, -259492, -345892, -432292, -518692, -605092, -691492, -777892, -864292, -292, 0]}, {"tz": "Australia/North", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********, -**********], "diffs": [175600, 89200, 2800, -83600, -170000, -256400, -342800, -429200, -515600, -602000, -688400, -774800, -861200, 2800, 1800, 0]}, {"tz": "Australia/Perth", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [173796, 87396, 996, -85404, -171804, -258204, -344604, -431004, -517404, -603804, -690204, -776604, -863004, 996, 0]}, {"tz": "Australia/Queensland", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [172072, 85672, -728, -87128, -173528, -259928, -346328, -432728, -519128, -605528, -691928, -778328, -864728, -728, 0]}, {"tz": "Australia/South", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********, -**********], "diffs": [173740, 87340, 940, -85460, -171860, -258260, -344660, -431060, -517460, -603860, -690260, -776660, -863060, 940, 1800, 0]}, {"tz": "Australia/Sydney", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [172508, 86108, -292, -86692, -173092, -259492, -345892, -432292, -518692, -605092, -691492, -777892, -864292, -292, 0]}, {"tz": "Australia/Tasmania", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173444, 87044, 644, -85756, -172156, -258556, -344956, -431356, -517756, -604156, -690556, -776956, -863356, 644, 0]}, {"tz": "Australia/Victoria", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174008, 87608, 1208, -85192, -171592, -257992, -344392, -430792, -517192, -603592, -689992, -776392, -862792, 1208, 0]}, {"tz": "Australia/West", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [173796, 87396, 996, -85404, -171804, -258204, -344604, -431004, -517404, -603804, -690204, -776604, -863004, 996, 0]}, {"tz": "Australia/Yancowinna", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********, -**********, -**********], "diffs": [173052, 86652, 252, -86148, -172548, -258948, -345348, -431748, -518148, -604548, -690948, -777348, -863748, 252, -1800, 1800, 0]}, {"tz": "Brazil/Acre", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [171072, 84672, -1728, -88128, -174528, -260928, -347328, -433728, -520128, -606528, -692928, -779328, -865728, -1728, 0]}, {"tz": "Brazil/DeNoronha", "switches": [-62135762400, -59006440800, -55850680800, -52694920800, -46383400800, -43227640800, -40071880800, -33760360800, -30604600800, -27448840800, -21137320800, -17981560800, -14825800800, -12219285600, -**********], "diffs": [173380, 86980, 580, -85820, -172220, -258620, -345020, -431420, -517820, -604220, -690620, -777020, -863420, 580, 0]}, {"tz": "Brazil/East", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173188, 86788, 388, -86012, -172412, -258812, -345212, -431612, -518012, -604412, -690812, -777212, -863612, 388, 0]}, {"tz": "Brazil/West", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [172804, 86404, 4, -86396, -172796, -259196, -345596, -431996, -518396, -604796, -691196, -777596, -863996, 4, 0]}, {"tz": "CET", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "CST6CDT", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Canada/Atlantic", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [173664, 87264, 864, -85536, -171936, -258336, -344736, -431136, -517536, -603936, -690336, -776736, -863136, 864, 0]}, {"tz": "Canada/Central", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2602258284], "diffs": [174516, 88116, 1716, -84684, -171084, -257484, -343884, -430284, -516684, -603084, -689484, -775884, -862284, 1716, 0]}, {"tz": "Canada/Eastern", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2366736148], "diffs": [173852, 87452, 1052, -85348, -171748, -258148, -344548, -430948, -517348, -603748, -690148, -776548, -862948, 1052, 0]}, {"tz": "Canada/Mountain", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [174832, 88432, 2032, -84368, -170768, -257168, -343568, -429968, -516368, -602768, -689168, -775568, -861968, 2032, 0]}, {"tz": "Canada/Newfoundland", "switches": [-62135757000, -59006435400, -55850675400, -52694915400, -46383395400, -43227635400, -40071875400, -33760355400, -30604595400, -27448835400, -21137315400, -17981555400, -14825795400, -12219280200, -**********], "diffs": [172852, 86452, 52, -86348, -172748, -259148, -345548, -431948, -518348, -604748, -691148, -777548, -863948, 52, 0]}, {"tz": "Canada/Pacific", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000, -2713880852], "diffs": [173548, 87148, 748, -85652, -172052, -258452, -344852, -431252, -517652, -604052, -690452, -776852, -863252, 748, 0]}, {"tz": "Canada/Saskatchewan", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [176316, 89916, 3516, -82884, -169284, -255684, -342084, -428484, -514884, -601284, -687684, -774084, -860484, 3516, 0]}, {"tz": "Canada/Yukon", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [180012, 93612, 7212, -79188, -165588, -251988, -338388, -424788, -511188, -597588, -683988, -770388, -856788, 7212, 0]}, {"tz": "Chile/Continental", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400, -**********], "diffs": [175365, 88965, 2565, -83835, -170235, -256635, -343035, -429435, -515835, -602235, -688635, -775035, -861435, 2565, 0]}, {"tz": "Chile/EasterIsland", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [177448, 91048, 4648, -81752, -168152, -254552, -340952, -427352, -513752, -600152, -686552, -772952, -859352, 4648, 0]}, {"tz": "Cuba", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2524503608, -**********], "diffs": [174568, 88168, 1768, -84632, -171032, -257432, -343832, -430232, -516632, -603032, -689432, -775832, -862232, 1768, 1776, 0]}, {"tz": "EET", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "EST5EDT", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Egypt", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172491, 86091, -309, -86709, -173109, -259509, -345909, -432309, -518709, -605109, -691509, -777909, -864309, -309, 0]}, {"tz": "Eire", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174321, 87921, 1521, -84879, -171279, -257679, -344079, -430479, -516879, -603279, -689679, -776079, -862479, 1521, 0]}, {"tz": "Etc/GMT", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+0", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+1", "switches": [-62135766000, -59006444400, -55850684400, -52694924400, -46383404400, -43227644400, -40071884400, -33760364400, -30604604400, -27448844400, -21137324400, -17981564400, -14825804400, -12219289200], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+10", "switches": [-62135733600, -59006412000, -55850652000, -52694892000, -46383372000, -43227612000, -40071852000, -33760332000, -30604572000, -27448812000, -21137292000, -17981532000, -14825772000, -12219256800], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+11", "switches": [-62135730000, -59006408400, -55850648400, -52694888400, -46383368400, -43227608400, -40071848400, -33760328400, -30604568400, -27448808400, -21137288400, -17981528400, -14825768400, -12219253200], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+12", "switches": [-62135726400, -59006404800, -55850644800, -52694884800, -46383364800, -43227604800, -40071844800, -33760324800, -30604564800, -27448804800, -21137284800, -17981524800, -14825764800, -12219249600], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+2", "switches": [-62135762400, -59006440800, -55850680800, -52694920800, -46383400800, -43227640800, -40071880800, -33760360800, -30604600800, -27448840800, -21137320800, -17981560800, -14825800800, -12219285600], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+3", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+4", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+5", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+6", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+7", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+8", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT+9", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-0", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-1", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-10", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-11", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-12", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-13", "switches": [-62135816400, -59006494800, -55850734800, -52694974800, -46383454800, -43227694800, -40071934800, -33760414800, -30604654800, -27448894800, -21137374800, -17981614800, -14825854800, -12219339600], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-14", "switches": [-62135820000, -59006498400, -55850738400, -52694978400, -46383458400, -43227698400, -40071938400, -33760418400, -30604658400, -27448898400, -21137378400, -17981618400, -14825858400, -12219343200], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-2", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-3", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-4", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-5", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-6", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-7", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-8", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT-9", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/GMT0", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/Greenwich", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/UCT", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/UTC", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/Universal", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Etc/Zulu", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Europe/Amsterdam", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2450998800, -**********], "diffs": [175350, 88950, 2550, -83850, -170250, -256650, -343050, -429450, -515850, -602250, -688650, -775050, -861450, 2550, 3600, 0]}, {"tz": "Europe/Andorra", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [176036, 89636, 3236, -83164, -169564, -255964, -342364, -428764, -515164, -601564, -687964, -774364, -860764, 3236, 0]}, {"tz": "Europe/Astrakhan", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [175668, 89268, 2868, -83532, -169932, -256332, -342732, -429132, -515532, -601932, -688332, -774732, -861132, 2868, 0]}, {"tz": "Europe/Athens", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [174308, 87908, 1508, -84892, -171292, -257692, -344092, -430492, -516892, -603292, -689692, -776092, -862492, 1508, 0]}, {"tz": "Europe/Belfast", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3852662325], "diffs": [172875, 86475, 75, -86325, -172725, -259125, -345525, -431925, -518325, -604725, -691125, -777525, -863925, 75, 0]}, {"tz": "Europe/Belgrade", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2713915320], "diffs": [171480, 85080, -1320, -87720, -174120, -260520, -346920, -433320, -519720, -606120, -692520, -778920, -865320, -1320, 0]}, {"tz": "Europe/Berlin", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2422054408], "diffs": [173192, 86792, 392, -86008, -172408, -258808, -345208, -431608, -518008, -604408, -690808, -777208, -863608, 392, 0]}, {"tz": "Europe/Bratislava", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2469401864], "diffs": [172936, 86536, 136, -86264, -172664, -259064, -345464, -431864, -518264, -604664, -691064, -777464, -863864, 136, 0]}, {"tz": "Europe/Brussels", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2450998800, -**********], "diffs": [175350, 88950, 2550, -83850, -170250, -256650, -343050, -429450, -515850, -602250, -688650, -775050, -861450, 2550, 3600, 0]}, {"tz": "Europe/Bucharest", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [173736, 87336, 936, -85464, -171864, -258264, -344664, -431064, -517464, -603864, -690264, -776664, -863064, 936, 0]}, {"tz": "Europe/Budapest", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2498260580], "diffs": [171820, 85420, -980, -87380, -173780, -260180, -346580, -432980, -519380, -605780, -692180, -778580, -864980, -980, 0]}, {"tz": "Europe/Busingen", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3675200662, -2385246586], "diffs": [174352, 87952, 1552, -84848, -171248, -257648, -344048, -430448, -516848, -603248, -689648, -776048, -862448, 1552, 1814, 0]}, {"tz": "Europe/Chisinau", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2840148020, -**********], "diffs": [173080, 86680, 280, -86120, -172520, -258920, -345320, -431720, -518120, -604520, -690920, -777320, -863720, 280, 300, 0]}, {"tz": "Europe/Copenhagen", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2422054408], "diffs": [173192, 86792, 392, -86008, -172408, -258808, -345208, -431608, -518008, -604408, -690808, -777208, -863608, 392, 0]}, {"tz": "Europe/Dublin", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174321, 87921, 1521, -84879, -171279, -257679, -344079, -430479, -516879, -603279, -689679, -776079, -862479, 1521, 0]}, {"tz": "Europe/Gibraltar", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2821653516, -**********], "diffs": [177684, 91284, 4884, -81516, -167916, -254316, -340716, -427116, -513516, -599916, -686316, -772716, -859116, 4884, 3600, 0]}, {"tz": "Europe/Guernsey", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3852662325], "diffs": [172875, 86475, 75, -86325, -172725, -259125, -345525, -431925, -518325, -604725, -691125, -777525, -863925, 75, 0]}, {"tz": "Europe/Helsinki", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [174011, 87611, 1211, -85189, -171589, -257989, -344389, -430789, -517189, -603589, -689989, -776389, -862789, 1211, 0]}, {"tz": "Europe/Isle_of_Man", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3852662325], "diffs": [172875, 86475, 75, -86325, -172725, -259125, -345525, -431925, -518325, -604725, -691125, -777525, -863925, 75, 0]}, {"tz": "Europe/Istanbul", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -2840151536, -**********], "diffs": [176648, 90248, 3848, -82552, -168952, -255352, -341752, -428152, -514552, -600952, -687352, -773752, -860152, 3848, 3784, 0]}, {"tz": "Europe/Jersey", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3852662325], "diffs": [172875, 86475, 75, -86325, -172725, -259125, -345525, -431925, -518325, -604725, -691125, -777525, -863925, 75, 0]}, {"tz": "Europe/Kaliningrad", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2422059720, -**********], "diffs": [175080, 88680, 2280, -84120, -170520, -256920, -343320, -429720, -516120, -602520, -688920, -775320, -861720, 2280, 3600, 0]}, {"tz": "Europe/Kiev", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172676, 86276, -124, -86524, -172924, -259324, -345724, -432124, -518524, -604924, -691324, -777724, -864124, -124, 0]}, {"tz": "Europe/Kirov", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [171672, 85272, -1128, -87528, -173928, -260328, -346728, -433128, -519528, -605928, -692328, -778728, -865128, -1128, 0]}, {"tz": "Europe/Kyiv", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [172676, 86276, -124, -86524, -172924, -259324, -345724, -432124, -518524, -604924, -691324, -777724, -864124, -124, 0]}, {"tz": "Europe/Lisbon", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175005, 88605, 2205, -84195, -170595, -256995, -343395, -429795, -516195, -602595, -688995, -775395, -861795, 2205, 0]}, {"tz": "Europe/Ljubljana", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2713915320], "diffs": [171480, 85080, -1320, -87720, -174120, -260520, -346920, -433320, -519720, -606120, -692520, -778920, -865320, -1320, 0]}, {"tz": "Europe/London", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3852662325], "diffs": [172875, 86475, 75, -86325, -172725, -259125, -345525, -431925, -518325, -604725, -691125, -777525, -863925, 75, 0]}, {"tz": "Europe/Luxembourg", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2450998800, -**********], "diffs": [175350, 88950, 2550, -83850, -170250, -256650, -343050, -429450, -515850, -602250, -688650, -775050, -861450, 2550, 3600, 0]}, {"tz": "Europe/Madrid", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [177284, 90884, 4484, -81916, -168316, -254716, -341116, -427516, -513916, -600316, -686716, -773116, -859516, 4484, 0]}, {"tz": "Europe/Malta", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2403478684], "diffs": [172916, 86516, 116, -86284, -172684, -259084, -345484, -431884, -518284, -604684, -691084, -777484, -863884, 116, 0]}, {"tz": "Europe/Mariehamn", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [174011, 87611, 1211, -85189, -171589, -257989, -344389, -430789, -517189, -603589, -689989, -776389, -862789, 1211, 0]}, {"tz": "Europe/Minsk", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -2840151616, -**********], "diffs": [176984, 90584, 4184, -82216, -168616, -255016, -341416, -427816, -514216, -600616, -687016, -773416, -859816, 4184, 4200, 0]}, {"tz": "Europe/Monaco", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175839, 89439, 3039, -83361, -169761, -256161, -342561, -428961, -515361, -601761, -688161, -774561, -860961, 3039, 0]}, {"tz": "Europe/Moscow", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174583, 88183, 1783, -84617, -171017, -257417, -343817, -430217, -516617, -603017, -689417, -775817, -862217, 1783, 0]}, {"tz": "Europe/Nicosia", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [171992, 85592, -808, -87208, -173608, -260008, -346408, -432808, -519208, -605608, -692008, -778408, -864808, -808, 0]}, {"tz": "Europe/Oslo", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2422054408], "diffs": [173192, 86792, 392, -86008, -172408, -258808, -345208, -431608, -518008, -604408, -690808, -777208, -863608, 392, 0]}, {"tz": "Europe/Paris", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175839, 89439, 3039, -83361, -169761, -256161, -342561, -428961, -515361, -601761, -688161, -774561, -860961, 3039, 0]}, {"tz": "Europe/Podgorica", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2713915320], "diffs": [171480, 85080, -1320, -87720, -174120, -260520, -346920, -433320, -519720, -606120, -692520, -778920, -865320, -1320, 0]}, {"tz": "Europe/Prague", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2469401864], "diffs": [172936, 86536, 136, -86264, -172664, -259064, -345464, -431864, -518264, -604664, -691064, -777464, -863864, 136, 0]}, {"tz": "Europe/Riga", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [174206, 87806, 1406, -84994, -171394, -257794, -344194, -430594, -516994, -603394, -689794, -776194, -862594, 1406, 0]}, {"tz": "Europe/Rome", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2403565200], "diffs": [173404, 87004, 604, -85796, -172196, -258596, -344996, -431396, -517796, -604196, -690596, -776996, -863396, 604, 0]}, {"tz": "Europe/Samara", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [175180, 88780, 2380, -84020, -170420, -256820, -343220, -429620, -516020, -602420, -688820, -775220, -861620, 2380, 0]}, {"tz": "Europe/San_Marino", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2403565200], "diffs": [173404, 87004, 604, -85796, -172196, -258596, -344996, -431396, -517796, -604196, -690596, -776996, -863396, 604, 0]}, {"tz": "Europe/Sarajevo", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2713915320], "diffs": [171480, 85080, -1320, -87720, -174120, -260520, -346920, -433320, -519720, -606120, -692520, -778920, -865320, -1320, 0]}, {"tz": "Europe/Saratov", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [176142, 89742, 3342, -83058, -169458, -255858, -342258, -428658, -515058, -601458, -687858, -774258, -860658, 3342, 0]}, {"tz": "Europe/Simferopol", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -2840151624, -**********], "diffs": [175416, 89016, 2616, -83784, -170184, -256584, -342984, -429384, -515784, -602184, -688584, -774984, -861384, 2616, 2640, 0]}, {"tz": "Europe/Skopje", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2713915320], "diffs": [171480, 85080, -1320, -87720, -174120, -260520, -346920, -433320, -519720, -606120, -692520, -778920, -865320, -1320, 0]}, {"tz": "Europe/Sofia", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2840146580, -2369527016], "diffs": [174404, 88004, 1604, -84796, -171196, -257596, -343996, -430396, -516796, -603196, -689596, -775996, -862396, 1604, 184, 0]}, {"tz": "Europe/Stockholm", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2422054408], "diffs": [173192, 86792, 392, -86008, -172408, -258808, -345208, -431608, -518008, -604408, -690808, -777208, -863608, 392, 0]}, {"tz": "Europe/Tallinn", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [174060, 87660, 1260, -85140, -171540, -257940, -344340, -430740, -517140, -603540, -689940, -776340, -862740, 1260, 0]}, {"tz": "Europe/Tirane", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [171640, 85240, -1160, -87560, -173960, -260360, -346760, -433160, -519560, -605960, -692360, -778760, -865160, -1160, 0]}, {"tz": "Europe/Tiraspol", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2840148020, -**********], "diffs": [173080, 86680, 280, -86120, -172520, -258920, -345320, -431720, -518120, -604520, -690920, -777320, -863720, 280, 300, 0]}, {"tz": "Europe/Ulyanovsk", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [175584, 89184, 2784, -83616, -170016, -256416, -342816, -429216, -515616, -602016, -688416, -774816, -861216, 2784, 0]}, {"tz": "Europe/Uzhgorod", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2500943352, -**********], "diffs": [174648, 88248, 1848, -84552, -170952, -257352, -343752, -430152, -516552, -602952, -689352, -775752, -862152, 1848, 3600, 0]}, {"tz": "Europe/Vaduz", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3675200662, -2385246586], "diffs": [174352, 87952, 1552, -84848, -171248, -257648, -344048, -430448, -516848, -603248, -689648, -776048, -862448, 1552, 1814, 0]}, {"tz": "Europe/Vatican", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2403565200], "diffs": [173404, 87004, 604, -85796, -172196, -258596, -344996, -431396, -517796, -604196, -690596, -776996, -863396, 604, 0]}, {"tz": "Europe/Vienna", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2422055121], "diffs": [172479, 86079, -321, -86721, -173121, -259521, -345921, -432321, -518721, -605121, -691521, -777921, -864321, -321, 0]}, {"tz": "Europe/Vilnius", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2840149036, -**********], "diffs": [173924, 87524, 1124, -85276, -171676, -258076, -344476, -430876, -517276, -603676, -690076, -776476, -862876, 1124, 2160, 0]}, {"tz": "Europe/Volgograd", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [172940, 86540, 140, -86260, -172660, -259060, -345460, -431860, -518260, -604660, -691060, -777460, -863860, 140, 0]}, {"tz": "Europe/Warsaw", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [171360, 84960, -1440, -87840, -174240, -260640, -347040, -433440, -519840, -606240, -692640, -779040, -865440, -1440, 0]}, {"tz": "Europe/Zagreb", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2713915320], "diffs": [171480, 85080, -1320, -87720, -174120, -260520, -346920, -433320, -519720, -606120, -692520, -778920, -865320, -1320, 0]}, {"tz": "Europe/Zaporozhye", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2840148040, -**********], "diffs": [171560, 85160, -1240, -87640, -174040, -260440, -346840, -433240, -519640, -606040, -692440, -778840, -865240, -1240, -1200, 0]}, {"tz": "Europe/Zurich", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3675200662, -2385246586], "diffs": [174352, 87952, 1552, -84848, -171248, -257648, -344048, -430448, -516848, -603248, -689648, -776048, -862448, 1552, 1814, 0]}, {"tz": "GB", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3852662325], "diffs": [172875, 86475, 75, -86325, -172725, -259125, -345525, -431925, -518325, -604725, -691125, -777525, -863925, 75, 0]}, {"tz": "GB-Eire", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3852662325], "diffs": [172875, 86475, 75, -86325, -172725, -259125, -345525, -431925, -518325, -604725, -691125, -777525, -863925, 75, 0]}, {"tz": "GMT", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "GMT0", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Greenwich", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Hongkong", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [174198, 87798, 1398, -85002, -171402, -257802, -344202, -430602, -517002, -603402, -689802, -776202, -862602, 1398, 0]}, {"tz": "Iceland", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [173768, 87368, 968, -85432, -171832, -258232, -344632, -431032, -517432, -603832, -690232, -776632, -863032, 968, 0]}, {"tz": "Indian/Antananarivo", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Indian/Chagos", "switches": [-62135791200, -59006469600, -55850709600, -52694949600, -46383429600, -43227669600, -40071909600, -33760389600, -30604629600, -27448869600, -21137349600, -17981589600, -14825829600, -12219314400, -**********], "diffs": [177020, 90620, 4220, -82180, -168580, -254980, -341380, -427780, -514180, -600580, -686980, -773380, -859780, 4220, 0]}, {"tz": "Indian/Christmas", "switches": [-62135794800, -59006473200, -55850713200, -52694953200, -46383433200, -43227673200, -40071913200, -33760393200, -30604633200, -27448873200, -21137353200, -17981593200, -14825833200, -12219318000, -**********], "diffs": [173876, 87476, 1076, -85324, -171724, -258124, -344524, -430924, -517324, -603724, -690124, -776524, -862924, 1076, 0]}, {"tz": "Indian/Cocos", "switches": [-62135793000, -59006471400, -55850711400, -52694951400, -46383431400, -43227671400, -40071911400, -33760391400, -30604631400, -27448871400, -21137351400, -17981591400, -14825831400, -12219316200, -**********], "diffs": [173113, 86713, 313, -86087, -172487, -258887, -345287, -431687, -518087, -604487, -690887, -777287, -863687, 313, 0]}, {"tz": "Indian/Comoro", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Indian/Kerguelen", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [173160, 86760, 360, -86040, -172440, -258840, -345240, -431640, -518040, -604440, -690840, -777240, -863640, 360, 0]}, {"tz": "Indian/Mahe", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [173928, 87528, 1128, -85272, -171672, -258072, -344472, -430872, -517272, -603672, -690072, -776472, -862872, 1128, 0]}, {"tz": "Indian/Maldives", "switches": [-62135787600, -59006466000, -55850706000, -52694946000, -46383426000, -43227666000, -40071906000, -33760386000, -30604626000, -27448866000, -21137346000, -17981586000, -14825826000, -12219310800, -**********], "diffs": [173160, 86760, 360, -86040, -172440, -258840, -345240, -431640, -518040, -604440, -690840, -777240, -863640, 360, 0]}, {"tz": "Indian/Mauritius", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [173400, 87000, 600, -85800, -172200, -258600, -345000, -431400, -517800, -604200, -690600, -777000, -863400, 600, 0]}, {"tz": "Indian/Mayotte", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174764, 88364, 1964, -84436, -170836, -257236, -343636, -430036, -516436, -602836, -689236, -775636, -862036, 1964, 0]}, {"tz": "Indian/Reunion", "switches": [-62135784000, -59006462400, -55850702400, -52694942400, -46383422400, -43227662400, -40071902400, -33760382400, -30604622400, -27448862400, -21137342400, -17981582400, -14825822400, -12219307200, -**********], "diffs": [173928, 87528, 1128, -85272, -171672, -258072, -344472, -430872, -517272, -603672, -690072, -776472, -862872, 1128, 0]}, {"tz": "Iran", "switches": [-62135782200, -59006460600, -55850700600, -52694940600, -46383420600, -43227660600, -40071900600, -33760380600, -30604620600, -27448860600, -21137340600, -17981580600, -14825820600, -12219305400, -**********], "diffs": [173056, 86656, 256, -86144, -172544, -258944, -345344, -431744, -518144, -604544, -690944, -777344, -863744, 256, 0]}, {"tz": "Israel", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -2840148014, -**********], "diffs": [171546, 85146, -1254, -87654, -174054, -260454, -346854, -433254, -519654, -606054, -692454, -778854, -865254, -1254, -1240, 0]}, {"tz": "Jamaica", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [173230, 86830, 430, -85970, -172370, -258770, -345170, -431570, -517970, -604370, -690770, -777170, -863570, 430, 0]}, {"tz": "Japan", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2587712400], "diffs": [171661, 85261, -1139, -87539, -173939, -260339, -346739, -433139, -519539, -605939, -692339, -778739, -865139, -1139, 0]}, {"tz": "<PERSON><PERSON><PERSON><PERSON>", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [175840, 89440, 3040, -83360, -169760, -256160, -342560, -428960, -515360, -601760, -688160, -774560, -860960, 3040, 0]}, {"tz": "Libya", "switches": [-62135776800, -59006455200, -55850695200, -52694935200, -46383415200, -43227655200, -40071895200, -33760375200, -30604615200, -27448855200, -21137335200, -17981575200, -14825815200, -12219300000, -**********], "diffs": [176836, 90436, 4036, -82364, -168764, -255164, -341564, -427964, -514364, -600764, -687164, -773564, -859964, 4036, 0]}, {"tz": "MET", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "MST7MDT", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Mexico/BajaNorte", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000, -**********], "diffs": [172084, 85684, -716, -87116, -173516, -259916, -346316, -432716, -519116, -605516, -691916, -778316, -864716, -716, 0]}, {"tz": "Mexico/BajaSur", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -**********], "diffs": [173140, 86740, 340, -86060, -172460, -258860, -345260, -431660, -518060, -604460, -690860, -777260, -863660, 340, 0]}, {"tz": "Mexico/General", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [174996, 88596, 2196, -84204, -170604, -257004, -343404, -429804, -516204, -602604, -689004, -775404, -861804, 2196, 0]}, {"tz": "NZ", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -3192437344, -**********], "diffs": [174056, 87656, 1256, -85144, -171544, -257944, -344344, -430744, -517144, -603544, -689944, -776344, -862744, 1256, 1800, 0]}, {"tz": "NZ-CHAT", "switches": [-62135815500, -59006493900, -55850733900, -52694973900, -46383453900, -43227693900, -40071933900, -33760413900, -30604653900, -27448893900, -21137373900, -17981613900, -14825853900, -12219338700, -3192439428, -**********], "diffs": [174672, 88272, 1872, -84528, -170928, -257328, -343728, -430128, -516528, -602928, -689328, -775728, -862128, 1872, 1800, 0]}, {"tz": "Navajo", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -2717643600], "diffs": [172796, 86396, -4, -86404, -172804, -259204, -345604, -432004, -518404, -604804, -691204, -777604, -864004, -4, 0]}, {"tz": "PRC", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [172457, 86057, -343, -86743, -173143, -259543, -345943, -432343, -518743, -605143, -691543, -777943, -864343, -343, 0]}, {"tz": "PST8PDT", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Pacific/Apia", "switches": [-62135816400, -59006494800, -55850734800, -52694974800, -46383454800, -43227694800, -40071934800, -33760414800, -30604654800, -27448894800, -21137374800, -17981614800, -14825854800, -12219339600, -2445512400, -**********], "diffs": [174416, 88016, 1616, -84784, -171184, -257584, -343984, -430384, -516784, -603184, -689584, -775984, -862384, 1616, 88016, 0]}, {"tz": "Pacific/Auckland", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -3192437344, -**********], "diffs": [174056, 87656, 1256, -85144, -171544, -257944, -344344, -430744, -517144, -603544, -689944, -776344, -862744, 1256, 1800, 0]}, {"tz": "Pacific/Bougainville", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -2840182424, -2366794112, -**********], "diffs": [175064, 88664, 2264, -84136, -170536, -256936, -343336, -429736, -516136, -602536, -688936, -775336, -861736, 2264, 4288, 3600, 0]}, {"tz": "Pacific/Chatham", "switches": [-62135815500, -59006493900, -55850733900, -52694973900, -46383453900, -43227693900, -40071933900, -33760413900, -30604653900, -27448893900, -21137373900, -17981613900, -14825853900, -12219338700, -3192439428, -**********], "diffs": [174672, 88272, 1872, -84528, -170928, -257328, -343728, -430128, -516528, -602928, -689328, -775728, -862128, 1872, 1800, 0]}, {"tz": "Pacific/Chuuk", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2840176808, -2366790512], "diffs": [173480, 87080, 680, -85720, -172120, -258520, -344920, -431320, -517720, -604120, -690520, -776920, -863320, 680, 688, 0]}, {"tz": "Pacific/Easter", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [177448, 91048, 4648, -81752, -168152, -254552, -340952, -427352, -513752, -600152, -686552, -772952, -859352, 4648, 0]}, {"tz": "Pacific/Efate", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -**********], "diffs": [172004, 85604, -796, -87196, -173596, -259996, -346396, -432796, -519196, -605596, -691996, -778396, -864796, -796, 0]}, {"tz": "Pacific/Enderbury", "switches": [-62135816400, -59006494800, -55850734800, -52694974800, -46383454800, -43227694800, -40071934800, -33760414800, -30604654800, -27448894800, -21137374800, -17981614800, -14825854800, -12219339600, -**********], "diffs": [219600, 133200, 46800, -39600, -126000, -212400, -298800, -385200, -471600, -558000, -644400, -730800, -817200, 46800, 0]}, {"tz": "Pacific/Fakaofo", "switches": [-62135816400, -59006494800, -55850734800, -52694974800, -46383454800, -43227694800, -40071934800, -33760414800, -30604654800, -27448894800, -21137374800, -17981614800, -14825854800, -12219339600, -**********], "diffs": [260696, 174296, 87896, 1496, -84904, -171304, -257704, -344104, -430504, -516904, -603304, -689704, -776104, 87896, 0]}, {"tz": "Pacific/Fiji", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [173056, 86656, 256, -86144, -172544, -258944, -345344, -431744, -518144, -604544, -690944, -777344, -863744, 256, 0]}, {"tz": "Pacific/Funafuti", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [174476, 88076, 1676, -84724, -171124, -257524, -343924, -430324, -516724, -603124, -689524, -775924, -862324, 1676, 0]}, {"tz": "Pacific/Galapagos", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -**********], "diffs": [172704, 86304, -96, -86496, -172896, -259296, -345696, -432096, -518496, -604896, -691296, -777696, -864096, -96, 0]}, {"tz": "Pacific/Gambier", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400, -**********], "diffs": [172788, 86388, -12, -86412, -172812, -259212, -345612, -432012, -518412, -604812, -691212, -777612, -864012, -12, 0]}, {"tz": "Pacific/Guadalcanal", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -**********], "diffs": [174012, 87612, 1212, -85188, -171588, -257988, -344388, -430788, -517188, -603588, -689988, -776388, -862788, 1212, 0]}, {"tz": "Pacific/Guam", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3944628000, -**********], "diffs": [260460, 174060, 87660, 1260, -85140, -171540, -257940, -344340, -430740, -517140, -603540, -689940, -776340, 87660, 1260, 0]}, {"tz": "Pacific/Honolulu", "switches": [-62135733600, -59006412000, -55850652000, -52694892000, -46383372000, -43227612000, -40071852000, -33760332000, -30604572000, -27448812000, -21137292000, -17981532000, -14825772000, -12219256800, -2334103114, -**********], "diffs": [174686, 88286, 1886, -84514, -170914, -257314, -343714, -430114, -516514, -602914, -689314, -775714, -862114, 1886, 1800, 0]}, {"tz": "Pacific/Johnston", "switches": [-62135733600, -59006412000, -55850652000, -52694892000, -46383372000, -43227612000, -40071852000, -33760332000, -30604572000, -27448812000, -21137292000, -17981532000, -14825772000, -12219256800, -2334103114, -**********], "diffs": [174686, 88286, 1886, -84514, -170914, -257314, -343714, -430114, -516514, -602914, -689314, -775714, -862114, 1886, 1800, 0]}, {"tz": "Pacific/Kanton", "switches": [-62135816400, -59006494800, -55850734800, -52694974800, -46383454800, -43227694800, -40071934800, -33760414800, -30604654800, -27448894800, -21137374800, -17981614800, -14825854800, -12219339600, -**********], "diffs": [219600, 133200, 46800, -39600, -126000, -212400, -298800, -385200, -471600, -558000, -644400, -730800, -817200, 46800, 0]}, {"tz": "Pacific/Kiritimati", "switches": [-62135820000, -59006498400, -55850738400, -52694978400, -46383458400, -43227698400, -40071938400, -33760418400, -30604658400, -27448898400, -21137378400, -17981618400, -14825858400, -12219343200, -**********], "diffs": [260960, 174560, 88160, 1760, -84640, -171040, -257440, -343840, -430240, -516640, -603040, -689440, -775840, 88160, 0]}, {"tz": "Pacific/Kosrae", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -3944631600, -**********], "diffs": [259684, 173284, 86884, 484, -85916, -172316, -258716, -345116, -431516, -517916, -604316, -690716, -777116, 86884, 484, 0]}, {"tz": "Pacific/Kwajalein", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [175840, 89440, 3040, -83360, -169760, -256160, -342560, -428960, -515360, -601760, -688160, -774560, -860960, 3040, 0]}, {"tz": "Pacific/Majuro", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [174476, 88076, 1676, -84724, -171124, -257524, -343924, -430324, -516724, -603124, -689524, -775924, -862324, 1676, 0]}, {"tz": "Pacific/Marquesas", "switches": [-62135735400, -59006413800, -55850653800, -52694893800, -46383373800, -43227613800, -40071853800, -33760333800, -30604573800, -27448813800, -21137293800, -17981533800, -14825773800, -12219258600, -**********], "diffs": [172080, 85680, -720, -87120, -173520, -259920, -346320, -432720, -519120, -605520, -691920, -778320, -864720, -720, 0]}, {"tz": "Pacific/Midway", "switches": [-62135730000, -59006408400, -55850648400, -52694888400, -46383368400, -43227608400, -40071848400, -33760328400, -30604568400, -27448808400, -21137288400, -17981528400, -14825768400, -12219253200, -2445426000, -**********], "diffs": [87768, 1368, -85032, -171432, -257832, -344232, -430632, -517032, -603432, -689832, -776232, -862632, -949032, -85032, 1368, 0]}, {"tz": "Pacific/Nauru", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [175940, 89540, 3140, -83260, -169660, -256060, -342460, -428860, -515260, -601660, -688060, -774460, -860860, 3140, 0]}, {"tz": "Pacific/Niue", "switches": [-62135730000, -59006408400, -55850648400, -52694888400, -46383368400, -43227608400, -40071848400, -33760328400, -30604568400, -27448808400, -21137288400, -17981528400, -14825768400, -12219253200, -**********], "diffs": [173980, 87580, 1180, -85220, -171620, -258020, -344420, -430820, -517220, -603620, -690020, -776420, -862820, 1180, 0]}, {"tz": "Pacific/Norfolk", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -**********], "diffs": [172088, 85688, -712, -87112, -173512, -259912, -346312, -432712, -519112, -605512, -691912, -778312, -864712, -712, 0]}, {"tz": "Pacific/Noumea", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -**********], "diffs": [172452, 86052, -348, -86748, -173148, -259548, -345948, -432348, -518748, -605148, -691548, -777948, -864348, -348, 0]}, {"tz": "Pacific/Pago_Pago", "switches": [-62135730000, -59006408400, -55850648400, -52694888400, -46383368400, -43227608400, -40071848400, -33760328400, -30604568400, -27448808400, -21137288400, -17981528400, -14825768400, -12219253200, -2445426000, -**********], "diffs": [87768, 1368, -85032, -171432, -257832, -344232, -430632, -517032, -603432, -689832, -776232, -862632, -949032, -85032, 1368, 0]}, {"tz": "Pacific/Palau", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3944624400, -**********], "diffs": [259324, 172924, 86524, 124, -86276, -172676, -259076, -345476, -431876, -518276, -604676, -691076, -777476, 86524, 124, 0]}, {"tz": "Pacific/Pitcairn", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000, -**********], "diffs": [175220, 88820, 2420, -83980, -170380, -256780, -343180, -429580, -515980, -602380, -688780, -775180, -861580, 2420, 0]}, {"tz": "Pacific/Pohnpei", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -**********], "diffs": [174012, 87612, 1212, -85188, -171588, -257988, -344388, -430788, -517188, -603588, -689988, -776388, -862788, 1212, 0]}, {"tz": "Pacific/Ponape", "switches": [-62135809200, -59006487600, -55850727600, -52694967600, -46383447600, -43227687600, -40071927600, -33760407600, -30604647600, -27448887600, -21137367600, -17981607600, -14825847600, -12219332400, -**********], "diffs": [174012, 87612, 1212, -85188, -171588, -257988, -344388, -430788, -517188, -603588, -689988, -776388, -862788, 1212, 0]}, {"tz": "Pacific/Port_Moresby", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2840176808, -2366790512], "diffs": [173480, 87080, 680, -85720, -172120, -258520, -344920, -431320, -517720, -604120, -690520, -776920, -863320, 680, 688, 0]}, {"tz": "Pacific/Rarotonga", "switches": [-62135733600, -59006412000, -55850652000, -52694892000, -46383372000, -43227612000, -40071852000, -33760332000, -30604572000, -27448812000, -21137292000, -17981532000, -14825772000, -12219256800, -2209557600, -**********], "diffs": [88744, 2344, -84056, -170456, -256856, -343256, -429656, -516056, -602456, -688856, -775256, -861656, -948056, -84056, 2344, 0]}, {"tz": "Pacific/Saipan", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -3944628000, -**********], "diffs": [260460, 174060, 87660, 1260, -85140, -171540, -257940, -344340, -430740, -517140, -603540, -689940, -776340, 87660, 1260, 0]}, {"tz": "Pacific/Samoa", "switches": [-62135730000, -59006408400, -55850648400, -52694888400, -46383368400, -43227608400, -40071848400, -33760328400, -30604568400, -27448808400, -21137288400, -17981528400, -14825768400, -12219253200, -2445426000, -**********], "diffs": [87768, 1368, -85032, -171432, -257832, -344232, -430632, -517032, -603432, -689832, -776232, -862632, -949032, -85032, 1368, 0]}, {"tz": "Pacific/Tahiti", "switches": [-62135733600, -59006412000, -55850652000, -52694892000, -46383372000, -43227612000, -40071852000, -33760332000, -30604572000, -27448812000, -21137292000, -17981532000, -14825772000, -12219256800, -**********], "diffs": [172696, 86296, -104, -86504, -172904, -259304, -345704, -432104, -518504, -604904, -691304, -777704, -864104, -104, 0]}, {"tz": "Pacific/Tarawa", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [174476, 88076, 1676, -84724, -171124, -257524, -343924, -430324, -516724, -603124, -689524, -775924, -862324, 1676, 0]}, {"tz": "Pacific/Tongatapu", "switches": [-62135816400, -59006494800, -55850734800, -52694974800, -46383454800, -43227694800, -40071934800, -33760414800, -30604654800, -27448894800, -21137374800, -17981614800, -14825854800, -12219339600, -**********], "diffs": [175248, 88848, 2448, -83952, -170352, -256752, -343152, -429552, -515952, -602352, -688752, -775152, -861552, 2448, 0]}, {"tz": "Pacific/Truk", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2840176808, -2366790512], "diffs": [173480, 87080, 680, -85720, -172120, -258520, -344920, -431320, -517720, -604120, -690520, -776920, -863320, 680, 688, 0]}, {"tz": "Pacific/Wake", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [174476, 88076, 1676, -84724, -171124, -257524, -343924, -430324, -516724, -603124, -689524, -775924, -862324, 1676, 0]}, {"tz": "Pacific/Wallis", "switches": [-62135812800, -59006491200, -55850731200, -52694971200, -46383451200, -43227691200, -40071931200, -33760411200, -30604651200, -27448891200, -21137371200, -17981611200, -14825851200, -12219336000, -**********], "diffs": [174476, 88076, 1676, -84724, -171124, -257524, -343924, -430324, -516724, -603124, -689524, -775924, -862324, 1676, 0]}, {"tz": "Pacific/Yap", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -2840176808, -2366790512], "diffs": [173480, 87080, 680, -85720, -172120, -258520, -344920, -431320, -517720, -604120, -690520, -776920, -863320, 680, 688, 0]}, {"tz": "Poland", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [171360, 84960, -1440, -87840, -174240, -260640, -347040, -433440, -519840, -606240, -692640, -779040, -865440, -1440, 0]}, {"tz": "Portugal", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [175005, 88605, 2205, -84195, -170595, -256995, -343395, -429795, -516195, -602595, -688995, -775395, -861795, 2205, 0]}, {"tz": "ROK", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -**********], "diffs": [174728, 88328, 1928, -84472, -170872, -257272, -343672, -430072, -516472, -602872, -689272, -775672, -862072, 1928, 0]}, {"tz": "Singapore", "switches": [-62135798400, -59006476800, -55850716800, -52694956800, -46383436800, -43227676800, -40071916800, -33760396800, -30604636800, -27448876800, -21137356800, -17981596800, -14825836800, -12219321600, -**********], "diffs": [176675, 90275, 3875, -82525, -168925, -255325, -341725, -428125, -514525, -600925, -687325, -773725, -860125, 3875, 0]}, {"tz": "SystemV/AST4", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/AST4ADT", "switches": [-62135755200, -59006433600, -55850673600, -52694913600, -46383393600, -43227633600, -40071873600, -33760353600, -30604593600, -27448833600, -21137313600, -17981553600, -14825793600, -12219278400], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/CST6", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/CST6CDT", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/EST5", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/EST5EDT", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/HST10", "switches": [-62135733600, -59006412000, -55850652000, -52694892000, -46383372000, -43227612000, -40071852000, -33760332000, -30604572000, -27448812000, -21137292000, -17981532000, -14825772000, -12219256800], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/MST7", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/MST7MDT", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/PST8", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/PST8PDT", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/YST9", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "SystemV/YST9YDT", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Turkey", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -2840151536, -**********], "diffs": [176648, 90248, 3848, -82552, -168952, -255352, -341752, -428152, -514552, -600952, -687352, -773752, -860152, 3848, 3784, 0]}, {"tz": "UCT", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "US/Alaska", "switches": [-62135737200, -59006415600, -55850655600, -52694895600, -46383375600, -43227615600, -40071855600, -33760335600, -30604575600, -27448815600, -21137295600, -17981535600, -14825775600, -12219260400, -3225227303, -**********], "diffs": [89976, 3576, -82824, -169224, -255624, -342024, -428424, -514824, -601224, -687624, -774024, -860424, -946824, -82824, 3576, 0]}, {"tz": "US/Aleutian", "switches": [-62135733600, -59006412000, -55850652000, -52694892000, -46383372000, -43227612000, -40071852000, -33760332000, -30604572000, -27448812000, -21137292000, -17981532000, -14825772000, -12219256800, -3225230125, -**********], "diffs": [92798, 6398, -80002, -166402, -252802, -339202, -425602, -512002, -598402, -684802, -771202, -857602, -944002, -80002, 6398, 0]}, {"tz": "US/Arizona", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -2717643600], "diffs": [174498, 88098, 1698, -84702, -171102, -257502, -343902, -430302, -516702, -603102, -689502, -775902, -862302, 1698, 0]}, {"tz": "US/Central", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2717647200], "diffs": [172236, 85836, -564, -86964, -173364, -259764, -346164, -432564, -518964, -605364, -691764, -778164, -864564, -564, 0]}, {"tz": "US/East-Indiana", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800, -**********], "diffs": [175478, 89078, 2678, -83722, -170122, -256522, -342922, -429322, -515722, -602122, -688522, -774922, -861322, 2678, 3600, 0]}, {"tz": "US/Eastern", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -2717650800], "diffs": [172562, 86162, -238, -86638, -173038, -259438, -345838, -432238, -518638, -605038, -691438, -777838, -864238, -238, 0]}, {"tz": "US/Hawaii", "switches": [-62135733600, -59006412000, -55850652000, -52694892000, -46383372000, -43227612000, -40071852000, -33760332000, -30604572000, -27448812000, -21137292000, -17981532000, -14825772000, -12219256800, -2334103114, -**********], "diffs": [174686, 88286, 1886, -84514, -170914, -257314, -343714, -430114, -516514, -602914, -689314, -775714, -862114, 1886, 1800, 0]}, {"tz": "US/Indiana-Starke", "switches": [-62135748000, -59006426400, -55850666400, -52694906400, -46383386400, -43227626400, -40071866400, -33760346400, -30604586400, -27448826400, -21137306400, -17981546400, -14825786400, -12219271200, -2717647200], "diffs": [171990, 85590, -810, -87210, -173610, -260010, -346410, -432810, -519210, -605610, -692010, -778410, -864810, -810, 0]}, {"tz": "US/Michigan", "switches": [-62135751600, -59006430000, -55850670000, -52694910000, -46383390000, -43227630000, -40071870000, -33760350000, -30604590000, -27448830000, -21137310000, -17981550000, -14825790000, -12219274800, -**********], "diffs": [174731, 88331, 1931, -84469, -170869, -257269, -343669, -430069, -516469, -602869, -689269, -775669, -862069, 1931, 0]}, {"tz": "US/Mountain", "switches": [-62135744400, -59006422800, -55850662800, -52694902800, -46383382800, -43227622800, -40071862800, -33760342800, -30604582800, -27448822800, -21137302800, -17981542800, -14825782800, -12219267600, -2717643600], "diffs": [172796, 86396, -4, -86404, -172804, -259204, -345604, -432004, -518404, -604804, -691204, -777604, -864004, -4, 0]}, {"tz": "US/Pacific", "switches": [-62135740800, -59006419200, -55850659200, -52694899200, -46383379200, -43227619200, -40071859200, -33760339200, -30604579200, -27448819200, -21137299200, -17981539200, -14825779200, -12219264000, -2717640000], "diffs": [172378, 85978, -422, -86822, -173222, -259622, -346022, -432422, -518822, -605222, -691622, -778022, -864422, -422, 0]}, {"tz": "US/Samoa", "switches": [-62135730000, -59006408400, -55850648400, -52694888400, -46383368400, -43227608400, -40071848400, -33760328400, -30604568400, -27448808400, -21137288400, -17981528400, -14825768400, -12219253200, -2445426000, -**********], "diffs": [87768, 1368, -85032, -171432, -257832, -344232, -430632, -517032, -603432, -689832, -776232, -862632, -949032, -85032, 1368, 0]}, {"tz": "UTC", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Universal", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "W-SU", "switches": [-62135780400, -59006458800, -55850698800, -52694938800, -46383418800, -43227658800, -40071898800, -33760378800, -30604618800, -27448858800, -21137338800, -17981578800, -14825818800, -12219303600, -**********], "diffs": [174583, 88183, 1783, -84617, -171017, -257417, -343817, -430217, -516617, -603017, -689417, -775817, -862217, 1783, 0]}, {"tz": "WET", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}, {"tz": "Zulu", "switches": [-***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********, -***********], "diffs": [172800, 86400, 0, -86400, -172800, -259200, -345600, -432000, -518400, -604800, -691200, -777600, -864000, 0]}]