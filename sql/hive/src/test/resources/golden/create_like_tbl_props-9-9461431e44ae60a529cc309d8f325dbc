# col_name            	data_type           	comment             
	 	 
key                 	int                 	                    
value               	string              	                    
	 	 
# Detailed Table Information	 	 
Database:           	default             	 
Owner:              	hcheng              	 
CreateTime:         	Fri Nov 28 00:04:16 PST 2014	 
LastAccessTime:     	UNKNOWN             	 
Protect Mode:       	None                	 
Retention:          	0                   	 
Location:           	file:/tmp/sparkHiveWarehouse3490012261419180285/test_table3	 
Table Type:         	MANAGED_TABLE       	 
Table Parameters:	 	 
	key1                	value1              
	key2                	value3              
	transient_lastDdlTime	1417161856          
	 	 
# Storage Information	 	 
SerDe Library:      	org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe	 
InputFormat:        	org.apache.hadoop.mapred.TextInputFormat	 
OutputFormat:       	org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat	 
Compressed:         	No                  	 
Num Buckets:        	-1                  	 
Bucket Columns:     	[]                  	 
Sort Columns:       	[]                  	 
Storage Desc Params:	 	 
	serialization.format	1                   
