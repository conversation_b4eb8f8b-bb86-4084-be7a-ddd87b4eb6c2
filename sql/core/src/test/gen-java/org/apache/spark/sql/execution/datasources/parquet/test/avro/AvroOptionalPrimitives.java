/**
 * Autogenerated by Avro
 * 
 * DO NOT EDIT DIRECTLY
 */
package org.apache.spark.sql.execution.datasources.parquet.test.avro;  
@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class AvroOptionalPrimitives extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"AvroOptionalPrimitives\",\"namespace\":\"org.apache.spark.sql.execution.datasources.parquet.test.avro\",\"fields\":[{\"name\":\"maybe_bool_column\",\"type\":[\"null\",\"boolean\"]},{\"name\":\"maybe_int_column\",\"type\":[\"null\",\"int\"]},{\"name\":\"maybe_long_column\",\"type\":[\"null\",\"long\"]},{\"name\":\"maybe_float_column\",\"type\":[\"null\",\"float\"]},{\"name\":\"maybe_double_column\",\"type\":[\"null\",\"double\"]},{\"name\":\"maybe_binary_column\",\"type\":[\"null\",\"bytes\"]},{\"name\":\"maybe_string_column\",\"type\":[\"null\",{\"type\":\"string\",\"avro.java.string\":\"String\"}]}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }
  @Deprecated public java.lang.Boolean maybe_bool_column;
  @Deprecated public java.lang.Integer maybe_int_column;
  @Deprecated public java.lang.Long maybe_long_column;
  @Deprecated public java.lang.Float maybe_float_column;
  @Deprecated public java.lang.Double maybe_double_column;
  @Deprecated public java.nio.ByteBuffer maybe_binary_column;
  @Deprecated public java.lang.String maybe_string_column;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>. 
   */
  public AvroOptionalPrimitives() {}

  /**
   * All-args constructor.
   */
  public AvroOptionalPrimitives(java.lang.Boolean maybe_bool_column, java.lang.Integer maybe_int_column, java.lang.Long maybe_long_column, java.lang.Float maybe_float_column, java.lang.Double maybe_double_column, java.nio.ByteBuffer maybe_binary_column, java.lang.String maybe_string_column) {
    this.maybe_bool_column = maybe_bool_column;
    this.maybe_int_column = maybe_int_column;
    this.maybe_long_column = maybe_long_column;
    this.maybe_float_column = maybe_float_column;
    this.maybe_double_column = maybe_double_column;
    this.maybe_binary_column = maybe_binary_column;
    this.maybe_string_column = maybe_string_column;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call. 
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return maybe_bool_column;
    case 1: return maybe_int_column;
    case 2: return maybe_long_column;
    case 3: return maybe_float_column;
    case 4: return maybe_double_column;
    case 5: return maybe_binary_column;
    case 6: return maybe_string_column;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }
  // Used by DatumReader.  Applications should not call. 
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: maybe_bool_column = (java.lang.Boolean)value$; break;
    case 1: maybe_int_column = (java.lang.Integer)value$; break;
    case 2: maybe_long_column = (java.lang.Long)value$; break;
    case 3: maybe_float_column = (java.lang.Float)value$; break;
    case 4: maybe_double_column = (java.lang.Double)value$; break;
    case 5: maybe_binary_column = (java.nio.ByteBuffer)value$; break;
    case 6: maybe_string_column = (java.lang.String)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'maybe_bool_column' field.
   */
  public java.lang.Boolean getMaybeBoolColumn() {
    return maybe_bool_column;
  }

  /**
   * Sets the value of the 'maybe_bool_column' field.
   * @param value the value to set.
   */
  public void setMaybeBoolColumn(java.lang.Boolean value) {
    this.maybe_bool_column = value;
  }

  /**
   * Gets the value of the 'maybe_int_column' field.
   */
  public java.lang.Integer getMaybeIntColumn() {
    return maybe_int_column;
  }

  /**
   * Sets the value of the 'maybe_int_column' field.
   * @param value the value to set.
   */
  public void setMaybeIntColumn(java.lang.Integer value) {
    this.maybe_int_column = value;
  }

  /**
   * Gets the value of the 'maybe_long_column' field.
   */
  public java.lang.Long getMaybeLongColumn() {
    return maybe_long_column;
  }

  /**
   * Sets the value of the 'maybe_long_column' field.
   * @param value the value to set.
   */
  public void setMaybeLongColumn(java.lang.Long value) {
    this.maybe_long_column = value;
  }

  /**
   * Gets the value of the 'maybe_float_column' field.
   */
  public java.lang.Float getMaybeFloatColumn() {
    return maybe_float_column;
  }

  /**
   * Sets the value of the 'maybe_float_column' field.
   * @param value the value to set.
   */
  public void setMaybeFloatColumn(java.lang.Float value) {
    this.maybe_float_column = value;
  }

  /**
   * Gets the value of the 'maybe_double_column' field.
   */
  public java.lang.Double getMaybeDoubleColumn() {
    return maybe_double_column;
  }

  /**
   * Sets the value of the 'maybe_double_column' field.
   * @param value the value to set.
   */
  public void setMaybeDoubleColumn(java.lang.Double value) {
    this.maybe_double_column = value;
  }

  /**
   * Gets the value of the 'maybe_binary_column' field.
   */
  public java.nio.ByteBuffer getMaybeBinaryColumn() {
    return maybe_binary_column;
  }

  /**
   * Sets the value of the 'maybe_binary_column' field.
   * @param value the value to set.
   */
  public void setMaybeBinaryColumn(java.nio.ByteBuffer value) {
    this.maybe_binary_column = value;
  }

  /**
   * Gets the value of the 'maybe_string_column' field.
   */
  public java.lang.String getMaybeStringColumn() {
    return maybe_string_column;
  }

  /**
   * Sets the value of the 'maybe_string_column' field.
   * @param value the value to set.
   */
  public void setMaybeStringColumn(java.lang.String value) {
    this.maybe_string_column = value;
  }

  /** Creates a new AvroOptionalPrimitives RecordBuilder */
  public static org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder newBuilder() {
    return new org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder();
  }
  
  /** Creates a new AvroOptionalPrimitives RecordBuilder by copying an existing Builder */
  public static org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder newBuilder(org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder other) {
    return new org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder(other);
  }
  
  /** Creates a new AvroOptionalPrimitives RecordBuilder by copying an existing AvroOptionalPrimitives instance */
  public static org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder newBuilder(org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives other) {
    return new org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder(other);
  }
  
  /**
   * RecordBuilder for AvroOptionalPrimitives instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<AvroOptionalPrimitives>
    implements org.apache.avro.data.RecordBuilder<AvroOptionalPrimitives> {

    private java.lang.Boolean maybe_bool_column;
    private java.lang.Integer maybe_int_column;
    private java.lang.Long maybe_long_column;
    private java.lang.Float maybe_float_column;
    private java.lang.Double maybe_double_column;
    private java.nio.ByteBuffer maybe_binary_column;
    private java.lang.String maybe_string_column;

    /** Creates a new Builder */
    private Builder() {
      super(org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.SCHEMA$);
    }
    
    /** Creates a Builder by copying an existing Builder */
    private Builder(org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.maybe_bool_column)) {
        this.maybe_bool_column = data().deepCopy(fields()[0].schema(), other.maybe_bool_column);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.maybe_int_column)) {
        this.maybe_int_column = data().deepCopy(fields()[1].schema(), other.maybe_int_column);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.maybe_long_column)) {
        this.maybe_long_column = data().deepCopy(fields()[2].schema(), other.maybe_long_column);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.maybe_float_column)) {
        this.maybe_float_column = data().deepCopy(fields()[3].schema(), other.maybe_float_column);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.maybe_double_column)) {
        this.maybe_double_column = data().deepCopy(fields()[4].schema(), other.maybe_double_column);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.maybe_binary_column)) {
        this.maybe_binary_column = data().deepCopy(fields()[5].schema(), other.maybe_binary_column);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.maybe_string_column)) {
        this.maybe_string_column = data().deepCopy(fields()[6].schema(), other.maybe_string_column);
        fieldSetFlags()[6] = true;
      }
    }
    
    /** Creates a Builder by copying an existing AvroOptionalPrimitives instance */
    private Builder(org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives other) {
            super(org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.SCHEMA$);
      if (isValidValue(fields()[0], other.maybe_bool_column)) {
        this.maybe_bool_column = data().deepCopy(fields()[0].schema(), other.maybe_bool_column);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.maybe_int_column)) {
        this.maybe_int_column = data().deepCopy(fields()[1].schema(), other.maybe_int_column);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.maybe_long_column)) {
        this.maybe_long_column = data().deepCopy(fields()[2].schema(), other.maybe_long_column);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.maybe_float_column)) {
        this.maybe_float_column = data().deepCopy(fields()[3].schema(), other.maybe_float_column);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.maybe_double_column)) {
        this.maybe_double_column = data().deepCopy(fields()[4].schema(), other.maybe_double_column);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.maybe_binary_column)) {
        this.maybe_binary_column = data().deepCopy(fields()[5].schema(), other.maybe_binary_column);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.maybe_string_column)) {
        this.maybe_string_column = data().deepCopy(fields()[6].schema(), other.maybe_string_column);
        fieldSetFlags()[6] = true;
      }
    }

    /** Gets the value of the 'maybe_bool_column' field */
    public java.lang.Boolean getMaybeBoolColumn() {
      return maybe_bool_column;
    }
    
    /** Sets the value of the 'maybe_bool_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder setMaybeBoolColumn(java.lang.Boolean value) {
      validate(fields()[0], value);
      this.maybe_bool_column = value;
      fieldSetFlags()[0] = true;
      return this; 
    }
    
    /** Checks whether the 'maybe_bool_column' field has been set */
    public boolean hasMaybeBoolColumn() {
      return fieldSetFlags()[0];
    }
    
    /** Clears the value of the 'maybe_bool_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder clearMaybeBoolColumn() {
      maybe_bool_column = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /** Gets the value of the 'maybe_int_column' field */
    public java.lang.Integer getMaybeIntColumn() {
      return maybe_int_column;
    }
    
    /** Sets the value of the 'maybe_int_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder setMaybeIntColumn(java.lang.Integer value) {
      validate(fields()[1], value);
      this.maybe_int_column = value;
      fieldSetFlags()[1] = true;
      return this; 
    }
    
    /** Checks whether the 'maybe_int_column' field has been set */
    public boolean hasMaybeIntColumn() {
      return fieldSetFlags()[1];
    }
    
    /** Clears the value of the 'maybe_int_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder clearMaybeIntColumn() {
      maybe_int_column = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /** Gets the value of the 'maybe_long_column' field */
    public java.lang.Long getMaybeLongColumn() {
      return maybe_long_column;
    }
    
    /** Sets the value of the 'maybe_long_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder setMaybeLongColumn(java.lang.Long value) {
      validate(fields()[2], value);
      this.maybe_long_column = value;
      fieldSetFlags()[2] = true;
      return this; 
    }
    
    /** Checks whether the 'maybe_long_column' field has been set */
    public boolean hasMaybeLongColumn() {
      return fieldSetFlags()[2];
    }
    
    /** Clears the value of the 'maybe_long_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder clearMaybeLongColumn() {
      maybe_long_column = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /** Gets the value of the 'maybe_float_column' field */
    public java.lang.Float getMaybeFloatColumn() {
      return maybe_float_column;
    }
    
    /** Sets the value of the 'maybe_float_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder setMaybeFloatColumn(java.lang.Float value) {
      validate(fields()[3], value);
      this.maybe_float_column = value;
      fieldSetFlags()[3] = true;
      return this; 
    }
    
    /** Checks whether the 'maybe_float_column' field has been set */
    public boolean hasMaybeFloatColumn() {
      return fieldSetFlags()[3];
    }
    
    /** Clears the value of the 'maybe_float_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder clearMaybeFloatColumn() {
      maybe_float_column = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /** Gets the value of the 'maybe_double_column' field */
    public java.lang.Double getMaybeDoubleColumn() {
      return maybe_double_column;
    }
    
    /** Sets the value of the 'maybe_double_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder setMaybeDoubleColumn(java.lang.Double value) {
      validate(fields()[4], value);
      this.maybe_double_column = value;
      fieldSetFlags()[4] = true;
      return this; 
    }
    
    /** Checks whether the 'maybe_double_column' field has been set */
    public boolean hasMaybeDoubleColumn() {
      return fieldSetFlags()[4];
    }
    
    /** Clears the value of the 'maybe_double_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder clearMaybeDoubleColumn() {
      maybe_double_column = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /** Gets the value of the 'maybe_binary_column' field */
    public java.nio.ByteBuffer getMaybeBinaryColumn() {
      return maybe_binary_column;
    }
    
    /** Sets the value of the 'maybe_binary_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder setMaybeBinaryColumn(java.nio.ByteBuffer value) {
      validate(fields()[5], value);
      this.maybe_binary_column = value;
      fieldSetFlags()[5] = true;
      return this; 
    }
    
    /** Checks whether the 'maybe_binary_column' field has been set */
    public boolean hasMaybeBinaryColumn() {
      return fieldSetFlags()[5];
    }
    
    /** Clears the value of the 'maybe_binary_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder clearMaybeBinaryColumn() {
      maybe_binary_column = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /** Gets the value of the 'maybe_string_column' field */
    public java.lang.String getMaybeStringColumn() {
      return maybe_string_column;
    }
    
    /** Sets the value of the 'maybe_string_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder setMaybeStringColumn(java.lang.String value) {
      validate(fields()[6], value);
      this.maybe_string_column = value;
      fieldSetFlags()[6] = true;
      return this; 
    }
    
    /** Checks whether the 'maybe_string_column' field has been set */
    public boolean hasMaybeStringColumn() {
      return fieldSetFlags()[6];
    }
    
    /** Clears the value of the 'maybe_string_column' field */
    public org.apache.spark.sql.execution.datasources.parquet.test.avro.AvroOptionalPrimitives.Builder clearMaybeStringColumn() {
      maybe_string_column = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    @Override
    public AvroOptionalPrimitives build() {
      try {
        AvroOptionalPrimitives record = new AvroOptionalPrimitives();
        record.maybe_bool_column = fieldSetFlags()[0] ? this.maybe_bool_column : (java.lang.Boolean) defaultValue(fields()[0]);
        record.maybe_int_column = fieldSetFlags()[1] ? this.maybe_int_column : (java.lang.Integer) defaultValue(fields()[1]);
        record.maybe_long_column = fieldSetFlags()[2] ? this.maybe_long_column : (java.lang.Long) defaultValue(fields()[2]);
        record.maybe_float_column = fieldSetFlags()[3] ? this.maybe_float_column : (java.lang.Float) defaultValue(fields()[3]);
        record.maybe_double_column = fieldSetFlags()[4] ? this.maybe_double_column : (java.lang.Double) defaultValue(fields()[4]);
        record.maybe_binary_column = fieldSetFlags()[5] ? this.maybe_binary_column : (java.nio.ByteBuffer) defaultValue(fields()[5]);
        record.maybe_string_column = fieldSetFlags()[6] ? this.maybe_string_column : (java.lang.String) defaultValue(fields()[6]);
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }
}
