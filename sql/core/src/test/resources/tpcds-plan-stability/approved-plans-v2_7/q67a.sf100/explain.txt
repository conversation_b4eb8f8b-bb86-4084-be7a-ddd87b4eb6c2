== Physical Plan ==
TakeOrderedAndProject (73)
+- * Filter (72)
   +- Window (71)
      +- WindowGroupLimit (70)
         +- * Sort (69)
            +- Exchange (68)
               +- WindowGroupLimit (67)
                  +- * Sort (66)
                     +- Union (65)
                        :- * HashAggregate (24)
                        :  +- Exchange (23)
                        :     +- * HashAggregate (22)
                        :        +- * Project (21)
                        :           +- * SortMergeJoin Inner (20)
                        :              :- * Sort (14)
                        :              :  +- Exchange (13)
                        :              :     +- * Project (12)
                        :              :        +- * BroadcastHashJoin Inner BuildRight (11)
                        :              :           :- * Project (6)
                        :              :           :  +- * BroadcastHashJoin Inner BuildRight (5)
                        :              :           :     :- * Filter (3)
                        :              :           :     :  +- * ColumnarToRow (2)
                        :              :           :     :     +- Scan parquet spark_catalog.default.store_sales (1)
                        :              :           :     +- ReusedExchange (4)
                        :              :           +- BroadcastExchange (10)
                        :              :              +- * Filter (9)
                        :              :                 +- * ColumnarToRow (8)
                        :              :                    +- Scan parquet spark_catalog.default.store (7)
                        :              +- * Sort (19)
                        :                 +- Exchange (18)
                        :                    +- * Filter (17)
                        :                       +- * ColumnarToRow (16)
                        :                          +- Scan parquet spark_catalog.default.item (15)
                        :- * HashAggregate (29)
                        :  +- Exchange (28)
                        :     +- * HashAggregate (27)
                        :        +- * HashAggregate (26)
                        :           +- ReusedExchange (25)
                        :- * HashAggregate (34)
                        :  +- Exchange (33)
                        :     +- * HashAggregate (32)
                        :        +- * HashAggregate (31)
                        :           +- ReusedExchange (30)
                        :- * HashAggregate (39)
                        :  +- Exchange (38)
                        :     +- * HashAggregate (37)
                        :        +- * HashAggregate (36)
                        :           +- ReusedExchange (35)
                        :- * HashAggregate (44)
                        :  +- Exchange (43)
                        :     +- * HashAggregate (42)
                        :        +- * HashAggregate (41)
                        :           +- ReusedExchange (40)
                        :- * HashAggregate (49)
                        :  +- Exchange (48)
                        :     +- * HashAggregate (47)
                        :        +- * HashAggregate (46)
                        :           +- ReusedExchange (45)
                        :- * HashAggregate (54)
                        :  +- Exchange (53)
                        :     +- * HashAggregate (52)
                        :        +- * HashAggregate (51)
                        :           +- ReusedExchange (50)
                        :- * HashAggregate (59)
                        :  +- Exchange (58)
                        :     +- * HashAggregate (57)
                        :        +- * HashAggregate (56)
                        :           +- ReusedExchange (55)
                        +- * HashAggregate (64)
                           +- Exchange (63)
                              +- * HashAggregate (62)
                                 +- * HashAggregate (61)
                                    +- ReusedExchange (60)


(1) Scan parquet spark_catalog.default.store_sales
Output [5]: [ss_item_sk#1, ss_store_sk#2, ss_quantity#3, ss_sales_price#4, ss_sold_date_sk#5]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ss_sold_date_sk#5), dynamicpruningexpression(ss_sold_date_sk#5 IN dynamicpruning#6)]
PushedFilters: [IsNotNull(ss_store_sk), IsNotNull(ss_item_sk)]
ReadSchema: struct<ss_item_sk:int,ss_store_sk:int,ss_quantity:int,ss_sales_price:decimal(7,2)>

(2) ColumnarToRow [codegen id : 3]
Input [5]: [ss_item_sk#1, ss_store_sk#2, ss_quantity#3, ss_sales_price#4, ss_sold_date_sk#5]

(3) Filter [codegen id : 3]
Input [5]: [ss_item_sk#1, ss_store_sk#2, ss_quantity#3, ss_sales_price#4, ss_sold_date_sk#5]
Condition : (isnotnull(ss_store_sk#2) AND isnotnull(ss_item_sk#1))

(4) ReusedExchange [Reuses operator id: 78]
Output [4]: [d_date_sk#7, d_year#8, d_moy#9, d_qoy#10]

(5) BroadcastHashJoin [codegen id : 3]
Left keys [1]: [ss_sold_date_sk#5]
Right keys [1]: [d_date_sk#7]
Join type: Inner
Join condition: None

(6) Project [codegen id : 3]
Output [7]: [ss_item_sk#1, ss_store_sk#2, ss_quantity#3, ss_sales_price#4, d_year#8, d_moy#9, d_qoy#10]
Input [9]: [ss_item_sk#1, ss_store_sk#2, ss_quantity#3, ss_sales_price#4, ss_sold_date_sk#5, d_date_sk#7, d_year#8, d_moy#9, d_qoy#10]

(7) Scan parquet spark_catalog.default.store
Output [2]: [s_store_sk#11, s_store_id#12]
Batched: true
Location [not included in comparison]/{warehouse_dir}/store]
PushedFilters: [IsNotNull(s_store_sk)]
ReadSchema: struct<s_store_sk:int,s_store_id:string>

(8) ColumnarToRow [codegen id : 2]
Input [2]: [s_store_sk#11, s_store_id#12]

(9) Filter [codegen id : 2]
Input [2]: [s_store_sk#11, s_store_id#12]
Condition : isnotnull(s_store_sk#11)

(10) BroadcastExchange
Input [2]: [s_store_sk#11, s_store_id#12]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, false] as bigint)),false), [plan_id=1]

(11) BroadcastHashJoin [codegen id : 3]
Left keys [1]: [ss_store_sk#2]
Right keys [1]: [s_store_sk#11]
Join type: Inner
Join condition: None

(12) Project [codegen id : 3]
Output [7]: [ss_item_sk#1, ss_quantity#3, ss_sales_price#4, d_year#8, d_moy#9, d_qoy#10, s_store_id#12]
Input [9]: [ss_item_sk#1, ss_store_sk#2, ss_quantity#3, ss_sales_price#4, d_year#8, d_moy#9, d_qoy#10, s_store_sk#11, s_store_id#12]

(13) Exchange
Input [7]: [ss_item_sk#1, ss_quantity#3, ss_sales_price#4, d_year#8, d_moy#9, d_qoy#10, s_store_id#12]
Arguments: hashpartitioning(ss_item_sk#1, 5), ENSURE_REQUIREMENTS, [plan_id=2]

(14) Sort [codegen id : 4]
Input [7]: [ss_item_sk#1, ss_quantity#3, ss_sales_price#4, d_year#8, d_moy#9, d_qoy#10, s_store_id#12]
Arguments: [ss_item_sk#1 ASC NULLS FIRST], false, 0

(15) Scan parquet spark_catalog.default.item
Output [5]: [i_item_sk#13, i_brand#14, i_class#15, i_category#16, i_product_name#17]
Batched: true
Location [not included in comparison]/{warehouse_dir}/item]
PushedFilters: [IsNotNull(i_item_sk)]
ReadSchema: struct<i_item_sk:int,i_brand:string,i_class:string,i_category:string,i_product_name:string>

(16) ColumnarToRow [codegen id : 5]
Input [5]: [i_item_sk#13, i_brand#14, i_class#15, i_category#16, i_product_name#17]

(17) Filter [codegen id : 5]
Input [5]: [i_item_sk#13, i_brand#14, i_class#15, i_category#16, i_product_name#17]
Condition : isnotnull(i_item_sk#13)

(18) Exchange
Input [5]: [i_item_sk#13, i_brand#14, i_class#15, i_category#16, i_product_name#17]
Arguments: hashpartitioning(i_item_sk#13, 5), ENSURE_REQUIREMENTS, [plan_id=3]

(19) Sort [codegen id : 6]
Input [5]: [i_item_sk#13, i_brand#14, i_class#15, i_category#16, i_product_name#17]
Arguments: [i_item_sk#13 ASC NULLS FIRST], false, 0

(20) SortMergeJoin [codegen id : 7]
Left keys [1]: [ss_item_sk#1]
Right keys [1]: [i_item_sk#13]
Join type: Inner
Join condition: None

(21) Project [codegen id : 7]
Output [10]: [ss_quantity#3, ss_sales_price#4, d_year#8, d_moy#9, d_qoy#10, s_store_id#12, i_brand#14, i_class#15, i_category#16, i_product_name#17]
Input [12]: [ss_item_sk#1, ss_quantity#3, ss_sales_price#4, d_year#8, d_moy#9, d_qoy#10, s_store_id#12, i_item_sk#13, i_brand#14, i_class#15, i_category#16, i_product_name#17]

(22) HashAggregate [codegen id : 7]
Input [10]: [ss_quantity#3, ss_sales_price#4, d_year#8, d_moy#9, d_qoy#10, s_store_id#12, i_brand#14, i_class#15, i_category#16, i_product_name#17]
Keys [8]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12]
Functions [1]: [partial_sum(coalesce((ss_sales_price#4 * cast(ss_quantity#3 as decimal(10,0))), 0.00))]
Aggregate Attributes [2]: [sum#18, isEmpty#19]
Results [10]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sum#20, isEmpty#21]

(23) Exchange
Input [10]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sum#20, isEmpty#21]
Arguments: hashpartitioning(i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, 5), ENSURE_REQUIREMENTS, [plan_id=4]

(24) HashAggregate [codegen id : 8]
Input [10]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sum#20, isEmpty#21]
Keys [8]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12]
Functions [1]: [sum(coalesce((ss_sales_price#4 * cast(ss_quantity#3 as decimal(10,0))), 0.00))]
Aggregate Attributes [1]: [sum(coalesce((ss_sales_price#4 * cast(ss_quantity#3 as decimal(10,0))), 0.00))#22]
Results [9]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, cast(sum(coalesce((ss_sales_price#4 * cast(ss_quantity#3 as decimal(10,0))), 0.00))#22 as decimal(38,2)) AS sumsales#23]

(25) ReusedExchange [Reuses operator id: 23]
Output [10]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30, s_store_id#31, sum#32, isEmpty#33]

(26) HashAggregate [codegen id : 16]
Input [10]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30, s_store_id#31, sum#32, isEmpty#33]
Keys [8]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30, s_store_id#31]
Functions [1]: [sum(coalesce((ss_sales_price#34 * cast(ss_quantity#35 as decimal(10,0))), 0.00))]
Aggregate Attributes [1]: [sum(coalesce((ss_sales_price#34 * cast(ss_quantity#35 as decimal(10,0))), 0.00))#22]
Results [8]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30, sum(coalesce((ss_sales_price#34 * cast(ss_quantity#35 as decimal(10,0))), 0.00))#22 AS sumsales#36]

(27) HashAggregate [codegen id : 16]
Input [8]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30, sumsales#36]
Keys [7]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30]
Functions [1]: [partial_sum(sumsales#36)]
Aggregate Attributes [2]: [sum#37, isEmpty#38]
Results [9]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30, sum#39, isEmpty#40]

(28) Exchange
Input [9]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30, sum#39, isEmpty#40]
Arguments: hashpartitioning(i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30, 5), ENSURE_REQUIREMENTS, [plan_id=5]

(29) HashAggregate [codegen id : 17]
Input [9]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30, sum#39, isEmpty#40]
Keys [7]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30]
Functions [1]: [sum(sumsales#36)]
Aggregate Attributes [1]: [sum(sumsales#36)#41]
Results [9]: [i_category#24, i_class#25, i_brand#26, i_product_name#27, d_year#28, d_qoy#29, d_moy#30, null AS s_store_id#42, sum(sumsales#36)#41 AS sumsales#43]

(30) ReusedExchange [Reuses operator id: 23]
Output [10]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49, d_moy#50, s_store_id#51, sum#52, isEmpty#53]

(31) HashAggregate [codegen id : 25]
Input [10]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49, d_moy#50, s_store_id#51, sum#52, isEmpty#53]
Keys [8]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49, d_moy#50, s_store_id#51]
Functions [1]: [sum(coalesce((ss_sales_price#54 * cast(ss_quantity#55 as decimal(10,0))), 0.00))]
Aggregate Attributes [1]: [sum(coalesce((ss_sales_price#54 * cast(ss_quantity#55 as decimal(10,0))), 0.00))#22]
Results [7]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49, sum(coalesce((ss_sales_price#54 * cast(ss_quantity#55 as decimal(10,0))), 0.00))#22 AS sumsales#56]

(32) HashAggregate [codegen id : 25]
Input [7]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49, sumsales#56]
Keys [6]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49]
Functions [1]: [partial_sum(sumsales#56)]
Aggregate Attributes [2]: [sum#57, isEmpty#58]
Results [8]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49, sum#59, isEmpty#60]

(33) Exchange
Input [8]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49, sum#59, isEmpty#60]
Arguments: hashpartitioning(i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49, 5), ENSURE_REQUIREMENTS, [plan_id=6]

(34) HashAggregate [codegen id : 26]
Input [8]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49, sum#59, isEmpty#60]
Keys [6]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49]
Functions [1]: [sum(sumsales#56)]
Aggregate Attributes [1]: [sum(sumsales#56)#61]
Results [9]: [i_category#44, i_class#45, i_brand#46, i_product_name#47, d_year#48, d_qoy#49, null AS d_moy#62, null AS s_store_id#63, sum(sumsales#56)#61 AS sumsales#64]

(35) ReusedExchange [Reuses operator id: 23]
Output [10]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69, d_qoy#70, d_moy#71, s_store_id#72, sum#73, isEmpty#74]

(36) HashAggregate [codegen id : 34]
Input [10]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69, d_qoy#70, d_moy#71, s_store_id#72, sum#73, isEmpty#74]
Keys [8]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69, d_qoy#70, d_moy#71, s_store_id#72]
Functions [1]: [sum(coalesce((ss_sales_price#75 * cast(ss_quantity#76 as decimal(10,0))), 0.00))]
Aggregate Attributes [1]: [sum(coalesce((ss_sales_price#75 * cast(ss_quantity#76 as decimal(10,0))), 0.00))#22]
Results [6]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69, sum(coalesce((ss_sales_price#75 * cast(ss_quantity#76 as decimal(10,0))), 0.00))#22 AS sumsales#77]

(37) HashAggregate [codegen id : 34]
Input [6]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69, sumsales#77]
Keys [5]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69]
Functions [1]: [partial_sum(sumsales#77)]
Aggregate Attributes [2]: [sum#78, isEmpty#79]
Results [7]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69, sum#80, isEmpty#81]

(38) Exchange
Input [7]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69, sum#80, isEmpty#81]
Arguments: hashpartitioning(i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69, 5), ENSURE_REQUIREMENTS, [plan_id=7]

(39) HashAggregate [codegen id : 35]
Input [7]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69, sum#80, isEmpty#81]
Keys [5]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69]
Functions [1]: [sum(sumsales#77)]
Aggregate Attributes [1]: [sum(sumsales#77)#82]
Results [9]: [i_category#65, i_class#66, i_brand#67, i_product_name#68, d_year#69, null AS d_qoy#83, null AS d_moy#84, null AS s_store_id#85, sum(sumsales#77)#82 AS sumsales#86]

(40) ReusedExchange [Reuses operator id: 23]
Output [10]: [i_category#87, i_class#88, i_brand#89, i_product_name#90, d_year#91, d_qoy#92, d_moy#93, s_store_id#94, sum#95, isEmpty#96]

(41) HashAggregate [codegen id : 43]
Input [10]: [i_category#87, i_class#88, i_brand#89, i_product_name#90, d_year#91, d_qoy#92, d_moy#93, s_store_id#94, sum#95, isEmpty#96]
Keys [8]: [i_category#87, i_class#88, i_brand#89, i_product_name#90, d_year#91, d_qoy#92, d_moy#93, s_store_id#94]
Functions [1]: [sum(coalesce((ss_sales_price#97 * cast(ss_quantity#98 as decimal(10,0))), 0.00))]
Aggregate Attributes [1]: [sum(coalesce((ss_sales_price#97 * cast(ss_quantity#98 as decimal(10,0))), 0.00))#22]
Results [5]: [i_category#87, i_class#88, i_brand#89, i_product_name#90, sum(coalesce((ss_sales_price#97 * cast(ss_quantity#98 as decimal(10,0))), 0.00))#22 AS sumsales#99]

(42) HashAggregate [codegen id : 43]
Input [5]: [i_category#87, i_class#88, i_brand#89, i_product_name#90, sumsales#99]
Keys [4]: [i_category#87, i_class#88, i_brand#89, i_product_name#90]
Functions [1]: [partial_sum(sumsales#99)]
Aggregate Attributes [2]: [sum#100, isEmpty#101]
Results [6]: [i_category#87, i_class#88, i_brand#89, i_product_name#90, sum#102, isEmpty#103]

(43) Exchange
Input [6]: [i_category#87, i_class#88, i_brand#89, i_product_name#90, sum#102, isEmpty#103]
Arguments: hashpartitioning(i_category#87, i_class#88, i_brand#89, i_product_name#90, 5), ENSURE_REQUIREMENTS, [plan_id=8]

(44) HashAggregate [codegen id : 44]
Input [6]: [i_category#87, i_class#88, i_brand#89, i_product_name#90, sum#102, isEmpty#103]
Keys [4]: [i_category#87, i_class#88, i_brand#89, i_product_name#90]
Functions [1]: [sum(sumsales#99)]
Aggregate Attributes [1]: [sum(sumsales#99)#104]
Results [9]: [i_category#87, i_class#88, i_brand#89, i_product_name#90, null AS d_year#105, null AS d_qoy#106, null AS d_moy#107, null AS s_store_id#108, sum(sumsales#99)#104 AS sumsales#109]

(45) ReusedExchange [Reuses operator id: 23]
Output [10]: [i_category#110, i_class#111, i_brand#112, i_product_name#113, d_year#114, d_qoy#115, d_moy#116, s_store_id#117, sum#118, isEmpty#119]

(46) HashAggregate [codegen id : 52]
Input [10]: [i_category#110, i_class#111, i_brand#112, i_product_name#113, d_year#114, d_qoy#115, d_moy#116, s_store_id#117, sum#118, isEmpty#119]
Keys [8]: [i_category#110, i_class#111, i_brand#112, i_product_name#113, d_year#114, d_qoy#115, d_moy#116, s_store_id#117]
Functions [1]: [sum(coalesce((ss_sales_price#120 * cast(ss_quantity#121 as decimal(10,0))), 0.00))]
Aggregate Attributes [1]: [sum(coalesce((ss_sales_price#120 * cast(ss_quantity#121 as decimal(10,0))), 0.00))#22]
Results [4]: [i_category#110, i_class#111, i_brand#112, sum(coalesce((ss_sales_price#120 * cast(ss_quantity#121 as decimal(10,0))), 0.00))#22 AS sumsales#122]

(47) HashAggregate [codegen id : 52]
Input [4]: [i_category#110, i_class#111, i_brand#112, sumsales#122]
Keys [3]: [i_category#110, i_class#111, i_brand#112]
Functions [1]: [partial_sum(sumsales#122)]
Aggregate Attributes [2]: [sum#123, isEmpty#124]
Results [5]: [i_category#110, i_class#111, i_brand#112, sum#125, isEmpty#126]

(48) Exchange
Input [5]: [i_category#110, i_class#111, i_brand#112, sum#125, isEmpty#126]
Arguments: hashpartitioning(i_category#110, i_class#111, i_brand#112, 5), ENSURE_REQUIREMENTS, [plan_id=9]

(49) HashAggregate [codegen id : 53]
Input [5]: [i_category#110, i_class#111, i_brand#112, sum#125, isEmpty#126]
Keys [3]: [i_category#110, i_class#111, i_brand#112]
Functions [1]: [sum(sumsales#122)]
Aggregate Attributes [1]: [sum(sumsales#122)#127]
Results [9]: [i_category#110, i_class#111, i_brand#112, null AS i_product_name#128, null AS d_year#129, null AS d_qoy#130, null AS d_moy#131, null AS s_store_id#132, sum(sumsales#122)#127 AS sumsales#133]

(50) ReusedExchange [Reuses operator id: 23]
Output [10]: [i_category#134, i_class#135, i_brand#136, i_product_name#137, d_year#138, d_qoy#139, d_moy#140, s_store_id#141, sum#142, isEmpty#143]

(51) HashAggregate [codegen id : 61]
Input [10]: [i_category#134, i_class#135, i_brand#136, i_product_name#137, d_year#138, d_qoy#139, d_moy#140, s_store_id#141, sum#142, isEmpty#143]
Keys [8]: [i_category#134, i_class#135, i_brand#136, i_product_name#137, d_year#138, d_qoy#139, d_moy#140, s_store_id#141]
Functions [1]: [sum(coalesce((ss_sales_price#144 * cast(ss_quantity#145 as decimal(10,0))), 0.00))]
Aggregate Attributes [1]: [sum(coalesce((ss_sales_price#144 * cast(ss_quantity#145 as decimal(10,0))), 0.00))#22]
Results [3]: [i_category#134, i_class#135, sum(coalesce((ss_sales_price#144 * cast(ss_quantity#145 as decimal(10,0))), 0.00))#22 AS sumsales#146]

(52) HashAggregate [codegen id : 61]
Input [3]: [i_category#134, i_class#135, sumsales#146]
Keys [2]: [i_category#134, i_class#135]
Functions [1]: [partial_sum(sumsales#146)]
Aggregate Attributes [2]: [sum#147, isEmpty#148]
Results [4]: [i_category#134, i_class#135, sum#149, isEmpty#150]

(53) Exchange
Input [4]: [i_category#134, i_class#135, sum#149, isEmpty#150]
Arguments: hashpartitioning(i_category#134, i_class#135, 5), ENSURE_REQUIREMENTS, [plan_id=10]

(54) HashAggregate [codegen id : 62]
Input [4]: [i_category#134, i_class#135, sum#149, isEmpty#150]
Keys [2]: [i_category#134, i_class#135]
Functions [1]: [sum(sumsales#146)]
Aggregate Attributes [1]: [sum(sumsales#146)#151]
Results [9]: [i_category#134, i_class#135, null AS i_brand#152, null AS i_product_name#153, null AS d_year#154, null AS d_qoy#155, null AS d_moy#156, null AS s_store_id#157, sum(sumsales#146)#151 AS sumsales#158]

(55) ReusedExchange [Reuses operator id: 23]
Output [10]: [i_category#159, i_class#160, i_brand#161, i_product_name#162, d_year#163, d_qoy#164, d_moy#165, s_store_id#166, sum#167, isEmpty#168]

(56) HashAggregate [codegen id : 70]
Input [10]: [i_category#159, i_class#160, i_brand#161, i_product_name#162, d_year#163, d_qoy#164, d_moy#165, s_store_id#166, sum#167, isEmpty#168]
Keys [8]: [i_category#159, i_class#160, i_brand#161, i_product_name#162, d_year#163, d_qoy#164, d_moy#165, s_store_id#166]
Functions [1]: [sum(coalesce((ss_sales_price#169 * cast(ss_quantity#170 as decimal(10,0))), 0.00))]
Aggregate Attributes [1]: [sum(coalesce((ss_sales_price#169 * cast(ss_quantity#170 as decimal(10,0))), 0.00))#22]
Results [2]: [i_category#159, sum(coalesce((ss_sales_price#169 * cast(ss_quantity#170 as decimal(10,0))), 0.00))#22 AS sumsales#171]

(57) HashAggregate [codegen id : 70]
Input [2]: [i_category#159, sumsales#171]
Keys [1]: [i_category#159]
Functions [1]: [partial_sum(sumsales#171)]
Aggregate Attributes [2]: [sum#172, isEmpty#173]
Results [3]: [i_category#159, sum#174, isEmpty#175]

(58) Exchange
Input [3]: [i_category#159, sum#174, isEmpty#175]
Arguments: hashpartitioning(i_category#159, 5), ENSURE_REQUIREMENTS, [plan_id=11]

(59) HashAggregate [codegen id : 71]
Input [3]: [i_category#159, sum#174, isEmpty#175]
Keys [1]: [i_category#159]
Functions [1]: [sum(sumsales#171)]
Aggregate Attributes [1]: [sum(sumsales#171)#176]
Results [9]: [i_category#159, null AS i_class#177, null AS i_brand#178, null AS i_product_name#179, null AS d_year#180, null AS d_qoy#181, null AS d_moy#182, null AS s_store_id#183, sum(sumsales#171)#176 AS sumsales#184]

(60) ReusedExchange [Reuses operator id: 23]
Output [10]: [i_category#185, i_class#186, i_brand#187, i_product_name#188, d_year#189, d_qoy#190, d_moy#191, s_store_id#192, sum#193, isEmpty#194]

(61) HashAggregate [codegen id : 79]
Input [10]: [i_category#185, i_class#186, i_brand#187, i_product_name#188, d_year#189, d_qoy#190, d_moy#191, s_store_id#192, sum#193, isEmpty#194]
Keys [8]: [i_category#185, i_class#186, i_brand#187, i_product_name#188, d_year#189, d_qoy#190, d_moy#191, s_store_id#192]
Functions [1]: [sum(coalesce((ss_sales_price#195 * cast(ss_quantity#196 as decimal(10,0))), 0.00))]
Aggregate Attributes [1]: [sum(coalesce((ss_sales_price#195 * cast(ss_quantity#196 as decimal(10,0))), 0.00))#22]
Results [1]: [sum(coalesce((ss_sales_price#195 * cast(ss_quantity#196 as decimal(10,0))), 0.00))#22 AS sumsales#197]

(62) HashAggregate [codegen id : 79]
Input [1]: [sumsales#197]
Keys: []
Functions [1]: [partial_sum(sumsales#197)]
Aggregate Attributes [2]: [sum#198, isEmpty#199]
Results [2]: [sum#200, isEmpty#201]

(63) Exchange
Input [2]: [sum#200, isEmpty#201]
Arguments: SinglePartition, ENSURE_REQUIREMENTS, [plan_id=12]

(64) HashAggregate [codegen id : 80]
Input [2]: [sum#200, isEmpty#201]
Keys: []
Functions [1]: [sum(sumsales#197)]
Aggregate Attributes [1]: [sum(sumsales#197)#202]
Results [9]: [null AS i_category#203, null AS i_class#204, null AS i_brand#205, null AS i_product_name#206, null AS d_year#207, null AS d_qoy#208, null AS d_moy#209, null AS s_store_id#210, sum(sumsales#197)#202 AS sumsales#211]

(65) Union

(66) Sort [codegen id : 81]
Input [9]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sumsales#23]
Arguments: [i_category#16 ASC NULLS FIRST, sumsales#23 DESC NULLS LAST], false, 0

(67) WindowGroupLimit
Input [9]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sumsales#23]
Arguments: [i_category#16], [sumsales#23 DESC NULLS LAST], rank(sumsales#23), 100, Partial

(68) Exchange
Input [9]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sumsales#23]
Arguments: hashpartitioning(i_category#16, 5), ENSURE_REQUIREMENTS, [plan_id=13]

(69) Sort [codegen id : 82]
Input [9]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sumsales#23]
Arguments: [i_category#16 ASC NULLS FIRST, sumsales#23 DESC NULLS LAST], false, 0

(70) WindowGroupLimit
Input [9]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sumsales#23]
Arguments: [i_category#16], [sumsales#23 DESC NULLS LAST], rank(sumsales#23), 100, Final

(71) Window
Input [9]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sumsales#23]
Arguments: [rank(sumsales#23) windowspecdefinition(i_category#16, sumsales#23 DESC NULLS LAST, specifiedwindowframe(RowFrame, unboundedpreceding$(), currentrow$())) AS rk#212], [i_category#16], [sumsales#23 DESC NULLS LAST]

(72) Filter [codegen id : 83]
Input [10]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sumsales#23, rk#212]
Condition : (rk#212 <= 100)

(73) TakeOrderedAndProject
Input [10]: [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sumsales#23, rk#212]
Arguments: 100, [i_category#16 ASC NULLS FIRST, i_class#15 ASC NULLS FIRST, i_brand#14 ASC NULLS FIRST, i_product_name#17 ASC NULLS FIRST, d_year#8 ASC NULLS FIRST, d_qoy#10 ASC NULLS FIRST, d_moy#9 ASC NULLS FIRST, s_store_id#12 ASC NULLS FIRST, sumsales#23 ASC NULLS FIRST, rk#212 ASC NULLS FIRST], [i_category#16, i_class#15, i_brand#14, i_product_name#17, d_year#8, d_qoy#10, d_moy#9, s_store_id#12, sumsales#23, rk#212]

===== Subqueries =====

Subquery:1 Hosting operator id = 1 Hosting Expression = ss_sold_date_sk#5 IN dynamicpruning#6
BroadcastExchange (78)
+- * Project (77)
   +- * Filter (76)
      +- * ColumnarToRow (75)
         +- Scan parquet spark_catalog.default.date_dim (74)


(74) Scan parquet spark_catalog.default.date_dim
Output [5]: [d_date_sk#7, d_month_seq#213, d_year#8, d_moy#9, d_qoy#10]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNotNull(d_month_seq), GreaterThanOrEqual(d_month_seq,1212), LessThanOrEqual(d_month_seq,1223), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_month_seq:int,d_year:int,d_moy:int,d_qoy:int>

(75) ColumnarToRow [codegen id : 1]
Input [5]: [d_date_sk#7, d_month_seq#213, d_year#8, d_moy#9, d_qoy#10]

(76) Filter [codegen id : 1]
Input [5]: [d_date_sk#7, d_month_seq#213, d_year#8, d_moy#9, d_qoy#10]
Condition : (((isnotnull(d_month_seq#213) AND (d_month_seq#213 >= 1212)) AND (d_month_seq#213 <= 1223)) AND isnotnull(d_date_sk#7))

(77) Project [codegen id : 1]
Output [4]: [d_date_sk#7, d_year#8, d_moy#9, d_qoy#10]
Input [5]: [d_date_sk#7, d_month_seq#213, d_year#8, d_moy#9, d_qoy#10]

(78) BroadcastExchange
Input [4]: [d_date_sk#7, d_year#8, d_moy#9, d_qoy#10]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=14]


