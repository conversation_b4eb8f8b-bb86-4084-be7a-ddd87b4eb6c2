== Physical Plan ==
TakeOrderedAndProject (202)
+- * HashAggregate (201)
   +- Exchange (200)
      +- * HashAggregate (199)
         +- Union (198)
            :- * HashAggregate (105)
            :  +- Exchange (104)
            :     +- * HashAggregate (103)
            :        +- Union (102)
            :           :- * HashAggregate (39)
            :           :  +- Exchange (38)
            :           :     +- * HashAggregate (37)
            :           :        +- * Project (36)
            :           :           +- * BroadcastHashJoin Inner BuildRight (35)
            :           :              :- * Project (30)
            :           :              :  +- * BroadcastHashJoin Inner BuildRight (29)
            :           :              :     :- * Project (27)
            :           :              :     :  +- * BroadcastHashJoin Inner BuildRight (26)
            :           :              :     :     :- * Project (20)
            :           :              :     :     :  +- * BroadcastHashJoin Inner BuildRight (19)
            :           :              :     :     :     :- * Project (13)
            :           :              :     :     :     :  +- * SortMergeJoin LeftOuter (12)
            :           :              :     :     :     :     :- * Sort (5)
            :           :              :     :     :     :     :  +- Exchange (4)
            :           :              :     :     :     :     :     +- * Filter (3)
            :           :              :     :     :     :     :        +- * ColumnarToRow (2)
            :           :              :     :     :     :     :           +- Scan parquet spark_catalog.default.store_sales (1)
            :           :              :     :     :     :     +- * Sort (11)
            :           :              :     :     :     :        +- Exchange (10)
            :           :              :     :     :     :           +- * Project (9)
            :           :              :     :     :     :              +- * Filter (8)
            :           :              :     :     :     :                 +- * ColumnarToRow (7)
            :           :              :     :     :     :                    +- Scan parquet spark_catalog.default.store_returns (6)
            :           :              :     :     :     +- BroadcastExchange (18)
            :           :              :     :     :        +- * Project (17)
            :           :              :     :     :           +- * Filter (16)
            :           :              :     :     :              +- * ColumnarToRow (15)
            :           :              :     :     :                 +- Scan parquet spark_catalog.default.item (14)
            :           :              :     :     +- BroadcastExchange (25)
            :           :              :     :        +- * Project (24)
            :           :              :     :           +- * Filter (23)
            :           :              :     :              +- * ColumnarToRow (22)
            :           :              :     :                 +- Scan parquet spark_catalog.default.promotion (21)
            :           :              :     +- ReusedExchange (28)
            :           :              +- BroadcastExchange (34)
            :           :                 +- * Filter (33)
            :           :                    +- * ColumnarToRow (32)
            :           :                       +- Scan parquet spark_catalog.default.store (31)
            :           :- * HashAggregate (70)
            :           :  +- Exchange (69)
            :           :     +- * HashAggregate (68)
            :           :        +- * Project (67)
            :           :           +- * BroadcastHashJoin Inner BuildRight (66)
            :           :              :- * Project (61)
            :           :              :  +- * BroadcastHashJoin Inner BuildRight (60)
            :           :              :     :- * Project (58)
            :           :              :     :  +- * BroadcastHashJoin Inner BuildRight (57)
            :           :              :     :     :- * Project (55)
            :           :              :     :     :  +- * BroadcastHashJoin Inner BuildRight (54)
            :           :              :     :     :     :- * Project (52)
            :           :              :     :     :     :  +- * SortMergeJoin LeftOuter (51)
            :           :              :     :     :     :     :- * Sort (44)
            :           :              :     :     :     :     :  +- Exchange (43)
            :           :              :     :     :     :     :     +- * Filter (42)
            :           :              :     :     :     :     :        +- * ColumnarToRow (41)
            :           :              :     :     :     :     :           +- Scan parquet spark_catalog.default.catalog_sales (40)
            :           :              :     :     :     :     +- * Sort (50)
            :           :              :     :     :     :        +- Exchange (49)
            :           :              :     :     :     :           +- * Project (48)
            :           :              :     :     :     :              +- * Filter (47)
            :           :              :     :     :     :                 +- * ColumnarToRow (46)
            :           :              :     :     :     :                    +- Scan parquet spark_catalog.default.catalog_returns (45)
            :           :              :     :     :     +- ReusedExchange (53)
            :           :              :     :     +- ReusedExchange (56)
            :           :              :     +- ReusedExchange (59)
            :           :              +- BroadcastExchange (65)
            :           :                 +- * Filter (64)
            :           :                    +- * ColumnarToRow (63)
            :           :                       +- Scan parquet spark_catalog.default.catalog_page (62)
            :           +- * HashAggregate (101)
            :              +- Exchange (100)
            :                 +- * HashAggregate (99)
            :                    +- * Project (98)
            :                       +- * BroadcastHashJoin Inner BuildRight (97)
            :                          :- * Project (92)
            :                          :  +- * BroadcastHashJoin Inner BuildRight (91)
            :                          :     :- * Project (89)
            :                          :     :  +- * BroadcastHashJoin Inner BuildRight (88)
            :                          :     :     :- * Project (86)
            :                          :     :     :  +- * BroadcastHashJoin Inner BuildRight (85)
            :                          :     :     :     :- * Project (83)
            :                          :     :     :     :  +- * SortMergeJoin LeftOuter (82)
            :                          :     :     :     :     :- * Sort (75)
            :                          :     :     :     :     :  +- Exchange (74)
            :                          :     :     :     :     :     +- * Filter (73)
            :                          :     :     :     :     :        +- * ColumnarToRow (72)
            :                          :     :     :     :     :           +- Scan parquet spark_catalog.default.web_sales (71)
            :                          :     :     :     :     +- * Sort (81)
            :                          :     :     :     :        +- Exchange (80)
            :                          :     :     :     :           +- * Project (79)
            :                          :     :     :     :              +- * Filter (78)
            :                          :     :     :     :                 +- * ColumnarToRow (77)
            :                          :     :     :     :                    +- Scan parquet spark_catalog.default.web_returns (76)
            :                          :     :     :     +- ReusedExchange (84)
            :                          :     :     +- ReusedExchange (87)
            :                          :     +- ReusedExchange (90)
            :                          +- BroadcastExchange (96)
            :                             +- * Filter (95)
            :                                +- * ColumnarToRow (94)
            :                                   +- Scan parquet spark_catalog.default.web_site (93)
            :- * HashAggregate (140)
            :  +- Exchange (139)
            :     +- * HashAggregate (138)
            :        +- * HashAggregate (137)
            :           +- Exchange (136)
            :              +- * HashAggregate (135)
            :                 +- Union (134)
            :                    :- * HashAggregate (107)
            :                    :  +- ReusedExchange (106)
            :                    :- * HashAggregate (109)
            :                    :  +- ReusedExchange (108)
            :                    +- * HashAggregate (133)
            :                       +- Exchange (132)
            :                          +- * HashAggregate (131)
            :                             +- * Project (130)
            :                                +- * BroadcastHashJoin Inner BuildRight (129)
            :                                   :- * Project (127)
            :                                   :  +- * BroadcastHashJoin Inner BuildRight (126)
            :                                   :     :- * Project (124)
            :                                   :     :  +- * BroadcastHashJoin Inner BuildRight (123)
            :                                   :     :     :- * Project (121)
            :                                   :     :     :  +- * BroadcastHashJoin Inner BuildRight (120)
            :                                   :     :     :     :- * Project (118)
            :                                   :     :     :     :  +- * SortMergeJoin LeftOuter (117)
            :                                   :     :     :     :     :- * Sort (114)
            :                                   :     :     :     :     :  +- Exchange (113)
            :                                   :     :     :     :     :     +- * Filter (112)
            :                                   :     :     :     :     :        +- * ColumnarToRow (111)
            :                                   :     :     :     :     :           +- Scan parquet spark_catalog.default.web_sales (110)
            :                                   :     :     :     :     +- * Sort (116)
            :                                   :     :     :     :        +- ReusedExchange (115)
            :                                   :     :     :     +- ReusedExchange (119)
            :                                   :     :     +- ReusedExchange (122)
            :                                   :     +- ReusedExchange (125)
            :                                   +- ReusedExchange (128)
            +- * HashAggregate (197)
               +- Exchange (196)
                  +- * HashAggregate (195)
                     +- * HashAggregate (194)
                        +- Exchange (193)
                           +- * HashAggregate (192)
                              +- Union (191)
                                 :- * HashAggregate (164)
                                 :  +- Exchange (163)
                                 :     +- * HashAggregate (162)
                                 :        +- * Project (161)
                                 :           +- * BroadcastHashJoin Inner BuildRight (160)
                                 :              :- * Project (158)
                                 :              :  +- * BroadcastHashJoin Inner BuildRight (157)
                                 :              :     :- * Project (155)
                                 :              :     :  +- * BroadcastHashJoin Inner BuildRight (154)
                                 :              :     :     :- * Project (152)
                                 :              :     :     :  +- * BroadcastHashJoin Inner BuildRight (151)
                                 :              :     :     :     :- * Project (149)
                                 :              :     :     :     :  +- * SortMergeJoin LeftOuter (148)
                                 :              :     :     :     :     :- * Sort (145)
                                 :              :     :     :     :     :  +- Exchange (144)
                                 :              :     :     :     :     :     +- * Filter (143)
                                 :              :     :     :     :     :        +- * ColumnarToRow (142)
                                 :              :     :     :     :     :           +- Scan parquet spark_catalog.default.store_sales (141)
                                 :              :     :     :     :     +- * Sort (147)
                                 :              :     :     :     :        +- ReusedExchange (146)
                                 :              :     :     :     +- ReusedExchange (150)
                                 :              :     :     +- ReusedExchange (153)
                                 :              :     +- ReusedExchange (156)
                                 :              +- ReusedExchange (159)
                                 :- * HashAggregate (188)
                                 :  +- Exchange (187)
                                 :     +- * HashAggregate (186)
                                 :        +- * Project (185)
                                 :           +- * BroadcastHashJoin Inner BuildRight (184)
                                 :              :- * Project (182)
                                 :              :  +- * BroadcastHashJoin Inner BuildRight (181)
                                 :              :     :- * Project (179)
                                 :              :     :  +- * BroadcastHashJoin Inner BuildRight (178)
                                 :              :     :     :- * Project (176)
                                 :              :     :     :  +- * BroadcastHashJoin Inner BuildRight (175)
                                 :              :     :     :     :- * Project (173)
                                 :              :     :     :     :  +- * SortMergeJoin LeftOuter (172)
                                 :              :     :     :     :     :- * Sort (169)
                                 :              :     :     :     :     :  +- Exchange (168)
                                 :              :     :     :     :     :     +- * Filter (167)
                                 :              :     :     :     :     :        +- * ColumnarToRow (166)
                                 :              :     :     :     :     :           +- Scan parquet spark_catalog.default.catalog_sales (165)
                                 :              :     :     :     :     +- * Sort (171)
                                 :              :     :     :     :        +- ReusedExchange (170)
                                 :              :     :     :     +- ReusedExchange (174)
                                 :              :     :     +- ReusedExchange (177)
                                 :              :     +- ReusedExchange (180)
                                 :              +- ReusedExchange (183)
                                 +- * HashAggregate (190)
                                    +- ReusedExchange (189)


(1) Scan parquet spark_catalog.default.store_sales
Output [7]: [ss_item_sk#1, ss_store_sk#2, ss_promo_sk#3, ss_ticket_number#4, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ss_sold_date_sk#7), dynamicpruningexpression(ss_sold_date_sk#7 IN dynamicpruning#8)]
PushedFilters: [IsNotNull(ss_store_sk), IsNotNull(ss_item_sk), IsNotNull(ss_promo_sk)]
ReadSchema: struct<ss_item_sk:int,ss_store_sk:int,ss_promo_sk:int,ss_ticket_number:int,ss_ext_sales_price:decimal(7,2),ss_net_profit:decimal(7,2)>

(2) ColumnarToRow [codegen id : 1]
Input [7]: [ss_item_sk#1, ss_store_sk#2, ss_promo_sk#3, ss_ticket_number#4, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7]

(3) Filter [codegen id : 1]
Input [7]: [ss_item_sk#1, ss_store_sk#2, ss_promo_sk#3, ss_ticket_number#4, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7]
Condition : ((((isnotnull(ss_store_sk#2) AND isnotnull(ss_item_sk#1)) AND isnotnull(ss_promo_sk#3)) AND might_contain(Subquery scalar-subquery#9, [id=#10], xxhash64(ss_item_sk#1, 42))) AND might_contain(Subquery scalar-subquery#11, [id=#12], xxhash64(ss_promo_sk#3, 42)))

(4) Exchange
Input [7]: [ss_item_sk#1, ss_store_sk#2, ss_promo_sk#3, ss_ticket_number#4, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7]
Arguments: hashpartitioning(ss_item_sk#1, ss_ticket_number#4, 5), ENSURE_REQUIREMENTS, [plan_id=1]

(5) Sort [codegen id : 2]
Input [7]: [ss_item_sk#1, ss_store_sk#2, ss_promo_sk#3, ss_ticket_number#4, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7]
Arguments: [ss_item_sk#1 ASC NULLS FIRST, ss_ticket_number#4 ASC NULLS FIRST], false, 0

(6) Scan parquet spark_catalog.default.store_returns
Output [5]: [sr_item_sk#13, sr_ticket_number#14, sr_return_amt#15, sr_net_loss#16, sr_returned_date_sk#17]
Batched: true
Location [not included in comparison]/{warehouse_dir}/store_returns]
PushedFilters: [IsNotNull(sr_item_sk), IsNotNull(sr_ticket_number)]
ReadSchema: struct<sr_item_sk:int,sr_ticket_number:int,sr_return_amt:decimal(7,2),sr_net_loss:decimal(7,2)>

(7) ColumnarToRow [codegen id : 3]
Input [5]: [sr_item_sk#13, sr_ticket_number#14, sr_return_amt#15, sr_net_loss#16, sr_returned_date_sk#17]

(8) Filter [codegen id : 3]
Input [5]: [sr_item_sk#13, sr_ticket_number#14, sr_return_amt#15, sr_net_loss#16, sr_returned_date_sk#17]
Condition : (isnotnull(sr_item_sk#13) AND isnotnull(sr_ticket_number#14))

(9) Project [codegen id : 3]
Output [4]: [sr_item_sk#13, sr_ticket_number#14, sr_return_amt#15, sr_net_loss#16]
Input [5]: [sr_item_sk#13, sr_ticket_number#14, sr_return_amt#15, sr_net_loss#16, sr_returned_date_sk#17]

(10) Exchange
Input [4]: [sr_item_sk#13, sr_ticket_number#14, sr_return_amt#15, sr_net_loss#16]
Arguments: hashpartitioning(sr_item_sk#13, sr_ticket_number#14, 5), ENSURE_REQUIREMENTS, [plan_id=2]

(11) Sort [codegen id : 4]
Input [4]: [sr_item_sk#13, sr_ticket_number#14, sr_return_amt#15, sr_net_loss#16]
Arguments: [sr_item_sk#13 ASC NULLS FIRST, sr_ticket_number#14 ASC NULLS FIRST], false, 0

(12) SortMergeJoin [codegen id : 9]
Left keys [2]: [ss_item_sk#1, ss_ticket_number#4]
Right keys [2]: [sr_item_sk#13, sr_ticket_number#14]
Join type: LeftOuter
Join condition: None

(13) Project [codegen id : 9]
Output [8]: [ss_item_sk#1, ss_store_sk#2, ss_promo_sk#3, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7, sr_return_amt#15, sr_net_loss#16]
Input [11]: [ss_item_sk#1, ss_store_sk#2, ss_promo_sk#3, ss_ticket_number#4, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7, sr_item_sk#13, sr_ticket_number#14, sr_return_amt#15, sr_net_loss#16]

(14) Scan parquet spark_catalog.default.item
Output [2]: [i_item_sk#18, i_current_price#19]
Batched: true
Location [not included in comparison]/{warehouse_dir}/item]
PushedFilters: [IsNotNull(i_current_price), GreaterThan(i_current_price,50.00), IsNotNull(i_item_sk)]
ReadSchema: struct<i_item_sk:int,i_current_price:decimal(7,2)>

(15) ColumnarToRow [codegen id : 5]
Input [2]: [i_item_sk#18, i_current_price#19]

(16) Filter [codegen id : 5]
Input [2]: [i_item_sk#18, i_current_price#19]
Condition : ((isnotnull(i_current_price#19) AND (i_current_price#19 > 50.00)) AND isnotnull(i_item_sk#18))

(17) Project [codegen id : 5]
Output [1]: [i_item_sk#18]
Input [2]: [i_item_sk#18, i_current_price#19]

(18) BroadcastExchange
Input [1]: [i_item_sk#18]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=3]

(19) BroadcastHashJoin [codegen id : 9]
Left keys [1]: [ss_item_sk#1]
Right keys [1]: [i_item_sk#18]
Join type: Inner
Join condition: None

(20) Project [codegen id : 9]
Output [7]: [ss_store_sk#2, ss_promo_sk#3, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7, sr_return_amt#15, sr_net_loss#16]
Input [9]: [ss_item_sk#1, ss_store_sk#2, ss_promo_sk#3, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7, sr_return_amt#15, sr_net_loss#16, i_item_sk#18]

(21) Scan parquet spark_catalog.default.promotion
Output [2]: [p_promo_sk#20, p_channel_tv#21]
Batched: true
Location [not included in comparison]/{warehouse_dir}/promotion]
PushedFilters: [IsNotNull(p_channel_tv), EqualTo(p_channel_tv,N), IsNotNull(p_promo_sk)]
ReadSchema: struct<p_promo_sk:int,p_channel_tv:string>

(22) ColumnarToRow [codegen id : 6]
Input [2]: [p_promo_sk#20, p_channel_tv#21]

(23) Filter [codegen id : 6]
Input [2]: [p_promo_sk#20, p_channel_tv#21]
Condition : ((isnotnull(p_channel_tv#21) AND (p_channel_tv#21 = N)) AND isnotnull(p_promo_sk#20))

(24) Project [codegen id : 6]
Output [1]: [p_promo_sk#20]
Input [2]: [p_promo_sk#20, p_channel_tv#21]

(25) BroadcastExchange
Input [1]: [p_promo_sk#20]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=4]

(26) BroadcastHashJoin [codegen id : 9]
Left keys [1]: [ss_promo_sk#3]
Right keys [1]: [p_promo_sk#20]
Join type: Inner
Join condition: None

(27) Project [codegen id : 9]
Output [6]: [ss_store_sk#2, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7, sr_return_amt#15, sr_net_loss#16]
Input [8]: [ss_store_sk#2, ss_promo_sk#3, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7, sr_return_amt#15, sr_net_loss#16, p_promo_sk#20]

(28) ReusedExchange [Reuses operator id: 221]
Output [1]: [d_date_sk#22]

(29) BroadcastHashJoin [codegen id : 9]
Left keys [1]: [ss_sold_date_sk#7]
Right keys [1]: [d_date_sk#22]
Join type: Inner
Join condition: None

(30) Project [codegen id : 9]
Output [5]: [ss_store_sk#2, ss_ext_sales_price#5, ss_net_profit#6, sr_return_amt#15, sr_net_loss#16]
Input [7]: [ss_store_sk#2, ss_ext_sales_price#5, ss_net_profit#6, ss_sold_date_sk#7, sr_return_amt#15, sr_net_loss#16, d_date_sk#22]

(31) Scan parquet spark_catalog.default.store
Output [2]: [s_store_sk#23, s_store_id#24]
Batched: true
Location [not included in comparison]/{warehouse_dir}/store]
PushedFilters: [IsNotNull(s_store_sk)]
ReadSchema: struct<s_store_sk:int,s_store_id:string>

(32) ColumnarToRow [codegen id : 8]
Input [2]: [s_store_sk#23, s_store_id#24]

(33) Filter [codegen id : 8]
Input [2]: [s_store_sk#23, s_store_id#24]
Condition : isnotnull(s_store_sk#23)

(34) BroadcastExchange
Input [2]: [s_store_sk#23, s_store_id#24]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, false] as bigint)),false), [plan_id=5]

(35) BroadcastHashJoin [codegen id : 9]
Left keys [1]: [ss_store_sk#2]
Right keys [1]: [s_store_sk#23]
Join type: Inner
Join condition: None

(36) Project [codegen id : 9]
Output [5]: [ss_ext_sales_price#5, ss_net_profit#6, sr_return_amt#15, sr_net_loss#16, s_store_id#24]
Input [7]: [ss_store_sk#2, ss_ext_sales_price#5, ss_net_profit#6, sr_return_amt#15, sr_net_loss#16, s_store_sk#23, s_store_id#24]

(37) HashAggregate [codegen id : 9]
Input [5]: [ss_ext_sales_price#5, ss_net_profit#6, sr_return_amt#15, sr_net_loss#16, s_store_id#24]
Keys [1]: [s_store_id#24]
Functions [3]: [partial_sum(UnscaledValue(ss_ext_sales_price#5)), partial_sum(coalesce(cast(sr_return_amt#15 as decimal(12,2)), 0.00)), partial_sum((ss_net_profit#6 - coalesce(cast(sr_net_loss#16 as decimal(12,2)), 0.00)))]
Aggregate Attributes [5]: [sum#25, sum#26, isEmpty#27, sum#28, isEmpty#29]
Results [6]: [s_store_id#24, sum#30, sum#31, isEmpty#32, sum#33, isEmpty#34]

(38) Exchange
Input [6]: [s_store_id#24, sum#30, sum#31, isEmpty#32, sum#33, isEmpty#34]
Arguments: hashpartitioning(s_store_id#24, 5), ENSURE_REQUIREMENTS, [plan_id=6]

(39) HashAggregate [codegen id : 10]
Input [6]: [s_store_id#24, sum#30, sum#31, isEmpty#32, sum#33, isEmpty#34]
Keys [1]: [s_store_id#24]
Functions [3]: [sum(UnscaledValue(ss_ext_sales_price#5)), sum(coalesce(cast(sr_return_amt#15 as decimal(12,2)), 0.00)), sum((ss_net_profit#6 - coalesce(cast(sr_net_loss#16 as decimal(12,2)), 0.00)))]
Aggregate Attributes [3]: [sum(UnscaledValue(ss_ext_sales_price#5))#35, sum(coalesce(cast(sr_return_amt#15 as decimal(12,2)), 0.00))#36, sum((ss_net_profit#6 - coalesce(cast(sr_net_loss#16 as decimal(12,2)), 0.00)))#37]
Results [5]: [store channel AS channel#38, concat(store, s_store_id#24) AS id#39, MakeDecimal(sum(UnscaledValue(ss_ext_sales_price#5))#35,17,2) AS sales#40, sum(coalesce(cast(sr_return_amt#15 as decimal(12,2)), 0.00))#36 AS returns#41, sum((ss_net_profit#6 - coalesce(cast(sr_net_loss#16 as decimal(12,2)), 0.00)))#37 AS profit#42]

(40) Scan parquet spark_catalog.default.catalog_sales
Output [7]: [cs_catalog_page_sk#43, cs_item_sk#44, cs_promo_sk#45, cs_order_number#46, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(cs_sold_date_sk#49), dynamicpruningexpression(cs_sold_date_sk#49 IN dynamicpruning#8)]
PushedFilters: [IsNotNull(cs_catalog_page_sk), IsNotNull(cs_item_sk), IsNotNull(cs_promo_sk)]
ReadSchema: struct<cs_catalog_page_sk:int,cs_item_sk:int,cs_promo_sk:int,cs_order_number:int,cs_ext_sales_price:decimal(7,2),cs_net_profit:decimal(7,2)>

(41) ColumnarToRow [codegen id : 11]
Input [7]: [cs_catalog_page_sk#43, cs_item_sk#44, cs_promo_sk#45, cs_order_number#46, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49]

(42) Filter [codegen id : 11]
Input [7]: [cs_catalog_page_sk#43, cs_item_sk#44, cs_promo_sk#45, cs_order_number#46, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49]
Condition : ((((isnotnull(cs_catalog_page_sk#43) AND isnotnull(cs_item_sk#44)) AND isnotnull(cs_promo_sk#45)) AND might_contain(ReusedSubquery Subquery scalar-subquery#9, [id=#10], xxhash64(cs_item_sk#44, 42))) AND might_contain(ReusedSubquery Subquery scalar-subquery#11, [id=#12], xxhash64(cs_promo_sk#45, 42)))

(43) Exchange
Input [7]: [cs_catalog_page_sk#43, cs_item_sk#44, cs_promo_sk#45, cs_order_number#46, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49]
Arguments: hashpartitioning(cs_item_sk#44, cs_order_number#46, 5), ENSURE_REQUIREMENTS, [plan_id=7]

(44) Sort [codegen id : 12]
Input [7]: [cs_catalog_page_sk#43, cs_item_sk#44, cs_promo_sk#45, cs_order_number#46, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49]
Arguments: [cs_item_sk#44 ASC NULLS FIRST, cs_order_number#46 ASC NULLS FIRST], false, 0

(45) Scan parquet spark_catalog.default.catalog_returns
Output [5]: [cr_item_sk#50, cr_order_number#51, cr_return_amount#52, cr_net_loss#53, cr_returned_date_sk#54]
Batched: true
Location [not included in comparison]/{warehouse_dir}/catalog_returns]
PushedFilters: [IsNotNull(cr_item_sk), IsNotNull(cr_order_number)]
ReadSchema: struct<cr_item_sk:int,cr_order_number:int,cr_return_amount:decimal(7,2),cr_net_loss:decimal(7,2)>

(46) ColumnarToRow [codegen id : 13]
Input [5]: [cr_item_sk#50, cr_order_number#51, cr_return_amount#52, cr_net_loss#53, cr_returned_date_sk#54]

(47) Filter [codegen id : 13]
Input [5]: [cr_item_sk#50, cr_order_number#51, cr_return_amount#52, cr_net_loss#53, cr_returned_date_sk#54]
Condition : (isnotnull(cr_item_sk#50) AND isnotnull(cr_order_number#51))

(48) Project [codegen id : 13]
Output [4]: [cr_item_sk#50, cr_order_number#51, cr_return_amount#52, cr_net_loss#53]
Input [5]: [cr_item_sk#50, cr_order_number#51, cr_return_amount#52, cr_net_loss#53, cr_returned_date_sk#54]

(49) Exchange
Input [4]: [cr_item_sk#50, cr_order_number#51, cr_return_amount#52, cr_net_loss#53]
Arguments: hashpartitioning(cr_item_sk#50, cr_order_number#51, 5), ENSURE_REQUIREMENTS, [plan_id=8]

(50) Sort [codegen id : 14]
Input [4]: [cr_item_sk#50, cr_order_number#51, cr_return_amount#52, cr_net_loss#53]
Arguments: [cr_item_sk#50 ASC NULLS FIRST, cr_order_number#51 ASC NULLS FIRST], false, 0

(51) SortMergeJoin [codegen id : 19]
Left keys [2]: [cs_item_sk#44, cs_order_number#46]
Right keys [2]: [cr_item_sk#50, cr_order_number#51]
Join type: LeftOuter
Join condition: None

(52) Project [codegen id : 19]
Output [8]: [cs_catalog_page_sk#43, cs_item_sk#44, cs_promo_sk#45, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49, cr_return_amount#52, cr_net_loss#53]
Input [11]: [cs_catalog_page_sk#43, cs_item_sk#44, cs_promo_sk#45, cs_order_number#46, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49, cr_item_sk#50, cr_order_number#51, cr_return_amount#52, cr_net_loss#53]

(53) ReusedExchange [Reuses operator id: 18]
Output [1]: [i_item_sk#55]

(54) BroadcastHashJoin [codegen id : 19]
Left keys [1]: [cs_item_sk#44]
Right keys [1]: [i_item_sk#55]
Join type: Inner
Join condition: None

(55) Project [codegen id : 19]
Output [7]: [cs_catalog_page_sk#43, cs_promo_sk#45, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49, cr_return_amount#52, cr_net_loss#53]
Input [9]: [cs_catalog_page_sk#43, cs_item_sk#44, cs_promo_sk#45, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49, cr_return_amount#52, cr_net_loss#53, i_item_sk#55]

(56) ReusedExchange [Reuses operator id: 25]
Output [1]: [p_promo_sk#56]

(57) BroadcastHashJoin [codegen id : 19]
Left keys [1]: [cs_promo_sk#45]
Right keys [1]: [p_promo_sk#56]
Join type: Inner
Join condition: None

(58) Project [codegen id : 19]
Output [6]: [cs_catalog_page_sk#43, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49, cr_return_amount#52, cr_net_loss#53]
Input [8]: [cs_catalog_page_sk#43, cs_promo_sk#45, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49, cr_return_amount#52, cr_net_loss#53, p_promo_sk#56]

(59) ReusedExchange [Reuses operator id: 221]
Output [1]: [d_date_sk#57]

(60) BroadcastHashJoin [codegen id : 19]
Left keys [1]: [cs_sold_date_sk#49]
Right keys [1]: [d_date_sk#57]
Join type: Inner
Join condition: None

(61) Project [codegen id : 19]
Output [5]: [cs_catalog_page_sk#43, cs_ext_sales_price#47, cs_net_profit#48, cr_return_amount#52, cr_net_loss#53]
Input [7]: [cs_catalog_page_sk#43, cs_ext_sales_price#47, cs_net_profit#48, cs_sold_date_sk#49, cr_return_amount#52, cr_net_loss#53, d_date_sk#57]

(62) Scan parquet spark_catalog.default.catalog_page
Output [2]: [cp_catalog_page_sk#58, cp_catalog_page_id#59]
Batched: true
Location [not included in comparison]/{warehouse_dir}/catalog_page]
PushedFilters: [IsNotNull(cp_catalog_page_sk)]
ReadSchema: struct<cp_catalog_page_sk:int,cp_catalog_page_id:string>

(63) ColumnarToRow [codegen id : 18]
Input [2]: [cp_catalog_page_sk#58, cp_catalog_page_id#59]

(64) Filter [codegen id : 18]
Input [2]: [cp_catalog_page_sk#58, cp_catalog_page_id#59]
Condition : isnotnull(cp_catalog_page_sk#58)

(65) BroadcastExchange
Input [2]: [cp_catalog_page_sk#58, cp_catalog_page_id#59]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, false] as bigint)),false), [plan_id=9]

(66) BroadcastHashJoin [codegen id : 19]
Left keys [1]: [cs_catalog_page_sk#43]
Right keys [1]: [cp_catalog_page_sk#58]
Join type: Inner
Join condition: None

(67) Project [codegen id : 19]
Output [5]: [cs_ext_sales_price#47, cs_net_profit#48, cr_return_amount#52, cr_net_loss#53, cp_catalog_page_id#59]
Input [7]: [cs_catalog_page_sk#43, cs_ext_sales_price#47, cs_net_profit#48, cr_return_amount#52, cr_net_loss#53, cp_catalog_page_sk#58, cp_catalog_page_id#59]

(68) HashAggregate [codegen id : 19]
Input [5]: [cs_ext_sales_price#47, cs_net_profit#48, cr_return_amount#52, cr_net_loss#53, cp_catalog_page_id#59]
Keys [1]: [cp_catalog_page_id#59]
Functions [3]: [partial_sum(UnscaledValue(cs_ext_sales_price#47)), partial_sum(coalesce(cast(cr_return_amount#52 as decimal(12,2)), 0.00)), partial_sum((cs_net_profit#48 - coalesce(cast(cr_net_loss#53 as decimal(12,2)), 0.00)))]
Aggregate Attributes [5]: [sum#60, sum#61, isEmpty#62, sum#63, isEmpty#64]
Results [6]: [cp_catalog_page_id#59, sum#65, sum#66, isEmpty#67, sum#68, isEmpty#69]

(69) Exchange
Input [6]: [cp_catalog_page_id#59, sum#65, sum#66, isEmpty#67, sum#68, isEmpty#69]
Arguments: hashpartitioning(cp_catalog_page_id#59, 5), ENSURE_REQUIREMENTS, [plan_id=10]

(70) HashAggregate [codegen id : 20]
Input [6]: [cp_catalog_page_id#59, sum#65, sum#66, isEmpty#67, sum#68, isEmpty#69]
Keys [1]: [cp_catalog_page_id#59]
Functions [3]: [sum(UnscaledValue(cs_ext_sales_price#47)), sum(coalesce(cast(cr_return_amount#52 as decimal(12,2)), 0.00)), sum((cs_net_profit#48 - coalesce(cast(cr_net_loss#53 as decimal(12,2)), 0.00)))]
Aggregate Attributes [3]: [sum(UnscaledValue(cs_ext_sales_price#47))#70, sum(coalesce(cast(cr_return_amount#52 as decimal(12,2)), 0.00))#71, sum((cs_net_profit#48 - coalesce(cast(cr_net_loss#53 as decimal(12,2)), 0.00)))#72]
Results [5]: [catalog channel AS channel#73, concat(catalog_page, cp_catalog_page_id#59) AS id#74, MakeDecimal(sum(UnscaledValue(cs_ext_sales_price#47))#70,17,2) AS sales#75, sum(coalesce(cast(cr_return_amount#52 as decimal(12,2)), 0.00))#71 AS returns#76, sum((cs_net_profit#48 - coalesce(cast(cr_net_loss#53 as decimal(12,2)), 0.00)))#72 AS profit#77]

(71) Scan parquet spark_catalog.default.web_sales
Output [7]: [ws_item_sk#78, ws_web_site_sk#79, ws_promo_sk#80, ws_order_number#81, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ws_sold_date_sk#84), dynamicpruningexpression(ws_sold_date_sk#84 IN dynamicpruning#8)]
PushedFilters: [IsNotNull(ws_web_site_sk), IsNotNull(ws_item_sk), IsNotNull(ws_promo_sk)]
ReadSchema: struct<ws_item_sk:int,ws_web_site_sk:int,ws_promo_sk:int,ws_order_number:int,ws_ext_sales_price:decimal(7,2),ws_net_profit:decimal(7,2)>

(72) ColumnarToRow [codegen id : 21]
Input [7]: [ws_item_sk#78, ws_web_site_sk#79, ws_promo_sk#80, ws_order_number#81, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84]

(73) Filter [codegen id : 21]
Input [7]: [ws_item_sk#78, ws_web_site_sk#79, ws_promo_sk#80, ws_order_number#81, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84]
Condition : ((((isnotnull(ws_web_site_sk#79) AND isnotnull(ws_item_sk#78)) AND isnotnull(ws_promo_sk#80)) AND might_contain(ReusedSubquery Subquery scalar-subquery#9, [id=#10], xxhash64(ws_item_sk#78, 42))) AND might_contain(ReusedSubquery Subquery scalar-subquery#11, [id=#12], xxhash64(ws_promo_sk#80, 42)))

(74) Exchange
Input [7]: [ws_item_sk#78, ws_web_site_sk#79, ws_promo_sk#80, ws_order_number#81, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84]
Arguments: hashpartitioning(ws_item_sk#78, ws_order_number#81, 5), ENSURE_REQUIREMENTS, [plan_id=11]

(75) Sort [codegen id : 22]
Input [7]: [ws_item_sk#78, ws_web_site_sk#79, ws_promo_sk#80, ws_order_number#81, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84]
Arguments: [ws_item_sk#78 ASC NULLS FIRST, ws_order_number#81 ASC NULLS FIRST], false, 0

(76) Scan parquet spark_catalog.default.web_returns
Output [5]: [wr_item_sk#85, wr_order_number#86, wr_return_amt#87, wr_net_loss#88, wr_returned_date_sk#89]
Batched: true
Location [not included in comparison]/{warehouse_dir}/web_returns]
PushedFilters: [IsNotNull(wr_item_sk), IsNotNull(wr_order_number)]
ReadSchema: struct<wr_item_sk:int,wr_order_number:int,wr_return_amt:decimal(7,2),wr_net_loss:decimal(7,2)>

(77) ColumnarToRow [codegen id : 23]
Input [5]: [wr_item_sk#85, wr_order_number#86, wr_return_amt#87, wr_net_loss#88, wr_returned_date_sk#89]

(78) Filter [codegen id : 23]
Input [5]: [wr_item_sk#85, wr_order_number#86, wr_return_amt#87, wr_net_loss#88, wr_returned_date_sk#89]
Condition : (isnotnull(wr_item_sk#85) AND isnotnull(wr_order_number#86))

(79) Project [codegen id : 23]
Output [4]: [wr_item_sk#85, wr_order_number#86, wr_return_amt#87, wr_net_loss#88]
Input [5]: [wr_item_sk#85, wr_order_number#86, wr_return_amt#87, wr_net_loss#88, wr_returned_date_sk#89]

(80) Exchange
Input [4]: [wr_item_sk#85, wr_order_number#86, wr_return_amt#87, wr_net_loss#88]
Arguments: hashpartitioning(wr_item_sk#85, wr_order_number#86, 5), ENSURE_REQUIREMENTS, [plan_id=12]

(81) Sort [codegen id : 24]
Input [4]: [wr_item_sk#85, wr_order_number#86, wr_return_amt#87, wr_net_loss#88]
Arguments: [wr_item_sk#85 ASC NULLS FIRST, wr_order_number#86 ASC NULLS FIRST], false, 0

(82) SortMergeJoin [codegen id : 29]
Left keys [2]: [ws_item_sk#78, ws_order_number#81]
Right keys [2]: [wr_item_sk#85, wr_order_number#86]
Join type: LeftOuter
Join condition: None

(83) Project [codegen id : 29]
Output [8]: [ws_item_sk#78, ws_web_site_sk#79, ws_promo_sk#80, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84, wr_return_amt#87, wr_net_loss#88]
Input [11]: [ws_item_sk#78, ws_web_site_sk#79, ws_promo_sk#80, ws_order_number#81, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84, wr_item_sk#85, wr_order_number#86, wr_return_amt#87, wr_net_loss#88]

(84) ReusedExchange [Reuses operator id: 18]
Output [1]: [i_item_sk#90]

(85) BroadcastHashJoin [codegen id : 29]
Left keys [1]: [ws_item_sk#78]
Right keys [1]: [i_item_sk#90]
Join type: Inner
Join condition: None

(86) Project [codegen id : 29]
Output [7]: [ws_web_site_sk#79, ws_promo_sk#80, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84, wr_return_amt#87, wr_net_loss#88]
Input [9]: [ws_item_sk#78, ws_web_site_sk#79, ws_promo_sk#80, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84, wr_return_amt#87, wr_net_loss#88, i_item_sk#90]

(87) ReusedExchange [Reuses operator id: 25]
Output [1]: [p_promo_sk#91]

(88) BroadcastHashJoin [codegen id : 29]
Left keys [1]: [ws_promo_sk#80]
Right keys [1]: [p_promo_sk#91]
Join type: Inner
Join condition: None

(89) Project [codegen id : 29]
Output [6]: [ws_web_site_sk#79, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84, wr_return_amt#87, wr_net_loss#88]
Input [8]: [ws_web_site_sk#79, ws_promo_sk#80, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84, wr_return_amt#87, wr_net_loss#88, p_promo_sk#91]

(90) ReusedExchange [Reuses operator id: 221]
Output [1]: [d_date_sk#92]

(91) BroadcastHashJoin [codegen id : 29]
Left keys [1]: [ws_sold_date_sk#84]
Right keys [1]: [d_date_sk#92]
Join type: Inner
Join condition: None

(92) Project [codegen id : 29]
Output [5]: [ws_web_site_sk#79, ws_ext_sales_price#82, ws_net_profit#83, wr_return_amt#87, wr_net_loss#88]
Input [7]: [ws_web_site_sk#79, ws_ext_sales_price#82, ws_net_profit#83, ws_sold_date_sk#84, wr_return_amt#87, wr_net_loss#88, d_date_sk#92]

(93) Scan parquet spark_catalog.default.web_site
Output [2]: [web_site_sk#93, web_site_id#94]
Batched: true
Location [not included in comparison]/{warehouse_dir}/web_site]
PushedFilters: [IsNotNull(web_site_sk)]
ReadSchema: struct<web_site_sk:int,web_site_id:string>

(94) ColumnarToRow [codegen id : 28]
Input [2]: [web_site_sk#93, web_site_id#94]

(95) Filter [codegen id : 28]
Input [2]: [web_site_sk#93, web_site_id#94]
Condition : isnotnull(web_site_sk#93)

(96) BroadcastExchange
Input [2]: [web_site_sk#93, web_site_id#94]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, false] as bigint)),false), [plan_id=13]

(97) BroadcastHashJoin [codegen id : 29]
Left keys [1]: [ws_web_site_sk#79]
Right keys [1]: [web_site_sk#93]
Join type: Inner
Join condition: None

(98) Project [codegen id : 29]
Output [5]: [ws_ext_sales_price#82, ws_net_profit#83, wr_return_amt#87, wr_net_loss#88, web_site_id#94]
Input [7]: [ws_web_site_sk#79, ws_ext_sales_price#82, ws_net_profit#83, wr_return_amt#87, wr_net_loss#88, web_site_sk#93, web_site_id#94]

(99) HashAggregate [codegen id : 29]
Input [5]: [ws_ext_sales_price#82, ws_net_profit#83, wr_return_amt#87, wr_net_loss#88, web_site_id#94]
Keys [1]: [web_site_id#94]
Functions [3]: [partial_sum(UnscaledValue(ws_ext_sales_price#82)), partial_sum(coalesce(cast(wr_return_amt#87 as decimal(12,2)), 0.00)), partial_sum((ws_net_profit#83 - coalesce(cast(wr_net_loss#88 as decimal(12,2)), 0.00)))]
Aggregate Attributes [5]: [sum#95, sum#96, isEmpty#97, sum#98, isEmpty#99]
Results [6]: [web_site_id#94, sum#100, sum#101, isEmpty#102, sum#103, isEmpty#104]

(100) Exchange
Input [6]: [web_site_id#94, sum#100, sum#101, isEmpty#102, sum#103, isEmpty#104]
Arguments: hashpartitioning(web_site_id#94, 5), ENSURE_REQUIREMENTS, [plan_id=14]

(101) HashAggregate [codegen id : 30]
Input [6]: [web_site_id#94, sum#100, sum#101, isEmpty#102, sum#103, isEmpty#104]
Keys [1]: [web_site_id#94]
Functions [3]: [sum(UnscaledValue(ws_ext_sales_price#82)), sum(coalesce(cast(wr_return_amt#87 as decimal(12,2)), 0.00)), sum((ws_net_profit#83 - coalesce(cast(wr_net_loss#88 as decimal(12,2)), 0.00)))]
Aggregate Attributes [3]: [sum(UnscaledValue(ws_ext_sales_price#82))#105, sum(coalesce(cast(wr_return_amt#87 as decimal(12,2)), 0.00))#106, sum((ws_net_profit#83 - coalesce(cast(wr_net_loss#88 as decimal(12,2)), 0.00)))#107]
Results [5]: [web channel AS channel#108, concat(web_site, web_site_id#94) AS id#109, MakeDecimal(sum(UnscaledValue(ws_ext_sales_price#82))#105,17,2) AS sales#110, sum(coalesce(cast(wr_return_amt#87 as decimal(12,2)), 0.00))#106 AS returns#111, sum((ws_net_profit#83 - coalesce(cast(wr_net_loss#88 as decimal(12,2)), 0.00)))#107 AS profit#112]

(102) Union

(103) HashAggregate [codegen id : 31]
Input [5]: [channel#38, id#39, sales#40, returns#41, profit#42]
Keys [2]: [channel#38, id#39]
Functions [3]: [partial_sum(sales#40), partial_sum(returns#41), partial_sum(profit#42)]
Aggregate Attributes [6]: [sum#113, isEmpty#114, sum#115, isEmpty#116, sum#117, isEmpty#118]
Results [8]: [channel#38, id#39, sum#119, isEmpty#120, sum#121, isEmpty#122, sum#123, isEmpty#124]

(104) Exchange
Input [8]: [channel#38, id#39, sum#119, isEmpty#120, sum#121, isEmpty#122, sum#123, isEmpty#124]
Arguments: hashpartitioning(channel#38, id#39, 5), ENSURE_REQUIREMENTS, [plan_id=15]

(105) HashAggregate [codegen id : 32]
Input [8]: [channel#38, id#39, sum#119, isEmpty#120, sum#121, isEmpty#122, sum#123, isEmpty#124]
Keys [2]: [channel#38, id#39]
Functions [3]: [sum(sales#40), sum(returns#41), sum(profit#42)]
Aggregate Attributes [3]: [sum(sales#40)#125, sum(returns#41)#126, sum(profit#42)#127]
Results [5]: [channel#38, id#39, cast(sum(sales#40)#125 as decimal(37,2)) AS sales#128, cast(sum(returns#41)#126 as decimal(38,2)) AS returns#129, cast(sum(profit#42)#127 as decimal(38,2)) AS profit#130]

(106) ReusedExchange [Reuses operator id: 38]
Output [6]: [s_store_id#131, sum#132, sum#133, isEmpty#134, sum#135, isEmpty#136]

(107) HashAggregate [codegen id : 42]
Input [6]: [s_store_id#131, sum#132, sum#133, isEmpty#134, sum#135, isEmpty#136]
Keys [1]: [s_store_id#131]
Functions [3]: [sum(UnscaledValue(ss_ext_sales_price#137)), sum(coalesce(cast(sr_return_amt#138 as decimal(12,2)), 0.00)), sum((ss_net_profit#139 - coalesce(cast(sr_net_loss#140 as decimal(12,2)), 0.00)))]
Aggregate Attributes [3]: [sum(UnscaledValue(ss_ext_sales_price#137))#35, sum(coalesce(cast(sr_return_amt#138 as decimal(12,2)), 0.00))#36, sum((ss_net_profit#139 - coalesce(cast(sr_net_loss#140 as decimal(12,2)), 0.00)))#37]
Results [5]: [store channel AS channel#38, concat(store, s_store_id#131) AS id#39, MakeDecimal(sum(UnscaledValue(ss_ext_sales_price#137))#35,17,2) AS sales#40, sum(coalesce(cast(sr_return_amt#138 as decimal(12,2)), 0.00))#36 AS returns#41, sum((ss_net_profit#139 - coalesce(cast(sr_net_loss#140 as decimal(12,2)), 0.00)))#37 AS profit#42]

(108) ReusedExchange [Reuses operator id: 69]
Output [6]: [cp_catalog_page_id#141, sum#142, sum#143, isEmpty#144, sum#145, isEmpty#146]

(109) HashAggregate [codegen id : 52]
Input [6]: [cp_catalog_page_id#141, sum#142, sum#143, isEmpty#144, sum#145, isEmpty#146]
Keys [1]: [cp_catalog_page_id#141]
Functions [3]: [sum(UnscaledValue(cs_ext_sales_price#147)), sum(coalesce(cast(cr_return_amount#148 as decimal(12,2)), 0.00)), sum((cs_net_profit#149 - coalesce(cast(cr_net_loss#150 as decimal(12,2)), 0.00)))]
Aggregate Attributes [3]: [sum(UnscaledValue(cs_ext_sales_price#147))#70, sum(coalesce(cast(cr_return_amount#148 as decimal(12,2)), 0.00))#71, sum((cs_net_profit#149 - coalesce(cast(cr_net_loss#150 as decimal(12,2)), 0.00)))#72]
Results [5]: [catalog channel AS channel#73, concat(catalog_page, cp_catalog_page_id#141) AS id#74, MakeDecimal(sum(UnscaledValue(cs_ext_sales_price#147))#70,17,2) AS sales#75, sum(coalesce(cast(cr_return_amount#148 as decimal(12,2)), 0.00))#71 AS returns#76, sum((cs_net_profit#149 - coalesce(cast(cr_net_loss#150 as decimal(12,2)), 0.00)))#72 AS profit#77]

(110) Scan parquet spark_catalog.default.web_sales
Output [7]: [ws_item_sk#151, ws_web_site_sk#152, ws_promo_sk#153, ws_order_number#154, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ws_sold_date_sk#157), dynamicpruningexpression(ws_sold_date_sk#157 IN dynamicpruning#8)]
PushedFilters: [IsNotNull(ws_web_site_sk), IsNotNull(ws_item_sk), IsNotNull(ws_promo_sk)]
ReadSchema: struct<ws_item_sk:int,ws_web_site_sk:int,ws_promo_sk:int,ws_order_number:int,ws_ext_sales_price:decimal(7,2),ws_net_profit:decimal(7,2)>

(111) ColumnarToRow [codegen id : 53]
Input [7]: [ws_item_sk#151, ws_web_site_sk#152, ws_promo_sk#153, ws_order_number#154, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157]

(112) Filter [codegen id : 53]
Input [7]: [ws_item_sk#151, ws_web_site_sk#152, ws_promo_sk#153, ws_order_number#154, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157]
Condition : ((isnotnull(ws_web_site_sk#152) AND isnotnull(ws_item_sk#151)) AND isnotnull(ws_promo_sk#153))

(113) Exchange
Input [7]: [ws_item_sk#151, ws_web_site_sk#152, ws_promo_sk#153, ws_order_number#154, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157]
Arguments: hashpartitioning(ws_item_sk#151, ws_order_number#154, 5), ENSURE_REQUIREMENTS, [plan_id=16]

(114) Sort [codegen id : 54]
Input [7]: [ws_item_sk#151, ws_web_site_sk#152, ws_promo_sk#153, ws_order_number#154, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157]
Arguments: [ws_item_sk#151 ASC NULLS FIRST, ws_order_number#154 ASC NULLS FIRST], false, 0

(115) ReusedExchange [Reuses operator id: 80]
Output [4]: [wr_item_sk#158, wr_order_number#159, wr_return_amt#160, wr_net_loss#161]

(116) Sort [codegen id : 56]
Input [4]: [wr_item_sk#158, wr_order_number#159, wr_return_amt#160, wr_net_loss#161]
Arguments: [wr_item_sk#158 ASC NULLS FIRST, wr_order_number#159 ASC NULLS FIRST], false, 0

(117) SortMergeJoin [codegen id : 61]
Left keys [2]: [ws_item_sk#151, ws_order_number#154]
Right keys [2]: [wr_item_sk#158, wr_order_number#159]
Join type: LeftOuter
Join condition: None

(118) Project [codegen id : 61]
Output [8]: [ws_item_sk#151, ws_web_site_sk#152, ws_promo_sk#153, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157, wr_return_amt#160, wr_net_loss#161]
Input [11]: [ws_item_sk#151, ws_web_site_sk#152, ws_promo_sk#153, ws_order_number#154, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157, wr_item_sk#158, wr_order_number#159, wr_return_amt#160, wr_net_loss#161]

(119) ReusedExchange [Reuses operator id: 18]
Output [1]: [i_item_sk#162]

(120) BroadcastHashJoin [codegen id : 61]
Left keys [1]: [ws_item_sk#151]
Right keys [1]: [i_item_sk#162]
Join type: Inner
Join condition: None

(121) Project [codegen id : 61]
Output [7]: [ws_web_site_sk#152, ws_promo_sk#153, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157, wr_return_amt#160, wr_net_loss#161]
Input [9]: [ws_item_sk#151, ws_web_site_sk#152, ws_promo_sk#153, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157, wr_return_amt#160, wr_net_loss#161, i_item_sk#162]

(122) ReusedExchange [Reuses operator id: 25]
Output [1]: [p_promo_sk#163]

(123) BroadcastHashJoin [codegen id : 61]
Left keys [1]: [ws_promo_sk#153]
Right keys [1]: [p_promo_sk#163]
Join type: Inner
Join condition: None

(124) Project [codegen id : 61]
Output [6]: [ws_web_site_sk#152, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157, wr_return_amt#160, wr_net_loss#161]
Input [8]: [ws_web_site_sk#152, ws_promo_sk#153, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157, wr_return_amt#160, wr_net_loss#161, p_promo_sk#163]

(125) ReusedExchange [Reuses operator id: 221]
Output [1]: [d_date_sk#164]

(126) BroadcastHashJoin [codegen id : 61]
Left keys [1]: [ws_sold_date_sk#157]
Right keys [1]: [d_date_sk#164]
Join type: Inner
Join condition: None

(127) Project [codegen id : 61]
Output [5]: [ws_web_site_sk#152, ws_ext_sales_price#155, ws_net_profit#156, wr_return_amt#160, wr_net_loss#161]
Input [7]: [ws_web_site_sk#152, ws_ext_sales_price#155, ws_net_profit#156, ws_sold_date_sk#157, wr_return_amt#160, wr_net_loss#161, d_date_sk#164]

(128) ReusedExchange [Reuses operator id: 96]
Output [2]: [web_site_sk#165, web_site_id#166]

(129) BroadcastHashJoin [codegen id : 61]
Left keys [1]: [ws_web_site_sk#152]
Right keys [1]: [web_site_sk#165]
Join type: Inner
Join condition: None

(130) Project [codegen id : 61]
Output [5]: [ws_ext_sales_price#155, ws_net_profit#156, wr_return_amt#160, wr_net_loss#161, web_site_id#166]
Input [7]: [ws_web_site_sk#152, ws_ext_sales_price#155, ws_net_profit#156, wr_return_amt#160, wr_net_loss#161, web_site_sk#165, web_site_id#166]

(131) HashAggregate [codegen id : 61]
Input [5]: [ws_ext_sales_price#155, ws_net_profit#156, wr_return_amt#160, wr_net_loss#161, web_site_id#166]
Keys [1]: [web_site_id#166]
Functions [3]: [partial_sum(UnscaledValue(ws_ext_sales_price#155)), partial_sum(coalesce(cast(wr_return_amt#160 as decimal(12,2)), 0.00)), partial_sum((ws_net_profit#156 - coalesce(cast(wr_net_loss#161 as decimal(12,2)), 0.00)))]
Aggregate Attributes [5]: [sum#167, sum#168, isEmpty#169, sum#170, isEmpty#171]
Results [6]: [web_site_id#166, sum#172, sum#173, isEmpty#174, sum#175, isEmpty#176]

(132) Exchange
Input [6]: [web_site_id#166, sum#172, sum#173, isEmpty#174, sum#175, isEmpty#176]
Arguments: hashpartitioning(web_site_id#166, 5), ENSURE_REQUIREMENTS, [plan_id=17]

(133) HashAggregate [codegen id : 62]
Input [6]: [web_site_id#166, sum#172, sum#173, isEmpty#174, sum#175, isEmpty#176]
Keys [1]: [web_site_id#166]
Functions [3]: [sum(UnscaledValue(ws_ext_sales_price#155)), sum(coalesce(cast(wr_return_amt#160 as decimal(12,2)), 0.00)), sum((ws_net_profit#156 - coalesce(cast(wr_net_loss#161 as decimal(12,2)), 0.00)))]
Aggregate Attributes [3]: [sum(UnscaledValue(ws_ext_sales_price#155))#105, sum(coalesce(cast(wr_return_amt#160 as decimal(12,2)), 0.00))#106, sum((ws_net_profit#156 - coalesce(cast(wr_net_loss#161 as decimal(12,2)), 0.00)))#107]
Results [5]: [web channel AS channel#108, concat(web_site, web_site_id#166) AS id#109, MakeDecimal(sum(UnscaledValue(ws_ext_sales_price#155))#105,17,2) AS sales#110, sum(coalesce(cast(wr_return_amt#160 as decimal(12,2)), 0.00))#106 AS returns#111, sum((ws_net_profit#156 - coalesce(cast(wr_net_loss#161 as decimal(12,2)), 0.00)))#107 AS profit#112]

(134) Union

(135) HashAggregate [codegen id : 63]
Input [5]: [channel#38, id#39, sales#40, returns#41, profit#42]
Keys [2]: [channel#38, id#39]
Functions [3]: [partial_sum(sales#40), partial_sum(returns#41), partial_sum(profit#42)]
Aggregate Attributes [6]: [sum#113, isEmpty#114, sum#115, isEmpty#116, sum#117, isEmpty#118]
Results [8]: [channel#38, id#39, sum#119, isEmpty#120, sum#121, isEmpty#122, sum#123, isEmpty#124]

(136) Exchange
Input [8]: [channel#38, id#39, sum#119, isEmpty#120, sum#121, isEmpty#122, sum#123, isEmpty#124]
Arguments: hashpartitioning(channel#38, id#39, 5), ENSURE_REQUIREMENTS, [plan_id=18]

(137) HashAggregate [codegen id : 64]
Input [8]: [channel#38, id#39, sum#119, isEmpty#120, sum#121, isEmpty#122, sum#123, isEmpty#124]
Keys [2]: [channel#38, id#39]
Functions [3]: [sum(sales#40), sum(returns#41), sum(profit#42)]
Aggregate Attributes [3]: [sum(sales#40)#125, sum(returns#41)#126, sum(profit#42)#127]
Results [4]: [channel#38, sum(sales#40)#125 AS sales#177, sum(returns#41)#126 AS returns#178, sum(profit#42)#127 AS profit#179]

(138) HashAggregate [codegen id : 64]
Input [4]: [channel#38, sales#177, returns#178, profit#179]
Keys [1]: [channel#38]
Functions [3]: [partial_sum(sales#177), partial_sum(returns#178), partial_sum(profit#179)]
Aggregate Attributes [6]: [sum#180, isEmpty#181, sum#182, isEmpty#183, sum#184, isEmpty#185]
Results [7]: [channel#38, sum#186, isEmpty#187, sum#188, isEmpty#189, sum#190, isEmpty#191]

(139) Exchange
Input [7]: [channel#38, sum#186, isEmpty#187, sum#188, isEmpty#189, sum#190, isEmpty#191]
Arguments: hashpartitioning(channel#38, 5), ENSURE_REQUIREMENTS, [plan_id=19]

(140) HashAggregate [codegen id : 65]
Input [7]: [channel#38, sum#186, isEmpty#187, sum#188, isEmpty#189, sum#190, isEmpty#191]
Keys [1]: [channel#38]
Functions [3]: [sum(sales#177), sum(returns#178), sum(profit#179)]
Aggregate Attributes [3]: [sum(sales#177)#192, sum(returns#178)#193, sum(profit#179)#194]
Results [5]: [channel#38, null AS id#195, sum(sales#177)#192 AS sales#196, sum(returns#178)#193 AS returns#197, sum(profit#179)#194 AS profit#198]

(141) Scan parquet spark_catalog.default.store_sales
Output [7]: [ss_item_sk#199, ss_store_sk#200, ss_promo_sk#201, ss_ticket_number#202, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ss_sold_date_sk#205), dynamicpruningexpression(ss_sold_date_sk#205 IN dynamicpruning#8)]
PushedFilters: [IsNotNull(ss_store_sk), IsNotNull(ss_item_sk), IsNotNull(ss_promo_sk)]
ReadSchema: struct<ss_item_sk:int,ss_store_sk:int,ss_promo_sk:int,ss_ticket_number:int,ss_ext_sales_price:decimal(7,2),ss_net_profit:decimal(7,2)>

(142) ColumnarToRow [codegen id : 66]
Input [7]: [ss_item_sk#199, ss_store_sk#200, ss_promo_sk#201, ss_ticket_number#202, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205]

(143) Filter [codegen id : 66]
Input [7]: [ss_item_sk#199, ss_store_sk#200, ss_promo_sk#201, ss_ticket_number#202, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205]
Condition : ((isnotnull(ss_store_sk#200) AND isnotnull(ss_item_sk#199)) AND isnotnull(ss_promo_sk#201))

(144) Exchange
Input [7]: [ss_item_sk#199, ss_store_sk#200, ss_promo_sk#201, ss_ticket_number#202, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205]
Arguments: hashpartitioning(ss_item_sk#199, ss_ticket_number#202, 5), ENSURE_REQUIREMENTS, [plan_id=20]

(145) Sort [codegen id : 67]
Input [7]: [ss_item_sk#199, ss_store_sk#200, ss_promo_sk#201, ss_ticket_number#202, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205]
Arguments: [ss_item_sk#199 ASC NULLS FIRST, ss_ticket_number#202 ASC NULLS FIRST], false, 0

(146) ReusedExchange [Reuses operator id: 10]
Output [4]: [sr_item_sk#206, sr_ticket_number#207, sr_return_amt#208, sr_net_loss#209]

(147) Sort [codegen id : 69]
Input [4]: [sr_item_sk#206, sr_ticket_number#207, sr_return_amt#208, sr_net_loss#209]
Arguments: [sr_item_sk#206 ASC NULLS FIRST, sr_ticket_number#207 ASC NULLS FIRST], false, 0

(148) SortMergeJoin [codegen id : 74]
Left keys [2]: [ss_item_sk#199, ss_ticket_number#202]
Right keys [2]: [sr_item_sk#206, sr_ticket_number#207]
Join type: LeftOuter
Join condition: None

(149) Project [codegen id : 74]
Output [8]: [ss_item_sk#199, ss_store_sk#200, ss_promo_sk#201, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205, sr_return_amt#208, sr_net_loss#209]
Input [11]: [ss_item_sk#199, ss_store_sk#200, ss_promo_sk#201, ss_ticket_number#202, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205, sr_item_sk#206, sr_ticket_number#207, sr_return_amt#208, sr_net_loss#209]

(150) ReusedExchange [Reuses operator id: 18]
Output [1]: [i_item_sk#210]

(151) BroadcastHashJoin [codegen id : 74]
Left keys [1]: [ss_item_sk#199]
Right keys [1]: [i_item_sk#210]
Join type: Inner
Join condition: None

(152) Project [codegen id : 74]
Output [7]: [ss_store_sk#200, ss_promo_sk#201, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205, sr_return_amt#208, sr_net_loss#209]
Input [9]: [ss_item_sk#199, ss_store_sk#200, ss_promo_sk#201, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205, sr_return_amt#208, sr_net_loss#209, i_item_sk#210]

(153) ReusedExchange [Reuses operator id: 25]
Output [1]: [p_promo_sk#211]

(154) BroadcastHashJoin [codegen id : 74]
Left keys [1]: [ss_promo_sk#201]
Right keys [1]: [p_promo_sk#211]
Join type: Inner
Join condition: None

(155) Project [codegen id : 74]
Output [6]: [ss_store_sk#200, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205, sr_return_amt#208, sr_net_loss#209]
Input [8]: [ss_store_sk#200, ss_promo_sk#201, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205, sr_return_amt#208, sr_net_loss#209, p_promo_sk#211]

(156) ReusedExchange [Reuses operator id: 221]
Output [1]: [d_date_sk#212]

(157) BroadcastHashJoin [codegen id : 74]
Left keys [1]: [ss_sold_date_sk#205]
Right keys [1]: [d_date_sk#212]
Join type: Inner
Join condition: None

(158) Project [codegen id : 74]
Output [5]: [ss_store_sk#200, ss_ext_sales_price#203, ss_net_profit#204, sr_return_amt#208, sr_net_loss#209]
Input [7]: [ss_store_sk#200, ss_ext_sales_price#203, ss_net_profit#204, ss_sold_date_sk#205, sr_return_amt#208, sr_net_loss#209, d_date_sk#212]

(159) ReusedExchange [Reuses operator id: 34]
Output [2]: [s_store_sk#213, s_store_id#214]

(160) BroadcastHashJoin [codegen id : 74]
Left keys [1]: [ss_store_sk#200]
Right keys [1]: [s_store_sk#213]
Join type: Inner
Join condition: None

(161) Project [codegen id : 74]
Output [5]: [ss_ext_sales_price#203, ss_net_profit#204, sr_return_amt#208, sr_net_loss#209, s_store_id#214]
Input [7]: [ss_store_sk#200, ss_ext_sales_price#203, ss_net_profit#204, sr_return_amt#208, sr_net_loss#209, s_store_sk#213, s_store_id#214]

(162) HashAggregate [codegen id : 74]
Input [5]: [ss_ext_sales_price#203, ss_net_profit#204, sr_return_amt#208, sr_net_loss#209, s_store_id#214]
Keys [1]: [s_store_id#214]
Functions [3]: [partial_sum(UnscaledValue(ss_ext_sales_price#203)), partial_sum(coalesce(cast(sr_return_amt#208 as decimal(12,2)), 0.00)), partial_sum((ss_net_profit#204 - coalesce(cast(sr_net_loss#209 as decimal(12,2)), 0.00)))]
Aggregate Attributes [5]: [sum#215, sum#216, isEmpty#217, sum#218, isEmpty#219]
Results [6]: [s_store_id#214, sum#220, sum#221, isEmpty#222, sum#223, isEmpty#224]

(163) Exchange
Input [6]: [s_store_id#214, sum#220, sum#221, isEmpty#222, sum#223, isEmpty#224]
Arguments: hashpartitioning(s_store_id#214, 5), ENSURE_REQUIREMENTS, [plan_id=21]

(164) HashAggregate [codegen id : 75]
Input [6]: [s_store_id#214, sum#220, sum#221, isEmpty#222, sum#223, isEmpty#224]
Keys [1]: [s_store_id#214]
Functions [3]: [sum(UnscaledValue(ss_ext_sales_price#203)), sum(coalesce(cast(sr_return_amt#208 as decimal(12,2)), 0.00)), sum((ss_net_profit#204 - coalesce(cast(sr_net_loss#209 as decimal(12,2)), 0.00)))]
Aggregate Attributes [3]: [sum(UnscaledValue(ss_ext_sales_price#203))#35, sum(coalesce(cast(sr_return_amt#208 as decimal(12,2)), 0.00))#36, sum((ss_net_profit#204 - coalesce(cast(sr_net_loss#209 as decimal(12,2)), 0.00)))#37]
Results [5]: [store channel AS channel#38, concat(store, s_store_id#214) AS id#39, MakeDecimal(sum(UnscaledValue(ss_ext_sales_price#203))#35,17,2) AS sales#40, sum(coalesce(cast(sr_return_amt#208 as decimal(12,2)), 0.00))#36 AS returns#41, sum((ss_net_profit#204 - coalesce(cast(sr_net_loss#209 as decimal(12,2)), 0.00)))#37 AS profit#42]

(165) Scan parquet spark_catalog.default.catalog_sales
Output [7]: [cs_catalog_page_sk#225, cs_item_sk#226, cs_promo_sk#227, cs_order_number#228, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(cs_sold_date_sk#231), dynamicpruningexpression(cs_sold_date_sk#231 IN dynamicpruning#8)]
PushedFilters: [IsNotNull(cs_catalog_page_sk), IsNotNull(cs_item_sk), IsNotNull(cs_promo_sk)]
ReadSchema: struct<cs_catalog_page_sk:int,cs_item_sk:int,cs_promo_sk:int,cs_order_number:int,cs_ext_sales_price:decimal(7,2),cs_net_profit:decimal(7,2)>

(166) ColumnarToRow [codegen id : 76]
Input [7]: [cs_catalog_page_sk#225, cs_item_sk#226, cs_promo_sk#227, cs_order_number#228, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231]

(167) Filter [codegen id : 76]
Input [7]: [cs_catalog_page_sk#225, cs_item_sk#226, cs_promo_sk#227, cs_order_number#228, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231]
Condition : ((isnotnull(cs_catalog_page_sk#225) AND isnotnull(cs_item_sk#226)) AND isnotnull(cs_promo_sk#227))

(168) Exchange
Input [7]: [cs_catalog_page_sk#225, cs_item_sk#226, cs_promo_sk#227, cs_order_number#228, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231]
Arguments: hashpartitioning(cs_item_sk#226, cs_order_number#228, 5), ENSURE_REQUIREMENTS, [plan_id=22]

(169) Sort [codegen id : 77]
Input [7]: [cs_catalog_page_sk#225, cs_item_sk#226, cs_promo_sk#227, cs_order_number#228, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231]
Arguments: [cs_item_sk#226 ASC NULLS FIRST, cs_order_number#228 ASC NULLS FIRST], false, 0

(170) ReusedExchange [Reuses operator id: 49]
Output [4]: [cr_item_sk#232, cr_order_number#233, cr_return_amount#234, cr_net_loss#235]

(171) Sort [codegen id : 79]
Input [4]: [cr_item_sk#232, cr_order_number#233, cr_return_amount#234, cr_net_loss#235]
Arguments: [cr_item_sk#232 ASC NULLS FIRST, cr_order_number#233 ASC NULLS FIRST], false, 0

(172) SortMergeJoin [codegen id : 84]
Left keys [2]: [cs_item_sk#226, cs_order_number#228]
Right keys [2]: [cr_item_sk#232, cr_order_number#233]
Join type: LeftOuter
Join condition: None

(173) Project [codegen id : 84]
Output [8]: [cs_catalog_page_sk#225, cs_item_sk#226, cs_promo_sk#227, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231, cr_return_amount#234, cr_net_loss#235]
Input [11]: [cs_catalog_page_sk#225, cs_item_sk#226, cs_promo_sk#227, cs_order_number#228, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231, cr_item_sk#232, cr_order_number#233, cr_return_amount#234, cr_net_loss#235]

(174) ReusedExchange [Reuses operator id: 18]
Output [1]: [i_item_sk#236]

(175) BroadcastHashJoin [codegen id : 84]
Left keys [1]: [cs_item_sk#226]
Right keys [1]: [i_item_sk#236]
Join type: Inner
Join condition: None

(176) Project [codegen id : 84]
Output [7]: [cs_catalog_page_sk#225, cs_promo_sk#227, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231, cr_return_amount#234, cr_net_loss#235]
Input [9]: [cs_catalog_page_sk#225, cs_item_sk#226, cs_promo_sk#227, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231, cr_return_amount#234, cr_net_loss#235, i_item_sk#236]

(177) ReusedExchange [Reuses operator id: 25]
Output [1]: [p_promo_sk#237]

(178) BroadcastHashJoin [codegen id : 84]
Left keys [1]: [cs_promo_sk#227]
Right keys [1]: [p_promo_sk#237]
Join type: Inner
Join condition: None

(179) Project [codegen id : 84]
Output [6]: [cs_catalog_page_sk#225, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231, cr_return_amount#234, cr_net_loss#235]
Input [8]: [cs_catalog_page_sk#225, cs_promo_sk#227, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231, cr_return_amount#234, cr_net_loss#235, p_promo_sk#237]

(180) ReusedExchange [Reuses operator id: 221]
Output [1]: [d_date_sk#238]

(181) BroadcastHashJoin [codegen id : 84]
Left keys [1]: [cs_sold_date_sk#231]
Right keys [1]: [d_date_sk#238]
Join type: Inner
Join condition: None

(182) Project [codegen id : 84]
Output [5]: [cs_catalog_page_sk#225, cs_ext_sales_price#229, cs_net_profit#230, cr_return_amount#234, cr_net_loss#235]
Input [7]: [cs_catalog_page_sk#225, cs_ext_sales_price#229, cs_net_profit#230, cs_sold_date_sk#231, cr_return_amount#234, cr_net_loss#235, d_date_sk#238]

(183) ReusedExchange [Reuses operator id: 65]
Output [2]: [cp_catalog_page_sk#239, cp_catalog_page_id#240]

(184) BroadcastHashJoin [codegen id : 84]
Left keys [1]: [cs_catalog_page_sk#225]
Right keys [1]: [cp_catalog_page_sk#239]
Join type: Inner
Join condition: None

(185) Project [codegen id : 84]
Output [5]: [cs_ext_sales_price#229, cs_net_profit#230, cr_return_amount#234, cr_net_loss#235, cp_catalog_page_id#240]
Input [7]: [cs_catalog_page_sk#225, cs_ext_sales_price#229, cs_net_profit#230, cr_return_amount#234, cr_net_loss#235, cp_catalog_page_sk#239, cp_catalog_page_id#240]

(186) HashAggregate [codegen id : 84]
Input [5]: [cs_ext_sales_price#229, cs_net_profit#230, cr_return_amount#234, cr_net_loss#235, cp_catalog_page_id#240]
Keys [1]: [cp_catalog_page_id#240]
Functions [3]: [partial_sum(UnscaledValue(cs_ext_sales_price#229)), partial_sum(coalesce(cast(cr_return_amount#234 as decimal(12,2)), 0.00)), partial_sum((cs_net_profit#230 - coalesce(cast(cr_net_loss#235 as decimal(12,2)), 0.00)))]
Aggregate Attributes [5]: [sum#241, sum#242, isEmpty#243, sum#244, isEmpty#245]
Results [6]: [cp_catalog_page_id#240, sum#246, sum#247, isEmpty#248, sum#249, isEmpty#250]

(187) Exchange
Input [6]: [cp_catalog_page_id#240, sum#246, sum#247, isEmpty#248, sum#249, isEmpty#250]
Arguments: hashpartitioning(cp_catalog_page_id#240, 5), ENSURE_REQUIREMENTS, [plan_id=23]

(188) HashAggregate [codegen id : 85]
Input [6]: [cp_catalog_page_id#240, sum#246, sum#247, isEmpty#248, sum#249, isEmpty#250]
Keys [1]: [cp_catalog_page_id#240]
Functions [3]: [sum(UnscaledValue(cs_ext_sales_price#229)), sum(coalesce(cast(cr_return_amount#234 as decimal(12,2)), 0.00)), sum((cs_net_profit#230 - coalesce(cast(cr_net_loss#235 as decimal(12,2)), 0.00)))]
Aggregate Attributes [3]: [sum(UnscaledValue(cs_ext_sales_price#229))#70, sum(coalesce(cast(cr_return_amount#234 as decimal(12,2)), 0.00))#71, sum((cs_net_profit#230 - coalesce(cast(cr_net_loss#235 as decimal(12,2)), 0.00)))#72]
Results [5]: [catalog channel AS channel#73, concat(catalog_page, cp_catalog_page_id#240) AS id#74, MakeDecimal(sum(UnscaledValue(cs_ext_sales_price#229))#70,17,2) AS sales#75, sum(coalesce(cast(cr_return_amount#234 as decimal(12,2)), 0.00))#71 AS returns#76, sum((cs_net_profit#230 - coalesce(cast(cr_net_loss#235 as decimal(12,2)), 0.00)))#72 AS profit#77]

(189) ReusedExchange [Reuses operator id: 132]
Output [6]: [web_site_id#251, sum#252, sum#253, isEmpty#254, sum#255, isEmpty#256]

(190) HashAggregate [codegen id : 95]
Input [6]: [web_site_id#251, sum#252, sum#253, isEmpty#254, sum#255, isEmpty#256]
Keys [1]: [web_site_id#251]
Functions [3]: [sum(UnscaledValue(ws_ext_sales_price#257)), sum(coalesce(cast(wr_return_amt#258 as decimal(12,2)), 0.00)), sum((ws_net_profit#259 - coalesce(cast(wr_net_loss#260 as decimal(12,2)), 0.00)))]
Aggregate Attributes [3]: [sum(UnscaledValue(ws_ext_sales_price#257))#105, sum(coalesce(cast(wr_return_amt#258 as decimal(12,2)), 0.00))#106, sum((ws_net_profit#259 - coalesce(cast(wr_net_loss#260 as decimal(12,2)), 0.00)))#107]
Results [5]: [web channel AS channel#108, concat(web_site, web_site_id#251) AS id#109, MakeDecimal(sum(UnscaledValue(ws_ext_sales_price#257))#105,17,2) AS sales#110, sum(coalesce(cast(wr_return_amt#258 as decimal(12,2)), 0.00))#106 AS returns#111, sum((ws_net_profit#259 - coalesce(cast(wr_net_loss#260 as decimal(12,2)), 0.00)))#107 AS profit#112]

(191) Union

(192) HashAggregate [codegen id : 96]
Input [5]: [channel#38, id#39, sales#40, returns#41, profit#42]
Keys [2]: [channel#38, id#39]
Functions [3]: [partial_sum(sales#40), partial_sum(returns#41), partial_sum(profit#42)]
Aggregate Attributes [6]: [sum#113, isEmpty#114, sum#115, isEmpty#116, sum#117, isEmpty#118]
Results [8]: [channel#38, id#39, sum#119, isEmpty#120, sum#121, isEmpty#122, sum#123, isEmpty#124]

(193) Exchange
Input [8]: [channel#38, id#39, sum#119, isEmpty#120, sum#121, isEmpty#122, sum#123, isEmpty#124]
Arguments: hashpartitioning(channel#38, id#39, 5), ENSURE_REQUIREMENTS, [plan_id=24]

(194) HashAggregate [codegen id : 97]
Input [8]: [channel#38, id#39, sum#119, isEmpty#120, sum#121, isEmpty#122, sum#123, isEmpty#124]
Keys [2]: [channel#38, id#39]
Functions [3]: [sum(sales#40), sum(returns#41), sum(profit#42)]
Aggregate Attributes [3]: [sum(sales#40)#125, sum(returns#41)#126, sum(profit#42)#127]
Results [3]: [sum(sales#40)#125 AS sales#261, sum(returns#41)#126 AS returns#262, sum(profit#42)#127 AS profit#263]

(195) HashAggregate [codegen id : 97]
Input [3]: [sales#261, returns#262, profit#263]
Keys: []
Functions [3]: [partial_sum(sales#261), partial_sum(returns#262), partial_sum(profit#263)]
Aggregate Attributes [6]: [sum#264, isEmpty#265, sum#266, isEmpty#267, sum#268, isEmpty#269]
Results [6]: [sum#270, isEmpty#271, sum#272, isEmpty#273, sum#274, isEmpty#275]

(196) Exchange
Input [6]: [sum#270, isEmpty#271, sum#272, isEmpty#273, sum#274, isEmpty#275]
Arguments: SinglePartition, ENSURE_REQUIREMENTS, [plan_id=25]

(197) HashAggregate [codegen id : 98]
Input [6]: [sum#270, isEmpty#271, sum#272, isEmpty#273, sum#274, isEmpty#275]
Keys: []
Functions [3]: [sum(sales#261), sum(returns#262), sum(profit#263)]
Aggregate Attributes [3]: [sum(sales#261)#276, sum(returns#262)#277, sum(profit#263)#278]
Results [5]: [null AS channel#279, null AS id#280, sum(sales#261)#276 AS sales#281, sum(returns#262)#277 AS returns#282, sum(profit#263)#278 AS profit#283]

(198) Union

(199) HashAggregate [codegen id : 99]
Input [5]: [channel#38, id#39, sales#128, returns#129, profit#130]
Keys [5]: [channel#38, id#39, sales#128, returns#129, profit#130]
Functions: []
Aggregate Attributes: []
Results [5]: [channel#38, id#39, sales#128, returns#129, profit#130]

(200) Exchange
Input [5]: [channel#38, id#39, sales#128, returns#129, profit#130]
Arguments: hashpartitioning(channel#38, id#39, sales#128, returns#129, profit#130, 5), ENSURE_REQUIREMENTS, [plan_id=26]

(201) HashAggregate [codegen id : 100]
Input [5]: [channel#38, id#39, sales#128, returns#129, profit#130]
Keys [5]: [channel#38, id#39, sales#128, returns#129, profit#130]
Functions: []
Aggregate Attributes: []
Results [5]: [channel#38, id#39, sales#128, returns#129, profit#130]

(202) TakeOrderedAndProject
Input [5]: [channel#38, id#39, sales#128, returns#129, profit#130]
Arguments: 100, [channel#38 ASC NULLS FIRST, id#39 ASC NULLS FIRST], [channel#38, id#39, sales#128, returns#129, profit#130]

===== Subqueries =====

Subquery:1 Hosting operator id = 3 Hosting Expression = Subquery scalar-subquery#9, [id=#10]
ObjectHashAggregate (209)
+- Exchange (208)
   +- ObjectHashAggregate (207)
      +- * Project (206)
         +- * Filter (205)
            +- * ColumnarToRow (204)
               +- Scan parquet spark_catalog.default.item (203)


(203) Scan parquet spark_catalog.default.item
Output [2]: [i_item_sk#18, i_current_price#19]
Batched: true
Location [not included in comparison]/{warehouse_dir}/item]
PushedFilters: [IsNotNull(i_current_price), GreaterThan(i_current_price,50.00), IsNotNull(i_item_sk)]
ReadSchema: struct<i_item_sk:int,i_current_price:decimal(7,2)>

(204) ColumnarToRow [codegen id : 1]
Input [2]: [i_item_sk#18, i_current_price#19]

(205) Filter [codegen id : 1]
Input [2]: [i_item_sk#18, i_current_price#19]
Condition : ((isnotnull(i_current_price#19) AND (i_current_price#19 > 50.00)) AND isnotnull(i_item_sk#18))

(206) Project [codegen id : 1]
Output [1]: [i_item_sk#18]
Input [2]: [i_item_sk#18, i_current_price#19]

(207) ObjectHashAggregate
Input [1]: [i_item_sk#18]
Keys: []
Functions [1]: [partial_bloom_filter_agg(xxhash64(i_item_sk#18, 42), 101823, 1521109, 0, 0)]
Aggregate Attributes [1]: [buf#284]
Results [1]: [buf#285]

(208) Exchange
Input [1]: [buf#285]
Arguments: SinglePartition, ENSURE_REQUIREMENTS, [plan_id=27]

(209) ObjectHashAggregate
Input [1]: [buf#285]
Keys: []
Functions [1]: [bloom_filter_agg(xxhash64(i_item_sk#18, 42), 101823, 1521109, 0, 0)]
Aggregate Attributes [1]: [bloom_filter_agg(xxhash64(i_item_sk#18, 42), 101823, 1521109, 0, 0)#286]
Results [1]: [bloom_filter_agg(xxhash64(i_item_sk#18, 42), 101823, 1521109, 0, 0)#286 AS bloomFilter#287]

Subquery:2 Hosting operator id = 3 Hosting Expression = Subquery scalar-subquery#11, [id=#12]
ObjectHashAggregate (216)
+- Exchange (215)
   +- ObjectHashAggregate (214)
      +- * Project (213)
         +- * Filter (212)
            +- * ColumnarToRow (211)
               +- Scan parquet spark_catalog.default.promotion (210)


(210) Scan parquet spark_catalog.default.promotion
Output [2]: [p_promo_sk#20, p_channel_tv#21]
Batched: true
Location [not included in comparison]/{warehouse_dir}/promotion]
PushedFilters: [IsNotNull(p_channel_tv), EqualTo(p_channel_tv,N), IsNotNull(p_promo_sk)]
ReadSchema: struct<p_promo_sk:int,p_channel_tv:string>

(211) ColumnarToRow [codegen id : 1]
Input [2]: [p_promo_sk#20, p_channel_tv#21]

(212) Filter [codegen id : 1]
Input [2]: [p_promo_sk#20, p_channel_tv#21]
Condition : ((isnotnull(p_channel_tv#21) AND (p_channel_tv#21 = N)) AND isnotnull(p_promo_sk#20))

(213) Project [codegen id : 1]
Output [1]: [p_promo_sk#20]
Input [2]: [p_promo_sk#20, p_channel_tv#21]

(214) ObjectHashAggregate
Input [1]: [p_promo_sk#20]
Keys: []
Functions [1]: [partial_bloom_filter_agg(xxhash64(p_promo_sk#20, 42), 986, 24246, 0, 0)]
Aggregate Attributes [1]: [buf#288]
Results [1]: [buf#289]

(215) Exchange
Input [1]: [buf#289]
Arguments: SinglePartition, ENSURE_REQUIREMENTS, [plan_id=28]

(216) ObjectHashAggregate
Input [1]: [buf#289]
Keys: []
Functions [1]: [bloom_filter_agg(xxhash64(p_promo_sk#20, 42), 986, 24246, 0, 0)]
Aggregate Attributes [1]: [bloom_filter_agg(xxhash64(p_promo_sk#20, 42), 986, 24246, 0, 0)#290]
Results [1]: [bloom_filter_agg(xxhash64(p_promo_sk#20, 42), 986, 24246, 0, 0)#290 AS bloomFilter#291]

Subquery:3 Hosting operator id = 1 Hosting Expression = ss_sold_date_sk#7 IN dynamicpruning#8
BroadcastExchange (221)
+- * Project (220)
   +- * Filter (219)
      +- * ColumnarToRow (218)
         +- Scan parquet spark_catalog.default.date_dim (217)


(217) Scan parquet spark_catalog.default.date_dim
Output [2]: [d_date_sk#22, d_date#292]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNotNull(d_date), GreaterThanOrEqual(d_date,1998-08-04), LessThanOrEqual(d_date,1998-09-03), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_date:date>

(218) ColumnarToRow [codegen id : 1]
Input [2]: [d_date_sk#22, d_date#292]

(219) Filter [codegen id : 1]
Input [2]: [d_date_sk#22, d_date#292]
Condition : (((isnotnull(d_date#292) AND (d_date#292 >= 1998-08-04)) AND (d_date#292 <= 1998-09-03)) AND isnotnull(d_date_sk#22))

(220) Project [codegen id : 1]
Output [1]: [d_date_sk#22]
Input [2]: [d_date_sk#22, d_date#292]

(221) BroadcastExchange
Input [1]: [d_date_sk#22]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=29]

Subquery:4 Hosting operator id = 42 Hosting Expression = ReusedSubquery Subquery scalar-subquery#9, [id=#10]

Subquery:5 Hosting operator id = 42 Hosting Expression = ReusedSubquery Subquery scalar-subquery#11, [id=#12]

Subquery:6 Hosting operator id = 40 Hosting Expression = cs_sold_date_sk#49 IN dynamicpruning#8

Subquery:7 Hosting operator id = 73 Hosting Expression = ReusedSubquery Subquery scalar-subquery#9, [id=#10]

Subquery:8 Hosting operator id = 73 Hosting Expression = ReusedSubquery Subquery scalar-subquery#11, [id=#12]

Subquery:9 Hosting operator id = 71 Hosting Expression = ws_sold_date_sk#84 IN dynamicpruning#8

Subquery:10 Hosting operator id = 110 Hosting Expression = ws_sold_date_sk#157 IN dynamicpruning#8

Subquery:11 Hosting operator id = 141 Hosting Expression = ss_sold_date_sk#205 IN dynamicpruning#8

Subquery:12 Hosting operator id = 165 Hosting Expression = cs_sold_date_sk#231 IN dynamicpruning#8


