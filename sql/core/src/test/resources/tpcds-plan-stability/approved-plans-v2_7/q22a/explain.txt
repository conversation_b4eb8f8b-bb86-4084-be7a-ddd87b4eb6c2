== Physical Plan ==
TakeOrderedAndProject (45)
+- Union (44)
   :- * HashAggregate (23)
   :  +- * HashAggregate (22)
   :     +- * HashAggregate (21)
   :        +- Exchange (20)
   :           +- * HashAggregate (19)
   :              +- * Project (18)
   :                 +- * BroadcastHashJoin Inner BuildRight (17)
   :                    :- * Project (12)
   :                    :  +- * BroadcastHashJoin Inner BuildRight (11)
   :                    :     :- * Project (6)
   :                    :     :  +- * BroadcastHashJoin Inner BuildRight (5)
   :                    :     :     :- * Filter (3)
   :                    :     :     :  +- * ColumnarToRow (2)
   :                    :     :     :     +- Scan parquet spark_catalog.default.inventory (1)
   :                    :     :     +- ReusedExchange (4)
   :                    :     +- BroadcastExchange (10)
   :                    :        +- * Filter (9)
   :                    :           +- * ColumnarToRow (8)
   :                    :              +- Scan parquet spark_catalog.default.item (7)
   :                    +- BroadcastExchange (16)
   :                       +- * Filter (15)
   :                          +- * ColumnarToRow (14)
   :                             +- Scan parquet spark_catalog.default.warehouse (13)
   :- * HashAggregate (28)
   :  +- Exchange (27)
   :     +- * HashAggregate (26)
   :        +- * HashAggregate (25)
   :           +- ReusedExchange (24)
   :- * HashAggregate (33)
   :  +- Exchange (32)
   :     +- * HashAggregate (31)
   :        +- * HashAggregate (30)
   :           +- ReusedExchange (29)
   :- * HashAggregate (38)
   :  +- Exchange (37)
   :     +- * HashAggregate (36)
   :        +- * HashAggregate (35)
   :           +- ReusedExchange (34)
   +- * HashAggregate (43)
      +- Exchange (42)
         +- * HashAggregate (41)
            +- * HashAggregate (40)
               +- ReusedExchange (39)


(1) Scan parquet spark_catalog.default.inventory
Output [4]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, inv_date_sk#4]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(inv_date_sk#4), dynamicpruningexpression(inv_date_sk#4 IN dynamicpruning#5)]
PushedFilters: [IsNotNull(inv_item_sk), IsNotNull(inv_warehouse_sk)]
ReadSchema: struct<inv_item_sk:int,inv_warehouse_sk:int,inv_quantity_on_hand:int>

(2) ColumnarToRow [codegen id : 4]
Input [4]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, inv_date_sk#4]

(3) Filter [codegen id : 4]
Input [4]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, inv_date_sk#4]
Condition : (isnotnull(inv_item_sk#1) AND isnotnull(inv_warehouse_sk#2))

(4) ReusedExchange [Reuses operator id: 50]
Output [1]: [d_date_sk#6]

(5) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [inv_date_sk#4]
Right keys [1]: [d_date_sk#6]
Join type: Inner
Join condition: None

(6) Project [codegen id : 4]
Output [3]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3]
Input [5]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, inv_date_sk#4, d_date_sk#6]

(7) Scan parquet spark_catalog.default.item
Output [5]: [i_item_sk#7, i_brand#8, i_class#9, i_category#10, i_product_name#11]
Batched: true
Location [not included in comparison]/{warehouse_dir}/item]
PushedFilters: [IsNotNull(i_item_sk)]
ReadSchema: struct<i_item_sk:int,i_brand:string,i_class:string,i_category:string,i_product_name:string>

(8) ColumnarToRow [codegen id : 2]
Input [5]: [i_item_sk#7, i_brand#8, i_class#9, i_category#10, i_product_name#11]

(9) Filter [codegen id : 2]
Input [5]: [i_item_sk#7, i_brand#8, i_class#9, i_category#10, i_product_name#11]
Condition : isnotnull(i_item_sk#7)

(10) BroadcastExchange
Input [5]: [i_item_sk#7, i_brand#8, i_class#9, i_category#10, i_product_name#11]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, false] as bigint)),false), [plan_id=1]

(11) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [inv_item_sk#1]
Right keys [1]: [i_item_sk#7]
Join type: Inner
Join condition: None

(12) Project [codegen id : 4]
Output [6]: [inv_warehouse_sk#2, inv_quantity_on_hand#3, i_brand#8, i_class#9, i_category#10, i_product_name#11]
Input [8]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, i_item_sk#7, i_brand#8, i_class#9, i_category#10, i_product_name#11]

(13) Scan parquet spark_catalog.default.warehouse
Output [1]: [w_warehouse_sk#12]
Batched: true
Location [not included in comparison]/{warehouse_dir}/warehouse]
PushedFilters: [IsNotNull(w_warehouse_sk)]
ReadSchema: struct<w_warehouse_sk:int>

(14) ColumnarToRow [codegen id : 3]
Input [1]: [w_warehouse_sk#12]

(15) Filter [codegen id : 3]
Input [1]: [w_warehouse_sk#12]
Condition : isnotnull(w_warehouse_sk#12)

(16) BroadcastExchange
Input [1]: [w_warehouse_sk#12]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, false] as bigint)),false), [plan_id=2]

(17) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [inv_warehouse_sk#2]
Right keys [1]: [w_warehouse_sk#12]
Join type: Inner
Join condition: None

(18) Project [codegen id : 4]
Output [5]: [inv_quantity_on_hand#3, i_brand#8, i_class#9, i_category#10, i_product_name#11]
Input [7]: [inv_warehouse_sk#2, inv_quantity_on_hand#3, i_brand#8, i_class#9, i_category#10, i_product_name#11, w_warehouse_sk#12]

(19) HashAggregate [codegen id : 4]
Input [5]: [inv_quantity_on_hand#3, i_brand#8, i_class#9, i_category#10, i_product_name#11]
Keys [4]: [i_product_name#11, i_brand#8, i_class#9, i_category#10]
Functions [1]: [partial_avg(inv_quantity_on_hand#3)]
Aggregate Attributes [2]: [sum#13, count#14]
Results [6]: [i_product_name#11, i_brand#8, i_class#9, i_category#10, sum#15, count#16]

(20) Exchange
Input [6]: [i_product_name#11, i_brand#8, i_class#9, i_category#10, sum#15, count#16]
Arguments: hashpartitioning(i_product_name#11, i_brand#8, i_class#9, i_category#10, 5), ENSURE_REQUIREMENTS, [plan_id=3]

(21) HashAggregate [codegen id : 5]
Input [6]: [i_product_name#11, i_brand#8, i_class#9, i_category#10, sum#15, count#16]
Keys [4]: [i_product_name#11, i_brand#8, i_class#9, i_category#10]
Functions [1]: [avg(inv_quantity_on_hand#3)]
Aggregate Attributes [1]: [avg(inv_quantity_on_hand#3)#17]
Results [5]: [i_product_name#11, i_brand#8, i_class#9, i_category#10, avg(inv_quantity_on_hand#3)#17 AS qoh#18]

(22) HashAggregate [codegen id : 5]
Input [5]: [i_product_name#11, i_brand#8, i_class#9, i_category#10, qoh#18]
Keys [4]: [i_product_name#11, i_brand#8, i_class#9, i_category#10]
Functions [1]: [partial_avg(qoh#18)]
Aggregate Attributes [2]: [sum#19, count#20]
Results [6]: [i_product_name#11, i_brand#8, i_class#9, i_category#10, sum#21, count#22]

(23) HashAggregate [codegen id : 5]
Input [6]: [i_product_name#11, i_brand#8, i_class#9, i_category#10, sum#21, count#22]
Keys [4]: [i_product_name#11, i_brand#8, i_class#9, i_category#10]
Functions [1]: [avg(qoh#18)]
Aggregate Attributes [1]: [avg(qoh#18)#23]
Results [5]: [i_product_name#11, i_brand#8, i_class#9, i_category#10, avg(qoh#18)#23 AS qoh#24]

(24) ReusedExchange [Reuses operator id: 20]
Output [6]: [i_product_name#25, i_brand#26, i_class#27, i_category#28, sum#29, count#30]

(25) HashAggregate [codegen id : 10]
Input [6]: [i_product_name#25, i_brand#26, i_class#27, i_category#28, sum#29, count#30]
Keys [4]: [i_product_name#25, i_brand#26, i_class#27, i_category#28]
Functions [1]: [avg(inv_quantity_on_hand#31)]
Aggregate Attributes [1]: [avg(inv_quantity_on_hand#31)#17]
Results [4]: [i_product_name#25, i_brand#26, i_class#27, avg(inv_quantity_on_hand#31)#17 AS qoh#32]

(26) HashAggregate [codegen id : 10]
Input [4]: [i_product_name#25, i_brand#26, i_class#27, qoh#32]
Keys [3]: [i_product_name#25, i_brand#26, i_class#27]
Functions [1]: [partial_avg(qoh#32)]
Aggregate Attributes [2]: [sum#33, count#34]
Results [5]: [i_product_name#25, i_brand#26, i_class#27, sum#35, count#36]

(27) Exchange
Input [5]: [i_product_name#25, i_brand#26, i_class#27, sum#35, count#36]
Arguments: hashpartitioning(i_product_name#25, i_brand#26, i_class#27, 5), ENSURE_REQUIREMENTS, [plan_id=4]

(28) HashAggregate [codegen id : 11]
Input [5]: [i_product_name#25, i_brand#26, i_class#27, sum#35, count#36]
Keys [3]: [i_product_name#25, i_brand#26, i_class#27]
Functions [1]: [avg(qoh#32)]
Aggregate Attributes [1]: [avg(qoh#32)#37]
Results [5]: [i_product_name#25, i_brand#26, i_class#27, null AS i_category#38, avg(qoh#32)#37 AS qoh#39]

(29) ReusedExchange [Reuses operator id: 20]
Output [6]: [i_product_name#40, i_brand#41, i_class#42, i_category#43, sum#44, count#45]

(30) HashAggregate [codegen id : 16]
Input [6]: [i_product_name#40, i_brand#41, i_class#42, i_category#43, sum#44, count#45]
Keys [4]: [i_product_name#40, i_brand#41, i_class#42, i_category#43]
Functions [1]: [avg(inv_quantity_on_hand#46)]
Aggregate Attributes [1]: [avg(inv_quantity_on_hand#46)#17]
Results [3]: [i_product_name#40, i_brand#41, avg(inv_quantity_on_hand#46)#17 AS qoh#47]

(31) HashAggregate [codegen id : 16]
Input [3]: [i_product_name#40, i_brand#41, qoh#47]
Keys [2]: [i_product_name#40, i_brand#41]
Functions [1]: [partial_avg(qoh#47)]
Aggregate Attributes [2]: [sum#48, count#49]
Results [4]: [i_product_name#40, i_brand#41, sum#50, count#51]

(32) Exchange
Input [4]: [i_product_name#40, i_brand#41, sum#50, count#51]
Arguments: hashpartitioning(i_product_name#40, i_brand#41, 5), ENSURE_REQUIREMENTS, [plan_id=5]

(33) HashAggregate [codegen id : 17]
Input [4]: [i_product_name#40, i_brand#41, sum#50, count#51]
Keys [2]: [i_product_name#40, i_brand#41]
Functions [1]: [avg(qoh#47)]
Aggregate Attributes [1]: [avg(qoh#47)#52]
Results [5]: [i_product_name#40, i_brand#41, null AS i_class#53, null AS i_category#54, avg(qoh#47)#52 AS qoh#55]

(34) ReusedExchange [Reuses operator id: 20]
Output [6]: [i_product_name#56, i_brand#57, i_class#58, i_category#59, sum#60, count#61]

(35) HashAggregate [codegen id : 22]
Input [6]: [i_product_name#56, i_brand#57, i_class#58, i_category#59, sum#60, count#61]
Keys [4]: [i_product_name#56, i_brand#57, i_class#58, i_category#59]
Functions [1]: [avg(inv_quantity_on_hand#62)]
Aggregate Attributes [1]: [avg(inv_quantity_on_hand#62)#17]
Results [2]: [i_product_name#56, avg(inv_quantity_on_hand#62)#17 AS qoh#63]

(36) HashAggregate [codegen id : 22]
Input [2]: [i_product_name#56, qoh#63]
Keys [1]: [i_product_name#56]
Functions [1]: [partial_avg(qoh#63)]
Aggregate Attributes [2]: [sum#64, count#65]
Results [3]: [i_product_name#56, sum#66, count#67]

(37) Exchange
Input [3]: [i_product_name#56, sum#66, count#67]
Arguments: hashpartitioning(i_product_name#56, 5), ENSURE_REQUIREMENTS, [plan_id=6]

(38) HashAggregate [codegen id : 23]
Input [3]: [i_product_name#56, sum#66, count#67]
Keys [1]: [i_product_name#56]
Functions [1]: [avg(qoh#63)]
Aggregate Attributes [1]: [avg(qoh#63)#68]
Results [5]: [i_product_name#56, null AS i_brand#69, null AS i_class#70, null AS i_category#71, avg(qoh#63)#68 AS qoh#72]

(39) ReusedExchange [Reuses operator id: 20]
Output [6]: [i_product_name#73, i_brand#74, i_class#75, i_category#76, sum#77, count#78]

(40) HashAggregate [codegen id : 28]
Input [6]: [i_product_name#73, i_brand#74, i_class#75, i_category#76, sum#77, count#78]
Keys [4]: [i_product_name#73, i_brand#74, i_class#75, i_category#76]
Functions [1]: [avg(inv_quantity_on_hand#79)]
Aggregate Attributes [1]: [avg(inv_quantity_on_hand#79)#17]
Results [1]: [avg(inv_quantity_on_hand#79)#17 AS qoh#80]

(41) HashAggregate [codegen id : 28]
Input [1]: [qoh#80]
Keys: []
Functions [1]: [partial_avg(qoh#80)]
Aggregate Attributes [2]: [sum#81, count#82]
Results [2]: [sum#83, count#84]

(42) Exchange
Input [2]: [sum#83, count#84]
Arguments: SinglePartition, ENSURE_REQUIREMENTS, [plan_id=7]

(43) HashAggregate [codegen id : 29]
Input [2]: [sum#83, count#84]
Keys: []
Functions [1]: [avg(qoh#80)]
Aggregate Attributes [1]: [avg(qoh#80)#85]
Results [5]: [null AS i_product_name#86, null AS i_brand#87, null AS i_class#88, null AS i_category#89, avg(qoh#80)#85 AS qoh#90]

(44) Union

(45) TakeOrderedAndProject
Input [5]: [i_product_name#11, i_brand#8, i_class#9, i_category#10, qoh#24]
Arguments: 100, [qoh#24 ASC NULLS FIRST, i_product_name#11 ASC NULLS FIRST, i_brand#8 ASC NULLS FIRST, i_class#9 ASC NULLS FIRST, i_category#10 ASC NULLS FIRST], [i_product_name#11, i_brand#8, i_class#9, i_category#10, qoh#24]

===== Subqueries =====

Subquery:1 Hosting operator id = 1 Hosting Expression = inv_date_sk#4 IN dynamicpruning#5
BroadcastExchange (50)
+- * Project (49)
   +- * Filter (48)
      +- * ColumnarToRow (47)
         +- Scan parquet spark_catalog.default.date_dim (46)


(46) Scan parquet spark_catalog.default.date_dim
Output [2]: [d_date_sk#6, d_month_seq#91]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNotNull(d_month_seq), GreaterThanOrEqual(d_month_seq,1212), LessThanOrEqual(d_month_seq,1223), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_month_seq:int>

(47) ColumnarToRow [codegen id : 1]
Input [2]: [d_date_sk#6, d_month_seq#91]

(48) Filter [codegen id : 1]
Input [2]: [d_date_sk#6, d_month_seq#91]
Condition : (((isnotnull(d_month_seq#91) AND (d_month_seq#91 >= 1212)) AND (d_month_seq#91 <= 1223)) AND isnotnull(d_date_sk#6))

(49) Project [codegen id : 1]
Output [1]: [d_date_sk#6]
Input [2]: [d_date_sk#6, d_month_seq#91]

(50) BroadcastExchange
Input [1]: [d_date_sk#6]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=8]


