== Physical Plan ==
TakeOrderedAndProject (50)
+- * HashAggregate (49)
   +- Exchange (48)
      +- * HashAggregate (47)
         +- * Project (46)
            +- * SortMergeJoin Inner (45)
               :- * Sort (39)
               :  +- Exchange (38)
               :     +- * Project (37)
               :        +- * SortMergeJoin Inner (36)
               :           :- * Sort (30)
               :           :  +- Exchange (29)
               :           :     +- * Project (28)
               :           :        +- * SortMergeJoin <PERSON>Semi (27)
               :           :           :- * SortMergeJoin LeftSemi (13)
               :           :           :  :- * Sort (5)
               :           :           :  :  +- Exchange (4)
               :           :           :  :     +- * Filter (3)
               :           :           :  :        +- * ColumnarToRow (2)
               :           :           :  :           +- Scan parquet spark_catalog.default.customer (1)
               :           :           :  +- * Sort (12)
               :           :           :     +- Exchange (11)
               :           :           :        +- * Project (10)
               :           :           :           +- * BroadcastHashJoin Inner BuildRight (9)
               :           :           :              :- * ColumnarToRow (7)
               :           :           :              :  +- Scan parquet spark_catalog.default.store_sales (6)
               :           :           :              +- ReusedExchange (8)
               :           :           +- * Sort (26)
               :           :              +- Exchange (25)
               :           :                 +- Union (24)
               :           :                    :- * Project (18)
               :           :                    :  +- * BroadcastHashJoin Inner BuildRight (17)
               :           :                    :     :- * ColumnarToRow (15)
               :           :                    :     :  +- Scan parquet spark_catalog.default.web_sales (14)
               :           :                    :     +- ReusedExchange (16)
               :           :                    +- * Project (23)
               :           :                       +- * BroadcastHashJoin Inner BuildRight (22)
               :           :                          :- * ColumnarToRow (20)
               :           :                          :  +- Scan parquet spark_catalog.default.catalog_sales (19)
               :           :                          +- ReusedExchange (21)
               :           +- * Sort (35)
               :              +- Exchange (34)
               :                 +- * Filter (33)
               :                    +- * ColumnarToRow (32)
               :                       +- Scan parquet spark_catalog.default.customer_address (31)
               +- * Sort (44)
                  +- Exchange (43)
                     +- * Filter (42)
                        +- * ColumnarToRow (41)
                           +- Scan parquet spark_catalog.default.customer_demographics (40)


(1) Scan parquet spark_catalog.default.customer
Output [3]: [c_customer_sk#1, c_current_cdemo_sk#2, c_current_addr_sk#3]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer]
PushedFilters: [IsNotNull(c_current_addr_sk), IsNotNull(c_current_cdemo_sk)]
ReadSchema: struct<c_customer_sk:int,c_current_cdemo_sk:int,c_current_addr_sk:int>

(2) ColumnarToRow [codegen id : 1]
Input [3]: [c_customer_sk#1, c_current_cdemo_sk#2, c_current_addr_sk#3]

(3) Filter [codegen id : 1]
Input [3]: [c_customer_sk#1, c_current_cdemo_sk#2, c_current_addr_sk#3]
Condition : (isnotnull(c_current_addr_sk#3) AND isnotnull(c_current_cdemo_sk#2))

(4) Exchange
Input [3]: [c_customer_sk#1, c_current_cdemo_sk#2, c_current_addr_sk#3]
Arguments: hashpartitioning(c_customer_sk#1, 5), ENSURE_REQUIREMENTS, [plan_id=1]

(5) Sort [codegen id : 2]
Input [3]: [c_customer_sk#1, c_current_cdemo_sk#2, c_current_addr_sk#3]
Arguments: [c_customer_sk#1 ASC NULLS FIRST], false, 0

(6) Scan parquet spark_catalog.default.store_sales
Output [2]: [ss_customer_sk#4, ss_sold_date_sk#5]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ss_sold_date_sk#5), dynamicpruningexpression(ss_sold_date_sk#5 IN dynamicpruning#6)]
ReadSchema: struct<ss_customer_sk:int>

(7) ColumnarToRow [codegen id : 4]
Input [2]: [ss_customer_sk#4, ss_sold_date_sk#5]

(8) ReusedExchange [Reuses operator id: 55]
Output [1]: [d_date_sk#7]

(9) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [ss_sold_date_sk#5]
Right keys [1]: [d_date_sk#7]
Join type: Inner
Join condition: None

(10) Project [codegen id : 4]
Output [1]: [ss_customer_sk#4]
Input [3]: [ss_customer_sk#4, ss_sold_date_sk#5, d_date_sk#7]

(11) Exchange
Input [1]: [ss_customer_sk#4]
Arguments: hashpartitioning(ss_customer_sk#4, 5), ENSURE_REQUIREMENTS, [plan_id=2]

(12) Sort [codegen id : 5]
Input [1]: [ss_customer_sk#4]
Arguments: [ss_customer_sk#4 ASC NULLS FIRST], false, 0

(13) SortMergeJoin [codegen id : 6]
Left keys [1]: [c_customer_sk#1]
Right keys [1]: [ss_customer_sk#4]
Join type: LeftSemi
Join condition: None

(14) Scan parquet spark_catalog.default.web_sales
Output [2]: [ws_bill_customer_sk#8, ws_sold_date_sk#9]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ws_sold_date_sk#9), dynamicpruningexpression(ws_sold_date_sk#9 IN dynamicpruning#6)]
ReadSchema: struct<ws_bill_customer_sk:int>

(15) ColumnarToRow [codegen id : 8]
Input [2]: [ws_bill_customer_sk#8, ws_sold_date_sk#9]

(16) ReusedExchange [Reuses operator id: 55]
Output [1]: [d_date_sk#10]

(17) BroadcastHashJoin [codegen id : 8]
Left keys [1]: [ws_sold_date_sk#9]
Right keys [1]: [d_date_sk#10]
Join type: Inner
Join condition: None

(18) Project [codegen id : 8]
Output [1]: [ws_bill_customer_sk#8 AS customsk#11]
Input [3]: [ws_bill_customer_sk#8, ws_sold_date_sk#9, d_date_sk#10]

(19) Scan parquet spark_catalog.default.catalog_sales
Output [2]: [cs_ship_customer_sk#12, cs_sold_date_sk#13]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(cs_sold_date_sk#13), dynamicpruningexpression(cs_sold_date_sk#13 IN dynamicpruning#6)]
ReadSchema: struct<cs_ship_customer_sk:int>

(20) ColumnarToRow [codegen id : 10]
Input [2]: [cs_ship_customer_sk#12, cs_sold_date_sk#13]

(21) ReusedExchange [Reuses operator id: 55]
Output [1]: [d_date_sk#14]

(22) BroadcastHashJoin [codegen id : 10]
Left keys [1]: [cs_sold_date_sk#13]
Right keys [1]: [d_date_sk#14]
Join type: Inner
Join condition: None

(23) Project [codegen id : 10]
Output [1]: [cs_ship_customer_sk#12 AS customsk#15]
Input [3]: [cs_ship_customer_sk#12, cs_sold_date_sk#13, d_date_sk#14]

(24) Union

(25) Exchange
Input [1]: [customsk#11]
Arguments: hashpartitioning(customsk#11, 5), ENSURE_REQUIREMENTS, [plan_id=3]

(26) Sort [codegen id : 11]
Input [1]: [customsk#11]
Arguments: [customsk#11 ASC NULLS FIRST], false, 0

(27) SortMergeJoin [codegen id : 12]
Left keys [1]: [c_customer_sk#1]
Right keys [1]: [customsk#11]
Join type: LeftSemi
Join condition: None

(28) Project [codegen id : 12]
Output [2]: [c_current_cdemo_sk#2, c_current_addr_sk#3]
Input [3]: [c_customer_sk#1, c_current_cdemo_sk#2, c_current_addr_sk#3]

(29) Exchange
Input [2]: [c_current_cdemo_sk#2, c_current_addr_sk#3]
Arguments: hashpartitioning(c_current_addr_sk#3, 5), ENSURE_REQUIREMENTS, [plan_id=4]

(30) Sort [codegen id : 13]
Input [2]: [c_current_cdemo_sk#2, c_current_addr_sk#3]
Arguments: [c_current_addr_sk#3 ASC NULLS FIRST], false, 0

(31) Scan parquet spark_catalog.default.customer_address
Output [2]: [ca_address_sk#16, ca_state#17]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer_address]
PushedFilters: [IsNotNull(ca_address_sk)]
ReadSchema: struct<ca_address_sk:int,ca_state:string>

(32) ColumnarToRow [codegen id : 14]
Input [2]: [ca_address_sk#16, ca_state#17]

(33) Filter [codegen id : 14]
Input [2]: [ca_address_sk#16, ca_state#17]
Condition : isnotnull(ca_address_sk#16)

(34) Exchange
Input [2]: [ca_address_sk#16, ca_state#17]
Arguments: hashpartitioning(ca_address_sk#16, 5), ENSURE_REQUIREMENTS, [plan_id=5]

(35) Sort [codegen id : 15]
Input [2]: [ca_address_sk#16, ca_state#17]
Arguments: [ca_address_sk#16 ASC NULLS FIRST], false, 0

(36) SortMergeJoin [codegen id : 16]
Left keys [1]: [c_current_addr_sk#3]
Right keys [1]: [ca_address_sk#16]
Join type: Inner
Join condition: None

(37) Project [codegen id : 16]
Output [2]: [c_current_cdemo_sk#2, ca_state#17]
Input [4]: [c_current_cdemo_sk#2, c_current_addr_sk#3, ca_address_sk#16, ca_state#17]

(38) Exchange
Input [2]: [c_current_cdemo_sk#2, ca_state#17]
Arguments: hashpartitioning(c_current_cdemo_sk#2, 5), ENSURE_REQUIREMENTS, [plan_id=6]

(39) Sort [codegen id : 17]
Input [2]: [c_current_cdemo_sk#2, ca_state#17]
Arguments: [c_current_cdemo_sk#2 ASC NULLS FIRST], false, 0

(40) Scan parquet spark_catalog.default.customer_demographics
Output [6]: [cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer_demographics]
PushedFilters: [IsNotNull(cd_demo_sk)]
ReadSchema: struct<cd_demo_sk:int,cd_gender:string,cd_marital_status:string,cd_dep_count:int,cd_dep_employed_count:int,cd_dep_college_count:int>

(41) ColumnarToRow [codegen id : 18]
Input [6]: [cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]

(42) Filter [codegen id : 18]
Input [6]: [cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Condition : isnotnull(cd_demo_sk#18)

(43) Exchange
Input [6]: [cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Arguments: hashpartitioning(cd_demo_sk#18, 5), ENSURE_REQUIREMENTS, [plan_id=7]

(44) Sort [codegen id : 19]
Input [6]: [cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Arguments: [cd_demo_sk#18 ASC NULLS FIRST], false, 0

(45) SortMergeJoin [codegen id : 20]
Left keys [1]: [c_current_cdemo_sk#2]
Right keys [1]: [cd_demo_sk#18]
Join type: Inner
Join condition: None

(46) Project [codegen id : 20]
Output [6]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Input [8]: [c_current_cdemo_sk#2, ca_state#17, cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]

(47) HashAggregate [codegen id : 20]
Input [6]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Keys [6]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Functions [10]: [partial_count(1), partial_avg(cd_dep_count#21), partial_max(cd_dep_count#21), partial_sum(cd_dep_count#21), partial_avg(cd_dep_employed_count#22), partial_max(cd_dep_employed_count#22), partial_sum(cd_dep_employed_count#22), partial_avg(cd_dep_college_count#23), partial_max(cd_dep_college_count#23), partial_sum(cd_dep_college_count#23)]
Aggregate Attributes [13]: [count#24, sum#25, count#26, max#27, sum#28, sum#29, count#30, max#31, sum#32, sum#33, count#34, max#35, sum#36]
Results [19]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23, count#37, sum#38, count#39, max#40, sum#41, sum#42, count#43, max#44, sum#45, sum#46, count#47, max#48, sum#49]

(48) Exchange
Input [19]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23, count#37, sum#38, count#39, max#40, sum#41, sum#42, count#43, max#44, sum#45, sum#46, count#47, max#48, sum#49]
Arguments: hashpartitioning(ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23, 5), ENSURE_REQUIREMENTS, [plan_id=8]

(49) HashAggregate [codegen id : 21]
Input [19]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23, count#37, sum#38, count#39, max#40, sum#41, sum#42, count#43, max#44, sum#45, sum#46, count#47, max#48, sum#49]
Keys [6]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Functions [10]: [count(1), avg(cd_dep_count#21), max(cd_dep_count#21), sum(cd_dep_count#21), avg(cd_dep_employed_count#22), max(cd_dep_employed_count#22), sum(cd_dep_employed_count#22), avg(cd_dep_college_count#23), max(cd_dep_college_count#23), sum(cd_dep_college_count#23)]
Aggregate Attributes [10]: [count(1)#50, avg(cd_dep_count#21)#51, max(cd_dep_count#21)#52, sum(cd_dep_count#21)#53, avg(cd_dep_employed_count#22)#54, max(cd_dep_employed_count#22)#55, sum(cd_dep_employed_count#22)#56, avg(cd_dep_college_count#23)#57, max(cd_dep_college_count#23)#58, sum(cd_dep_college_count#23)#59]
Results [18]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, count(1)#50 AS cnt1#60, avg(cd_dep_count#21)#51 AS avg(cd_dep_count)#61, max(cd_dep_count#21)#52 AS max(cd_dep_count)#62, sum(cd_dep_count#21)#53 AS sum(cd_dep_count)#63, cd_dep_employed_count#22, count(1)#50 AS cnt2#64, avg(cd_dep_employed_count#22)#54 AS avg(cd_dep_employed_count)#65, max(cd_dep_employed_count#22)#55 AS max(cd_dep_employed_count)#66, sum(cd_dep_employed_count#22)#56 AS sum(cd_dep_employed_count)#67, cd_dep_college_count#23, count(1)#50 AS cnt3#68, avg(cd_dep_college_count#23)#57 AS avg(cd_dep_college_count)#69, max(cd_dep_college_count#23)#58 AS max(cd_dep_college_count)#70, sum(cd_dep_college_count#23)#59 AS sum(cd_dep_college_count)#71]

(50) TakeOrderedAndProject
Input [18]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cnt1#60, avg(cd_dep_count)#61, max(cd_dep_count)#62, sum(cd_dep_count)#63, cd_dep_employed_count#22, cnt2#64, avg(cd_dep_employed_count)#65, max(cd_dep_employed_count)#66, sum(cd_dep_employed_count)#67, cd_dep_college_count#23, cnt3#68, avg(cd_dep_college_count)#69, max(cd_dep_college_count)#70, sum(cd_dep_college_count)#71]
Arguments: 100, [ca_state#17 ASC NULLS FIRST, cd_gender#19 ASC NULLS FIRST, cd_marital_status#20 ASC NULLS FIRST, cd_dep_count#21 ASC NULLS FIRST, cd_dep_employed_count#22 ASC NULLS FIRST, cd_dep_college_count#23 ASC NULLS FIRST], [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cnt1#60, avg(cd_dep_count)#61, max(cd_dep_count)#62, sum(cd_dep_count)#63, cd_dep_employed_count#22, cnt2#64, avg(cd_dep_employed_count)#65, max(cd_dep_employed_count)#66, sum(cd_dep_employed_count)#67, cd_dep_college_count#23, cnt3#68, avg(cd_dep_college_count)#69, max(cd_dep_college_count)#70, sum(cd_dep_college_count)#71]

===== Subqueries =====

Subquery:1 Hosting operator id = 6 Hosting Expression = ss_sold_date_sk#5 IN dynamicpruning#6
BroadcastExchange (55)
+- * Project (54)
   +- * Filter (53)
      +- * ColumnarToRow (52)
         +- Scan parquet spark_catalog.default.date_dim (51)


(51) Scan parquet spark_catalog.default.date_dim
Output [3]: [d_date_sk#7, d_year#72, d_qoy#73]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNotNull(d_year), IsNotNull(d_qoy), EqualTo(d_year,1999), LessThan(d_qoy,4), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_year:int,d_qoy:int>

(52) ColumnarToRow [codegen id : 1]
Input [3]: [d_date_sk#7, d_year#72, d_qoy#73]

(53) Filter [codegen id : 1]
Input [3]: [d_date_sk#7, d_year#72, d_qoy#73]
Condition : ((((isnotnull(d_year#72) AND isnotnull(d_qoy#73)) AND (d_year#72 = 1999)) AND (d_qoy#73 < 4)) AND isnotnull(d_date_sk#7))

(54) Project [codegen id : 1]
Output [1]: [d_date_sk#7]
Input [3]: [d_date_sk#7, d_year#72, d_qoy#73]

(55) BroadcastExchange
Input [1]: [d_date_sk#7]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=9]

Subquery:2 Hosting operator id = 14 Hosting Expression = ws_sold_date_sk#9 IN dynamicpruning#6

Subquery:3 Hosting operator id = 19 Hosting Expression = cs_sold_date_sk#13 IN dynamicpruning#6


