TakeOrderedAndProject [lochierarchy,s_state,rank_within_parent,total_sum,s_county]
  WholeStageCodegen (33)
    Project [total_sum,s_state,s_county,lochierarchy,rank_within_parent]
      InputAdapter
        Window [total_sum,lochierarchy,_w0]
          WholeStageCodegen (32)
            Sort [lochierarchy,_w0,total_sum]
              InputAdapter
                Exchange [lochierarchy,_w0] #1
                  WholeStageCodegen (31)
                    HashAggregate [total_sum,s_state,s_county,g_state,g_county,lochierarchy] [_w0]
                      InputAdapter
                        Exchange [total_sum,s_state,s_county,g_state,g_county,lochierarchy] #2
                          WholeStageCodegen (30)
                            HashAggregate [total_sum,s_state,s_county,g_state,g_county,lochierarchy]
                              InputAdapter
                                Union
                                  WholeStageCodegen (9)
                                    HashAggregate [s_state,s_county,sum] [sum(UnscaledValue(ss_net_profit)),total_sum,g_state,g_county,lochierarchy,sum]
                                      InputAdapter
                                        Exchange [s_state,s_county] #3
                                          WholeStageCodegen (8)
                                            HashAggregate [s_state,s_county,ss_net_profit] [sum,sum]
                                              Project [ss_net_profit,s_county,s_state]
                                                BroadcastHashJoin [ss_store_sk,s_store_sk]
                                                  Project [ss_store_sk,ss_net_profit]
                                                    BroadcastHashJoin [ss_sold_date_sk,d_date_sk]
                                                      Filter [ss_store_sk]
                                                        ColumnarToRow
                                                          InputAdapter
                                                            Scan parquet spark_catalog.default.store_sales [ss_store_sk,ss_net_profit,ss_sold_date_sk]
                                                              SubqueryBroadcast [d_date_sk] #1
                                                                BroadcastExchange #4
                                                                  WholeStageCodegen (1)
                                                                    Project [d_date_sk]
                                                                      Filter [d_month_seq,d_date_sk]
                                                                        ColumnarToRow
                                                                          InputAdapter
                                                                            Scan parquet spark_catalog.default.date_dim [d_date_sk,d_month_seq]
                                                      InputAdapter
                                                        ReusedExchange [d_date_sk] #4
                                                  InputAdapter
                                                    BroadcastExchange #5
                                                      WholeStageCodegen (7)
                                                        BroadcastHashJoin [s_state,s_state]
                                                          Filter [s_store_sk]
                                                            ColumnarToRow
                                                              InputAdapter
                                                                Scan parquet spark_catalog.default.store [s_store_sk,s_county,s_state]
                                                          InputAdapter
                                                            BroadcastExchange #6
                                                              WholeStageCodegen (6)
                                                                Project [s_state]
                                                                  Filter [ranking]
                                                                    InputAdapter
                                                                      Window [_w0,s_state]
                                                                        WindowGroupLimit [s_state,_w0]
                                                                          WholeStageCodegen (5)
                                                                            Sort [s_state,_w0]
                                                                              HashAggregate [sum] [sum(UnscaledValue(ss_net_profit)),_w0,s_state,sum]
                                                                                InputAdapter
                                                                                  Exchange [s_state] #7
                                                                                    WholeStageCodegen (4)
                                                                                      HashAggregate [s_state,ss_net_profit] [sum,sum]
                                                                                        Project [ss_net_profit,s_state]
                                                                                          BroadcastHashJoin [ss_store_sk,s_store_sk]
                                                                                            Project [ss_store_sk,ss_net_profit]
                                                                                              BroadcastHashJoin [ss_sold_date_sk,d_date_sk]
                                                                                                Filter [ss_store_sk]
                                                                                                  ColumnarToRow
                                                                                                    InputAdapter
                                                                                                      Scan parquet spark_catalog.default.store_sales [ss_store_sk,ss_net_profit,ss_sold_date_sk]
                                                                                                        ReusedSubquery [d_date_sk] #1
                                                                                                InputAdapter
                                                                                                  ReusedExchange [d_date_sk] #4
                                                                                            InputAdapter
                                                                                              BroadcastExchange #8
                                                                                                WholeStageCodegen (3)
                                                                                                  Filter [s_store_sk]
                                                                                                    ColumnarToRow
                                                                                                      InputAdapter
                                                                                                        Scan parquet spark_catalog.default.store [s_store_sk,s_state]
                                  WholeStageCodegen (19)
                                    HashAggregate [s_state,sum,isEmpty] [sum(total_sum),total_sum,s_county,g_state,g_county,lochierarchy,sum,isEmpty]
                                      InputAdapter
                                        Exchange [s_state] #9
                                          WholeStageCodegen (18)
                                            HashAggregate [s_state,total_sum] [sum,isEmpty,sum,isEmpty]
                                              HashAggregate [s_state,s_county,sum] [sum(UnscaledValue(ss_net_profit)),total_sum,sum]
                                                InputAdapter
                                                  ReusedExchange [s_state,s_county,sum] #3
                                  WholeStageCodegen (29)
                                    HashAggregate [sum,isEmpty] [sum(total_sum),total_sum,s_state,s_county,g_state,g_county,lochierarchy,sum,isEmpty]
                                      InputAdapter
                                        Exchange #10
                                          WholeStageCodegen (28)
                                            HashAggregate [total_sum] [sum,isEmpty,sum,isEmpty]
                                              HashAggregate [s_state,s_county,sum] [sum(UnscaledValue(ss_net_profit)),total_sum,sum]
                                                InputAdapter
                                                  ReusedExchange [s_state,s_county,sum] #3
