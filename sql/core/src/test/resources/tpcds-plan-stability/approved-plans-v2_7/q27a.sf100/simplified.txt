TakeOrderedAndProject [i_item_id,s_state,g_state,agg1,agg2,agg3,agg4]
  Union
    WholeStageCodegen (6)
      HashAggregate [i_item_id,s_state,sum,count,sum,count,sum,count,sum,count] [avg(agg1),avg(UnscaledValue(agg2)),avg(UnscaledValue(agg3)),avg(UnscaledValue(agg4)),g_state,agg1,agg2,agg3,agg4,sum,count,sum,count,sum,count,sum,count]
        InputAdapter
          Exchange [i_item_id,s_state] #1
            WholeStageCodegen (5)
              HashAggregate [i_item_id,s_state,agg1,agg2,agg3,agg4] [sum,count,sum,count,sum,count,sum,count,sum,count,sum,count,sum,count,sum,count]
                Project [i_item_id,s_state,ss_quantity,ss_list_price,ss_coupon_amt,ss_sales_price]
                  BroadcastHashJoin [ss_item_sk,i_item_sk]
                    Project [ss_item_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt,s_state]
                      BroadcastHashJoin [ss_store_sk,s_store_sk]
                        Project [ss_item_sk,ss_store_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt]
                          BroadcastHashJoin [ss_sold_date_sk,d_date_sk]
                            Project [ss_item_sk,ss_store_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt,ss_sold_date_sk]
                              BroadcastHashJoin [ss_cdemo_sk,cd_demo_sk]
                                Filter [ss_cdemo_sk,ss_store_sk,ss_item_sk]
                                  ColumnarToRow
                                    InputAdapter
                                      Scan parquet spark_catalog.default.store_sales [ss_item_sk,ss_cdemo_sk,ss_store_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt,ss_sold_date_sk]
                                        SubqueryBroadcast [d_date_sk] #1
                                          BroadcastExchange #2
                                            WholeStageCodegen (1)
                                              Project [d_date_sk]
                                                Filter [d_year,d_date_sk]
                                                  ColumnarToRow
                                                    InputAdapter
                                                      Scan parquet spark_catalog.default.date_dim [d_date_sk,d_year]
                                InputAdapter
                                  BroadcastExchange #3
                                    WholeStageCodegen (1)
                                      Project [cd_demo_sk]
                                        Filter [cd_gender,cd_marital_status,cd_education_status,cd_demo_sk]
                                          ColumnarToRow
                                            InputAdapter
                                              Scan parquet spark_catalog.default.customer_demographics [cd_demo_sk,cd_gender,cd_marital_status,cd_education_status]
                            InputAdapter
                              ReusedExchange [d_date_sk] #2
                        InputAdapter
                          BroadcastExchange #4
                            WholeStageCodegen (3)
                              Filter [s_state,s_store_sk]
                                ColumnarToRow
                                  InputAdapter
                                    Scan parquet spark_catalog.default.store [s_store_sk,s_state]
                    InputAdapter
                      BroadcastExchange #5
                        WholeStageCodegen (4)
                          Filter [i_item_sk]
                            ColumnarToRow
                              InputAdapter
                                Scan parquet spark_catalog.default.item [i_item_sk,i_item_id]
    WholeStageCodegen (12)
      HashAggregate [i_item_id,sum,count,sum,count,sum,count,sum,count] [avg(agg1),avg(UnscaledValue(agg2)),avg(UnscaledValue(agg3)),avg(UnscaledValue(agg4)),s_state,g_state,agg1,agg2,agg3,agg4,sum,count,sum,count,sum,count,sum,count]
        InputAdapter
          Exchange [i_item_id] #6
            WholeStageCodegen (11)
              HashAggregate [i_item_id,agg1,agg2,agg3,agg4] [sum,count,sum,count,sum,count,sum,count,sum,count,sum,count,sum,count,sum,count]
                Project [i_item_id,ss_quantity,ss_list_price,ss_coupon_amt,ss_sales_price]
                  BroadcastHashJoin [ss_item_sk,i_item_sk]
                    Project [ss_item_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt]
                      BroadcastHashJoin [ss_sold_date_sk,d_date_sk]
                        Project [ss_item_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt,ss_sold_date_sk]
                          BroadcastHashJoin [ss_store_sk,s_store_sk]
                            Project [ss_item_sk,ss_store_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt,ss_sold_date_sk]
                              BroadcastHashJoin [ss_cdemo_sk,cd_demo_sk]
                                Filter [ss_cdemo_sk,ss_store_sk,ss_item_sk]
                                  ColumnarToRow
                                    InputAdapter
                                      Scan parquet spark_catalog.default.store_sales [ss_item_sk,ss_cdemo_sk,ss_store_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt,ss_sold_date_sk]
                                        ReusedSubquery [d_date_sk] #1
                                InputAdapter
                                  ReusedExchange [cd_demo_sk] #3
                            InputAdapter
                              BroadcastExchange #7
                                WholeStageCodegen (8)
                                  Project [s_store_sk]
                                    Filter [s_state,s_store_sk]
                                      ColumnarToRow
                                        InputAdapter
                                          Scan parquet spark_catalog.default.store [s_store_sk,s_state]
                        InputAdapter
                          ReusedExchange [d_date_sk] #2
                    InputAdapter
                      ReusedExchange [i_item_sk,i_item_id] #5
    WholeStageCodegen (18)
      HashAggregate [sum,count,sum,count,sum,count,sum,count] [avg(agg1),avg(UnscaledValue(agg2)),avg(UnscaledValue(agg3)),avg(UnscaledValue(agg4)),i_item_id,s_state,g_state,agg1,agg2,agg3,agg4,sum,count,sum,count,sum,count,sum,count]
        InputAdapter
          Exchange #8
            WholeStageCodegen (17)
              HashAggregate [agg1,agg2,agg3,agg4] [sum,count,sum,count,sum,count,sum,count,sum,count,sum,count,sum,count,sum,count]
                Project [ss_quantity,ss_list_price,ss_coupon_amt,ss_sales_price]
                  BroadcastHashJoin [ss_item_sk,i_item_sk]
                    Project [ss_item_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt]
                      BroadcastHashJoin [ss_sold_date_sk,d_date_sk]
                        Project [ss_item_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt,ss_sold_date_sk]
                          BroadcastHashJoin [ss_store_sk,s_store_sk]
                            Project [ss_item_sk,ss_store_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt,ss_sold_date_sk]
                              BroadcastHashJoin [ss_cdemo_sk,cd_demo_sk]
                                Filter [ss_cdemo_sk,ss_store_sk,ss_item_sk]
                                  ColumnarToRow
                                    InputAdapter
                                      Scan parquet spark_catalog.default.store_sales [ss_item_sk,ss_cdemo_sk,ss_store_sk,ss_quantity,ss_list_price,ss_sales_price,ss_coupon_amt,ss_sold_date_sk]
                                        ReusedSubquery [d_date_sk] #1
                                InputAdapter
                                  ReusedExchange [cd_demo_sk] #3
                            InputAdapter
                              ReusedExchange [s_store_sk] #7
                        InputAdapter
                          ReusedExchange [d_date_sk] #2
                    InputAdapter
                      BroadcastExchange #9
                        WholeStageCodegen (16)
                          Filter [i_item_sk]
                            ColumnarToRow
                              InputAdapter
                                Scan parquet spark_catalog.default.item [i_item_sk]
