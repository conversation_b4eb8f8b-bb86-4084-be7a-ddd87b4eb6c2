== Physical Plan ==
TakeOrderedAndProject (53)
+- * HashAggregate (52)
   +- Exchange (51)
      +- * HashAggregate (50)
         +- * Project (49)
            +- * SortMergeJoin Inner (48)
               :- * Sort (42)
               :  +- Exchange (41)
               :     +- * Project (40)
               :        +- * SortMergeJoin Inner (39)
               :           :- * Sort (33)
               :           :  +- Exchange (32)
               :           :     +- * Project (31)
               :           :        +- * Filter (30)
               :           :           +- * SortMergeJoin ExistenceJoin(exists#1) (29)
               :           :              :- * SortMergeJoin ExistenceJoin(exists#2) (21)
               :           :              :  :- * SortMergeJoin LeftSemi (13)
               :           :              :  :  :- * Sort (5)
               :           :              :  :  :  +- Exchange (4)
               :           :              :  :  :     +- * Filter (3)
               :           :              :  :  :        +- * ColumnarToRow (2)
               :           :              :  :  :           +- Scan parquet spark_catalog.default.customer (1)
               :           :              :  :  +- * Sort (12)
               :           :              :  :     +- Exchange (11)
               :           :              :  :        +- * Project (10)
               :           :              :  :           +- * BroadcastHashJoin Inner BuildRight (9)
               :           :              :  :              :- * ColumnarToRow (7)
               :           :              :  :              :  +- Scan parquet spark_catalog.default.store_sales (6)
               :           :              :  :              +- ReusedExchange (8)
               :           :              :  +- * Sort (20)
               :           :              :     +- Exchange (19)
               :           :              :        +- * Project (18)
               :           :              :           +- * BroadcastHashJoin Inner BuildRight (17)
               :           :              :              :- * ColumnarToRow (15)
               :           :              :              :  +- Scan parquet spark_catalog.default.web_sales (14)
               :           :              :              +- ReusedExchange (16)
               :           :              +- * Sort (28)
               :           :                 +- Exchange (27)
               :           :                    +- * Project (26)
               :           :                       +- * BroadcastHashJoin Inner BuildRight (25)
               :           :                          :- * ColumnarToRow (23)
               :           :                          :  +- Scan parquet spark_catalog.default.catalog_sales (22)
               :           :                          +- ReusedExchange (24)
               :           +- * Sort (38)
               :              +- Exchange (37)
               :                 +- * Filter (36)
               :                    +- * ColumnarToRow (35)
               :                       +- Scan parquet spark_catalog.default.customer_address (34)
               +- * Sort (47)
                  +- Exchange (46)
                     +- * Filter (45)
                        +- * ColumnarToRow (44)
                           +- Scan parquet spark_catalog.default.customer_demographics (43)


(1) Scan parquet spark_catalog.default.customer
Output [3]: [c_customer_sk#3, c_current_cdemo_sk#4, c_current_addr_sk#5]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer]
PushedFilters: [IsNotNull(c_current_addr_sk), IsNotNull(c_current_cdemo_sk)]
ReadSchema: struct<c_customer_sk:int,c_current_cdemo_sk:int,c_current_addr_sk:int>

(2) ColumnarToRow [codegen id : 1]
Input [3]: [c_customer_sk#3, c_current_cdemo_sk#4, c_current_addr_sk#5]

(3) Filter [codegen id : 1]
Input [3]: [c_customer_sk#3, c_current_cdemo_sk#4, c_current_addr_sk#5]
Condition : (isnotnull(c_current_addr_sk#5) AND isnotnull(c_current_cdemo_sk#4))

(4) Exchange
Input [3]: [c_customer_sk#3, c_current_cdemo_sk#4, c_current_addr_sk#5]
Arguments: hashpartitioning(c_customer_sk#3, 5), ENSURE_REQUIREMENTS, [plan_id=1]

(5) Sort [codegen id : 2]
Input [3]: [c_customer_sk#3, c_current_cdemo_sk#4, c_current_addr_sk#5]
Arguments: [c_customer_sk#3 ASC NULLS FIRST], false, 0

(6) Scan parquet spark_catalog.default.store_sales
Output [2]: [ss_customer_sk#6, ss_sold_date_sk#7]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ss_sold_date_sk#7), dynamicpruningexpression(ss_sold_date_sk#7 IN dynamicpruning#8)]
ReadSchema: struct<ss_customer_sk:int>

(7) ColumnarToRow [codegen id : 4]
Input [2]: [ss_customer_sk#6, ss_sold_date_sk#7]

(8) ReusedExchange [Reuses operator id: 58]
Output [1]: [d_date_sk#9]

(9) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [ss_sold_date_sk#7]
Right keys [1]: [d_date_sk#9]
Join type: Inner
Join condition: None

(10) Project [codegen id : 4]
Output [1]: [ss_customer_sk#6]
Input [3]: [ss_customer_sk#6, ss_sold_date_sk#7, d_date_sk#9]

(11) Exchange
Input [1]: [ss_customer_sk#6]
Arguments: hashpartitioning(ss_customer_sk#6, 5), ENSURE_REQUIREMENTS, [plan_id=2]

(12) Sort [codegen id : 5]
Input [1]: [ss_customer_sk#6]
Arguments: [ss_customer_sk#6 ASC NULLS FIRST], false, 0

(13) SortMergeJoin [codegen id : 6]
Left keys [1]: [c_customer_sk#3]
Right keys [1]: [ss_customer_sk#6]
Join type: LeftSemi
Join condition: None

(14) Scan parquet spark_catalog.default.web_sales
Output [2]: [ws_bill_customer_sk#10, ws_sold_date_sk#11]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ws_sold_date_sk#11), dynamicpruningexpression(ws_sold_date_sk#11 IN dynamicpruning#8)]
ReadSchema: struct<ws_bill_customer_sk:int>

(15) ColumnarToRow [codegen id : 8]
Input [2]: [ws_bill_customer_sk#10, ws_sold_date_sk#11]

(16) ReusedExchange [Reuses operator id: 58]
Output [1]: [d_date_sk#12]

(17) BroadcastHashJoin [codegen id : 8]
Left keys [1]: [ws_sold_date_sk#11]
Right keys [1]: [d_date_sk#12]
Join type: Inner
Join condition: None

(18) Project [codegen id : 8]
Output [1]: [ws_bill_customer_sk#10]
Input [3]: [ws_bill_customer_sk#10, ws_sold_date_sk#11, d_date_sk#12]

(19) Exchange
Input [1]: [ws_bill_customer_sk#10]
Arguments: hashpartitioning(ws_bill_customer_sk#10, 5), ENSURE_REQUIREMENTS, [plan_id=3]

(20) Sort [codegen id : 9]
Input [1]: [ws_bill_customer_sk#10]
Arguments: [ws_bill_customer_sk#10 ASC NULLS FIRST], false, 0

(21) SortMergeJoin [codegen id : 10]
Left keys [1]: [c_customer_sk#3]
Right keys [1]: [ws_bill_customer_sk#10]
Join type: ExistenceJoin(exists#2)
Join condition: None

(22) Scan parquet spark_catalog.default.catalog_sales
Output [2]: [cs_ship_customer_sk#13, cs_sold_date_sk#14]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(cs_sold_date_sk#14), dynamicpruningexpression(cs_sold_date_sk#14 IN dynamicpruning#8)]
ReadSchema: struct<cs_ship_customer_sk:int>

(23) ColumnarToRow [codegen id : 12]
Input [2]: [cs_ship_customer_sk#13, cs_sold_date_sk#14]

(24) ReusedExchange [Reuses operator id: 58]
Output [1]: [d_date_sk#15]

(25) BroadcastHashJoin [codegen id : 12]
Left keys [1]: [cs_sold_date_sk#14]
Right keys [1]: [d_date_sk#15]
Join type: Inner
Join condition: None

(26) Project [codegen id : 12]
Output [1]: [cs_ship_customer_sk#13]
Input [3]: [cs_ship_customer_sk#13, cs_sold_date_sk#14, d_date_sk#15]

(27) Exchange
Input [1]: [cs_ship_customer_sk#13]
Arguments: hashpartitioning(cs_ship_customer_sk#13, 5), ENSURE_REQUIREMENTS, [plan_id=4]

(28) Sort [codegen id : 13]
Input [1]: [cs_ship_customer_sk#13]
Arguments: [cs_ship_customer_sk#13 ASC NULLS FIRST], false, 0

(29) SortMergeJoin [codegen id : 14]
Left keys [1]: [c_customer_sk#3]
Right keys [1]: [cs_ship_customer_sk#13]
Join type: ExistenceJoin(exists#1)
Join condition: None

(30) Filter [codegen id : 14]
Input [5]: [c_customer_sk#3, c_current_cdemo_sk#4, c_current_addr_sk#5, exists#2, exists#1]
Condition : (exists#2 OR exists#1)

(31) Project [codegen id : 14]
Output [2]: [c_current_cdemo_sk#4, c_current_addr_sk#5]
Input [5]: [c_customer_sk#3, c_current_cdemo_sk#4, c_current_addr_sk#5, exists#2, exists#1]

(32) Exchange
Input [2]: [c_current_cdemo_sk#4, c_current_addr_sk#5]
Arguments: hashpartitioning(c_current_addr_sk#5, 5), ENSURE_REQUIREMENTS, [plan_id=5]

(33) Sort [codegen id : 15]
Input [2]: [c_current_cdemo_sk#4, c_current_addr_sk#5]
Arguments: [c_current_addr_sk#5 ASC NULLS FIRST], false, 0

(34) Scan parquet spark_catalog.default.customer_address
Output [2]: [ca_address_sk#16, ca_state#17]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer_address]
PushedFilters: [IsNotNull(ca_address_sk)]
ReadSchema: struct<ca_address_sk:int,ca_state:string>

(35) ColumnarToRow [codegen id : 16]
Input [2]: [ca_address_sk#16, ca_state#17]

(36) Filter [codegen id : 16]
Input [2]: [ca_address_sk#16, ca_state#17]
Condition : isnotnull(ca_address_sk#16)

(37) Exchange
Input [2]: [ca_address_sk#16, ca_state#17]
Arguments: hashpartitioning(ca_address_sk#16, 5), ENSURE_REQUIREMENTS, [plan_id=6]

(38) Sort [codegen id : 17]
Input [2]: [ca_address_sk#16, ca_state#17]
Arguments: [ca_address_sk#16 ASC NULLS FIRST], false, 0

(39) SortMergeJoin [codegen id : 18]
Left keys [1]: [c_current_addr_sk#5]
Right keys [1]: [ca_address_sk#16]
Join type: Inner
Join condition: None

(40) Project [codegen id : 18]
Output [2]: [c_current_cdemo_sk#4, ca_state#17]
Input [4]: [c_current_cdemo_sk#4, c_current_addr_sk#5, ca_address_sk#16, ca_state#17]

(41) Exchange
Input [2]: [c_current_cdemo_sk#4, ca_state#17]
Arguments: hashpartitioning(c_current_cdemo_sk#4, 5), ENSURE_REQUIREMENTS, [plan_id=7]

(42) Sort [codegen id : 19]
Input [2]: [c_current_cdemo_sk#4, ca_state#17]
Arguments: [c_current_cdemo_sk#4 ASC NULLS FIRST], false, 0

(43) Scan parquet spark_catalog.default.customer_demographics
Output [6]: [cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer_demographics]
PushedFilters: [IsNotNull(cd_demo_sk)]
ReadSchema: struct<cd_demo_sk:int,cd_gender:string,cd_marital_status:string,cd_dep_count:int,cd_dep_employed_count:int,cd_dep_college_count:int>

(44) ColumnarToRow [codegen id : 20]
Input [6]: [cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]

(45) Filter [codegen id : 20]
Input [6]: [cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Condition : isnotnull(cd_demo_sk#18)

(46) Exchange
Input [6]: [cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Arguments: hashpartitioning(cd_demo_sk#18, 5), ENSURE_REQUIREMENTS, [plan_id=8]

(47) Sort [codegen id : 21]
Input [6]: [cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Arguments: [cd_demo_sk#18 ASC NULLS FIRST], false, 0

(48) SortMergeJoin [codegen id : 22]
Left keys [1]: [c_current_cdemo_sk#4]
Right keys [1]: [cd_demo_sk#18]
Join type: Inner
Join condition: None

(49) Project [codegen id : 22]
Output [6]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Input [8]: [c_current_cdemo_sk#4, ca_state#17, cd_demo_sk#18, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]

(50) HashAggregate [codegen id : 22]
Input [6]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Keys [6]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Functions [10]: [partial_count(1), partial_avg(cd_dep_count#21), partial_max(cd_dep_count#21), partial_sum(cd_dep_count#21), partial_avg(cd_dep_employed_count#22), partial_max(cd_dep_employed_count#22), partial_sum(cd_dep_employed_count#22), partial_avg(cd_dep_college_count#23), partial_max(cd_dep_college_count#23), partial_sum(cd_dep_college_count#23)]
Aggregate Attributes [13]: [count#24, sum#25, count#26, max#27, sum#28, sum#29, count#30, max#31, sum#32, sum#33, count#34, max#35, sum#36]
Results [19]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23, count#37, sum#38, count#39, max#40, sum#41, sum#42, count#43, max#44, sum#45, sum#46, count#47, max#48, sum#49]

(51) Exchange
Input [19]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23, count#37, sum#38, count#39, max#40, sum#41, sum#42, count#43, max#44, sum#45, sum#46, count#47, max#48, sum#49]
Arguments: hashpartitioning(ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23, 5), ENSURE_REQUIREMENTS, [plan_id=9]

(52) HashAggregate [codegen id : 23]
Input [19]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23, count#37, sum#38, count#39, max#40, sum#41, sum#42, count#43, max#44, sum#45, sum#46, count#47, max#48, sum#49]
Keys [6]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cd_dep_employed_count#22, cd_dep_college_count#23]
Functions [10]: [count(1), avg(cd_dep_count#21), max(cd_dep_count#21), sum(cd_dep_count#21), avg(cd_dep_employed_count#22), max(cd_dep_employed_count#22), sum(cd_dep_employed_count#22), avg(cd_dep_college_count#23), max(cd_dep_college_count#23), sum(cd_dep_college_count#23)]
Aggregate Attributes [10]: [count(1)#50, avg(cd_dep_count#21)#51, max(cd_dep_count#21)#52, sum(cd_dep_count#21)#53, avg(cd_dep_employed_count#22)#54, max(cd_dep_employed_count#22)#55, sum(cd_dep_employed_count#22)#56, avg(cd_dep_college_count#23)#57, max(cd_dep_college_count#23)#58, sum(cd_dep_college_count#23)#59]
Results [18]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, count(1)#50 AS cnt1#60, avg(cd_dep_count#21)#51 AS avg(cd_dep_count)#61, max(cd_dep_count#21)#52 AS max(cd_dep_count)#62, sum(cd_dep_count#21)#53 AS sum(cd_dep_count)#63, cd_dep_employed_count#22, count(1)#50 AS cnt2#64, avg(cd_dep_employed_count#22)#54 AS avg(cd_dep_employed_count)#65, max(cd_dep_employed_count#22)#55 AS max(cd_dep_employed_count)#66, sum(cd_dep_employed_count#22)#56 AS sum(cd_dep_employed_count)#67, cd_dep_college_count#23, count(1)#50 AS cnt3#68, avg(cd_dep_college_count#23)#57 AS avg(cd_dep_college_count)#69, max(cd_dep_college_count#23)#58 AS max(cd_dep_college_count)#70, sum(cd_dep_college_count#23)#59 AS sum(cd_dep_college_count)#71]

(53) TakeOrderedAndProject
Input [18]: [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cnt1#60, avg(cd_dep_count)#61, max(cd_dep_count)#62, sum(cd_dep_count)#63, cd_dep_employed_count#22, cnt2#64, avg(cd_dep_employed_count)#65, max(cd_dep_employed_count)#66, sum(cd_dep_employed_count)#67, cd_dep_college_count#23, cnt3#68, avg(cd_dep_college_count)#69, max(cd_dep_college_count)#70, sum(cd_dep_college_count)#71]
Arguments: 100, [ca_state#17 ASC NULLS FIRST, cd_gender#19 ASC NULLS FIRST, cd_marital_status#20 ASC NULLS FIRST, cd_dep_count#21 ASC NULLS FIRST, cd_dep_employed_count#22 ASC NULLS FIRST, cd_dep_college_count#23 ASC NULLS FIRST], [ca_state#17, cd_gender#19, cd_marital_status#20, cd_dep_count#21, cnt1#60, avg(cd_dep_count)#61, max(cd_dep_count)#62, sum(cd_dep_count)#63, cd_dep_employed_count#22, cnt2#64, avg(cd_dep_employed_count)#65, max(cd_dep_employed_count)#66, sum(cd_dep_employed_count)#67, cd_dep_college_count#23, cnt3#68, avg(cd_dep_college_count)#69, max(cd_dep_college_count)#70, sum(cd_dep_college_count)#71]

===== Subqueries =====

Subquery:1 Hosting operator id = 6 Hosting Expression = ss_sold_date_sk#7 IN dynamicpruning#8
BroadcastExchange (58)
+- * Project (57)
   +- * Filter (56)
      +- * ColumnarToRow (55)
         +- Scan parquet spark_catalog.default.date_dim (54)


(54) Scan parquet spark_catalog.default.date_dim
Output [3]: [d_date_sk#9, d_year#72, d_qoy#73]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNotNull(d_year), IsNotNull(d_qoy), EqualTo(d_year,2002), LessThan(d_qoy,4), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_year:int,d_qoy:int>

(55) ColumnarToRow [codegen id : 1]
Input [3]: [d_date_sk#9, d_year#72, d_qoy#73]

(56) Filter [codegen id : 1]
Input [3]: [d_date_sk#9, d_year#72, d_qoy#73]
Condition : ((((isnotnull(d_year#72) AND isnotnull(d_qoy#73)) AND (d_year#72 = 2002)) AND (d_qoy#73 < 4)) AND isnotnull(d_date_sk#9))

(57) Project [codegen id : 1]
Output [1]: [d_date_sk#9]
Input [3]: [d_date_sk#9, d_year#72, d_qoy#73]

(58) BroadcastExchange
Input [1]: [d_date_sk#9]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=10]

Subquery:2 Hosting operator id = 14 Hosting Expression = ws_sold_date_sk#11 IN dynamicpruning#8

Subquery:3 Hosting operator id = 22 Hosting Expression = cs_sold_date_sk#14 IN dynamicpruning#8


