WholeStageCodegen (7)
  Sort [cnt]
    InputAdapter
      Exchange [cnt] #1
        WholeStageCodegen (6)
          Project [c_last_name,c_first_name,c_salutation,c_preferred_cust_flag,ss_ticket_number,cnt]
            BroadcastHashJoin [ss_customer_sk,c_customer_sk]
              Filter [cnt]
                HashAggregate [ss_ticket_number,ss_customer_sk,count] [count(1),cnt,count]
                  InputAdapter
                    Exchange [ss_ticket_number,ss_customer_sk] #2
                      WholeStageCodegen (4)
                        HashAggregate [ss_ticket_number,ss_customer_sk] [count,count]
                          Project [ss_customer_sk,ss_ticket_number]
                            BroadcastHashJoin [ss_hdemo_sk,hd_demo_sk]
                              Project [ss_customer_sk,ss_hdemo_sk,ss_ticket_number]
                                BroadcastHashJoin [ss_store_sk,s_store_sk]
                                  Project [ss_customer_sk,ss_hdemo_sk,ss_store_sk,ss_ticket_number]
                                    BroadcastHashJoin [ss_sold_date_sk,d_date_sk]
                                      Filter [ss_store_sk,ss_hdemo_sk,ss_customer_sk]
                                        ColumnarToRow
                                          InputAdapter
                                            Scan parquet spark_catalog.default.store_sales [ss_customer_sk,ss_hdemo_sk,ss_store_sk,ss_ticket_number,ss_sold_date_sk]
                                              SubqueryBroadcast [d_date_sk] #1
                                                BroadcastExchange #3
                                                  WholeStageCodegen (1)
                                                    Project [d_date_sk]
                                                      Filter [d_dom,d_year,d_date_sk]
                                                        ColumnarToRow
                                                          InputAdapter
                                                            Scan parquet spark_catalog.default.date_dim [d_date_sk,d_year,d_dom]
                                      InputAdapter
                                        ReusedExchange [d_date_sk] #3
                                  InputAdapter
                                    BroadcastExchange #4
                                      WholeStageCodegen (2)
                                        Project [s_store_sk]
                                          Filter [s_county,s_store_sk]
                                            ColumnarToRow
                                              InputAdapter
                                                Scan parquet spark_catalog.default.store [s_store_sk,s_county]
                              InputAdapter
                                BroadcastExchange #5
                                  WholeStageCodegen (3)
                                    Project [hd_demo_sk]
                                      Filter [hd_vehicle_count,hd_buy_potential,hd_dep_count,hd_demo_sk]
                                        ColumnarToRow
                                          InputAdapter
                                            Scan parquet spark_catalog.default.household_demographics [hd_demo_sk,hd_buy_potential,hd_dep_count,hd_vehicle_count]
              InputAdapter
                BroadcastExchange #6
                  WholeStageCodegen (5)
                    Filter [c_customer_sk]
                      ColumnarToRow
                        InputAdapter
                          Scan parquet spark_catalog.default.customer [c_customer_sk,c_salutation,c_first_name,c_last_name,c_preferred_cust_flag]
