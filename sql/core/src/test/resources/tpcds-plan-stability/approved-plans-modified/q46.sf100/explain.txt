== Physical Plan ==
TakeOrderedAndProject (47)
+- * Project (46)
   +- * SortMergeJoin Inner (45)
      :- * Sort (42)
      :  +- Exchange (41)
      :     +- * Project (40)
      :        +- * SortMergeJoin Inner (39)
      :           :- * Sort (33)
      :           :  +- Exchange (32)
      :           :     +- * HashAggregate (31)
      :           :        +- * HashAggregate (30)
      :           :           +- * Project (29)
      :           :              +- * SortMergeJoin Inner (28)
      :           :                 :- * Sort (22)
      :           :                 :  +- Exchange (21)
      :           :                 :     +- * Project (20)
      :           :                 :        +- * BroadcastHashJoin Inner BuildRight (19)
      :           :                 :           :- * Project (13)
      :           :                 :           :  +- * BroadcastHashJoin Inner BuildRight (12)
      :           :                 :           :     :- * Project (6)
      :           :                 :           :     :  +- * BroadcastHashJoin Inner BuildRight (5)
      :           :                 :           :     :     :- * Filter (3)
      :           :                 :           :     :     :  +- * ColumnarToRow (2)
      :           :                 :           :     :     :     +- <PERSON>an parquet spark_catalog.default.store_sales (1)
      :           :                 :           :     :     +- ReusedExchange (4)
      :           :                 :           :     +- BroadcastExchange (11)
      :           :                 :           :        +- * Project (10)
      :           :                 :           :           +- * Filter (9)
      :           :                 :           :              +- * ColumnarToRow (8)
      :           :                 :           :                 +- Scan parquet spark_catalog.default.store (7)
      :           :                 :           +- BroadcastExchange (18)
      :           :                 :              +- * Project (17)
      :           :                 :                 +- * Filter (16)
      :           :                 :                    +- * ColumnarToRow (15)
      :           :                 :                       +- Scan parquet spark_catalog.default.household_demographics (14)
      :           :                 +- * Sort (27)
      :           :                    +- Exchange (26)
      :           :                       +- * Filter (25)
      :           :                          +- * ColumnarToRow (24)
      :           :                             +- Scan parquet spark_catalog.default.customer_address (23)
      :           +- * Sort (38)
      :              +- Exchange (37)
      :                 +- * Filter (36)
      :                    +- * ColumnarToRow (35)
      :                       +- Scan parquet spark_catalog.default.customer (34)
      +- * Sort (44)
         +- ReusedExchange (43)


(1) Scan parquet spark_catalog.default.store_sales
Output [8]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ss_sold_date_sk#8]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [ss_sold_date_sk#8 INSET 2451181, 2451182, 2451188, 2451189, 2451195, 2451196, 2451202, 2451203, 2451209, 2451210, 2451216, 2451217, 2451223, 2451224, 2451230, 2451231, 2451237, 2451238, 2451244, 2451245, 2451251, 2451252, 2451258, 2451259, 2451265, 2451266, 2451272, 2451273, 2451279, 2451280, 2451286, 2451287, 2451293, 2451294, 2451300, 2451301, 2451307, 2451308, 2451314, 2451315, 2451321, 2451322, 2451328, 2451329, 2451335, 2451336, 2451342, 2451343, 2451349, 2451350, 2451356, 2451357, 2451363, 2451364, 2451370, 2451371, 2451377, 2451378, 2451384, 2451385, 2451391, 2451392, 2451398, 2451399, 2451405, 2451406, 2451412, 2451413, 2451419, 2451420, 2451426, 2451427, 2451433, 2451434, 2451440, 2451441, 2451447, 2451448, 2451454, 2451455, 2451461, 2451462, 2451468, 2451469, 2451475, 2451476, 2451482, 2451483, 2451489, 2451490, 2451496, 2451497, 2451503, 2451504, 2451510, 2451511, 2451517, 2451518, 2451524, 2451525, 2451531, 2451532, 2451538, 2451539, 2451545, 2451546, 2451552, 2451553, 2451559, 2451560, 2451566, 2451567, 2451573, 2451574, 2451580, 2451581, 2451587, 2451588, 2451594, 2451595, 2451601, 2451602, 2451608, 2451609, 2451615, 2451616, 2451622, 2451623, 2451629, 2451630, 2451636, 2451637, 2451643, 2451644, 2451650, 2451651, 2451657, 2451658, 2451664, 2451665, 2451671, 2451672, 2451678, 2451679, 2451685, 2451686, 2451692, 2451693, 2451699, 2451700, 2451706, 2451707, 2451713, 2451714, 2451720, 2451721, 2451727, 2451728, 2451734, 2451735, 2451741, 2451742, 2451748, 2451749, 2451755, 2451756, 2451762, 2451763, 2451769, 2451770, 2451776, 2451777, 2451783, 2451784, 2451790, 2451791, 2451797, 2451798, 2451804, 2451805, 2451811, 2451812, 2451818, 2451819, 2451825, 2451826, 2451832, 2451833, 2451839, 2451840, 2451846, 2451847, 2451853, 2451854, 2451860, 2451861, 2451867, 2451868, 2451874, 2451875, 2451881, 2451882, 2451888, 2451889, 2451895, 2451896, 2451902, 2451903, 2451909, 2451910, 2451916, 2451917, 2451923, 2451924, 2451930, 2451931, 2451937, 2451938, 2451944, 2451945, 2451951, 2451952, 2451958, 2451959, 2451965, 2451966, 2451972, 2451973, 2451979, 2451980, 2451986, 2451987, 2451993, 2451994, 2452000, 2452001, 2452007, 2452008, 2452014, 2452015, 2452021, 2452022, 2452028, 2452029, 2452035, 2452036, 2452042, 2452043, 2452049, 2452050, 2452056, 2452057, 2452063, 2452064, 2452070, 2452071, 2452077, 2452078, 2452084, 2452085, 2452091, 2452092, 2452098, 2452099, 2452105, 2452106, 2452112, 2452113, 2452119, 2452120, 2452126, 2452127, 2452133, 2452134, 2452140, 2452141, 2452147, 2452148, 2452154, 2452155, 2452161, 2452162, 2452168, 2452169, 2452175, 2452176, 2452182, 2452183, 2452189, 2452190, 2452196, 2452197, 2452203, 2452204, 2452210, 2452211, 2452217, 2452218, 2452224, 2452225, 2452231, 2452232, 2452238, 2452239, 2452245, 2452246, 2452252, 2452253, 2452259, 2452260, 2452266, 2452267, 2452273, 2452274, isnotnull(ss_sold_date_sk#8), dynamicpruningexpression(ss_sold_date_sk#8 IN dynamicpruning#9)]
PushedFilters: [IsNotNull(ss_store_sk), IsNotNull(ss_hdemo_sk), IsNotNull(ss_addr_sk), IsNotNull(ss_customer_sk)]
ReadSchema: struct<ss_customer_sk:int,ss_hdemo_sk:int,ss_addr_sk:int,ss_store_sk:int,ss_ticket_number:int,ss_coupon_amt:decimal(7,2),ss_net_profit:decimal(7,2)>

(2) ColumnarToRow [codegen id : 4]
Input [8]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ss_sold_date_sk#8]

(3) Filter [codegen id : 4]
Input [8]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ss_sold_date_sk#8]
Condition : (((isnotnull(ss_store_sk#4) AND isnotnull(ss_hdemo_sk#2)) AND isnotnull(ss_addr_sk#3)) AND isnotnull(ss_customer_sk#1))

(4) ReusedExchange [Reuses operator id: 52]
Output [1]: [d_date_sk#10]

(5) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [ss_sold_date_sk#8]
Right keys [1]: [d_date_sk#10]
Join type: Inner
Join condition: None

(6) Project [codegen id : 4]
Output [7]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7]
Input [9]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ss_sold_date_sk#8, d_date_sk#10]

(7) Scan parquet spark_catalog.default.store
Output [2]: [s_store_sk#11, s_city#12]
Batched: true
Location [not included in comparison]/{warehouse_dir}/store]
PushedFilters: [In(s_city, [Brownsville,Concord,Greenville,Midway,Spring Hill]), IsNotNull(s_store_sk)]
ReadSchema: struct<s_store_sk:int,s_city:string>

(8) ColumnarToRow [codegen id : 2]
Input [2]: [s_store_sk#11, s_city#12]

(9) Filter [codegen id : 2]
Input [2]: [s_store_sk#11, s_city#12]
Condition : (s_city#12 IN (Midway,Concord,Spring Hill,Brownsville,Greenville) AND isnotnull(s_store_sk#11))

(10) Project [codegen id : 2]
Output [1]: [s_store_sk#11]
Input [2]: [s_store_sk#11, s_city#12]

(11) BroadcastExchange
Input [1]: [s_store_sk#11]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=1]

(12) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [ss_store_sk#4]
Right keys [1]: [s_store_sk#11]
Join type: Inner
Join condition: None

(13) Project [codegen id : 4]
Output [6]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7]
Input [8]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, s_store_sk#11]

(14) Scan parquet spark_catalog.default.household_demographics
Output [3]: [hd_demo_sk#13, hd_dep_count#14, hd_vehicle_count#15]
Batched: true
Location [not included in comparison]/{warehouse_dir}/household_demographics]
PushedFilters: [Or(EqualTo(hd_dep_count,5),EqualTo(hd_vehicle_count,3)), IsNotNull(hd_demo_sk)]
ReadSchema: struct<hd_demo_sk:int,hd_dep_count:int,hd_vehicle_count:int>

(15) ColumnarToRow [codegen id : 3]
Input [3]: [hd_demo_sk#13, hd_dep_count#14, hd_vehicle_count#15]

(16) Filter [codegen id : 3]
Input [3]: [hd_demo_sk#13, hd_dep_count#14, hd_vehicle_count#15]
Condition : (((hd_dep_count#14 = 5) OR (hd_vehicle_count#15 = 3)) AND isnotnull(hd_demo_sk#13))

(17) Project [codegen id : 3]
Output [1]: [hd_demo_sk#13]
Input [3]: [hd_demo_sk#13, hd_dep_count#14, hd_vehicle_count#15]

(18) BroadcastExchange
Input [1]: [hd_demo_sk#13]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=2]

(19) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [ss_hdemo_sk#2]
Right keys [1]: [hd_demo_sk#13]
Join type: Inner
Join condition: None

(20) Project [codegen id : 4]
Output [5]: [ss_customer_sk#1, ss_addr_sk#3, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7]
Input [7]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, hd_demo_sk#13]

(21) Exchange
Input [5]: [ss_customer_sk#1, ss_addr_sk#3, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7]
Arguments: hashpartitioning(ss_addr_sk#3, 5), ENSURE_REQUIREMENTS, [plan_id=3]

(22) Sort [codegen id : 5]
Input [5]: [ss_customer_sk#1, ss_addr_sk#3, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7]
Arguments: [ss_addr_sk#3 ASC NULLS FIRST], false, 0

(23) Scan parquet spark_catalog.default.customer_address
Output [2]: [ca_address_sk#16, ca_city#17]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer_address]
PushedFilters: [IsNotNull(ca_address_sk), IsNotNull(ca_city)]
ReadSchema: struct<ca_address_sk:int,ca_city:string>

(24) ColumnarToRow [codegen id : 6]
Input [2]: [ca_address_sk#16, ca_city#17]

(25) Filter [codegen id : 6]
Input [2]: [ca_address_sk#16, ca_city#17]
Condition : (isnotnull(ca_address_sk#16) AND isnotnull(ca_city#17))

(26) Exchange
Input [2]: [ca_address_sk#16, ca_city#17]
Arguments: hashpartitioning(ca_address_sk#16, 5), ENSURE_REQUIREMENTS, [plan_id=4]

(27) Sort [codegen id : 7]
Input [2]: [ca_address_sk#16, ca_city#17]
Arguments: [ca_address_sk#16 ASC NULLS FIRST], false, 0

(28) SortMergeJoin [codegen id : 8]
Left keys [1]: [ss_addr_sk#3]
Right keys [1]: [ca_address_sk#16]
Join type: Inner
Join condition: None

(29) Project [codegen id : 8]
Output [6]: [ss_customer_sk#1, ss_addr_sk#3, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ca_city#17]
Input [7]: [ss_customer_sk#1, ss_addr_sk#3, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ca_address_sk#16, ca_city#17]

(30) HashAggregate [codegen id : 8]
Input [6]: [ss_customer_sk#1, ss_addr_sk#3, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ca_city#17]
Keys [4]: [ss_ticket_number#5, ss_customer_sk#1, ss_addr_sk#3, ca_city#17]
Functions [2]: [partial_sum(UnscaledValue(ss_coupon_amt#6)), partial_sum(UnscaledValue(ss_net_profit#7))]
Aggregate Attributes [2]: [sum#18, sum#19]
Results [6]: [ss_ticket_number#5, ss_customer_sk#1, ss_addr_sk#3, ca_city#17, sum#20, sum#21]

(31) HashAggregate [codegen id : 8]
Input [6]: [ss_ticket_number#5, ss_customer_sk#1, ss_addr_sk#3, ca_city#17, sum#20, sum#21]
Keys [4]: [ss_ticket_number#5, ss_customer_sk#1, ss_addr_sk#3, ca_city#17]
Functions [2]: [sum(UnscaledValue(ss_coupon_amt#6)), sum(UnscaledValue(ss_net_profit#7))]
Aggregate Attributes [2]: [sum(UnscaledValue(ss_coupon_amt#6))#22, sum(UnscaledValue(ss_net_profit#7))#23]
Results [5]: [ss_ticket_number#5, ss_customer_sk#1, ca_city#17 AS bought_city#24, MakeDecimal(sum(UnscaledValue(ss_coupon_amt#6))#22,17,2) AS amt#25, MakeDecimal(sum(UnscaledValue(ss_net_profit#7))#23,17,2) AS profit#26]

(32) Exchange
Input [5]: [ss_ticket_number#5, ss_customer_sk#1, bought_city#24, amt#25, profit#26]
Arguments: hashpartitioning(ss_customer_sk#1, 5), ENSURE_REQUIREMENTS, [plan_id=5]

(33) Sort [codegen id : 9]
Input [5]: [ss_ticket_number#5, ss_customer_sk#1, bought_city#24, amt#25, profit#26]
Arguments: [ss_customer_sk#1 ASC NULLS FIRST], false, 0

(34) Scan parquet spark_catalog.default.customer
Output [4]: [c_customer_sk#27, c_current_addr_sk#28, c_first_name#29, c_last_name#30]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer]
PushedFilters: [IsNotNull(c_customer_sk), IsNotNull(c_current_addr_sk)]
ReadSchema: struct<c_customer_sk:int,c_current_addr_sk:int,c_first_name:string,c_last_name:string>

(35) ColumnarToRow [codegen id : 10]
Input [4]: [c_customer_sk#27, c_current_addr_sk#28, c_first_name#29, c_last_name#30]

(36) Filter [codegen id : 10]
Input [4]: [c_customer_sk#27, c_current_addr_sk#28, c_first_name#29, c_last_name#30]
Condition : (isnotnull(c_customer_sk#27) AND isnotnull(c_current_addr_sk#28))

(37) Exchange
Input [4]: [c_customer_sk#27, c_current_addr_sk#28, c_first_name#29, c_last_name#30]
Arguments: hashpartitioning(c_customer_sk#27, 5), ENSURE_REQUIREMENTS, [plan_id=6]

(38) Sort [codegen id : 11]
Input [4]: [c_customer_sk#27, c_current_addr_sk#28, c_first_name#29, c_last_name#30]
Arguments: [c_customer_sk#27 ASC NULLS FIRST], false, 0

(39) SortMergeJoin [codegen id : 12]
Left keys [1]: [ss_customer_sk#1]
Right keys [1]: [c_customer_sk#27]
Join type: Inner
Join condition: None

(40) Project [codegen id : 12]
Output [7]: [ss_ticket_number#5, bought_city#24, amt#25, profit#26, c_current_addr_sk#28, c_first_name#29, c_last_name#30]
Input [9]: [ss_ticket_number#5, ss_customer_sk#1, bought_city#24, amt#25, profit#26, c_customer_sk#27, c_current_addr_sk#28, c_first_name#29, c_last_name#30]

(41) Exchange
Input [7]: [ss_ticket_number#5, bought_city#24, amt#25, profit#26, c_current_addr_sk#28, c_first_name#29, c_last_name#30]
Arguments: hashpartitioning(c_current_addr_sk#28, 5), ENSURE_REQUIREMENTS, [plan_id=7]

(42) Sort [codegen id : 13]
Input [7]: [ss_ticket_number#5, bought_city#24, amt#25, profit#26, c_current_addr_sk#28, c_first_name#29, c_last_name#30]
Arguments: [c_current_addr_sk#28 ASC NULLS FIRST], false, 0

(43) ReusedExchange [Reuses operator id: 26]
Output [2]: [ca_address_sk#31, ca_city#32]

(44) Sort [codegen id : 15]
Input [2]: [ca_address_sk#31, ca_city#32]
Arguments: [ca_address_sk#31 ASC NULLS FIRST], false, 0

(45) SortMergeJoin [codegen id : 16]
Left keys [1]: [c_current_addr_sk#28]
Right keys [1]: [ca_address_sk#31]
Join type: Inner
Join condition: NOT (ca_city#32 = bought_city#24)

(46) Project [codegen id : 16]
Output [7]: [c_last_name#30, c_first_name#29, ca_city#32, bought_city#24, ss_ticket_number#5, amt#25, profit#26]
Input [9]: [ss_ticket_number#5, bought_city#24, amt#25, profit#26, c_current_addr_sk#28, c_first_name#29, c_last_name#30, ca_address_sk#31, ca_city#32]

(47) TakeOrderedAndProject
Input [7]: [c_last_name#30, c_first_name#29, ca_city#32, bought_city#24, ss_ticket_number#5, amt#25, profit#26]
Arguments: 100, [c_last_name#30 ASC NULLS FIRST, c_first_name#29 ASC NULLS FIRST, ca_city#32 ASC NULLS FIRST, bought_city#24 ASC NULLS FIRST, ss_ticket_number#5 ASC NULLS FIRST], [c_last_name#30, c_first_name#29, ca_city#32, bought_city#24, ss_ticket_number#5, amt#25, profit#26]

===== Subqueries =====

Subquery:1 Hosting operator id = 1 Hosting Expression = ss_sold_date_sk#8 IN dynamicpruning#9
BroadcastExchange (52)
+- * Project (51)
   +- * Filter (50)
      +- * ColumnarToRow (49)
         +- Scan parquet spark_catalog.default.date_dim (48)


(48) Scan parquet spark_catalog.default.date_dim
Output [3]: [d_date_sk#10, d_year#33, d_dow#34]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [In(d_dow, [0,6]), In(d_year, [1999,2000,2001]), In(d_date_sk, [2451181,2451182,2451188,2451189,2451195,2451196,2451202,2451203,2451209,2451210,2451216,2451217,2451223,2451224,2451230,2451231,2451237,2451238,2451244,2451245,2451251,2451252,2451258,2451259,2451265,2451266,2451272,2451273,2451279,2451280,2451286,2451287,2451293,2451294,2451300,2451301,2451307,2451308,2451314,2451315,2451321,2451322,2451328,2451329,2451335,2451336,2451342,2451343,2451349,2451350,2451356,2451357,2451363,2451364,2451370,2451371,2451377,2451378,2451384,2451385,2451391,2451392,2451398,2451399,2451405,2451406,2451412,2451413,2451419,2451420,2451426,2451427,2451433,2451434,2451440,2451441,2451447,2451448,2451454,2451455,2451461,2451462,2451468,2451469,2451475,2451476,2451482,2451483,2451489,2451490,2451496,2451497,2451503,2451504,2451510,2451511,2451517,2451518,2451524,2451525,2451531,2451532,2451538,2451539,2451545,2451546,2451552,2451553,2451559,2451560,2451566,2451567,2451573,2451574,2451580,2451581,2451587,2451588,2451594,2451595,2451601,2451602,2451608,2451609,2451615,2451616,2451622,2451623,2451629,2451630,2451636,2451637,2451643,2451644,2451650,2451651,2451657,2451658,2451664,2451665,2451671,2451672,2451678,2451679,2451685,2451686,2451692,2451693,2451699,2451700,2451706,2451707,2451713,2451714,2451720,2451721,2451727,2451728,2451734,2451735,2451741,2451742,2451748,2451749,2451755,2451756,2451762,2451763,2451769,2451770,2451776,2451777,2451783,2451784,2451790,2451791,2451797,2451798,2451804,2451805,2451811,2451812,2451818,2451819,2451825,2451826,2451832,2451833,2451839,2451840,2451846,2451847,2451853,2451854,2451860,2451861,2451867,2451868,2451874,2451875,2451881,2451882,2451888,2451889,2451895,2451896,2451902,2451903,2451909,2451910,2451916,2451917,2451923,2451924,2451930,2451931,2451937,2451938,2451944,2451945,2451951,2451952,2451958,2451959,2451965,2451966,2451972,2451973,2451979,2451980,2451986,2451987,2451993,2451994,2452000,2452001,2452007,2452008,2452014,2452015,2452021,2452022,2452028,2452029,2452035,2452036,2452042,2452043,2452049,2452050,2452056,2452057,2452063,2452064,2452070,2452071,2452077,2452078,2452084,2452085,2452091,2452092,2452098,2452099,2452105,2452106,2452112,2452113,2452119,2452120,2452126,2452127,2452133,2452134,2452140,2452141,2452147,2452148,2452154,2452155,2452161,2452162,2452168,2452169,2452175,2452176,2452182,2452183,2452189,2452190,2452196,2452197,2452203,2452204,2452210,2452211,2452217,2452218,2452224,2452225,2452231,2452232,2452238,2452239,2452245,2452246,2452252,2452253,2452259,2452260,2452266,2452267,2452273,2452274]), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_year:int,d_dow:int>

(49) ColumnarToRow [codegen id : 1]
Input [3]: [d_date_sk#10, d_year#33, d_dow#34]

(50) Filter [codegen id : 1]
Input [3]: [d_date_sk#10, d_year#33, d_dow#34]
Condition : (((d_dow#34 IN (6,0) AND d_year#33 IN (1999,2000,2001)) AND d_date_sk#10 INSET 2451181, 2451182, 2451188, 2451189, 2451195, 2451196, 2451202, 2451203, 2451209, 2451210, 2451216, 2451217, 2451223, 2451224, 2451230, 2451231, 2451237, 2451238, 2451244, 2451245, 2451251, 2451252, 2451258, 2451259, 2451265, 2451266, 2451272, 2451273, 2451279, 2451280, 2451286, 2451287, 2451293, 2451294, 2451300, 2451301, 2451307, 2451308, 2451314, 2451315, 2451321, 2451322, 2451328, 2451329, 2451335, 2451336, 2451342, 2451343, 2451349, 2451350, 2451356, 2451357, 2451363, 2451364, 2451370, 2451371, 2451377, 2451378, 2451384, 2451385, 2451391, 2451392, 2451398, 2451399, 2451405, 2451406, 2451412, 2451413, 2451419, 2451420, 2451426, 2451427, 2451433, 2451434, 2451440, 2451441, 2451447, 2451448, 2451454, 2451455, 2451461, 2451462, 2451468, 2451469, 2451475, 2451476, 2451482, 2451483, 2451489, 2451490, 2451496, 2451497, 2451503, 2451504, 2451510, 2451511, 2451517, 2451518, 2451524, 2451525, 2451531, 2451532, 2451538, 2451539, 2451545, 2451546, 2451552, 2451553, 2451559, 2451560, 2451566, 2451567, 2451573, 2451574, 2451580, 2451581, 2451587, 2451588, 2451594, 2451595, 2451601, 2451602, 2451608, 2451609, 2451615, 2451616, 2451622, 2451623, 2451629, 2451630, 2451636, 2451637, 2451643, 2451644, 2451650, 2451651, 2451657, 2451658, 2451664, 2451665, 2451671, 2451672, 2451678, 2451679, 2451685, 2451686, 2451692, 2451693, 2451699, 2451700, 2451706, 2451707, 2451713, 2451714, 2451720, 2451721, 2451727, 2451728, 2451734, 2451735, 2451741, 2451742, 2451748, 2451749, 2451755, 2451756, 2451762, 2451763, 2451769, 2451770, 2451776, 2451777, 2451783, 2451784, 2451790, 2451791, 2451797, 2451798, 2451804, 2451805, 2451811, 2451812, 2451818, 2451819, 2451825, 2451826, 2451832, 2451833, 2451839, 2451840, 2451846, 2451847, 2451853, 2451854, 2451860, 2451861, 2451867, 2451868, 2451874, 2451875, 2451881, 2451882, 2451888, 2451889, 2451895, 2451896, 2451902, 2451903, 2451909, 2451910, 2451916, 2451917, 2451923, 2451924, 2451930, 2451931, 2451937, 2451938, 2451944, 2451945, 2451951, 2451952, 2451958, 2451959, 2451965, 2451966, 2451972, 2451973, 2451979, 2451980, 2451986, 2451987, 2451993, 2451994, 2452000, 2452001, 2452007, 2452008, 2452014, 2452015, 2452021, 2452022, 2452028, 2452029, 2452035, 2452036, 2452042, 2452043, 2452049, 2452050, 2452056, 2452057, 2452063, 2452064, 2452070, 2452071, 2452077, 2452078, 2452084, 2452085, 2452091, 2452092, 2452098, 2452099, 2452105, 2452106, 2452112, 2452113, 2452119, 2452120, 2452126, 2452127, 2452133, 2452134, 2452140, 2452141, 2452147, 2452148, 2452154, 2452155, 2452161, 2452162, 2452168, 2452169, 2452175, 2452176, 2452182, 2452183, 2452189, 2452190, 2452196, 2452197, 2452203, 2452204, 2452210, 2452211, 2452217, 2452218, 2452224, 2452225, 2452231, 2452232, 2452238, 2452239, 2452245, 2452246, 2452252, 2452253, 2452259, 2452260, 2452266, 2452267, 2452273, 2452274) AND isnotnull(d_date_sk#10))

(51) Project [codegen id : 1]
Output [1]: [d_date_sk#10]
Input [3]: [d_date_sk#10, d_year#33, d_dow#34]

(52) BroadcastExchange
Input [1]: [d_date_sk#10]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=8]


