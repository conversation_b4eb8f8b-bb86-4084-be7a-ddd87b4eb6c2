== Physical Plan ==
TakeOrderedAndProject (21)
+- * HashAggregate (20)
   +- Exchange (19)
      +- * HashAggregate (18)
         +- * Project (17)
            +- * BroadcastHashJoin Inner BuildRight (16)
               :- * Project (10)
               :  +- * BroadcastHashJoin Inner BuildRight (9)
               :     :- * Project (4)
               :     :  +- * Filter (3)
               :     :     +- * ColumnarToRow (2)
               :     :        +- Scan parquet spark_catalog.default.date_dim (1)
               :     +- BroadcastExchange (8)
               :        +- * Filter (7)
               :           +- * ColumnarToRow (6)
               :              +- Scan parquet spark_catalog.default.store_sales (5)
               +- BroadcastExchange (15)
                  +- * Project (14)
                     +- * Filter (13)
                        +- * ColumnarToRow (12)
                           +- Scan parquet spark_catalog.default.item (11)


(1) Scan parquet spark_catalog.default.date_dim
Output [3]: [d_date_sk#1, d_year#2, d_moy#3]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNot<PERSON>ull(d_moy), IsNotNull(d_year), <PERSON>To(d_moy,11), EqualTo(d_year,2001), GreaterThanOrEqual(d_date_sk,2452215), LessThanOrEqual(d_date_sk,2452244), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_year:int,d_moy:int>

(2) ColumnarToRow [codegen id : 3]
Input [3]: [d_date_sk#1, d_year#2, d_moy#3]

(3) Filter [codegen id : 3]
Input [3]: [d_date_sk#1, d_year#2, d_moy#3]
Condition : ((((((isnotnull(d_moy#3) AND isnotnull(d_year#2)) AND (d_moy#3 = 11)) AND (d_year#2 = 2001)) AND (d_date_sk#1 >= 2452215)) AND (d_date_sk#1 <= 2452244)) AND isnotnull(d_date_sk#1))

(4) Project [codegen id : 3]
Output [1]: [d_date_sk#1]
Input [3]: [d_date_sk#1, d_year#2, d_moy#3]

(5) Scan parquet spark_catalog.default.store_sales
Output [3]: [ss_item_sk#4, ss_ext_sales_price#5, ss_sold_date_sk#6]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ss_sold_date_sk#6), (ss_sold_date_sk#6 >= 2452215), (ss_sold_date_sk#6 <= 2452244), dynamicpruningexpression(true)]
PushedFilters: [IsNotNull(ss_item_sk)]
ReadSchema: struct<ss_item_sk:int,ss_ext_sales_price:decimal(7,2)>

(6) ColumnarToRow [codegen id : 1]
Input [3]: [ss_item_sk#4, ss_ext_sales_price#5, ss_sold_date_sk#6]

(7) Filter [codegen id : 1]
Input [3]: [ss_item_sk#4, ss_ext_sales_price#5, ss_sold_date_sk#6]
Condition : isnotnull(ss_item_sk#4)

(8) BroadcastExchange
Input [3]: [ss_item_sk#4, ss_ext_sales_price#5, ss_sold_date_sk#6]
Arguments: HashedRelationBroadcastMode(List(cast(input[2, int, true] as bigint)),false), [plan_id=1]

(9) BroadcastHashJoin [codegen id : 3]
Left keys [1]: [d_date_sk#1]
Right keys [1]: [ss_sold_date_sk#6]
Join type: Inner
Join condition: None

(10) Project [codegen id : 3]
Output [2]: [ss_item_sk#4, ss_ext_sales_price#5]
Input [4]: [d_date_sk#1, ss_item_sk#4, ss_ext_sales_price#5, ss_sold_date_sk#6]

(11) Scan parquet spark_catalog.default.item
Output [4]: [i_item_sk#7, i_brand_id#8, i_brand#9, i_manager_id#10]
Batched: true
Location [not included in comparison]/{warehouse_dir}/item]
PushedFilters: [IsNotNull(i_manager_id), EqualTo(i_manager_id,48), IsNotNull(i_item_sk)]
ReadSchema: struct<i_item_sk:int,i_brand_id:int,i_brand:string,i_manager_id:int>

(12) ColumnarToRow [codegen id : 2]
Input [4]: [i_item_sk#7, i_brand_id#8, i_brand#9, i_manager_id#10]

(13) Filter [codegen id : 2]
Input [4]: [i_item_sk#7, i_brand_id#8, i_brand#9, i_manager_id#10]
Condition : ((isnotnull(i_manager_id#10) AND (i_manager_id#10 = 48)) AND isnotnull(i_item_sk#7))

(14) Project [codegen id : 2]
Output [3]: [i_item_sk#7, i_brand_id#8, i_brand#9]
Input [4]: [i_item_sk#7, i_brand_id#8, i_brand#9, i_manager_id#10]

(15) BroadcastExchange
Input [3]: [i_item_sk#7, i_brand_id#8, i_brand#9]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=2]

(16) BroadcastHashJoin [codegen id : 3]
Left keys [1]: [ss_item_sk#4]
Right keys [1]: [i_item_sk#7]
Join type: Inner
Join condition: None

(17) Project [codegen id : 3]
Output [3]: [ss_ext_sales_price#5, i_brand_id#8, i_brand#9]
Input [5]: [ss_item_sk#4, ss_ext_sales_price#5, i_item_sk#7, i_brand_id#8, i_brand#9]

(18) HashAggregate [codegen id : 3]
Input [3]: [ss_ext_sales_price#5, i_brand_id#8, i_brand#9]
Keys [2]: [i_brand#9, i_brand_id#8]
Functions [1]: [partial_sum(UnscaledValue(ss_ext_sales_price#5))]
Aggregate Attributes [1]: [sum#11]
Results [3]: [i_brand#9, i_brand_id#8, sum#12]

(19) Exchange
Input [3]: [i_brand#9, i_brand_id#8, sum#12]
Arguments: hashpartitioning(i_brand#9, i_brand_id#8, 5), ENSURE_REQUIREMENTS, [plan_id=3]

(20) HashAggregate [codegen id : 4]
Input [3]: [i_brand#9, i_brand_id#8, sum#12]
Keys [2]: [i_brand#9, i_brand_id#8]
Functions [1]: [sum(UnscaledValue(ss_ext_sales_price#5))]
Aggregate Attributes [1]: [sum(UnscaledValue(ss_ext_sales_price#5))#13]
Results [3]: [i_brand_id#8 AS brand_id#14, i_brand#9 AS brand#15, MakeDecimal(sum(UnscaledValue(ss_ext_sales_price#5))#13,17,2) AS ext_price#16]

(21) TakeOrderedAndProject
Input [3]: [brand_id#14, brand#15, ext_price#16]
Arguments: 100, [ext_price#16 DESC NULLS LAST, brand_id#14 ASC NULLS FIRST], [brand_id#14, brand#15, ext_price#16]

