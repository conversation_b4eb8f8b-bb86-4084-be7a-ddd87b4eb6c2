TakeOrderedAndProject [s_store_name,s_store_id,sun_sales,mon_sales,tue_sales,wed_sales,thu_sales,fri_sales,sat_sales]
  WholeStageCodegen (4)
    HashAggregate [s_store_name,s_store_id,sum,sum,sum,sum,sum,sum,sum] [sum(UnscaledValue(CASE WHEN (d_day_name = Sunday   ) THEN ss_sales_price END)),sum(UnscaledValue(CASE WHEN (d_day_name = Monday   ) THEN ss_sales_price END)),sum(UnscaledValue(CASE WHEN (d_day_name = Tuesday  ) THEN ss_sales_price END)),sum(UnscaledValue(CASE WHEN (d_day_name = Wednesday) THEN ss_sales_price END)),sum(UnscaledValue(CASE WHEN (d_day_name = Thursday ) THEN ss_sales_price END)),sum(UnscaledValue(CASE WHEN (d_day_name = Friday   ) THEN ss_sales_price END)),sum(UnscaledValue(CASE WHEN (d_day_name = Saturday ) THEN ss_sales_price END)),sun_sales,mon_sales,tue_sales,wed_sales,thu_sales,fri_sales,sat_sales,sum,sum,sum,sum,sum,sum,sum]
      InputAdapter
        Exchange [s_store_name,s_store_id] #1
          WholeStageCodegen (3)
            HashAggregate [s_store_name,s_store_id,d_day_name,ss_sales_price] [sum,sum,sum,sum,sum,sum,sum,sum,sum,sum,sum,sum,sum,sum]
              Project [d_day_name,ss_sales_price,s_store_id,s_store_name]
                BroadcastHashJoin [ss_store_sk,s_store_sk]
                  Project [d_day_name,ss_store_sk,ss_sales_price]
                    BroadcastHashJoin [d_date_sk,ss_sold_date_sk]
                      InputAdapter
                        BroadcastExchange #2
                          WholeStageCodegen (1)
                            Project [d_date_sk,d_day_name]
                              Filter [d_year,d_date_sk]
                                ColumnarToRow
                                  InputAdapter
                                    Scan parquet spark_catalog.default.date_dim [d_date_sk,d_year,d_day_name]
                      Filter [ss_store_sk]
                        ColumnarToRow
                          InputAdapter
                            Scan parquet spark_catalog.default.store_sales [ss_store_sk,ss_sales_price,ss_sold_date_sk]
                              SubqueryBroadcast [d_date_sk] #1
                                ReusedExchange [d_date_sk,d_day_name] #2
                  InputAdapter
                    BroadcastExchange #3
                      WholeStageCodegen (2)
                        Project [s_store_sk,s_store_id,s_store_name]
                          Filter [s_gmt_offset,s_store_sk]
                            ColumnarToRow
                              InputAdapter
                                Scan parquet spark_catalog.default.store [s_store_sk,s_store_id,s_store_name,s_gmt_offset]
