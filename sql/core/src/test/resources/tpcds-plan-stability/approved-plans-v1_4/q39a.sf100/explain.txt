== Physical Plan ==
* Sort (47)
+- Exchange (46)
   +- * SortMergeJoin Inner (45)
      :- * Sort (25)
      :  +- Exchange (24)
      :     +- * Project (23)
      :        +- * Filter (22)
      :           +- * HashAggregate (21)
      :              +- Exchange (20)
      :                 +- * HashAggregate (19)
      :                    +- * Project (18)
      :                       +- * BroadcastHashJoin Inner BuildRight (17)
      :                          :- * Project (12)
      :                          :  +- * BroadcastHashJoin Inner BuildRight (11)
      :                          :     :- * Project (6)
      :                          :     :  +- * BroadcastHashJoin Inner BuildRight (5)
      :                          :     :     :- * Filter (3)
      :                          :     :     :  +- * ColumnarToRow (2)
      :                          :     :     :     +- Scan parquet spark_catalog.default.inventory (1)
      :                          :     :     +- ReusedExchange (4)
      :                          :     +- BroadcastExchange (10)
      :                          :        +- * Filter (9)
      :                          :           +- * ColumnarToRow (8)
      :                          :              +- Scan parquet spark_catalog.default.item (7)
      :                          +- BroadcastExchange (16)
      :                             +- * Filter (15)
      :                                +- * ColumnarToRow (14)
      :                                   +- Scan parquet spark_catalog.default.warehouse (13)
      +- * Sort (44)
         +- Exchange (43)
            +- * Project (42)
               +- * Filter (41)
                  +- * HashAggregate (40)
                     +- Exchange (39)
                        +- * HashAggregate (38)
                           +- * Project (37)
                              +- * BroadcastHashJoin Inner BuildRight (36)
                                 :- * Project (34)
                                 :  +- * BroadcastHashJoin Inner BuildRight (33)
                                 :     :- * Project (31)
                                 :     :  +- * BroadcastHashJoin Inner BuildRight (30)
                                 :     :     :- * Filter (28)
                                 :     :     :  +- * ColumnarToRow (27)
                                 :     :     :     +- Scan parquet spark_catalog.default.inventory (26)
                                 :     :     +- ReusedExchange (29)
                                 :     +- ReusedExchange (32)
                                 +- ReusedExchange (35)


(1) Scan parquet spark_catalog.default.inventory
Output [4]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, inv_date_sk#4]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(inv_date_sk#4), dynamicpruningexpression(inv_date_sk#4 IN dynamicpruning#5)]
PushedFilters: [IsNotNull(inv_item_sk), IsNotNull(inv_warehouse_sk)]
ReadSchema: struct<inv_item_sk:int,inv_warehouse_sk:int,inv_quantity_on_hand:int>

(2) ColumnarToRow [codegen id : 4]
Input [4]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, inv_date_sk#4]

(3) Filter [codegen id : 4]
Input [4]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, inv_date_sk#4]
Condition : (isnotnull(inv_item_sk#1) AND isnotnull(inv_warehouse_sk#2))

(4) ReusedExchange [Reuses operator id: 52]
Output [2]: [d_date_sk#6, d_moy#7]

(5) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [inv_date_sk#4]
Right keys [1]: [d_date_sk#6]
Join type: Inner
Join condition: None

(6) Project [codegen id : 4]
Output [4]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, d_moy#7]
Input [6]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, inv_date_sk#4, d_date_sk#6, d_moy#7]

(7) Scan parquet spark_catalog.default.item
Output [1]: [i_item_sk#8]
Batched: true
Location [not included in comparison]/{warehouse_dir}/item]
PushedFilters: [IsNotNull(i_item_sk)]
ReadSchema: struct<i_item_sk:int>

(8) ColumnarToRow [codegen id : 2]
Input [1]: [i_item_sk#8]

(9) Filter [codegen id : 2]
Input [1]: [i_item_sk#8]
Condition : isnotnull(i_item_sk#8)

(10) BroadcastExchange
Input [1]: [i_item_sk#8]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, false] as bigint)),false), [plan_id=1]

(11) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [inv_item_sk#1]
Right keys [1]: [i_item_sk#8]
Join type: Inner
Join condition: None

(12) Project [codegen id : 4]
Output [4]: [inv_warehouse_sk#2, inv_quantity_on_hand#3, d_moy#7, i_item_sk#8]
Input [5]: [inv_item_sk#1, inv_warehouse_sk#2, inv_quantity_on_hand#3, d_moy#7, i_item_sk#8]

(13) Scan parquet spark_catalog.default.warehouse
Output [2]: [w_warehouse_sk#9, w_warehouse_name#10]
Batched: true
Location [not included in comparison]/{warehouse_dir}/warehouse]
PushedFilters: [IsNotNull(w_warehouse_sk)]
ReadSchema: struct<w_warehouse_sk:int,w_warehouse_name:string>

(14) ColumnarToRow [codegen id : 3]
Input [2]: [w_warehouse_sk#9, w_warehouse_name#10]

(15) Filter [codegen id : 3]
Input [2]: [w_warehouse_sk#9, w_warehouse_name#10]
Condition : isnotnull(w_warehouse_sk#9)

(16) BroadcastExchange
Input [2]: [w_warehouse_sk#9, w_warehouse_name#10]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, false] as bigint)),false), [plan_id=2]

(17) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [inv_warehouse_sk#2]
Right keys [1]: [w_warehouse_sk#9]
Join type: Inner
Join condition: None

(18) Project [codegen id : 4]
Output [5]: [inv_quantity_on_hand#3, i_item_sk#8, w_warehouse_sk#9, w_warehouse_name#10, d_moy#7]
Input [6]: [inv_warehouse_sk#2, inv_quantity_on_hand#3, d_moy#7, i_item_sk#8, w_warehouse_sk#9, w_warehouse_name#10]

(19) HashAggregate [codegen id : 4]
Input [5]: [inv_quantity_on_hand#3, i_item_sk#8, w_warehouse_sk#9, w_warehouse_name#10, d_moy#7]
Keys [4]: [w_warehouse_name#10, w_warehouse_sk#9, i_item_sk#8, d_moy#7]
Functions [2]: [partial_stddev_samp(cast(inv_quantity_on_hand#3 as double)), partial_avg(inv_quantity_on_hand#3)]
Aggregate Attributes [5]: [n#11, avg#12, m2#13, sum#14, count#15]
Results [9]: [w_warehouse_name#10, w_warehouse_sk#9, i_item_sk#8, d_moy#7, n#16, avg#17, m2#18, sum#19, count#20]

(20) Exchange
Input [9]: [w_warehouse_name#10, w_warehouse_sk#9, i_item_sk#8, d_moy#7, n#16, avg#17, m2#18, sum#19, count#20]
Arguments: hashpartitioning(w_warehouse_name#10, w_warehouse_sk#9, i_item_sk#8, d_moy#7, 5), ENSURE_REQUIREMENTS, [plan_id=3]

(21) HashAggregate [codegen id : 5]
Input [9]: [w_warehouse_name#10, w_warehouse_sk#9, i_item_sk#8, d_moy#7, n#16, avg#17, m2#18, sum#19, count#20]
Keys [4]: [w_warehouse_name#10, w_warehouse_sk#9, i_item_sk#8, d_moy#7]
Functions [2]: [stddev_samp(cast(inv_quantity_on_hand#3 as double)), avg(inv_quantity_on_hand#3)]
Aggregate Attributes [2]: [stddev_samp(cast(inv_quantity_on_hand#3 as double))#21, avg(inv_quantity_on_hand#3)#22]
Results [5]: [w_warehouse_sk#9, i_item_sk#8, d_moy#7, stddev_samp(cast(inv_quantity_on_hand#3 as double))#21 AS stdev#23, avg(inv_quantity_on_hand#3)#22 AS mean#24]

(22) Filter [codegen id : 5]
Input [5]: [w_warehouse_sk#9, i_item_sk#8, d_moy#7, stdev#23, mean#24]
Condition : CASE WHEN (mean#24 = 0.0) THEN false ELSE ((stdev#23 / mean#24) > 1.0) END

(23) Project [codegen id : 5]
Output [5]: [w_warehouse_sk#9, i_item_sk#8, d_moy#7, mean#24, CASE WHEN (mean#24 = 0.0) THEN null ELSE (stdev#23 / mean#24) END AS cov#25]
Input [5]: [w_warehouse_sk#9, i_item_sk#8, d_moy#7, stdev#23, mean#24]

(24) Exchange
Input [5]: [w_warehouse_sk#9, i_item_sk#8, d_moy#7, mean#24, cov#25]
Arguments: hashpartitioning(i_item_sk#8, w_warehouse_sk#9, 5), ENSURE_REQUIREMENTS, [plan_id=4]

(25) Sort [codegen id : 6]
Input [5]: [w_warehouse_sk#9, i_item_sk#8, d_moy#7, mean#24, cov#25]
Arguments: [i_item_sk#8 ASC NULLS FIRST, w_warehouse_sk#9 ASC NULLS FIRST], false, 0

(26) Scan parquet spark_catalog.default.inventory
Output [4]: [inv_item_sk#26, inv_warehouse_sk#27, inv_quantity_on_hand#28, inv_date_sk#29]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(inv_date_sk#29), dynamicpruningexpression(inv_date_sk#29 IN dynamicpruning#30)]
PushedFilters: [IsNotNull(inv_item_sk), IsNotNull(inv_warehouse_sk)]
ReadSchema: struct<inv_item_sk:int,inv_warehouse_sk:int,inv_quantity_on_hand:int>

(27) ColumnarToRow [codegen id : 10]
Input [4]: [inv_item_sk#26, inv_warehouse_sk#27, inv_quantity_on_hand#28, inv_date_sk#29]

(28) Filter [codegen id : 10]
Input [4]: [inv_item_sk#26, inv_warehouse_sk#27, inv_quantity_on_hand#28, inv_date_sk#29]
Condition : (isnotnull(inv_item_sk#26) AND isnotnull(inv_warehouse_sk#27))

(29) ReusedExchange [Reuses operator id: 57]
Output [2]: [d_date_sk#31, d_moy#32]

(30) BroadcastHashJoin [codegen id : 10]
Left keys [1]: [inv_date_sk#29]
Right keys [1]: [d_date_sk#31]
Join type: Inner
Join condition: None

(31) Project [codegen id : 10]
Output [4]: [inv_item_sk#26, inv_warehouse_sk#27, inv_quantity_on_hand#28, d_moy#32]
Input [6]: [inv_item_sk#26, inv_warehouse_sk#27, inv_quantity_on_hand#28, inv_date_sk#29, d_date_sk#31, d_moy#32]

(32) ReusedExchange [Reuses operator id: 10]
Output [1]: [i_item_sk#33]

(33) BroadcastHashJoin [codegen id : 10]
Left keys [1]: [inv_item_sk#26]
Right keys [1]: [i_item_sk#33]
Join type: Inner
Join condition: None

(34) Project [codegen id : 10]
Output [4]: [inv_warehouse_sk#27, inv_quantity_on_hand#28, d_moy#32, i_item_sk#33]
Input [5]: [inv_item_sk#26, inv_warehouse_sk#27, inv_quantity_on_hand#28, d_moy#32, i_item_sk#33]

(35) ReusedExchange [Reuses operator id: 16]
Output [2]: [w_warehouse_sk#34, w_warehouse_name#35]

(36) BroadcastHashJoin [codegen id : 10]
Left keys [1]: [inv_warehouse_sk#27]
Right keys [1]: [w_warehouse_sk#34]
Join type: Inner
Join condition: None

(37) Project [codegen id : 10]
Output [5]: [inv_quantity_on_hand#28, i_item_sk#33, w_warehouse_sk#34, w_warehouse_name#35, d_moy#32]
Input [6]: [inv_warehouse_sk#27, inv_quantity_on_hand#28, d_moy#32, i_item_sk#33, w_warehouse_sk#34, w_warehouse_name#35]

(38) HashAggregate [codegen id : 10]
Input [5]: [inv_quantity_on_hand#28, i_item_sk#33, w_warehouse_sk#34, w_warehouse_name#35, d_moy#32]
Keys [4]: [w_warehouse_name#35, w_warehouse_sk#34, i_item_sk#33, d_moy#32]
Functions [2]: [partial_stddev_samp(cast(inv_quantity_on_hand#28 as double)), partial_avg(inv_quantity_on_hand#28)]
Aggregate Attributes [5]: [n#36, avg#37, m2#38, sum#39, count#40]
Results [9]: [w_warehouse_name#35, w_warehouse_sk#34, i_item_sk#33, d_moy#32, n#41, avg#42, m2#43, sum#44, count#45]

(39) Exchange
Input [9]: [w_warehouse_name#35, w_warehouse_sk#34, i_item_sk#33, d_moy#32, n#41, avg#42, m2#43, sum#44, count#45]
Arguments: hashpartitioning(w_warehouse_name#35, w_warehouse_sk#34, i_item_sk#33, d_moy#32, 5), ENSURE_REQUIREMENTS, [plan_id=5]

(40) HashAggregate [codegen id : 11]
Input [9]: [w_warehouse_name#35, w_warehouse_sk#34, i_item_sk#33, d_moy#32, n#41, avg#42, m2#43, sum#44, count#45]
Keys [4]: [w_warehouse_name#35, w_warehouse_sk#34, i_item_sk#33, d_moy#32]
Functions [2]: [stddev_samp(cast(inv_quantity_on_hand#28 as double)), avg(inv_quantity_on_hand#28)]
Aggregate Attributes [2]: [stddev_samp(cast(inv_quantity_on_hand#28 as double))#21, avg(inv_quantity_on_hand#28)#22]
Results [5]: [w_warehouse_sk#34, i_item_sk#33, d_moy#32, stddev_samp(cast(inv_quantity_on_hand#28 as double))#21 AS stdev#23, avg(inv_quantity_on_hand#28)#22 AS mean#24]

(41) Filter [codegen id : 11]
Input [5]: [w_warehouse_sk#34, i_item_sk#33, d_moy#32, stdev#23, mean#24]
Condition : CASE WHEN (mean#24 = 0.0) THEN false ELSE ((stdev#23 / mean#24) > 1.0) END

(42) Project [codegen id : 11]
Output [5]: [w_warehouse_sk#34, i_item_sk#33, d_moy#32, mean#24 AS mean#46, CASE WHEN (mean#24 = 0.0) THEN null ELSE (stdev#23 / mean#24) END AS cov#47]
Input [5]: [w_warehouse_sk#34, i_item_sk#33, d_moy#32, stdev#23, mean#24]

(43) Exchange
Input [5]: [w_warehouse_sk#34, i_item_sk#33, d_moy#32, mean#46, cov#47]
Arguments: hashpartitioning(i_item_sk#33, w_warehouse_sk#34, 5), ENSURE_REQUIREMENTS, [plan_id=6]

(44) Sort [codegen id : 12]
Input [5]: [w_warehouse_sk#34, i_item_sk#33, d_moy#32, mean#46, cov#47]
Arguments: [i_item_sk#33 ASC NULLS FIRST, w_warehouse_sk#34 ASC NULLS FIRST], false, 0

(45) SortMergeJoin [codegen id : 13]
Left keys [2]: [i_item_sk#8, w_warehouse_sk#9]
Right keys [2]: [i_item_sk#33, w_warehouse_sk#34]
Join type: Inner
Join condition: None

(46) Exchange
Input [10]: [w_warehouse_sk#9, i_item_sk#8, d_moy#7, mean#24, cov#25, w_warehouse_sk#34, i_item_sk#33, d_moy#32, mean#46, cov#47]
Arguments: rangepartitioning(w_warehouse_sk#9 ASC NULLS FIRST, i_item_sk#8 ASC NULLS FIRST, d_moy#7 ASC NULLS FIRST, mean#24 ASC NULLS FIRST, cov#25 ASC NULLS FIRST, d_moy#32 ASC NULLS FIRST, mean#46 ASC NULLS FIRST, cov#47 ASC NULLS FIRST, 5), ENSURE_REQUIREMENTS, [plan_id=7]

(47) Sort [codegen id : 14]
Input [10]: [w_warehouse_sk#9, i_item_sk#8, d_moy#7, mean#24, cov#25, w_warehouse_sk#34, i_item_sk#33, d_moy#32, mean#46, cov#47]
Arguments: [w_warehouse_sk#9 ASC NULLS FIRST, i_item_sk#8 ASC NULLS FIRST, d_moy#7 ASC NULLS FIRST, mean#24 ASC NULLS FIRST, cov#25 ASC NULLS FIRST, d_moy#32 ASC NULLS FIRST, mean#46 ASC NULLS FIRST, cov#47 ASC NULLS FIRST], true, 0

===== Subqueries =====

Subquery:1 Hosting operator id = 1 Hosting Expression = inv_date_sk#4 IN dynamicpruning#5
BroadcastExchange (52)
+- * Project (51)
   +- * Filter (50)
      +- * ColumnarToRow (49)
         +- Scan parquet spark_catalog.default.date_dim (48)


(48) Scan parquet spark_catalog.default.date_dim
Output [3]: [d_date_sk#6, d_year#48, d_moy#7]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNotNull(d_year), IsNotNull(d_moy), EqualTo(d_year,2001), EqualTo(d_moy,1), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_year:int,d_moy:int>

(49) ColumnarToRow [codegen id : 1]
Input [3]: [d_date_sk#6, d_year#48, d_moy#7]

(50) Filter [codegen id : 1]
Input [3]: [d_date_sk#6, d_year#48, d_moy#7]
Condition : ((((isnotnull(d_year#48) AND isnotnull(d_moy#7)) AND (d_year#48 = 2001)) AND (d_moy#7 = 1)) AND isnotnull(d_date_sk#6))

(51) Project [codegen id : 1]
Output [2]: [d_date_sk#6, d_moy#7]
Input [3]: [d_date_sk#6, d_year#48, d_moy#7]

(52) BroadcastExchange
Input [2]: [d_date_sk#6, d_moy#7]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=8]

Subquery:2 Hosting operator id = 26 Hosting Expression = inv_date_sk#29 IN dynamicpruning#30
BroadcastExchange (57)
+- * Project (56)
   +- * Filter (55)
      +- * ColumnarToRow (54)
         +- Scan parquet spark_catalog.default.date_dim (53)


(53) Scan parquet spark_catalog.default.date_dim
Output [3]: [d_date_sk#31, d_year#49, d_moy#32]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNotNull(d_year), IsNotNull(d_moy), EqualTo(d_year,2001), EqualTo(d_moy,2), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_year:int,d_moy:int>

(54) ColumnarToRow [codegen id : 1]
Input [3]: [d_date_sk#31, d_year#49, d_moy#32]

(55) Filter [codegen id : 1]
Input [3]: [d_date_sk#31, d_year#49, d_moy#32]
Condition : ((((isnotnull(d_year#49) AND isnotnull(d_moy#32)) AND (d_year#49 = 2001)) AND (d_moy#32 = 2)) AND isnotnull(d_date_sk#31))

(56) Project [codegen id : 1]
Output [2]: [d_date_sk#31, d_moy#32]
Input [3]: [d_date_sk#31, d_year#49, d_moy#32]

(57) BroadcastExchange
Input [2]: [d_date_sk#31, d_moy#32]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=9]


