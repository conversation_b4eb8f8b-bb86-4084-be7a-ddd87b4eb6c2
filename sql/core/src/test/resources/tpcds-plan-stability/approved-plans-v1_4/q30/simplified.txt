TakeOrderedAndProject [c_customer_id,c_salutation,c_first_name,c_last_name,c_preferred_cust_flag,c_birth_day,c_birth_month,c_birth_year,c_birth_country,c_login,c_email_address,c_last_review_date,ctr_total_return]
  WholeStageCodegen (11)
    Project [c_customer_id,c_salutation,c_first_name,c_last_name,c_preferred_cust_flag,c_birth_day,c_birth_month,c_birth_year,c_birth_country,c_login,c_email_address,c_last_review_date,ctr_total_return]
      BroadcastHashJoin [c_current_addr_sk,ca_address_sk]
        Project [ctr_total_return,c_customer_id,c_current_addr_sk,c_salutation,c_first_name,c_last_name,c_preferred_cust_flag,c_birth_day,c_birth_month,c_birth_year,c_birth_country,c_login,c_email_address,c_last_review_date]
          BroadcastHashJoin [ctr_customer_sk,c_customer_sk]
            Project [ctr_customer_sk,ctr_total_return]
              BroadcastHashJoin [ctr_state,ctr_state,ctr_total_return,(avg(ctr_total_return) * 1.2)]
                Filter [ctr_total_return]
                  HashAggregate [wr_returning_customer_sk,ca_state,sum] [sum(UnscaledValue(wr_return_amt)),ctr_customer_sk,ctr_state,ctr_total_return,sum]
                    InputAdapter
                      Exchange [wr_returning_customer_sk,ca_state] #1
                        WholeStageCodegen (3)
                          HashAggregate [wr_returning_customer_sk,ca_state,wr_return_amt] [sum,sum]
                            Project [wr_returning_customer_sk,wr_return_amt,ca_state]
                              BroadcastHashJoin [wr_returning_addr_sk,ca_address_sk]
                                Project [wr_returning_customer_sk,wr_returning_addr_sk,wr_return_amt]
                                  BroadcastHashJoin [wr_returned_date_sk,d_date_sk]
                                    Filter [wr_returning_addr_sk,wr_returning_customer_sk]
                                      ColumnarToRow
                                        InputAdapter
                                          Scan parquet spark_catalog.default.web_returns [wr_returning_customer_sk,wr_returning_addr_sk,wr_return_amt,wr_returned_date_sk]
                                            SubqueryBroadcast [d_date_sk] #1
                                              BroadcastExchange #2
                                                WholeStageCodegen (1)
                                                  Project [d_date_sk]
                                                    Filter [d_year,d_date_sk]
                                                      ColumnarToRow
                                                        InputAdapter
                                                          Scan parquet spark_catalog.default.date_dim [d_date_sk,d_year]
                                    InputAdapter
                                      ReusedExchange [d_date_sk] #2
                                InputAdapter
                                  BroadcastExchange #3
                                    WholeStageCodegen (2)
                                      Filter [ca_address_sk,ca_state]
                                        ColumnarToRow
                                          InputAdapter
                                            Scan parquet spark_catalog.default.customer_address [ca_address_sk,ca_state]
                InputAdapter
                  BroadcastExchange #4
                    WholeStageCodegen (8)
                      Filter [(avg(ctr_total_return) * 1.2)]
                        HashAggregate [ctr_state,sum,count] [avg(ctr_total_return),(avg(ctr_total_return) * 1.2),sum,count]
                          InputAdapter
                            Exchange [ctr_state] #5
                              WholeStageCodegen (7)
                                HashAggregate [ctr_state,ctr_total_return] [sum,count,sum,count]
                                  HashAggregate [wr_returning_customer_sk,ca_state,sum] [sum(UnscaledValue(wr_return_amt)),ctr_state,ctr_total_return,sum]
                                    InputAdapter
                                      Exchange [wr_returning_customer_sk,ca_state] #6
                                        WholeStageCodegen (6)
                                          HashAggregate [wr_returning_customer_sk,ca_state,wr_return_amt] [sum,sum]
                                            Project [wr_returning_customer_sk,wr_return_amt,ca_state]
                                              BroadcastHashJoin [wr_returning_addr_sk,ca_address_sk]
                                                Project [wr_returning_customer_sk,wr_returning_addr_sk,wr_return_amt]
                                                  BroadcastHashJoin [wr_returned_date_sk,d_date_sk]
                                                    Filter [wr_returning_addr_sk]
                                                      ColumnarToRow
                                                        InputAdapter
                                                          Scan parquet spark_catalog.default.web_returns [wr_returning_customer_sk,wr_returning_addr_sk,wr_return_amt,wr_returned_date_sk]
                                                            ReusedSubquery [d_date_sk] #1
                                                    InputAdapter
                                                      ReusedExchange [d_date_sk] #2
                                                InputAdapter
                                                  ReusedExchange [ca_address_sk,ca_state] #3
            InputAdapter
              BroadcastExchange #7
                WholeStageCodegen (9)
                  Filter [c_customer_sk,c_current_addr_sk]
                    ColumnarToRow
                      InputAdapter
                        Scan parquet spark_catalog.default.customer [c_customer_sk,c_customer_id,c_current_addr_sk,c_salutation,c_first_name,c_last_name,c_preferred_cust_flag,c_birth_day,c_birth_month,c_birth_year,c_birth_country,c_login,c_email_address,c_last_review_date]
        InputAdapter
          BroadcastExchange #8
            WholeStageCodegen (10)
              Project [ca_address_sk]
                Filter [ca_state,ca_address_sk]
                  ColumnarToRow
                    InputAdapter
                      Scan parquet spark_catalog.default.customer_address [ca_address_sk,ca_state]
