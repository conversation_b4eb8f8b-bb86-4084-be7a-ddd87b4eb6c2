TakeOrderedAndProject [i_item_id,total_sales]
  WholeStageCodegen (20)
    HashAggregate [i_item_id,sum,isEmpty] [sum(total_sales),total_sales,sum,isEmpty]
      InputAdapter
        Exchange [i_item_id] #1
          WholeStageCodegen (19)
            HashAggregate [i_item_id,total_sales] [sum,isEmpty,sum,isEmpty]
              InputAdapter
                Union
                  WholeStageCodegen (6)
                    HashAggregate [i_item_id,sum] [sum(UnscaledValue(ss_ext_sales_price)),total_sales,sum]
                      InputAdapter
                        Exchange [i_item_id] #2
                          WholeStageCodegen (5)
                            HashAggregate [i_item_id,ss_ext_sales_price] [sum,sum]
                              Project [ss_ext_sales_price,i_item_id]
                                BroadcastHashJoin [ss_item_sk,i_item_sk]
                                  Project [ss_item_sk,ss_ext_sales_price]
                                    BroadcastHashJoin [ss_addr_sk,ca_address_sk]
                                      Project [ss_item_sk,ss_addr_sk,ss_ext_sales_price]
                                        BroadcastHashJoin [ss_sold_date_sk,d_date_sk]
                                          Filter [ss_addr_sk,ss_item_sk]
                                            ColumnarToRow
                                              InputAdapter
                                                Scan parquet spark_catalog.default.store_sales [ss_item_sk,ss_addr_sk,ss_ext_sales_price,ss_sold_date_sk]
                                                  SubqueryBroadcast [d_date_sk] #1
                                                    BroadcastExchange #3
                                                      WholeStageCodegen (1)
                                                        Project [d_date_sk]
                                                          Filter [d_year,d_moy,d_date_sk]
                                                            ColumnarToRow
                                                              InputAdapter
                                                                Scan parquet spark_catalog.default.date_dim [d_date_sk,d_year,d_moy]
                                          InputAdapter
                                            ReusedExchange [d_date_sk] #3
                                      InputAdapter
                                        BroadcastExchange #4
                                          WholeStageCodegen (2)
                                            Project [ca_address_sk]
                                              Filter [ca_gmt_offset,ca_address_sk]
                                                ColumnarToRow
                                                  InputAdapter
                                                    Scan parquet spark_catalog.default.customer_address [ca_address_sk,ca_gmt_offset]
                                  InputAdapter
                                    BroadcastExchange #5
                                      WholeStageCodegen (4)
                                        BroadcastHashJoin [i_item_id,i_item_id]
                                          Filter [i_item_sk]
                                            ColumnarToRow
                                              InputAdapter
                                                Scan parquet spark_catalog.default.item [i_item_sk,i_item_id]
                                          InputAdapter
                                            BroadcastExchange #6
                                              WholeStageCodegen (3)
                                                Project [i_item_id]
                                                  Filter [i_category]
                                                    ColumnarToRow
                                                      InputAdapter
                                                        Scan parquet spark_catalog.default.item [i_item_id,i_category]
                  WholeStageCodegen (12)
                    HashAggregate [i_item_id,sum] [sum(UnscaledValue(cs_ext_sales_price)),total_sales,sum]
                      InputAdapter
                        Exchange [i_item_id] #7
                          WholeStageCodegen (11)
                            HashAggregate [i_item_id,cs_ext_sales_price] [sum,sum]
                              Project [cs_ext_sales_price,i_item_id]
                                BroadcastHashJoin [cs_item_sk,i_item_sk]
                                  Project [cs_item_sk,cs_ext_sales_price]
                                    BroadcastHashJoin [cs_bill_addr_sk,ca_address_sk]
                                      Project [cs_bill_addr_sk,cs_item_sk,cs_ext_sales_price]
                                        BroadcastHashJoin [cs_sold_date_sk,d_date_sk]
                                          Filter [cs_bill_addr_sk,cs_item_sk]
                                            ColumnarToRow
                                              InputAdapter
                                                Scan parquet spark_catalog.default.catalog_sales [cs_bill_addr_sk,cs_item_sk,cs_ext_sales_price,cs_sold_date_sk]
                                                  ReusedSubquery [d_date_sk] #1
                                          InputAdapter
                                            ReusedExchange [d_date_sk] #3
                                      InputAdapter
                                        ReusedExchange [ca_address_sk] #4
                                  InputAdapter
                                    ReusedExchange [i_item_sk,i_item_id] #5
                  WholeStageCodegen (18)
                    HashAggregate [i_item_id,sum] [sum(UnscaledValue(ws_ext_sales_price)),total_sales,sum]
                      InputAdapter
                        Exchange [i_item_id] #8
                          WholeStageCodegen (17)
                            HashAggregate [i_item_id,ws_ext_sales_price] [sum,sum]
                              Project [ws_ext_sales_price,i_item_id]
                                BroadcastHashJoin [ws_item_sk,i_item_sk]
                                  Project [ws_item_sk,ws_ext_sales_price]
                                    BroadcastHashJoin [ws_bill_addr_sk,ca_address_sk]
                                      Project [ws_item_sk,ws_bill_addr_sk,ws_ext_sales_price]
                                        BroadcastHashJoin [ws_sold_date_sk,d_date_sk]
                                          Filter [ws_bill_addr_sk,ws_item_sk]
                                            ColumnarToRow
                                              InputAdapter
                                                Scan parquet spark_catalog.default.web_sales [ws_item_sk,ws_bill_addr_sk,ws_ext_sales_price,ws_sold_date_sk]
                                                  ReusedSubquery [d_date_sk] #1
                                          InputAdapter
                                            ReusedExchange [d_date_sk] #3
                                      InputAdapter
                                        ReusedExchange [ca_address_sk] #4
                                  InputAdapter
                                    ReusedExchange [i_item_sk,i_item_id] #5
