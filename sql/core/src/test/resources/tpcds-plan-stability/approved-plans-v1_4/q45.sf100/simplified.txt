TakeOrderedAndProject [ca_zip,ca_city,sum(ws_sales_price)]
  WholeStageCodegen (13)
    HashAggregate [ca_zip,ca_city,sum] [sum(UnscaledValue(ws_sales_price)),sum(ws_sales_price),sum]
      InputAdapter
        Exchange [ca_zip,ca_city] #1
          WholeStageCodegen (12)
            HashAggregate [ca_zip,ca_city,ws_sales_price] [sum,sum]
              Project [ws_sales_price,ca_city,ca_zip]
                Filter [ca_zip,exists]
                  BroadcastHashJoin [i_item_id,i_item_id]
                    Project [ws_sales_price,ca_city,ca_zip,i_item_id]
                      SortMergeJoin [ws_bill_customer_sk,c_customer_sk]
                        InputAdapter
                          WholeStageCodegen (4)
                            Sort [ws_bill_customer_sk]
                              InputAdapter
                                Exchange [ws_bill_customer_sk] #2
                                  WholeStageCodegen (3)
                                    Project [ws_bill_customer_sk,ws_sales_price,i_item_id]
                                      BroadcastHashJoin [ws_item_sk,i_item_sk]
                                        Project [ws_item_sk,ws_bill_customer_sk,ws_sales_price]
                                          BroadcastHashJoin [ws_sold_date_sk,d_date_sk]
                                            Filter [ws_bill_customer_sk,ws_item_sk]
                                              ColumnarToRow
                                                InputAdapter
                                                  Scan parquet spark_catalog.default.web_sales [ws_item_sk,ws_bill_customer_sk,ws_sales_price,ws_sold_date_sk]
                                                    SubqueryBroadcast [d_date_sk] #1
                                                      BroadcastExchange #3
                                                        WholeStageCodegen (1)
                                                          Project [d_date_sk]
                                                            Filter [d_qoy,d_year,d_date_sk]
                                                              ColumnarToRow
                                                                InputAdapter
                                                                  Scan parquet spark_catalog.default.date_dim [d_date_sk,d_year,d_qoy]
                                            InputAdapter
                                              ReusedExchange [d_date_sk] #3
                                        InputAdapter
                                          BroadcastExchange #4
                                            WholeStageCodegen (2)
                                              Filter [i_item_sk]
                                                ColumnarToRow
                                                  InputAdapter
                                                    Scan parquet spark_catalog.default.item [i_item_sk,i_item_id]
                        InputAdapter
                          WholeStageCodegen (10)
                            Sort [c_customer_sk]
                              InputAdapter
                                Exchange [c_customer_sk] #5
                                  WholeStageCodegen (9)
                                    Project [c_customer_sk,ca_city,ca_zip]
                                      SortMergeJoin [c_current_addr_sk,ca_address_sk]
                                        InputAdapter
                                          WholeStageCodegen (6)
                                            Sort [c_current_addr_sk]
                                              InputAdapter
                                                Exchange [c_current_addr_sk] #6
                                                  WholeStageCodegen (5)
                                                    Filter [c_customer_sk,c_current_addr_sk]
                                                      ColumnarToRow
                                                        InputAdapter
                                                          Scan parquet spark_catalog.default.customer [c_customer_sk,c_current_addr_sk]
                                        InputAdapter
                                          WholeStageCodegen (8)
                                            Sort [ca_address_sk]
                                              InputAdapter
                                                Exchange [ca_address_sk] #7
                                                  WholeStageCodegen (7)
                                                    Filter [ca_address_sk]
                                                      ColumnarToRow
                                                        InputAdapter
                                                          Scan parquet spark_catalog.default.customer_address [ca_address_sk,ca_city,ca_zip]
                    InputAdapter
                      BroadcastExchange #8
                        WholeStageCodegen (11)
                          Project [i_item_id]
                            Filter [i_item_sk]
                              ColumnarToRow
                                InputAdapter
                                  Scan parquet spark_catalog.default.item [i_item_sk,i_item_id]
