== Physical Plan ==
TakeOrderedAndProject (57)
+- * Project (56)
   +- * BroadcastHashJoin Inner BuildRight (55)
      :- * Project (34)
      :  +- * SortMergeJoin Inner (33)
      :     :- * Sort (11)
      :     :  +- Exchange (10)
      :     :     +- * Project (9)
      :     :        +- * BroadcastHashJoin Inner BuildRight (8)
      :     :           :- * Filter (3)
      :     :           :  +- * ColumnarToRow (2)
      :     :           :     +- Scan parquet spark_catalog.default.customer (1)
      :     :           +- BroadcastExchange (7)
      :     :              +- * Filter (6)
      :     :                 +- * ColumnarToRow (5)
      :     :                    +- Scan parquet spark_catalog.default.customer_address (4)
      :     +- * Sort (32)
      :        +- Exchange (31)
      :           +- * Filter (30)
      :              +- * HashAggregate (29)
      :                 +- Exchange (28)
      :                    +- * HashAggregate (27)
      :                       +- * Project (26)
      :                          +- * SortMergeJoin Inner (25)
      :                             :- * Sort (19)
      :                             :  +- Exchange (18)
      :                             :     +- * Project (17)
      :                             :        +- * BroadcastHashJoin Inner BuildRight (16)
      :                             :           :- * Filter (14)
      :                             :           :  +- * ColumnarToRow (13)
      :                             :           :     +- Scan parquet spark_catalog.default.catalog_returns (12)
      :                             :           +- ReusedExchange (15)
      :                             +- * Sort (24)
      :                                +- Exchange (23)
      :                                   +- * Filter (22)
      :                                      +- * ColumnarToRow (21)
      :                                         +- Scan parquet spark_catalog.default.customer_address (20)
      +- BroadcastExchange (54)
         +- * Filter (53)
            +- * HashAggregate (52)
               +- Exchange (51)
                  +- * HashAggregate (50)
                     +- * HashAggregate (49)
                        +- Exchange (48)
                           +- * HashAggregate (47)
                              +- * Project (46)
                                 +- * SortMergeJoin Inner (45)
                                    :- * Sort (42)
                                    :  +- Exchange (41)
                                    :     +- * Project (40)
                                    :        +- * BroadcastHashJoin Inner BuildRight (39)
                                    :           :- * Filter (37)
                                    :           :  +- * ColumnarToRow (36)
                                    :           :     +- Scan parquet spark_catalog.default.catalog_returns (35)
                                    :           +- ReusedExchange (38)
                                    +- * Sort (44)
                                       +- ReusedExchange (43)


(1) Scan parquet spark_catalog.default.customer
Output [6]: [c_customer_sk#1, c_customer_id#2, c_current_addr_sk#3, c_salutation#4, c_first_name#5, c_last_name#6]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer]
PushedFilters: [IsNotNull(c_customer_sk), IsNotNull(c_current_addr_sk)]
ReadSchema: struct<c_customer_sk:int,c_customer_id:string,c_current_addr_sk:int,c_salutation:string,c_first_name:string,c_last_name:string>

(2) ColumnarToRow [codegen id : 2]
Input [6]: [c_customer_sk#1, c_customer_id#2, c_current_addr_sk#3, c_salutation#4, c_first_name#5, c_last_name#6]

(3) Filter [codegen id : 2]
Input [6]: [c_customer_sk#1, c_customer_id#2, c_current_addr_sk#3, c_salutation#4, c_first_name#5, c_last_name#6]
Condition : (isnotnull(c_customer_sk#1) AND isnotnull(c_current_addr_sk#3))

(4) Scan parquet spark_catalog.default.customer_address
Output [12]: [ca_address_sk#7, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer_address]
PushedFilters: [IsNotNull(ca_state), EqualTo(ca_state,GA), IsNotNull(ca_address_sk)]
ReadSchema: struct<ca_address_sk:int,ca_street_number:string,ca_street_name:string,ca_street_type:string,ca_suite_number:string,ca_city:string,ca_county:string,ca_state:string,ca_zip:string,ca_country:string,ca_gmt_offset:decimal(5,2),ca_location_type:string>

(5) ColumnarToRow [codegen id : 1]
Input [12]: [ca_address_sk#7, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18]

(6) Filter [codegen id : 1]
Input [12]: [ca_address_sk#7, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18]
Condition : ((isnotnull(ca_state#14) AND (ca_state#14 = GA)) AND isnotnull(ca_address_sk#7))

(7) BroadcastExchange
Input [12]: [ca_address_sk#7, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, false] as bigint)),false), [plan_id=1]

(8) BroadcastHashJoin [codegen id : 2]
Left keys [1]: [c_current_addr_sk#3]
Right keys [1]: [ca_address_sk#7]
Join type: Inner
Join condition: None

(9) Project [codegen id : 2]
Output [16]: [c_customer_sk#1, c_customer_id#2, c_salutation#4, c_first_name#5, c_last_name#6, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18]
Input [18]: [c_customer_sk#1, c_customer_id#2, c_current_addr_sk#3, c_salutation#4, c_first_name#5, c_last_name#6, ca_address_sk#7, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18]

(10) Exchange
Input [16]: [c_customer_sk#1, c_customer_id#2, c_salutation#4, c_first_name#5, c_last_name#6, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18]
Arguments: hashpartitioning(c_customer_sk#1, 5), ENSURE_REQUIREMENTS, [plan_id=2]

(11) Sort [codegen id : 3]
Input [16]: [c_customer_sk#1, c_customer_id#2, c_salutation#4, c_first_name#5, c_last_name#6, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18]
Arguments: [c_customer_sk#1 ASC NULLS FIRST], false, 0

(12) Scan parquet spark_catalog.default.catalog_returns
Output [4]: [cr_returning_customer_sk#19, cr_returning_addr_sk#20, cr_return_amt_inc_tax#21, cr_returned_date_sk#22]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(cr_returned_date_sk#22), dynamicpruningexpression(cr_returned_date_sk#22 IN dynamicpruning#23)]
PushedFilters: [IsNotNull(cr_returning_addr_sk), IsNotNull(cr_returning_customer_sk)]
ReadSchema: struct<cr_returning_customer_sk:int,cr_returning_addr_sk:int,cr_return_amt_inc_tax:decimal(7,2)>

(13) ColumnarToRow [codegen id : 5]
Input [4]: [cr_returning_customer_sk#19, cr_returning_addr_sk#20, cr_return_amt_inc_tax#21, cr_returned_date_sk#22]

(14) Filter [codegen id : 5]
Input [4]: [cr_returning_customer_sk#19, cr_returning_addr_sk#20, cr_return_amt_inc_tax#21, cr_returned_date_sk#22]
Condition : (isnotnull(cr_returning_addr_sk#20) AND isnotnull(cr_returning_customer_sk#19))

(15) ReusedExchange [Reuses operator id: 62]
Output [1]: [d_date_sk#24]

(16) BroadcastHashJoin [codegen id : 5]
Left keys [1]: [cr_returned_date_sk#22]
Right keys [1]: [d_date_sk#24]
Join type: Inner
Join condition: None

(17) Project [codegen id : 5]
Output [3]: [cr_returning_customer_sk#19, cr_returning_addr_sk#20, cr_return_amt_inc_tax#21]
Input [5]: [cr_returning_customer_sk#19, cr_returning_addr_sk#20, cr_return_amt_inc_tax#21, cr_returned_date_sk#22, d_date_sk#24]

(18) Exchange
Input [3]: [cr_returning_customer_sk#19, cr_returning_addr_sk#20, cr_return_amt_inc_tax#21]
Arguments: hashpartitioning(cr_returning_addr_sk#20, 5), ENSURE_REQUIREMENTS, [plan_id=3]

(19) Sort [codegen id : 6]
Input [3]: [cr_returning_customer_sk#19, cr_returning_addr_sk#20, cr_return_amt_inc_tax#21]
Arguments: [cr_returning_addr_sk#20 ASC NULLS FIRST], false, 0

(20) Scan parquet spark_catalog.default.customer_address
Output [2]: [ca_address_sk#25, ca_state#26]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer_address]
PushedFilters: [IsNotNull(ca_address_sk), IsNotNull(ca_state)]
ReadSchema: struct<ca_address_sk:int,ca_state:string>

(21) ColumnarToRow [codegen id : 7]
Input [2]: [ca_address_sk#25, ca_state#26]

(22) Filter [codegen id : 7]
Input [2]: [ca_address_sk#25, ca_state#26]
Condition : (isnotnull(ca_address_sk#25) AND isnotnull(ca_state#26))

(23) Exchange
Input [2]: [ca_address_sk#25, ca_state#26]
Arguments: hashpartitioning(ca_address_sk#25, 5), ENSURE_REQUIREMENTS, [plan_id=4]

(24) Sort [codegen id : 8]
Input [2]: [ca_address_sk#25, ca_state#26]
Arguments: [ca_address_sk#25 ASC NULLS FIRST], false, 0

(25) SortMergeJoin [codegen id : 9]
Left keys [1]: [cr_returning_addr_sk#20]
Right keys [1]: [ca_address_sk#25]
Join type: Inner
Join condition: None

(26) Project [codegen id : 9]
Output [3]: [cr_returning_customer_sk#19, cr_return_amt_inc_tax#21, ca_state#26]
Input [5]: [cr_returning_customer_sk#19, cr_returning_addr_sk#20, cr_return_amt_inc_tax#21, ca_address_sk#25, ca_state#26]

(27) HashAggregate [codegen id : 9]
Input [3]: [cr_returning_customer_sk#19, cr_return_amt_inc_tax#21, ca_state#26]
Keys [2]: [cr_returning_customer_sk#19, ca_state#26]
Functions [1]: [partial_sum(UnscaledValue(cr_return_amt_inc_tax#21))]
Aggregate Attributes [1]: [sum#27]
Results [3]: [cr_returning_customer_sk#19, ca_state#26, sum#28]

(28) Exchange
Input [3]: [cr_returning_customer_sk#19, ca_state#26, sum#28]
Arguments: hashpartitioning(cr_returning_customer_sk#19, ca_state#26, 5), ENSURE_REQUIREMENTS, [plan_id=5]

(29) HashAggregate [codegen id : 10]
Input [3]: [cr_returning_customer_sk#19, ca_state#26, sum#28]
Keys [2]: [cr_returning_customer_sk#19, ca_state#26]
Functions [1]: [sum(UnscaledValue(cr_return_amt_inc_tax#21))]
Aggregate Attributes [1]: [sum(UnscaledValue(cr_return_amt_inc_tax#21))#29]
Results [3]: [cr_returning_customer_sk#19 AS ctr_customer_sk#30, ca_state#26 AS ctr_state#31, MakeDecimal(sum(UnscaledValue(cr_return_amt_inc_tax#21))#29,17,2) AS ctr_total_return#32]

(30) Filter [codegen id : 10]
Input [3]: [ctr_customer_sk#30, ctr_state#31, ctr_total_return#32]
Condition : isnotnull(ctr_total_return#32)

(31) Exchange
Input [3]: [ctr_customer_sk#30, ctr_state#31, ctr_total_return#32]
Arguments: hashpartitioning(ctr_customer_sk#30, 5), ENSURE_REQUIREMENTS, [plan_id=6]

(32) Sort [codegen id : 11]
Input [3]: [ctr_customer_sk#30, ctr_state#31, ctr_total_return#32]
Arguments: [ctr_customer_sk#30 ASC NULLS FIRST], false, 0

(33) SortMergeJoin [codegen id : 20]
Left keys [1]: [c_customer_sk#1]
Right keys [1]: [ctr_customer_sk#30]
Join type: Inner
Join condition: None

(34) Project [codegen id : 20]
Output [17]: [c_customer_id#2, c_salutation#4, c_first_name#5, c_last_name#6, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18, ctr_state#31, ctr_total_return#32]
Input [19]: [c_customer_sk#1, c_customer_id#2, c_salutation#4, c_first_name#5, c_last_name#6, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18, ctr_customer_sk#30, ctr_state#31, ctr_total_return#32]

(35) Scan parquet spark_catalog.default.catalog_returns
Output [4]: [cr_returning_customer_sk#33, cr_returning_addr_sk#34, cr_return_amt_inc_tax#35, cr_returned_date_sk#36]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(cr_returned_date_sk#36), dynamicpruningexpression(cr_returned_date_sk#36 IN dynamicpruning#23)]
PushedFilters: [IsNotNull(cr_returning_addr_sk)]
ReadSchema: struct<cr_returning_customer_sk:int,cr_returning_addr_sk:int,cr_return_amt_inc_tax:decimal(7,2)>

(36) ColumnarToRow [codegen id : 13]
Input [4]: [cr_returning_customer_sk#33, cr_returning_addr_sk#34, cr_return_amt_inc_tax#35, cr_returned_date_sk#36]

(37) Filter [codegen id : 13]
Input [4]: [cr_returning_customer_sk#33, cr_returning_addr_sk#34, cr_return_amt_inc_tax#35, cr_returned_date_sk#36]
Condition : isnotnull(cr_returning_addr_sk#34)

(38) ReusedExchange [Reuses operator id: 62]
Output [1]: [d_date_sk#37]

(39) BroadcastHashJoin [codegen id : 13]
Left keys [1]: [cr_returned_date_sk#36]
Right keys [1]: [d_date_sk#37]
Join type: Inner
Join condition: None

(40) Project [codegen id : 13]
Output [3]: [cr_returning_customer_sk#33, cr_returning_addr_sk#34, cr_return_amt_inc_tax#35]
Input [5]: [cr_returning_customer_sk#33, cr_returning_addr_sk#34, cr_return_amt_inc_tax#35, cr_returned_date_sk#36, d_date_sk#37]

(41) Exchange
Input [3]: [cr_returning_customer_sk#33, cr_returning_addr_sk#34, cr_return_amt_inc_tax#35]
Arguments: hashpartitioning(cr_returning_addr_sk#34, 5), ENSURE_REQUIREMENTS, [plan_id=7]

(42) Sort [codegen id : 14]
Input [3]: [cr_returning_customer_sk#33, cr_returning_addr_sk#34, cr_return_amt_inc_tax#35]
Arguments: [cr_returning_addr_sk#34 ASC NULLS FIRST], false, 0

(43) ReusedExchange [Reuses operator id: 23]
Output [2]: [ca_address_sk#38, ca_state#39]

(44) Sort [codegen id : 16]
Input [2]: [ca_address_sk#38, ca_state#39]
Arguments: [ca_address_sk#38 ASC NULLS FIRST], false, 0

(45) SortMergeJoin [codegen id : 17]
Left keys [1]: [cr_returning_addr_sk#34]
Right keys [1]: [ca_address_sk#38]
Join type: Inner
Join condition: None

(46) Project [codegen id : 17]
Output [3]: [cr_returning_customer_sk#33, cr_return_amt_inc_tax#35, ca_state#39]
Input [5]: [cr_returning_customer_sk#33, cr_returning_addr_sk#34, cr_return_amt_inc_tax#35, ca_address_sk#38, ca_state#39]

(47) HashAggregate [codegen id : 17]
Input [3]: [cr_returning_customer_sk#33, cr_return_amt_inc_tax#35, ca_state#39]
Keys [2]: [cr_returning_customer_sk#33, ca_state#39]
Functions [1]: [partial_sum(UnscaledValue(cr_return_amt_inc_tax#35))]
Aggregate Attributes [1]: [sum#40]
Results [3]: [cr_returning_customer_sk#33, ca_state#39, sum#41]

(48) Exchange
Input [3]: [cr_returning_customer_sk#33, ca_state#39, sum#41]
Arguments: hashpartitioning(cr_returning_customer_sk#33, ca_state#39, 5), ENSURE_REQUIREMENTS, [plan_id=8]

(49) HashAggregate [codegen id : 18]
Input [3]: [cr_returning_customer_sk#33, ca_state#39, sum#41]
Keys [2]: [cr_returning_customer_sk#33, ca_state#39]
Functions [1]: [sum(UnscaledValue(cr_return_amt_inc_tax#35))]
Aggregate Attributes [1]: [sum(UnscaledValue(cr_return_amt_inc_tax#35))#29]
Results [2]: [ca_state#39 AS ctr_state#42, MakeDecimal(sum(UnscaledValue(cr_return_amt_inc_tax#35))#29,17,2) AS ctr_total_return#43]

(50) HashAggregate [codegen id : 18]
Input [2]: [ctr_state#42, ctr_total_return#43]
Keys [1]: [ctr_state#42]
Functions [1]: [partial_avg(ctr_total_return#43)]
Aggregate Attributes [2]: [sum#44, count#45]
Results [3]: [ctr_state#42, sum#46, count#47]

(51) Exchange
Input [3]: [ctr_state#42, sum#46, count#47]
Arguments: hashpartitioning(ctr_state#42, 5), ENSURE_REQUIREMENTS, [plan_id=9]

(52) HashAggregate [codegen id : 19]
Input [3]: [ctr_state#42, sum#46, count#47]
Keys [1]: [ctr_state#42]
Functions [1]: [avg(ctr_total_return#43)]
Aggregate Attributes [1]: [avg(ctr_total_return#43)#48]
Results [2]: [(avg(ctr_total_return#43)#48 * 1.2) AS (avg(ctr_total_return) * 1.2)#49, ctr_state#42]

(53) Filter [codegen id : 19]
Input [2]: [(avg(ctr_total_return) * 1.2)#49, ctr_state#42]
Condition : isnotnull((avg(ctr_total_return) * 1.2)#49)

(54) BroadcastExchange
Input [2]: [(avg(ctr_total_return) * 1.2)#49, ctr_state#42]
Arguments: HashedRelationBroadcastMode(List(input[1, string, true]),false), [plan_id=10]

(55) BroadcastHashJoin [codegen id : 20]
Left keys [1]: [ctr_state#31]
Right keys [1]: [ctr_state#42]
Join type: Inner
Join condition: (cast(ctr_total_return#32 as decimal(24,7)) > (avg(ctr_total_return) * 1.2)#49)

(56) Project [codegen id : 20]
Output [16]: [c_customer_id#2, c_salutation#4, c_first_name#5, c_last_name#6, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18, ctr_total_return#32]
Input [19]: [c_customer_id#2, c_salutation#4, c_first_name#5, c_last_name#6, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18, ctr_state#31, ctr_total_return#32, (avg(ctr_total_return) * 1.2)#49, ctr_state#42]

(57) TakeOrderedAndProject
Input [16]: [c_customer_id#2, c_salutation#4, c_first_name#5, c_last_name#6, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18, ctr_total_return#32]
Arguments: 100, [c_customer_id#2 ASC NULLS FIRST, c_salutation#4 ASC NULLS FIRST, c_first_name#5 ASC NULLS FIRST, c_last_name#6 ASC NULLS FIRST, ca_street_number#8 ASC NULLS FIRST, ca_street_name#9 ASC NULLS FIRST, ca_street_type#10 ASC NULLS FIRST, ca_suite_number#11 ASC NULLS FIRST, ca_city#12 ASC NULLS FIRST, ca_county#13 ASC NULLS FIRST, ca_state#14 ASC NULLS FIRST, ca_zip#15 ASC NULLS FIRST, ca_country#16 ASC NULLS FIRST, ca_gmt_offset#17 ASC NULLS FIRST, ca_location_type#18 ASC NULLS FIRST, ctr_total_return#32 ASC NULLS FIRST], [c_customer_id#2, c_salutation#4, c_first_name#5, c_last_name#6, ca_street_number#8, ca_street_name#9, ca_street_type#10, ca_suite_number#11, ca_city#12, ca_county#13, ca_state#14, ca_zip#15, ca_country#16, ca_gmt_offset#17, ca_location_type#18, ctr_total_return#32]

===== Subqueries =====

Subquery:1 Hosting operator id = 12 Hosting Expression = cr_returned_date_sk#22 IN dynamicpruning#23
BroadcastExchange (62)
+- * Project (61)
   +- * Filter (60)
      +- * ColumnarToRow (59)
         +- Scan parquet spark_catalog.default.date_dim (58)


(58) Scan parquet spark_catalog.default.date_dim
Output [2]: [d_date_sk#24, d_year#50]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNotNull(d_year), EqualTo(d_year,2000), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_year:int>

(59) ColumnarToRow [codegen id : 1]
Input [2]: [d_date_sk#24, d_year#50]

(60) Filter [codegen id : 1]
Input [2]: [d_date_sk#24, d_year#50]
Condition : ((isnotnull(d_year#50) AND (d_year#50 = 2000)) AND isnotnull(d_date_sk#24))

(61) Project [codegen id : 1]
Output [1]: [d_date_sk#24]
Input [2]: [d_date_sk#24, d_year#50]

(62) BroadcastExchange
Input [1]: [d_date_sk#24]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=11]

Subquery:2 Hosting operator id = 35 Hosting Expression = cr_returned_date_sk#36 IN dynamicpruning#23


