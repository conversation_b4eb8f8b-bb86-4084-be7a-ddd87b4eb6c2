== Physical Plan ==
TakeOrderedAndProject (23)
+- * Project (22)
   +- Window (21)
      +- * Sort (20)
         +- Exchange (19)
            +- * HashAggregate (18)
               +- Exchange (17)
                  +- * HashAggregate (16)
                     +- * Project (15)
                        +- * BroadcastHashJoin Inner BuildRight (14)
                           :- * Project (12)
                           :  +- * SortMergeJoin Inner (11)
                           :     :- * Sort (5)
                           :     :  +- Exchange (4)
                           :     :     +- * Filter (3)
                           :     :        +- * ColumnarToRow (2)
                           :     :           +- Scan parquet spark_catalog.default.web_sales (1)
                           :     +- * Sort (10)
                           :        +- Exchange (9)
                           :           +- * Filter (8)
                           :              +- * ColumnarToRow (7)
                           :                 +- Scan parquet spark_catalog.default.item (6)
                           +- ReusedExchange (13)


(1) Scan parquet spark_catalog.default.web_sales
Output [3]: [ws_item_sk#1, ws_ext_sales_price#2, ws_sold_date_sk#3]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ws_sold_date_sk#3), dynamicpruningexpression(ws_sold_date_sk#3 IN dynamicpruning#4)]
PushedFilters: [IsNotNull(ws_item_sk)]
ReadSchema: struct<ws_item_sk:int,ws_ext_sales_price:decimal(7,2)>

(2) ColumnarToRow [codegen id : 1]
Input [3]: [ws_item_sk#1, ws_ext_sales_price#2, ws_sold_date_sk#3]

(3) Filter [codegen id : 1]
Input [3]: [ws_item_sk#1, ws_ext_sales_price#2, ws_sold_date_sk#3]
Condition : isnotnull(ws_item_sk#1)

(4) Exchange
Input [3]: [ws_item_sk#1, ws_ext_sales_price#2, ws_sold_date_sk#3]
Arguments: hashpartitioning(ws_item_sk#1, 5), ENSURE_REQUIREMENTS, [plan_id=1]

(5) Sort [codegen id : 2]
Input [3]: [ws_item_sk#1, ws_ext_sales_price#2, ws_sold_date_sk#3]
Arguments: [ws_item_sk#1 ASC NULLS FIRST], false, 0

(6) Scan parquet spark_catalog.default.item
Output [6]: [i_item_sk#5, i_item_id#6, i_item_desc#7, i_current_price#8, i_class#9, i_category#10]
Batched: true
Location [not included in comparison]/{warehouse_dir}/item]
PushedFilters: [In(i_category, [Books                                             ,Home                                              ,Sports                                            ]), IsNotNull(i_item_sk)]
ReadSchema: struct<i_item_sk:int,i_item_id:string,i_item_desc:string,i_current_price:decimal(7,2),i_class:string,i_category:string>

(7) ColumnarToRow [codegen id : 3]
Input [6]: [i_item_sk#5, i_item_id#6, i_item_desc#7, i_current_price#8, i_class#9, i_category#10]

(8) Filter [codegen id : 3]
Input [6]: [i_item_sk#5, i_item_id#6, i_item_desc#7, i_current_price#8, i_class#9, i_category#10]
Condition : (i_category#10 IN (Sports                                            ,Books                                             ,Home                                              ) AND isnotnull(i_item_sk#5))

(9) Exchange
Input [6]: [i_item_sk#5, i_item_id#6, i_item_desc#7, i_current_price#8, i_class#9, i_category#10]
Arguments: hashpartitioning(i_item_sk#5, 5), ENSURE_REQUIREMENTS, [plan_id=2]

(10) Sort [codegen id : 4]
Input [6]: [i_item_sk#5, i_item_id#6, i_item_desc#7, i_current_price#8, i_class#9, i_category#10]
Arguments: [i_item_sk#5 ASC NULLS FIRST], false, 0

(11) SortMergeJoin [codegen id : 6]
Left keys [1]: [ws_item_sk#1]
Right keys [1]: [i_item_sk#5]
Join type: Inner
Join condition: None

(12) Project [codegen id : 6]
Output [7]: [ws_ext_sales_price#2, ws_sold_date_sk#3, i_item_id#6, i_item_desc#7, i_current_price#8, i_class#9, i_category#10]
Input [9]: [ws_item_sk#1, ws_ext_sales_price#2, ws_sold_date_sk#3, i_item_sk#5, i_item_id#6, i_item_desc#7, i_current_price#8, i_class#9, i_category#10]

(13) ReusedExchange [Reuses operator id: 28]
Output [1]: [d_date_sk#11]

(14) BroadcastHashJoin [codegen id : 6]
Left keys [1]: [ws_sold_date_sk#3]
Right keys [1]: [d_date_sk#11]
Join type: Inner
Join condition: None

(15) Project [codegen id : 6]
Output [6]: [ws_ext_sales_price#2, i_item_id#6, i_item_desc#7, i_current_price#8, i_class#9, i_category#10]
Input [8]: [ws_ext_sales_price#2, ws_sold_date_sk#3, i_item_id#6, i_item_desc#7, i_current_price#8, i_class#9, i_category#10, d_date_sk#11]

(16) HashAggregate [codegen id : 6]
Input [6]: [ws_ext_sales_price#2, i_item_id#6, i_item_desc#7, i_current_price#8, i_class#9, i_category#10]
Keys [5]: [i_item_id#6, i_item_desc#7, i_category#10, i_class#9, i_current_price#8]
Functions [1]: [partial_sum(UnscaledValue(ws_ext_sales_price#2))]
Aggregate Attributes [1]: [sum#12]
Results [6]: [i_item_id#6, i_item_desc#7, i_category#10, i_class#9, i_current_price#8, sum#13]

(17) Exchange
Input [6]: [i_item_id#6, i_item_desc#7, i_category#10, i_class#9, i_current_price#8, sum#13]
Arguments: hashpartitioning(i_item_id#6, i_item_desc#7, i_category#10, i_class#9, i_current_price#8, 5), ENSURE_REQUIREMENTS, [plan_id=3]

(18) HashAggregate [codegen id : 7]
Input [6]: [i_item_id#6, i_item_desc#7, i_category#10, i_class#9, i_current_price#8, sum#13]
Keys [5]: [i_item_id#6, i_item_desc#7, i_category#10, i_class#9, i_current_price#8]
Functions [1]: [sum(UnscaledValue(ws_ext_sales_price#2))]
Aggregate Attributes [1]: [sum(UnscaledValue(ws_ext_sales_price#2))#14]
Results [7]: [i_item_desc#7, i_category#10, i_class#9, i_current_price#8, MakeDecimal(sum(UnscaledValue(ws_ext_sales_price#2))#14,17,2) AS itemrevenue#15, MakeDecimal(sum(UnscaledValue(ws_ext_sales_price#2))#14,17,2) AS _w0#16, i_item_id#6]

(19) Exchange
Input [7]: [i_item_desc#7, i_category#10, i_class#9, i_current_price#8, itemrevenue#15, _w0#16, i_item_id#6]
Arguments: hashpartitioning(i_class#9, 5), ENSURE_REQUIREMENTS, [plan_id=4]

(20) Sort [codegen id : 8]
Input [7]: [i_item_desc#7, i_category#10, i_class#9, i_current_price#8, itemrevenue#15, _w0#16, i_item_id#6]
Arguments: [i_class#9 ASC NULLS FIRST], false, 0

(21) Window
Input [7]: [i_item_desc#7, i_category#10, i_class#9, i_current_price#8, itemrevenue#15, _w0#16, i_item_id#6]
Arguments: [sum(_w0#16) windowspecdefinition(i_class#9, specifiedwindowframe(RowFrame, unboundedpreceding$(), unboundedfollowing$())) AS _we0#17], [i_class#9]

(22) Project [codegen id : 9]
Output [7]: [i_item_desc#7, i_category#10, i_class#9, i_current_price#8, itemrevenue#15, ((_w0#16 * 100) / _we0#17) AS revenueratio#18, i_item_id#6]
Input [8]: [i_item_desc#7, i_category#10, i_class#9, i_current_price#8, itemrevenue#15, _w0#16, i_item_id#6, _we0#17]

(23) TakeOrderedAndProject
Input [7]: [i_item_desc#7, i_category#10, i_class#9, i_current_price#8, itemrevenue#15, revenueratio#18, i_item_id#6]
Arguments: 100, [i_category#10 ASC NULLS FIRST, i_class#9 ASC NULLS FIRST, i_item_id#6 ASC NULLS FIRST, i_item_desc#7 ASC NULLS FIRST, revenueratio#18 ASC NULLS FIRST], [i_item_desc#7, i_category#10, i_class#9, i_current_price#8, itemrevenue#15, revenueratio#18]

===== Subqueries =====

Subquery:1 Hosting operator id = 1 Hosting Expression = ws_sold_date_sk#3 IN dynamicpruning#4
BroadcastExchange (28)
+- * Project (27)
   +- * Filter (26)
      +- * ColumnarToRow (25)
         +- Scan parquet spark_catalog.default.date_dim (24)


(24) Scan parquet spark_catalog.default.date_dim
Output [2]: [d_date_sk#11, d_date#19]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNotNull(d_date), GreaterThanOrEqual(d_date,1999-02-22), LessThanOrEqual(d_date,1999-03-24), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_date:date>

(25) ColumnarToRow [codegen id : 1]
Input [2]: [d_date_sk#11, d_date#19]

(26) Filter [codegen id : 1]
Input [2]: [d_date_sk#11, d_date#19]
Condition : (((isnotnull(d_date#19) AND (d_date#19 >= 1999-02-22)) AND (d_date#19 <= 1999-03-24)) AND isnotnull(d_date_sk#11))

(27) Project [codegen id : 1]
Output [1]: [d_date_sk#11]
Input [2]: [d_date_sk#11, d_date#19]

(28) BroadcastExchange
Input [1]: [d_date_sk#11]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=5]


