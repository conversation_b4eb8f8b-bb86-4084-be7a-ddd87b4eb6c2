TakeOrderedAndProject [substr(w_warehouse_name, 1, 20),sm_type,web_name,30 days ,31 - 60 days ,61 - 90 days ,91 - 120 days ,>120 days ]
  WholeStageCodegen (6)
    HashAggregate [_groupingexpression,sm_type,web_name,sum,sum,sum,sum,sum] [sum(CASE WHEN ((ws_ship_date_sk - ws_sold_date_sk) <= 30) THEN 1 ELSE 0 END),sum(CASE WHEN (((ws_ship_date_sk - ws_sold_date_sk) > 30) AND ((ws_ship_date_sk - ws_sold_date_sk) <= 60)) THEN 1 ELSE 0 END),sum(CASE WHEN (((ws_ship_date_sk - ws_sold_date_sk) > 60) AND ((ws_ship_date_sk - ws_sold_date_sk) <= 90)) THEN 1 ELSE 0 END),sum(CASE WHEN (((ws_ship_date_sk - ws_sold_date_sk) > 90) AND ((ws_ship_date_sk - ws_sold_date_sk) <= 120)) THEN 1 ELSE 0 END),sum(CASE WHEN ((ws_ship_date_sk - ws_sold_date_sk) > 120) THEN 1 ELSE 0 END),substr(w_warehouse_name, 1, 20),30 days ,31 - 60 days ,61 - 90 days ,91 - 120 days ,>120 days ,sum,sum,sum,sum,sum]
      InputAdapter
        Exchange [_groupingexpression,sm_type,web_name] #1
          WholeStageCodegen (5)
            HashAggregate [_groupingexpression,sm_type,web_name,ws_ship_date_sk,ws_sold_date_sk] [sum,sum,sum,sum,sum,sum,sum,sum,sum,sum]
              Project [ws_ship_date_sk,ws_sold_date_sk,sm_type,web_name,w_warehouse_name]
                BroadcastHashJoin [ws_warehouse_sk,w_warehouse_sk]
                  Project [ws_ship_date_sk,ws_warehouse_sk,ws_sold_date_sk,web_name,sm_type]
                    BroadcastHashJoin [ws_ship_mode_sk,sm_ship_mode_sk]
                      Project [ws_ship_date_sk,ws_ship_mode_sk,ws_warehouse_sk,ws_sold_date_sk,web_name]
                        BroadcastHashJoin [ws_web_site_sk,web_site_sk]
                          Project [ws_ship_date_sk,ws_web_site_sk,ws_ship_mode_sk,ws_warehouse_sk,ws_sold_date_sk]
                            BroadcastHashJoin [ws_ship_date_sk,d_date_sk]
                              Filter [ws_warehouse_sk,ws_ship_mode_sk,ws_web_site_sk,ws_ship_date_sk]
                                ColumnarToRow
                                  InputAdapter
                                    Scan parquet spark_catalog.default.web_sales [ws_ship_date_sk,ws_web_site_sk,ws_ship_mode_sk,ws_warehouse_sk,ws_sold_date_sk]
                              InputAdapter
                                BroadcastExchange #2
                                  WholeStageCodegen (1)
                                    Project [d_date_sk]
                                      Filter [d_month_seq,d_date_sk]
                                        ColumnarToRow
                                          InputAdapter
                                            Scan parquet spark_catalog.default.date_dim [d_date_sk,d_month_seq]
                          InputAdapter
                            BroadcastExchange #3
                              WholeStageCodegen (2)
                                Filter [web_site_sk]
                                  ColumnarToRow
                                    InputAdapter
                                      Scan parquet spark_catalog.default.web_site [web_site_sk,web_name]
                      InputAdapter
                        BroadcastExchange #4
                          WholeStageCodegen (3)
                            Filter [sm_ship_mode_sk]
                              ColumnarToRow
                                InputAdapter
                                  Scan parquet spark_catalog.default.ship_mode [sm_ship_mode_sk,sm_type]
                  InputAdapter
                    BroadcastExchange #5
                      WholeStageCodegen (4)
                        Filter [w_warehouse_sk]
                          ColumnarToRow
                            InputAdapter
                              Scan parquet spark_catalog.default.warehouse [w_warehouse_sk,w_warehouse_name]
