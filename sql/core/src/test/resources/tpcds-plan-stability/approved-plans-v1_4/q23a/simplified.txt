WholeStageCodegen (24)
  HashAggregate [sum,isEmpty] [sum(sales),sum(sales),sum,isEmpty]
    InputAdapter
      Exchange #1
        WholeStageCodegen (23)
          HashAggregate [sales] [sum,isEmpty,sum,isEmpty]
            InputAdapter
              Union
                WholeStageCodegen (11)
                  Project [cs_quantity,cs_list_price]
                    BroadcastHashJoin [cs_sold_date_sk,d_date_sk]
                      Project [cs_quantity,cs_list_price,cs_sold_date_sk]
                        SortMergeJoin [cs_bill_customer_sk,c_customer_sk]
                          InputAdapter
                            WholeStageCodegen (6)
                              Sort [cs_bill_customer_sk]
                                InputAdapter
                                  Exchange [cs_bill_customer_sk] #2
                                    WholeStageCodegen (5)
                                      Project [cs_bill_customer_sk,cs_quantity,cs_list_price,cs_sold_date_sk]
                                        BroadcastHashJoin [cs_item_sk,item_sk]
                                          ColumnarToRow
                                            InputAdapter
                                              Scan parquet spark_catalog.default.catalog_sales [cs_bill_customer_sk,cs_item_sk,cs_quantity,cs_list_price,cs_sold_date_sk]
                                                SubqueryBroadcast [d_date_sk] #1
                                                  BroadcastExchange #3
                                                    WholeStageCodegen (1)
                                                      Project [d_date_sk]
                                                        Filter [d_year,d_moy,d_date_sk]
                                                          ColumnarToRow
                                                            InputAdapter
                                                              Scan parquet spark_catalog.default.date_dim [d_date_sk,d_year,d_moy]
                                          InputAdapter
                                            BroadcastExchange #4
                                              WholeStageCodegen (4)
                                                Project [item_sk]
                                                  Filter [cnt]
                                                    HashAggregate [_groupingexpression,i_item_sk,d_date,count] [count(1),item_sk,cnt,count]
                                                      InputAdapter
                                                        Exchange [_groupingexpression,i_item_sk,d_date] #5
                                                          WholeStageCodegen (3)
                                                            HashAggregate [_groupingexpression,i_item_sk,d_date] [count,count]
                                                              Project [d_date,i_item_sk,i_item_desc]
                                                                BroadcastHashJoin [ss_item_sk,i_item_sk]
                                                                  Project [ss_item_sk,d_date]
                                                                    BroadcastHashJoin [ss_sold_date_sk,d_date_sk]
                                                                      Filter [ss_item_sk]
                                                                        ColumnarToRow
                                                                          InputAdapter
                                                                            Scan parquet spark_catalog.default.store_sales [ss_item_sk,ss_sold_date_sk]
                                                                              SubqueryBroadcast [d_date_sk] #2
                                                                                BroadcastExchange #6
                                                                                  WholeStageCodegen (1)
                                                                                    Project [d_date_sk,d_date]
                                                                                      Filter [d_year,d_date_sk]
                                                                                        ColumnarToRow
                                                                                          InputAdapter
                                                                                            Scan parquet spark_catalog.default.date_dim [d_date_sk,d_date,d_year]
                                                                      InputAdapter
                                                                        ReusedExchange [d_date_sk,d_date] #6
                                                                  InputAdapter
                                                                    BroadcastExchange #7
                                                                      WholeStageCodegen (2)
                                                                        Filter [i_item_sk]
                                                                          ColumnarToRow
                                                                            InputAdapter
                                                                              Scan parquet spark_catalog.default.item [i_item_sk,i_item_desc]
                          InputAdapter
                            WholeStageCodegen (9)
                              Sort [c_customer_sk]
                                Project [c_customer_sk]
                                  Filter [ssales]
                                    Subquery #3
                                      WholeStageCodegen (5)
                                        HashAggregate [max] [max(csales),tpcds_cmax,max]
                                          InputAdapter
                                            Exchange #10
                                              WholeStageCodegen (4)
                                                HashAggregate [csales] [max,max]
                                                  HashAggregate [c_customer_sk,sum,isEmpty] [sum((cast(ss_quantity as decimal(10,0)) * ss_sales_price)),csales,sum,isEmpty]
                                                    InputAdapter
                                                      Exchange [c_customer_sk] #11
                                                        WholeStageCodegen (3)
                                                          HashAggregate [c_customer_sk,ss_quantity,ss_sales_price] [sum,isEmpty,sum,isEmpty]
                                                            Project [ss_quantity,ss_sales_price,c_customer_sk]
                                                              BroadcastHashJoin [ss_sold_date_sk,d_date_sk]
                                                                Project [ss_quantity,ss_sales_price,ss_sold_date_sk,c_customer_sk]
                                                                  BroadcastHashJoin [ss_customer_sk,c_customer_sk]
                                                                    Filter [ss_customer_sk]
                                                                      ColumnarToRow
                                                                        InputAdapter
                                                                          Scan parquet spark_catalog.default.store_sales [ss_customer_sk,ss_quantity,ss_sales_price,ss_sold_date_sk]
                                                                            SubqueryBroadcast [d_date_sk] #4
                                                                              BroadcastExchange #12
                                                                                WholeStageCodegen (1)
                                                                                  Project [d_date_sk]
                                                                                    Filter [d_year,d_date_sk]
                                                                                      ColumnarToRow
                                                                                        InputAdapter
                                                                                          Scan parquet spark_catalog.default.date_dim [d_date_sk,d_year]
                                                                    InputAdapter
                                                                      ReusedExchange [c_customer_sk] #9
                                                                InputAdapter
                                                                  ReusedExchange [d_date_sk] #12
                                    HashAggregate [c_customer_sk,sum,isEmpty] [sum((cast(ss_quantity as decimal(10,0)) * ss_sales_price)),ssales,sum,isEmpty]
                                      InputAdapter
                                        Exchange [c_customer_sk] #8
                                          WholeStageCodegen (8)
                                            HashAggregate [c_customer_sk,ss_quantity,ss_sales_price] [sum,isEmpty,sum,isEmpty]
                                              Project [ss_quantity,ss_sales_price,c_customer_sk]
                                                BroadcastHashJoin [ss_customer_sk,c_customer_sk]
                                                  Project [ss_customer_sk,ss_quantity,ss_sales_price]
                                                    Filter [ss_customer_sk]
                                                      ColumnarToRow
                                                        InputAdapter
                                                          Scan parquet spark_catalog.default.store_sales [ss_customer_sk,ss_quantity,ss_sales_price,ss_sold_date_sk]
                                                  InputAdapter
                                                    BroadcastExchange #9
                                                      WholeStageCodegen (7)
                                                        Filter [c_customer_sk]
                                                          ColumnarToRow
                                                            InputAdapter
                                                              Scan parquet spark_catalog.default.customer [c_customer_sk]
                      InputAdapter
                        ReusedExchange [d_date_sk] #3
                WholeStageCodegen (22)
                  Project [ws_quantity,ws_list_price]
                    BroadcastHashJoin [ws_sold_date_sk,d_date_sk]
                      Project [ws_quantity,ws_list_price,ws_sold_date_sk]
                        SortMergeJoin [ws_bill_customer_sk,c_customer_sk]
                          InputAdapter
                            WholeStageCodegen (17)
                              Sort [ws_bill_customer_sk]
                                InputAdapter
                                  Exchange [ws_bill_customer_sk] #13
                                    WholeStageCodegen (16)
                                      Project [ws_bill_customer_sk,ws_quantity,ws_list_price,ws_sold_date_sk]
                                        BroadcastHashJoin [ws_item_sk,item_sk]
                                          ColumnarToRow
                                            InputAdapter
                                              Scan parquet spark_catalog.default.web_sales [ws_item_sk,ws_bill_customer_sk,ws_quantity,ws_list_price,ws_sold_date_sk]
                                                ReusedSubquery [d_date_sk] #1
                                          InputAdapter
                                            ReusedExchange [item_sk] #4
                          InputAdapter
                            WholeStageCodegen (20)
                              Sort [c_customer_sk]
                                Project [c_customer_sk]
                                  Filter [ssales]
                                    ReusedSubquery [tpcds_cmax] #3
                                    HashAggregate [c_customer_sk,sum,isEmpty] [sum((cast(ss_quantity as decimal(10,0)) * ss_sales_price)),ssales,sum,isEmpty]
                                      InputAdapter
                                        ReusedExchange [c_customer_sk,sum,isEmpty] #8
                      InputAdapter
                        ReusedExchange [d_date_sk] #3
