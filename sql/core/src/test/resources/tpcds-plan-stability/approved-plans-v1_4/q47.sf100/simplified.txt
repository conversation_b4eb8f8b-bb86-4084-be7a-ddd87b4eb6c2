TakeOrderedAndProject [sum_sales,avg_monthly_sales,s_store_name,i_category,i_brand,s_company_name,d_year,d_moy,psum,nsum]
  WholeStageCodegen (36)
    Project [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy,avg_monthly_sales,sum_sales,sum_sales,sum_sales]
      SortMergeJoin [i_category,i_brand,s_store_name,s_company_name,rn,i_category,i_brand,s_store_name,s_company_name,rn]
        InputAdapter
          WholeStageCodegen (24)
            Project [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy,sum_sales,avg_monthly_sales,rn,sum_sales]
              SortMergeJoin [i_category,i_brand,s_store_name,s_company_name,rn,i_category,i_brand,s_store_name,s_company_name,rn]
                InputAdapter
                  WholeStageCodegen (12)
                    Sort [i_category,i_brand,s_store_name,s_company_name,rn]
                      InputAdapter
                        Exchange [i_category,i_brand,s_store_name,s_company_name,rn] #1
                          WholeStageCodegen (11)
                            Project [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy,sum_sales,avg_monthly_sales,rn]
                              Filter [avg_monthly_sales,sum_sales]
                                InputAdapter
                                  Window [_w0,i_category,i_brand,s_store_name,s_company_name,d_year]
                                    WholeStageCodegen (10)
                                      Filter [d_year]
                                        InputAdapter
                                          Window [d_year,d_moy,i_category,i_brand,s_store_name,s_company_name]
                                            WholeStageCodegen (9)
                                              Sort [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy]
                                                InputAdapter
                                                  Exchange [i_category,i_brand,s_store_name,s_company_name] #2
                                                    WholeStageCodegen (8)
                                                      HashAggregate [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy,sum] [sum(UnscaledValue(ss_sales_price)),sum_sales,_w0,sum]
                                                        InputAdapter
                                                          Exchange [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy] #3
                                                            WholeStageCodegen (7)
                                                              HashAggregate [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy,ss_sales_price] [sum,sum]
                                                                Project [i_brand,i_category,ss_sales_price,d_year,d_moy,s_store_name,s_company_name]
                                                                  SortMergeJoin [ss_item_sk,i_item_sk]
                                                                    InputAdapter
                                                                      WholeStageCodegen (4)
                                                                        Sort [ss_item_sk]
                                                                          InputAdapter
                                                                            Exchange [ss_item_sk] #4
                                                                              WholeStageCodegen (3)
                                                                                Project [ss_item_sk,ss_sales_price,d_year,d_moy,s_store_name,s_company_name]
                                                                                  BroadcastHashJoin [ss_store_sk,s_store_sk]
                                                                                    Project [ss_item_sk,ss_store_sk,ss_sales_price,d_year,d_moy]
                                                                                      BroadcastHashJoin [ss_sold_date_sk,d_date_sk]
                                                                                        Filter [ss_item_sk,ss_store_sk]
                                                                                          ColumnarToRow
                                                                                            InputAdapter
                                                                                              Scan parquet spark_catalog.default.store_sales [ss_item_sk,ss_store_sk,ss_sales_price,ss_sold_date_sk]
                                                                                                SubqueryBroadcast [d_date_sk] #1
                                                                                                  BroadcastExchange #5
                                                                                                    WholeStageCodegen (1)
                                                                                                      Filter [d_year,d_moy,d_date_sk]
                                                                                                        ColumnarToRow
                                                                                                          InputAdapter
                                                                                                            Scan parquet spark_catalog.default.date_dim [d_date_sk,d_year,d_moy]
                                                                                        InputAdapter
                                                                                          ReusedExchange [d_date_sk,d_year,d_moy] #5
                                                                                    InputAdapter
                                                                                      BroadcastExchange #6
                                                                                        WholeStageCodegen (2)
                                                                                          Filter [s_store_sk,s_store_name,s_company_name]
                                                                                            ColumnarToRow
                                                                                              InputAdapter
                                                                                                Scan parquet spark_catalog.default.store [s_store_sk,s_store_name,s_company_name]
                                                                    InputAdapter
                                                                      WholeStageCodegen (6)
                                                                        Sort [i_item_sk]
                                                                          InputAdapter
                                                                            Exchange [i_item_sk] #7
                                                                              WholeStageCodegen (5)
                                                                                Filter [i_item_sk,i_category,i_brand]
                                                                                  ColumnarToRow
                                                                                    InputAdapter
                                                                                      Scan parquet spark_catalog.default.item [i_item_sk,i_brand,i_category]
                InputAdapter
                  WholeStageCodegen (23)
                    Sort [i_category,i_brand,s_store_name,s_company_name,rn]
                      InputAdapter
                        Exchange [i_category,i_brand,s_store_name,s_company_name,rn] #8
                          WholeStageCodegen (22)
                            Project [i_category,i_brand,s_store_name,s_company_name,sum_sales,rn]
                              InputAdapter
                                Window [d_year,d_moy,i_category,i_brand,s_store_name,s_company_name]
                                  WholeStageCodegen (21)
                                    Sort [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy]
                                      InputAdapter
                                        Exchange [i_category,i_brand,s_store_name,s_company_name] #9
                                          WholeStageCodegen (20)
                                            HashAggregate [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy,sum] [sum(UnscaledValue(ss_sales_price)),sum_sales,sum]
                                              InputAdapter
                                                ReusedExchange [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy,sum] #3
        InputAdapter
          WholeStageCodegen (35)
            Sort [i_category,i_brand,s_store_name,s_company_name,rn]
              InputAdapter
                Exchange [i_category,i_brand,s_store_name,s_company_name,rn] #10
                  WholeStageCodegen (34)
                    Project [i_category,i_brand,s_store_name,s_company_name,sum_sales,rn]
                      InputAdapter
                        Window [d_year,d_moy,i_category,i_brand,s_store_name,s_company_name]
                          WholeStageCodegen (33)
                            Sort [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy]
                              InputAdapter
                                ReusedExchange [i_category,i_brand,s_store_name,s_company_name,d_year,d_moy,sum_sales] #9
