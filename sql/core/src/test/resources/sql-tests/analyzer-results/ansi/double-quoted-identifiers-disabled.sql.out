-- Automatically generated by SQLQueryTestSuite
-- !query
SELECT 1 FROM "not_exist"
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"not_exist\"'",
    "hint" : ""
  }
}


-- !query
USE SCHEMA "not_exist"
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"not_exist\"'",
    "hint" : ""
  }
}


-- !query
ALTER TABLE "not_exist" ADD COLUMN not_exist int
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"not_exist\"'",
    "hint" : ""
  }
}


-- !query
ALTER TABLE not_exist ADD COLUMN "not_exist" int
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"not_exist\"'",
    "hint" : ""
  }
}


-- !query
SELECT 1 AS "not_exist" FROM not_exist
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"not_exist\"'",
    "hint" : ""
  }
}


-- !query
SELECT 1 FROM not_exist AS X("hello")
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"hello\"'",
    "hint" : ""
  }
}


-- !query
SELECT "not_exist"()
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"not_exist\"'",
    "hint" : ""
  }
}


-- !query
SELECT "not_exist".not_exist()
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"not_exist\"'",
    "hint" : ""
  }
}


-- !query
SELECT 1 FROM `hello`
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "TABLE_OR_VIEW_NOT_FOUND",
  "sqlState" : "42P01",
  "messageParameters" : {
    "relationName" : "`hello`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 15,
    "stopIndex" : 21,
    "fragment" : "`hello`"
  } ]
}


-- !query
USE SCHEMA `not_exist`
-- !query analysis
org.apache.spark.sql.catalyst.analysis.NoSuchDatabaseException
{
  "errorClass" : "SCHEMA_NOT_FOUND",
  "sqlState" : "42704",
  "messageParameters" : {
    "schemaName" : "`not_exist`"
  }
}


-- !query
ALTER TABLE `not_exist` ADD COLUMN not_exist int
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "TABLE_OR_VIEW_NOT_FOUND",
  "sqlState" : "42P01",
  "messageParameters" : {
    "relationName" : "`not_exist`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 13,
    "stopIndex" : 23,
    "fragment" : "`not_exist`"
  } ]
}


-- !query
ALTER TABLE not_exist ADD COLUMN `not_exist` int
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "TABLE_OR_VIEW_NOT_FOUND",
  "sqlState" : "42P01",
  "messageParameters" : {
    "relationName" : "`not_exist`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 13,
    "stopIndex" : 21,
    "fragment" : "not_exist"
  } ]
}


-- !query
SELECT 1 AS `not_exist` FROM `not_exist`
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "TABLE_OR_VIEW_NOT_FOUND",
  "sqlState" : "42P01",
  "messageParameters" : {
    "relationName" : "`not_exist`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 30,
    "stopIndex" : 40,
    "fragment" : "`not_exist`"
  } ]
}


-- !query
SELECT 1 FROM not_exist AS X(`hello`)
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "TABLE_OR_VIEW_NOT_FOUND",
  "sqlState" : "42P01",
  "messageParameters" : {
    "relationName" : "`not_exist`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 15,
    "stopIndex" : 37,
    "fragment" : "not_exist AS X(`hello`)"
  } ]
}


-- !query
SELECT `not_exist`()
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "UNRESOLVED_ROUTINE",
  "sqlState" : "42883",
  "messageParameters" : {
    "routineName" : "`not_exist`",
    "searchPath" : "[`system`.`builtin`, `system`.`session`, `spark_catalog`.`default`]"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 20,
    "fragment" : "`not_exist`()"
  } ]
}


-- !query
SELECT `not_exist`.not_exist()
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "UNRESOLVED_ROUTINE",
  "sqlState" : "42883",
  "messageParameters" : {
    "routineName" : "`not_exist`.`not_exist`",
    "searchPath" : "[`system`.`builtin`, `system`.`session`, `spark_catalog`.`default`]"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 30,
    "fragment" : "`not_exist`.not_exist()"
  } ]
}


-- !query
SELECT "hello"
-- !query analysis
Project [hello AS hello#x]
+- OneRowRelation


-- !query
CREATE TEMPORARY VIEW v(c1 COMMENT "hello") AS SELECT 1
-- !query analysis
CreateViewCommand `v`, [(c1,Some(hello))], SELECT 1, false, false, LocalTempView, true
   +- Project [1 AS 1#x]
      +- OneRowRelation


-- !query
DROP VIEW v
-- !query analysis
DropTempViewCommand v


-- !query
SELECT INTERVAL "1" YEAR
-- !query analysis
Project [INTERVAL '1' YEAR AS INTERVAL '1' YEAR#x]
+- OneRowRelation


-- !query
SELECT 'hello'
-- !query analysis
Project [hello AS hello#x]
+- OneRowRelation


-- !query
CREATE TEMPORARY VIEW v(c1 COMMENT 'hello') AS SELECT 1
-- !query analysis
CreateViewCommand `v`, [(c1,Some(hello))], SELECT 1, false, false, LocalTempView, true
   +- Project [1 AS 1#x]
      +- OneRowRelation


-- !query
DROP VIEW v
-- !query analysis
DropTempViewCommand v


-- !query
SELECT INTERVAL '1' YEAR
-- !query analysis
Project [INTERVAL '1' YEAR AS INTERVAL '1' YEAR#x]
+- OneRowRelation


-- !query
CREATE SCHEMA "myschema"
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"myschema\"'",
    "hint" : ""
  }
}


-- !query
CREATE TEMPORARY VIEW "myview"("c1") AS
  WITH "v"("a") AS (SELECT 1) SELECT "a" FROM "v"
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"myview\"'",
    "hint" : ""
  }
}


-- !query
SELECT "a1" AS "a2" FROM "myview" AS "atab"("a1")
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"a2\"'",
    "hint" : ""
  }
}


-- !query
DROP TABLE "myview"
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"myview\"'",
    "hint" : ""
  }
}


-- !query
DROP SCHEMA "myschema"
-- !query analysis
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "PARSE_SYNTAX_ERROR",
  "sqlState" : "42601",
  "messageParameters" : {
    "error" : "'\"myschema\"'",
    "hint" : ""
  }
}
