-- Automatically generated by SQLQueryTestSuite
-- !query
CREATE TEMPORARY VIEW t AS SELECT 1.0 as a, 0.0 as b
-- !query analysis
CreateViewCommand `t`, SELECT 1.0 as a, 0.0 as b, false, false, LocalTempView, true
   +- Project [1.0 AS a#x, 0.0 AS b#x]
      +- OneRowRelation


-- !query
select a / b from t
-- !query analysis
Project [(a#x / b#x) AS (a / b)#x]
+- SubqueryAlias t
   +- View (`t`, [a#x,b#x])
      +- Project [cast(a#x as decimal(2,1)) AS a#x, cast(b#x as decimal(1,1)) AS b#x]
         +- Project [1.0 AS a#x, 0.0 AS b#x]
            +- OneRowRelation


-- !query
select a % b from t
-- !query analysis
Project [(a#x % b#x) AS (a % b)#x]
+- SubqueryAlias t
   +- View (`t`, [a#x,b#x])
      +- Project [cast(a#x as decimal(2,1)) AS a#x, cast(b#x as decimal(1,1)) AS b#x]
         +- Project [1.0 AS a#x, 0.0 AS b#x]
            +- OneRowRelation


-- !query
select pmod(a, b) from t
-- !query analysis
Project [pmod(a#x, b#x) AS pmod(a, b)#x]
+- SubqueryAlias t
   +- View (`t`, [a#x,b#x])
      +- Project [cast(a#x as decimal(2,1)) AS a#x, cast(b#x as decimal(1,1)) AS b#x]
         +- Project [1.0 AS a#x, 0.0 AS b#x]
            +- OneRowRelation


-- !query
create table decimals_test(id int, a decimal(38,18), b decimal(38,18)) using parquet
-- !query analysis
CreateDataSourceTableCommand `spark_catalog`.`default`.`decimals_test`, false


-- !query
insert into decimals_test values(1, 100.0, 999.0), (2, 12345.123, 12345.123),
  (3, 0.1234567891011, 1234.1), (4, 123456789123456789.0, 1.123456789123456789)
-- !query analysis
InsertIntoHadoopFsRelationCommand file:[not included in comparison]/{warehouse_dir}/decimals_test, false, Parquet, [path=file:[not included in comparison]/{warehouse_dir}/decimals_test], Append, `spark_catalog`.`default`.`decimals_test`, org.apache.spark.sql.execution.datasources.InMemoryFileIndex(file:[not included in comparison]/{warehouse_dir}/decimals_test), [id, a, b]
+- Project [cast(col1#x as int) AS id#x, cast(col2#x as decimal(38,18)) AS a#x, cast(col3#x as decimal(38,18)) AS b#x]
   +- LocalRelation [col1#x, col2#x, col3#x]


-- !query
select id, a+b, a-b, a*b, a/b from decimals_test order by id
-- !query analysis
Sort [id#x ASC NULLS FIRST], true
+- Project [id#x, (a#x + b#x) AS (a + b)#x, (a#x - b#x) AS (a - b)#x, (a#x * b#x) AS (a * b)#x, (a#x / b#x) AS (a / b)#x]
   +- SubqueryAlias spark_catalog.default.decimals_test
      +- Relation spark_catalog.default.decimals_test[id#x,a#x,b#x] parquet


-- !query
select id, a*10, b/10 from decimals_test order by id
-- !query analysis
Sort [id#x ASC NULLS FIRST], true
+- Project [id#x, (a#x * cast(10 as decimal(2,0))) AS (a * 10)#x, (b#x / cast(10 as decimal(2,0))) AS (b / 10)#x]
   +- SubqueryAlias spark_catalog.default.decimals_test
      +- Relation spark_catalog.default.decimals_test[id#x,a#x,b#x] parquet


-- !query
select 10.3 * 3.0
-- !query analysis
Project [(10.3 * 3.0) AS (10.3 * 3.0)#x]
+- OneRowRelation


-- !query
select 10.3000 * 3.0
-- !query analysis
Project [(10.3000 * 3.0) AS (10.3000 * 3.0)#x]
+- OneRowRelation


-- !query
select 10.30000 * 30.0
-- !query analysis
Project [(10.30000 * 30.0) AS (10.30000 * 30.0)#x]
+- OneRowRelation


-- !query
select 10.300000000000000000 * 3.000000000000000000
-- !query analysis
Project [(10.300000000000000000 * 3.000000000000000000) AS (10.300000000000000000 * 3.000000000000000000)#x]
+- OneRowRelation


-- !query
select 10.300000000000000000 * 3.0000000000000000000
-- !query analysis
Project [(10.300000000000000000 * 3.0000000000000000000) AS (10.300000000000000000 * 3.0000000000000000000)#x]
+- OneRowRelation


-- !query
select 2.35E10 * 1.0
-- !query analysis
Project [(2.35E10 * cast(1.0 as double)) AS (2.35E10 * 1.0)#x]
+- OneRowRelation


-- !query
select (5e36BD + 0.1) + 5e36BD
-- !query analysis
Project [((5000000000000000000000000000000000000 + 0.1) + 5000000000000000000000000000000000000) AS ((5000000000000000000000000000000000000 + 0.1) + 5000000000000000000000000000000000000)#x]
+- OneRowRelation


-- !query
select (-4e36BD - 0.1) - 7e36BD
-- !query analysis
Project [((-4000000000000000000000000000000000000 - 0.1) - 7000000000000000000000000000000000000) AS ((-4000000000000000000000000000000000000 - 0.1) - 7000000000000000000000000000000000000)#x]
+- OneRowRelation


-- !query
select 12345678901234567890.0 * 12345678901234567890.0
-- !query analysis
Project [(12345678901234567890.0 * 12345678901234567890.0) AS (12345678901234567890.0 * 12345678901234567890.0)#x]
+- OneRowRelation


-- !query
select 1e35BD / 0.1
-- !query analysis
Project [(100000000000000000000000000000000000 / 0.1) AS (100000000000000000000000000000000000 / 0.1)#x]
+- OneRowRelation


-- !query
select 1.2345678901234567890E30BD * 1.2345678901234567890E25BD
-- !query analysis
Project [(1234567890123456789000000000000 * 12345678901234567890000000) AS (1234567890123456789000000000000 * 12345678901234567890000000)#x]
+- OneRowRelation


-- !query
select 12345678912345678912345678912.1234567 + 9999999999999999999999999999999.12345
-- !query analysis
Project [(12345678912345678912345678912.1234567 + 9999999999999999999999999999999.12345) AS (12345678912345678912345678912.1234567 + 9999999999999999999999999999999.12345)#x]
+- OneRowRelation


-- !query
select 123456789123456789.1234567890 * 1.123456789123456789
-- !query analysis
Project [(123456789123456789.1234567890 * 1.123456789123456789) AS (123456789123456789.1234567890 * 1.123456789123456789)#x]
+- OneRowRelation


-- !query
select 12345678912345.123456789123 / 0.000000012345678
-- !query analysis
Project [(12345678912345.123456789123 / 1.2345678E-8) AS (12345678912345.123456789123 / 1.2345678E-8)#x]
+- OneRowRelation


-- !query
SELECT CAST(20 AS DECIMAL(4, 1))
UNION ALL
SELECT CAST(10 AS DECIMAL(3, 1)) + CAST(90 AS DECIMAL(3, 1))
-- !query analysis
Union false, false
:- Project [cast(20 as decimal(4,1)) AS CAST(20 AS DECIMAL(4,1))#x]
:  +- OneRowRelation
+- Project [(cast(10 as decimal(3,1)) + cast(90 as decimal(3,1))) AS (CAST(10 AS DECIMAL(3,1)) + CAST(90 AS DECIMAL(3,1)))#x]
   +- OneRowRelation


-- !query
SELECT CAST(20 AS DECIMAL(4, 1))
UNION ALL
SELECT CAST(10 AS DECIMAL(3, 1)) - CAST(-90 AS DECIMAL(3, 1))
-- !query analysis
Union false, false
:- Project [cast(20 as decimal(4,1)) AS CAST(20 AS DECIMAL(4,1))#x]
:  +- OneRowRelation
+- Project [(cast(10 as decimal(3,1)) - cast(-90 as decimal(3,1))) AS (CAST(10 AS DECIMAL(3,1)) - CAST(-90 AS DECIMAL(3,1)))#x]
   +- OneRowRelation


-- !query
SELECT CAST(20 AS DECIMAL(4, 1))
UNION ALL
SELECT CAST(10 AS DECIMAL(3, 1)) * CAST(10 AS DECIMAL(3, 1))
-- !query analysis
Union false, false
:- Project [cast(CAST(20 AS DECIMAL(4,1))#x as decimal(7,2)) AS CAST(20 AS DECIMAL(4,1))#x]
:  +- Project [cast(20 as decimal(4,1)) AS CAST(20 AS DECIMAL(4,1))#x]
:     +- OneRowRelation
+- Project [(cast(10 as decimal(3,1)) * cast(10 as decimal(3,1))) AS (CAST(10 AS DECIMAL(3,1)) * CAST(10 AS DECIMAL(3,1)))#x]
   +- OneRowRelation


-- !query
SELECT CAST(20 AS DECIMAL(4, 1))
UNION ALL
SELECT CAST(10 AS DECIMAL(3, 1)) / CAST(10 AS DECIMAL(3, 1))
-- !query analysis
Union false, false
:- Project [cast(CAST(20 AS DECIMAL(4,1))#x as decimal(9,6)) AS CAST(20 AS DECIMAL(4,1))#x]
:  +- Project [cast(20 as decimal(4,1)) AS CAST(20 AS DECIMAL(4,1))#x]
:     +- OneRowRelation
+- Project [(cast(10 as decimal(3,1)) / cast(10 as decimal(3,1))) AS (CAST(10 AS DECIMAL(3,1)) / CAST(10 AS DECIMAL(3,1)))#x]
   +- OneRowRelation


-- !query
SELECT CAST(20 AS DECIMAL(4, 1))
UNION ALL
SELECT CAST(10 AS DECIMAL(10, 2)) % CAST(3 AS DECIMAL(5, 1))
-- !query analysis
Union false, false
:- Project [cast(CAST(20 AS DECIMAL(4,1))#x as decimal(6,2)) AS CAST(20 AS DECIMAL(4,1))#x]
:  +- Project [cast(20 as decimal(4,1)) AS CAST(20 AS DECIMAL(4,1))#x]
:     +- OneRowRelation
+- Project [(cast(10 as decimal(10,2)) % cast(3 as decimal(5,1))) AS (CAST(10 AS DECIMAL(10,2)) % CAST(3 AS DECIMAL(5,1)))#x]
   +- OneRowRelation


-- !query
SELECT CAST(20 AS DECIMAL(4, 1))
UNION ALL
SELECT pmod(CAST(10 AS DECIMAL(10, 2)), CAST(3 AS DECIMAL(5, 1)))
-- !query analysis
Union false, false
:- Project [cast(CAST(20 AS DECIMAL(4,1))#x as decimal(6,2)) AS CAST(20 AS DECIMAL(4,1))#x]
:  +- Project [cast(20 as decimal(4,1)) AS CAST(20 AS DECIMAL(4,1))#x]
:     +- OneRowRelation
+- Project [pmod(cast(10 as decimal(10,2)), cast(3 as decimal(5,1))) AS pmod(CAST(10 AS DECIMAL(10,2)), CAST(3 AS DECIMAL(5,1)))#x]
   +- OneRowRelation


-- !query
SELECT CAST(20 AS DECIMAL(4, 1))
UNION ALL
SELECT CAST(10 AS DECIMAL(10, 2)) div CAST(3 AS DECIMAL(5, 1))
-- !query analysis
Union false, false
:- Project [cast(CAST(20 AS DECIMAL(4,1))#x as decimal(21,1)) AS CAST(20 AS DECIMAL(4,1))#x]
:  +- Project [cast(20 as decimal(4,1)) AS CAST(20 AS DECIMAL(4,1))#x]
:     +- OneRowRelation
+- Project [cast((CAST(10 AS DECIMAL(10,2)) div CAST(3 AS DECIMAL(5,1)))#xL as decimal(21,1)) AS (CAST(10 AS DECIMAL(10,2)) div CAST(3 AS DECIMAL(5,1)))#x]
   +- Project [(cast(10 as decimal(10,2)) div cast(3 as decimal(5,1))) AS (CAST(10 AS DECIMAL(10,2)) div CAST(3 AS DECIMAL(5,1)))#xL]
      +- OneRowRelation


-- !query
set spark.sql.decimalOperations.allowPrecisionLoss=false
-- !query analysis
SetCommand (spark.sql.decimalOperations.allowPrecisionLoss,Some(false))


-- !query
select id, a+b, a-b, a*b, a/b from decimals_test order by id
-- !query analysis
Sort [id#x ASC NULLS FIRST], true
+- Project [id#x, (a#x + b#x) AS (a + b)#x, (a#x - b#x) AS (a - b)#x, (a#x * b#x) AS (a * b)#x, (a#x / b#x) AS (a / b)#x]
   +- SubqueryAlias spark_catalog.default.decimals_test
      +- Relation spark_catalog.default.decimals_test[id#x,a#x,b#x] parquet


-- !query
select id, a*10, b/10 from decimals_test order by id
-- !query analysis
Sort [id#x ASC NULLS FIRST], true
+- Project [id#x, (a#x * cast(10 as decimal(2,0))) AS (a * 10)#x, (b#x / cast(10 as decimal(2,0))) AS (b / 10)#x]
   +- SubqueryAlias spark_catalog.default.decimals_test
      +- Relation spark_catalog.default.decimals_test[id#x,a#x,b#x] parquet


-- !query
select 10.3 * 3.0
-- !query analysis
Project [(10.3 * 3.0) AS (10.3 * 3.0)#x]
+- OneRowRelation


-- !query
select 10.3000 * 3.0
-- !query analysis
Project [(10.3000 * 3.0) AS (10.3000 * 3.0)#x]
+- OneRowRelation


-- !query
select 10.30000 * 30.0
-- !query analysis
Project [(10.30000 * 30.0) AS (10.30000 * 30.0)#x]
+- OneRowRelation


-- !query
select 10.300000000000000000 * 3.000000000000000000
-- !query analysis
Project [(10.300000000000000000 * 3.000000000000000000) AS (10.300000000000000000 * 3.000000000000000000)#x]
+- OneRowRelation


-- !query
select 10.300000000000000000 * 3.0000000000000000000
-- !query analysis
Project [(10.300000000000000000 * 3.0000000000000000000) AS (10.300000000000000000 * 3.0000000000000000000)#x]
+- OneRowRelation


-- !query
select 2.35E10 * 1.0
-- !query analysis
Project [(2.35E10 * cast(1.0 as double)) AS (2.35E10 * 1.0)#x]
+- OneRowRelation


-- !query
select (5e36BD + 0.1) + 5e36BD
-- !query analysis
Project [((5000000000000000000000000000000000000 + 0.1) + 5000000000000000000000000000000000000) AS ((5000000000000000000000000000000000000 + 0.1) + 5000000000000000000000000000000000000)#x]
+- OneRowRelation


-- !query
select (-4e36BD - 0.1) - 7e36BD
-- !query analysis
Project [((-4000000000000000000000000000000000000 - 0.1) - 7000000000000000000000000000000000000) AS ((-4000000000000000000000000000000000000 - 0.1) - 7000000000000000000000000000000000000)#x]
+- OneRowRelation


-- !query
select 12345678901234567890.0 * 12345678901234567890.0
-- !query analysis
Project [(12345678901234567890.0 * 12345678901234567890.0) AS (12345678901234567890.0 * 12345678901234567890.0)#x]
+- OneRowRelation


-- !query
select 1e35BD / 0.1
-- !query analysis
Project [(100000000000000000000000000000000000 / 0.1) AS (100000000000000000000000000000000000 / 0.1)#x]
+- OneRowRelation


-- !query
select 1.2345678901234567890E30BD * 1.2345678901234567890E25BD
-- !query analysis
Project [(1234567890123456789000000000000 * 12345678901234567890000000) AS (1234567890123456789000000000000 * 12345678901234567890000000)#x]
+- OneRowRelation


-- !query
select 12345678912345678912345678912.1234567 + 9999999999999999999999999999999.12345
-- !query analysis
Project [(12345678912345678912345678912.1234567 + 9999999999999999999999999999999.12345) AS (12345678912345678912345678912.1234567 + 9999999999999999999999999999999.12345)#x]
+- OneRowRelation


-- !query
select 123456789123456789.1234567890 * 1.123456789123456789
-- !query analysis
Project [(123456789123456789.1234567890 * 1.123456789123456789) AS (123456789123456789.1234567890 * 1.123456789123456789)#x]
+- OneRowRelation


-- !query
select 12345678912345.123456789123 / 0.000000012345678
-- !query analysis
Project [(12345678912345.123456789123 / 1.2345678E-8) AS (12345678912345.123456789123 / 1.2345678E-8)#x]
+- OneRowRelation


-- !query
select 1.0123456789012345678901234567890123456e36BD / 0.1
-- !query analysis
Project [(1012345678901234567890123456789012345.6 / 0.1) AS (1012345678901234567890123456789012345.6 / 0.1)#x]
+- OneRowRelation


-- !query
select 1.0123456789012345678901234567890123456e35BD / 1.0
-- !query analysis
Project [(101234567890123456789012345678901234.56 / 1.0) AS (101234567890123456789012345678901234.56 / 1.0)#x]
+- OneRowRelation


-- !query
select 1.0123456789012345678901234567890123456e34BD / 1.0
-- !query analysis
Project [(10123456789012345678901234567890123.456 / 1.0) AS (10123456789012345678901234567890123.456 / 1.0)#x]
+- OneRowRelation


-- !query
select 1.0123456789012345678901234567890123456e33BD / 1.0
-- !query analysis
Project [(1012345678901234567890123456789012.3456 / 1.0) AS (1012345678901234567890123456789012.3456 / 1.0)#x]
+- OneRowRelation


-- !query
select 1.0123456789012345678901234567890123456e32BD / 1.0
-- !query analysis
Project [(101234567890123456789012345678901.23456 / 1.0) AS (101234567890123456789012345678901.23456 / 1.0)#x]
+- OneRowRelation


-- !query
select 1.0123456789012345678901234567890123456e31BD / 1.0
-- !query analysis
Project [(10123456789012345678901234567890.123456 / 1.0) AS (10123456789012345678901234567890.123456 / 1.0)#x]
+- OneRowRelation


-- !query
select 1.0123456789012345678901234567890123456e31BD / 0.1
-- !query analysis
Project [(10123456789012345678901234567890.123456 / 0.1) AS (10123456789012345678901234567890.123456 / 0.1)#x]
+- OneRowRelation


-- !query
select 1.0123456789012345678901234567890123456e31BD / 10.0
-- !query analysis
Project [(10123456789012345678901234567890.123456 / 10.0) AS (10123456789012345678901234567890.123456 / 10.0)#x]
+- OneRowRelation


-- !query
drop table decimals_test
-- !query analysis
DropTableCommand `spark_catalog`.`default`.`decimals_test`, false, false, false
