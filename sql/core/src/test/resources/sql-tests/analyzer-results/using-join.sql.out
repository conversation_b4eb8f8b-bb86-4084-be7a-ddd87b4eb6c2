-- Automatically generated by SQLQueryTestSuite
-- !query
create temporary view nt1 as select * from values
  ("one", 1),
  ("two", 2),
  ("three", 3)
  as nt1(k, v1)
-- !query analysis
CreateViewCommand `nt1`, select * from values
  ("one", 1),
  ("two", 2),
  ("three", 3)
  as nt1(k, v1), false, false, LocalTempView, true
   +- Project [k#x, v1#x]
      +- SubqueryAlias nt1
         +- LocalRelation [k#x, v1#x]


-- !query
create temporary view nt2 as select * from values
  ("one", 1),
  ("two", 22),
  ("one", 5),
  ("four", 4)
  as nt2(k, v2)
-- !query analysis
CreateViewCommand `nt2`, select * from values
  ("one", 1),
  ("two", 22),
  ("one", 5),
  ("four", 4)
  as nt2(k, v2), false, false, LocalTempView, true
   +- Project [k#x, v2#x]
      +- SubqueryAlias nt2
         +- LocalRelation [k#x, v2#x]


-- !query
SELECT * FROM nt1 left outer join nt2 using (k)
-- !query analysis
Project [k#x, v1#x, v2#x]
+- Project [k#x, v1#x, v2#x]
   +- Join LeftOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k FROM nt1 left outer join nt2 using (k)
-- !query analysis
Project [k#x]
+- Project [k#x, v1#x, v2#x]
   +- Join LeftOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.*, nt2.* FROM nt1 left outer join nt2 using (k)
-- !query analysis
Project [k#x, v1#x, k#x, v2#x]
+- Project [k#x, v1#x, v2#x, k#x]
   +- Join LeftOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.k, nt2.k FROM nt1 left outer join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [k#x, v1#x, v2#x, k#x]
   +- Join LeftOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k FROM (SELECT nt2.k FROM nt1 left outer join nt2 using (k))
-- !query analysis
Project [k#x]
+- SubqueryAlias __auto_generated_subquery_name
   +- Project [k#x]
      +- Project [k#x, v1#x, v2#x, k#x]
         +- Join LeftOuter, (k#x = k#x)
            :- SubqueryAlias nt1
            :  +- View (`nt1`, [k#x,v1#x])
            :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
            :        +- Project [k#x, v1#x]
            :           +- SubqueryAlias nt1
            :              +- LocalRelation [k#x, v1#x]
            +- SubqueryAlias nt2
               +- View (`nt2`, [k#x,v2#x])
                  +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
                     +- Project [k#x, v2#x]
                        +- SubqueryAlias nt2
                           +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt2.k AS key FROM nt1 left outer join nt2 using (k) ORDER BY key
-- !query analysis
Sort [key#x ASC NULLS FIRST], true
+- Project [k#x AS key#x]
   +- Project [k#x, v1#x, v2#x, k#x]
      +- Join LeftOuter, (k#x = k#x)
         :- SubqueryAlias nt1
         :  +- View (`nt1`, [k#x,v1#x])
         :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
         :        +- Project [k#x, v1#x]
         :           +- SubqueryAlias nt1
         :              +- LocalRelation [k#x, v1#x]
         +- SubqueryAlias nt2
            +- View (`nt2`, [k#x,v2#x])
               +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
                  +- Project [k#x, v2#x]
                     +- SubqueryAlias nt2
                        +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.k, nt2.k FROM nt1 left outer join nt2 using (k) ORDER BY nt2.k
-- !query analysis
Sort [k#x ASC NULLS FIRST], true
+- Project [k#x, k#x]
   +- Project [k#x, v1#x, v2#x, k#x]
      +- Join LeftOuter, (k#x = k#x)
         :- SubqueryAlias nt1
         :  +- View (`nt1`, [k#x,v1#x])
         :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
         :        +- Project [k#x, v1#x]
         :           +- SubqueryAlias nt1
         :              +- LocalRelation [k#x, v1#x]
         +- SubqueryAlias nt2
            +- View (`nt2`, [k#x,v2#x])
               +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
                  +- Project [k#x, v2#x]
                     +- SubqueryAlias nt2
                        +- LocalRelation [k#x, v2#x]


-- !query
SELECT k, nt1.k FROM nt1 left outer join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [k#x, v1#x, v2#x]
   +- Join LeftOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k, nt2.k FROM nt1 left outer join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [k#x, v1#x, v2#x, k#x]
   +- Join LeftOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT * FROM nt1 left semi join nt2 using (k)
-- !query analysis
Project [k#x, v1#x]
+- Project [k#x, v1#x]
   +- Join LeftSemi, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k FROM nt1 left semi join nt2 using (k)
-- !query analysis
Project [k#x]
+- Project [k#x, v1#x]
   +- Join LeftSemi, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.* FROM nt1 left semi join nt2 using (k)
-- !query analysis
Project [k#x, v1#x]
+- Project [k#x, v1#x]
   +- Join LeftSemi, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.k FROM nt1 left semi join nt2 using (k)
-- !query analysis
Project [k#x]
+- Project [k#x, v1#x]
   +- Join LeftSemi, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k, nt1.k FROM nt1 left semi join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [k#x, v1#x]
   +- Join LeftSemi, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT * FROM nt1 right outer join nt2 using (k)
-- !query analysis
Project [k#x, v1#x, v2#x]
+- Project [k#x, v1#x, v2#x]
   +- Join RightOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k FROM nt1 right outer join nt2 using (k)
-- !query analysis
Project [k#x]
+- Project [k#x, v1#x, v2#x]
   +- Join RightOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.*, nt2.* FROM nt1 right outer join nt2 using (k)
-- !query analysis
Project [k#x, v1#x, k#x, v2#x]
+- Project [k#x, v1#x, v2#x, k#x]
   +- Join RightOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.k, nt2.k FROM nt1 right outer join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [k#x, v1#x, v2#x, k#x]
   +- Join RightOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k FROM (SELECT nt1.k FROM nt1 right outer join nt2 using (k))
-- !query analysis
Project [k#x]
+- SubqueryAlias __auto_generated_subquery_name
   +- Project [k#x]
      +- Project [k#x, v1#x, v2#x, k#x]
         +- Join RightOuter, (k#x = k#x)
            :- SubqueryAlias nt1
            :  +- View (`nt1`, [k#x,v1#x])
            :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
            :        +- Project [k#x, v1#x]
            :           +- SubqueryAlias nt1
            :              +- LocalRelation [k#x, v1#x]
            +- SubqueryAlias nt2
               +- View (`nt2`, [k#x,v2#x])
                  +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
                     +- Project [k#x, v2#x]
                        +- SubqueryAlias nt2
                           +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.k AS key FROM nt1 right outer join nt2 using (k) ORDER BY key
-- !query analysis
Sort [key#x ASC NULLS FIRST], true
+- Project [k#x AS key#x]
   +- Project [k#x, v1#x, v2#x, k#x]
      +- Join RightOuter, (k#x = k#x)
         :- SubqueryAlias nt1
         :  +- View (`nt1`, [k#x,v1#x])
         :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
         :        +- Project [k#x, v1#x]
         :           +- SubqueryAlias nt1
         :              +- LocalRelation [k#x, v1#x]
         +- SubqueryAlias nt2
            +- View (`nt2`, [k#x,v2#x])
               +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
                  +- Project [k#x, v2#x]
                     +- SubqueryAlias nt2
                        +- LocalRelation [k#x, v2#x]


-- !query
SELECT k, nt1.k FROM nt1 right outer join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [k#x, v1#x, v2#x, k#x]
   +- Join RightOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k, nt2.k FROM nt1 right outer join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [k#x, v1#x, v2#x]
   +- Join RightOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT * FROM nt1 full outer join nt2 using (k)
-- !query analysis
Project [k#x, v1#x, v2#x]
+- Project [coalesce(k#x, k#x) AS k#x, v1#x, v2#x]
   +- Join FullOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k FROM nt1 full outer join nt2 using (k)
-- !query analysis
Project [k#x]
+- Project [coalesce(k#x, k#x) AS k#x, v1#x, v2#x]
   +- Join FullOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.*, nt2.* FROM nt1 full outer join nt2 using (k)
-- !query analysis
Project [k#x, v1#x, k#x, v2#x]
+- Project [coalesce(k#x, k#x) AS k#x, v1#x, v2#x, k#x, k#x]
   +- Join FullOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.k, nt2.k FROM nt1 full outer join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [coalesce(k#x, k#x) AS k#x, v1#x, v2#x, k#x, k#x]
   +- Join FullOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k FROM (SELECT nt2.k FROM nt1 full outer join nt2 using (k))
-- !query analysis
Project [k#x]
+- SubqueryAlias __auto_generated_subquery_name
   +- Project [k#x]
      +- Project [coalesce(k#x, k#x) AS k#x, v1#x, v2#x, k#x, k#x]
         +- Join FullOuter, (k#x = k#x)
            :- SubqueryAlias nt1
            :  +- View (`nt1`, [k#x,v1#x])
            :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
            :        +- Project [k#x, v1#x]
            :           +- SubqueryAlias nt1
            :              +- LocalRelation [k#x, v1#x]
            +- SubqueryAlias nt2
               +- View (`nt2`, [k#x,v2#x])
                  +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
                     +- Project [k#x, v2#x]
                        +- SubqueryAlias nt2
                           +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt2.k AS key FROM nt1 full outer join nt2 using (k) ORDER BY key
-- !query analysis
Sort [key#x ASC NULLS FIRST], true
+- Project [k#x AS key#x]
   +- Project [coalesce(k#x, k#x) AS k#x, v1#x, v2#x, k#x, k#x]
      +- Join FullOuter, (k#x = k#x)
         :- SubqueryAlias nt1
         :  +- View (`nt1`, [k#x,v1#x])
         :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
         :        +- Project [k#x, v1#x]
         :           +- SubqueryAlias nt1
         :              +- LocalRelation [k#x, v1#x]
         +- SubqueryAlias nt2
            +- View (`nt2`, [k#x,v2#x])
               +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
                  +- Project [k#x, v2#x]
                     +- SubqueryAlias nt2
                        +- LocalRelation [k#x, v2#x]


-- !query
SELECT k, nt1.k FROM nt1 full outer join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [coalesce(k#x, k#x) AS k#x, v1#x, v2#x, k#x, k#x]
   +- Join FullOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k, nt2.k FROM nt1 full outer join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [coalesce(k#x, k#x) AS k#x, v1#x, v2#x, k#x, k#x]
   +- Join FullOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT * FROM nt1 full outer join nt2 using (k)
-- !query analysis
Project [k#x, v1#x, v2#x]
+- Project [coalesce(k#x, k#x) AS k#x, v1#x, v2#x]
   +- Join FullOuter, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k FROM nt1 inner join nt2 using (k)
-- !query analysis
Project [k#x]
+- Project [k#x, v1#x, v2#x]
   +- Join Inner, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.*, nt2.* FROM nt1 inner join nt2 using (k)
-- !query analysis
Project [k#x, v1#x, k#x, v2#x]
+- Project [k#x, v1#x, v2#x, k#x]
   +- Join Inner, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt1.k, nt2.k FROM nt1 inner join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [k#x, v1#x, v2#x, k#x]
   +- Join Inner, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k FROM (SELECT nt2.k FROM nt1 inner join nt2 using (k))
-- !query analysis
Project [k#x]
+- SubqueryAlias __auto_generated_subquery_name
   +- Project [k#x]
      +- Project [k#x, v1#x, v2#x, k#x]
         +- Join Inner, (k#x = k#x)
            :- SubqueryAlias nt1
            :  +- View (`nt1`, [k#x,v1#x])
            :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
            :        +- Project [k#x, v1#x]
            :           +- SubqueryAlias nt1
            :              +- LocalRelation [k#x, v1#x]
            +- SubqueryAlias nt2
               +- View (`nt2`, [k#x,v2#x])
                  +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
                     +- Project [k#x, v2#x]
                        +- SubqueryAlias nt2
                           +- LocalRelation [k#x, v2#x]


-- !query
SELECT nt2.k AS key FROM nt1 inner join nt2 using (k) ORDER BY key
-- !query analysis
Sort [key#x ASC NULLS FIRST], true
+- Project [k#x AS key#x]
   +- Project [k#x, v1#x, v2#x, k#x]
      +- Join Inner, (k#x = k#x)
         :- SubqueryAlias nt1
         :  +- View (`nt1`, [k#x,v1#x])
         :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
         :        +- Project [k#x, v1#x]
         :           +- SubqueryAlias nt1
         :              +- LocalRelation [k#x, v1#x]
         +- SubqueryAlias nt2
            +- View (`nt2`, [k#x,v2#x])
               +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
                  +- Project [k#x, v2#x]
                     +- SubqueryAlias nt2
                        +- LocalRelation [k#x, v2#x]


-- !query
SELECT k, nt1.k FROM nt1 inner join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [k#x, v1#x, v2#x]
   +- Join Inner, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
SELECT k, nt2.k FROM nt1 inner join nt2 using (k)
-- !query analysis
Project [k#x, k#x]
+- Project [k#x, v1#x, v2#x, k#x]
   +- Join Inner, (k#x = k#x)
      :- SubqueryAlias nt1
      :  +- View (`nt1`, [k#x,v1#x])
      :     +- Project [cast(k#x as string) AS k#x, cast(v1#x as int) AS v1#x]
      :        +- Project [k#x, v1#x]
      :           +- SubqueryAlias nt1
      :              +- LocalRelation [k#x, v1#x]
      +- SubqueryAlias nt2
         +- View (`nt2`, [k#x,v2#x])
            +- Project [cast(k#x as string) AS k#x, cast(v2#x as int) AS v2#x]
               +- Project [k#x, v2#x]
                  +- SubqueryAlias nt2
                     +- LocalRelation [k#x, v2#x]


-- !query
WITH
  t1 AS (select key from values ('a') t(key)),
  t2 AS (select key from values ('a') t(key))
SELECT t1.key
FROM t1 FULL OUTER JOIN t2 USING (key)
WHERE t1.key NOT LIKE 'bb.%'
-- !query analysis
WithCTE
:- CTERelationDef xxxx, false
:  +- SubqueryAlias t1
:     +- Project [key#x]
:        +- SubqueryAlias t
:           +- LocalRelation [key#x]
:- CTERelationDef xxxx, false
:  +- SubqueryAlias t2
:     +- Project [key#x]
:        +- SubqueryAlias t
:           +- LocalRelation [key#x]
+- Project [key#x]
   +- Project [key#x, key#x, key#x]
      +- Filter NOT key#x LIKE bb.%
         +- Project [coalesce(key#x, key#x) AS key#x, key#x, key#x, key#x]
            +- Join FullOuter, (key#x = key#x)
               :- SubqueryAlias t1
               :  +- CTERelationRef xxxx, true, [key#x]
               +- SubqueryAlias t2
                  +- CTERelationRef xxxx, true, [key#x]
