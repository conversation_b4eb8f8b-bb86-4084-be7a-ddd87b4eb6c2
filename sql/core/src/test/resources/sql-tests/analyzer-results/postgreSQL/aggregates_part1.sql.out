-- Automatically generated by SQLQueryTestSuite
-- !query
SELECT avg(four) AS avg_1 FROM onek
-- !query analysis
Aggregate [avg(four#x) AS avg_1#x]
+- SubqueryAlias spark_catalog.default.onek
   +- Relation spark_catalog.default.onek[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
SELECT avg(a) AS avg_32 FROM aggtest WHERE a < 100
-- !query analysis
Aggregate [avg(a#x) AS avg_32#x]
+- Filter (a#x < 100)
   +- SubqueryAlias spark_catalog.default.aggtest
      +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
select CAST(avg(b) AS Decimal(10,3)) AS avg_107_943 FROM aggtest
-- !query analysis
Aggregate [cast(avg(b#x) as decimal(10,3)) AS avg_107_943#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT sum(four) AS sum_1500 FROM onek
-- !query analysis
Aggregate [sum(four#x) AS sum_1500#xL]
+- SubqueryAlias spark_catalog.default.onek
   +- Relation spark_catalog.default.onek[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
SELECT sum(a) AS sum_198 FROM aggtest
-- !query analysis
Aggregate [sum(a#x) AS sum_198#xL]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT sum(b) AS avg_431_773 FROM aggtest
-- !query analysis
Aggregate [sum(b#x) AS avg_431_773#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT max(four) AS max_3 FROM onek
-- !query analysis
Aggregate [max(four#x) AS max_3#x]
+- SubqueryAlias spark_catalog.default.onek
   +- Relation spark_catalog.default.onek[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
SELECT max(a) AS max_100 FROM aggtest
-- !query analysis
Aggregate [max(a#x) AS max_100#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT max(aggtest.b) AS max_324_78 FROM aggtest
-- !query analysis
Aggregate [max(b#x) AS max_324_78#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT stddev_pop(b) FROM aggtest
-- !query analysis
Aggregate [stddev_pop(cast(b#x as double)) AS stddev_pop(b)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT stddev_samp(b) FROM aggtest
-- !query analysis
Aggregate [stddev_samp(cast(b#x as double)) AS stddev_samp(b)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT var_pop(b) FROM aggtest
-- !query analysis
Aggregate [var_pop(cast(b#x as double)) AS var_pop(b)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT var_samp(b) FROM aggtest
-- !query analysis
Aggregate [var_samp(cast(b#x as double)) AS var_samp(b)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT stddev_pop(CAST(b AS Decimal(38,0))) FROM aggtest
-- !query analysis
Aggregate [stddev_pop(cast(cast(b#x as decimal(38,0)) as double)) AS stddev_pop(CAST(b AS DECIMAL(38,0)))#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT stddev_samp(CAST(b AS Decimal(38,0))) FROM aggtest
-- !query analysis
Aggregate [stddev_samp(cast(cast(b#x as decimal(38,0)) as double)) AS stddev_samp(CAST(b AS DECIMAL(38,0)))#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT var_pop(CAST(b AS Decimal(38,0))) FROM aggtest
-- !query analysis
Aggregate [var_pop(cast(cast(b#x as decimal(38,0)) as double)) AS var_pop(CAST(b AS DECIMAL(38,0)))#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT var_samp(CAST(b AS Decimal(38,0))) FROM aggtest
-- !query analysis
Aggregate [var_samp(cast(cast(b#x as decimal(38,0)) as double)) AS var_samp(CAST(b AS DECIMAL(38,0)))#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT var_pop(1.0), var_samp(2.0)
-- !query analysis
Aggregate [var_pop(cast(1.0 as double)) AS var_pop(1.0)#x, var_samp(cast(2.0 as double)) AS var_samp(2.0)#x]
+- OneRowRelation


-- !query
SELECT stddev_pop(CAST(3.0 AS Decimal(38,0))), stddev_samp(CAST(4.0 AS Decimal(38,0)))
-- !query analysis
Aggregate [stddev_pop(cast(cast(3.0 as decimal(38,0)) as double)) AS stddev_pop(CAST(3.0 AS DECIMAL(38,0)))#x, stddev_samp(cast(cast(4.0 as decimal(38,0)) as double)) AS stddev_samp(CAST(4.0 AS DECIMAL(38,0)))#x]
+- OneRowRelation


-- !query
select sum(CAST(null AS int)) from range(1,4)
-- !query analysis
Aggregate [sum(cast(null as int)) AS sum(CAST(NULL AS INT))#xL]
+- Range (1, 4, step=1, splits=None)


-- !query
select sum(CAST(null AS long)) from range(1,4)
-- !query analysis
Aggregate [sum(cast(null as bigint)) AS sum(CAST(NULL AS BIGINT))#xL]
+- Range (1, 4, step=1, splits=None)


-- !query
select sum(CAST(null AS Decimal(38,0))) from range(1,4)
-- !query analysis
Aggregate [sum(cast(null as decimal(38,0))) AS sum(CAST(NULL AS DECIMAL(38,0)))#x]
+- Range (1, 4, step=1, splits=None)


-- !query
select sum(CAST(null AS DOUBLE)) from range(1,4)
-- !query analysis
Aggregate [sum(cast(null as double)) AS sum(CAST(NULL AS DOUBLE))#x]
+- Range (1, 4, step=1, splits=None)


-- !query
select avg(CAST(null AS int)) from range(1,4)
-- !query analysis
Aggregate [avg(cast(null as int)) AS avg(CAST(NULL AS INT))#x]
+- Range (1, 4, step=1, splits=None)


-- !query
select avg(CAST(null AS long)) from range(1,4)
-- !query analysis
Aggregate [avg(cast(null as bigint)) AS avg(CAST(NULL AS BIGINT))#x]
+- Range (1, 4, step=1, splits=None)


-- !query
select avg(CAST(null AS Decimal(38,0))) from range(1,4)
-- !query analysis
Aggregate [avg(cast(null as decimal(38,0))) AS avg(CAST(NULL AS DECIMAL(38,0)))#x]
+- Range (1, 4, step=1, splits=None)


-- !query
select avg(CAST(null AS DOUBLE)) from range(1,4)
-- !query analysis
Aggregate [avg(cast(null as double)) AS avg(CAST(NULL AS DOUBLE))#x]
+- Range (1, 4, step=1, splits=None)


-- !query
select sum(CAST('NaN' AS DOUBLE)) from range(1,4)
-- !query analysis
Aggregate [sum(cast(NaN as double)) AS sum(CAST(NaN AS DOUBLE))#x]
+- Range (1, 4, step=1, splits=None)


-- !query
select avg(CAST('NaN' AS DOUBLE)) from range(1,4)
-- !query analysis
Aggregate [avg(cast(NaN as double)) AS avg(CAST(NaN AS DOUBLE))#x]
+- Range (1, 4, step=1, splits=None)


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES (CAST('1' AS DOUBLE)), (CAST('infinity' AS DOUBLE))) v(x)
-- !query analysis
Aggregate [avg(cast(x#x as double)) AS avg(CAST(x AS DOUBLE))#x, var_pop(cast(x#x as double)) AS var_pop(CAST(x AS DOUBLE))#x]
+- SubqueryAlias v
   +- Project [col1#x AS x#x]
      +- LocalRelation [col1#x]


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES ('infinity'), ('1')) v(x)
-- !query analysis
Aggregate [avg(cast(x#x as double)) AS avg(CAST(x AS DOUBLE))#x, var_pop(cast(x#x as double)) AS var_pop(CAST(x AS DOUBLE))#x]
+- SubqueryAlias v
   +- Project [col1#x AS x#x]
      +- LocalRelation [col1#x]


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES ('infinity'), ('infinity')) v(x)
-- !query analysis
Aggregate [avg(cast(x#x as double)) AS avg(CAST(x AS DOUBLE))#x, var_pop(cast(x#x as double)) AS var_pop(CAST(x AS DOUBLE))#x]
+- SubqueryAlias v
   +- Project [col1#x AS x#x]
      +- LocalRelation [col1#x]


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES ('-infinity'), ('infinity')) v(x)
-- !query analysis
Aggregate [avg(cast(x#x as double)) AS avg(CAST(x AS DOUBLE))#x, var_pop(cast(x#x as double)) AS var_pop(CAST(x AS DOUBLE))#x]
+- SubqueryAlias v
   +- Project [col1#x AS x#x]
      +- LocalRelation [col1#x]


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES (100000003), (100000004), (100000006), (100000007)) v(x)
-- !query analysis
Aggregate [avg(cast(x#x as double)) AS avg(CAST(x AS DOUBLE))#x, var_pop(cast(x#x as double)) AS var_pop(CAST(x AS DOUBLE))#x]
+- SubqueryAlias v
   +- Project [col1#x AS x#x]
      +- LocalRelation [col1#x]


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES (7000000000005), (7000000000007)) v(x)
-- !query analysis
Aggregate [avg(cast(x#xL as double)) AS avg(CAST(x AS DOUBLE))#x, var_pop(cast(x#xL as double)) AS var_pop(CAST(x AS DOUBLE))#x]
+- SubqueryAlias v
   +- Project [col1#xL AS x#xL]
      +- LocalRelation [col1#xL]


-- !query
SELECT regr_count(b, a) FROM aggtest
-- !query analysis
Aggregate [regr_count(b#x, a#x) AS regr_count(b, a)#xL]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT regr_sxx(b, a) FROM aggtest
-- !query analysis
Aggregate [regr_sxx(cast(b#x as double), cast(a#x as double)) AS regr_sxx(b, a)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT regr_syy(b, a) FROM aggtest
-- !query analysis
Aggregate [regr_syy(cast(b#x as double), cast(a#x as double)) AS regr_syy(b, a)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT regr_sxy(b, a) FROM aggtest
-- !query analysis
Aggregate [regr_sxy(cast(b#x as double), cast(a#x as double)) AS regr_sxy(b, a)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT regr_avgx(b, a), regr_avgy(b, a) FROM aggtest
-- !query analysis
Aggregate [regr_avgx(b#x, a#x) AS regr_avgx(b, a)#x, regr_avgy(b#x, a#x) AS regr_avgy(b, a)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT regr_r2(b, a) FROM aggtest
-- !query analysis
Aggregate [regr_r2(cast(b#x as double), cast(a#x as double)) AS regr_r2(b, a)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT regr_slope(b, a), regr_intercept(b, a) FROM aggtest
-- !query analysis
Aggregate [regr_slope(cast(b#x as double), cast(a#x as double)) AS regr_slope(b, a)#x, regr_intercept(cast(b#x as double), cast(a#x as double)) AS regr_intercept(b, a)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT covar_pop(b, a), covar_samp(b, a) FROM aggtest
-- !query analysis
Aggregate [covar_pop(cast(b#x as double), cast(a#x as double)) AS covar_pop(b, a)#x, covar_samp(cast(b#x as double), cast(a#x as double)) AS covar_samp(b, a)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
SELECT corr(b, a) FROM aggtest
-- !query analysis
Aggregate [corr(cast(b#x as double), cast(a#x as double)) AS corr(b, a)#x]
+- SubqueryAlias spark_catalog.default.aggtest
   +- Relation spark_catalog.default.aggtest[a#x,b#x] parquet


-- !query
CREATE TEMPORARY VIEW regr_test AS SELECT * FROM VALUES (10,150),(20,250),(30,350),(80,540),(100,200) AS regr_test (x, y)
-- !query analysis
CreateViewCommand `regr_test`, SELECT * FROM VALUES (10,150),(20,250),(30,350),(80,540),(100,200) AS regr_test (x, y), false, false, LocalTempView, true
   +- Project [x#x, y#x]
      +- SubqueryAlias regr_test
         +- LocalRelation [x#x, y#x]


-- !query
SELECT count(*), sum(x), regr_sxx(y,x), sum(y),regr_syy(y,x), regr_sxy(y,x)
FROM regr_test WHERE x IN (10,20,30,80)
-- !query analysis
Aggregate [count(1) AS count(1)#xL, sum(x#x) AS sum(x)#xL, regr_sxx(cast(y#x as double), cast(x#x as double)) AS regr_sxx(y, x)#x, sum(y#x) AS sum(y)#xL, regr_syy(cast(y#x as double), cast(x#x as double)) AS regr_syy(y, x)#x, regr_sxy(cast(y#x as double), cast(x#x as double)) AS regr_sxy(y, x)#x]
+- Filter x#x IN (10,20,30,80)
   +- SubqueryAlias regr_test
      +- View (`regr_test`, [x#x,y#x])
         +- Project [cast(x#x as int) AS x#x, cast(y#x as int) AS y#x]
            +- Project [x#x, y#x]
               +- SubqueryAlias regr_test
                  +- LocalRelation [x#x, y#x]


-- !query
SELECT count(*), sum(x), regr_sxx(y,x), sum(y),regr_syy(y,x), regr_sxy(y,x)
FROM regr_test
-- !query analysis
Aggregate [count(1) AS count(1)#xL, sum(x#x) AS sum(x)#xL, regr_sxx(cast(y#x as double), cast(x#x as double)) AS regr_sxx(y, x)#x, sum(y#x) AS sum(y)#xL, regr_syy(cast(y#x as double), cast(x#x as double)) AS regr_syy(y, x)#x, regr_sxy(cast(y#x as double), cast(x#x as double)) AS regr_sxy(y, x)#x]
+- SubqueryAlias regr_test
   +- View (`regr_test`, [x#x,y#x])
      +- Project [cast(x#x as int) AS x#x, cast(y#x as int) AS y#x]
         +- Project [x#x, y#x]
            +- SubqueryAlias regr_test
               +- LocalRelation [x#x, y#x]


-- !query
SELECT count(*), sum(x), regr_sxx(y,x), sum(y),regr_syy(y,x), regr_sxy(y,x)
FROM regr_test WHERE x IN (10,20,30)
-- !query analysis
Aggregate [count(1) AS count(1)#xL, sum(x#x) AS sum(x)#xL, regr_sxx(cast(y#x as double), cast(x#x as double)) AS regr_sxx(y, x)#x, sum(y#x) AS sum(y)#xL, regr_syy(cast(y#x as double), cast(x#x as double)) AS regr_syy(y, x)#x, regr_sxy(cast(y#x as double), cast(x#x as double)) AS regr_sxy(y, x)#x]
+- Filter x#x IN (10,20,30)
   +- SubqueryAlias regr_test
      +- View (`regr_test`, [x#x,y#x])
         +- Project [cast(x#x as int) AS x#x, cast(y#x as int) AS y#x]
            +- Project [x#x, y#x]
               +- SubqueryAlias regr_test
                  +- LocalRelation [x#x, y#x]


-- !query
SELECT count(*), sum(x), regr_sxx(y,x), sum(y),regr_syy(y,x), regr_sxy(y,x)
FROM regr_test WHERE x IN (80,100)
-- !query analysis
Aggregate [count(1) AS count(1)#xL, sum(x#x) AS sum(x)#xL, regr_sxx(cast(y#x as double), cast(x#x as double)) AS regr_sxx(y, x)#x, sum(y#x) AS sum(y)#xL, regr_syy(cast(y#x as double), cast(x#x as double)) AS regr_syy(y, x)#x, regr_sxy(cast(y#x as double), cast(x#x as double)) AS regr_sxy(y, x)#x]
+- Filter x#x IN (80,100)
   +- SubqueryAlias regr_test
      +- View (`regr_test`, [x#x,y#x])
         +- Project [cast(x#x as int) AS x#x, cast(y#x as int) AS y#x]
            +- Project [x#x, y#x]
               +- SubqueryAlias regr_test
                  +- LocalRelation [x#x, y#x]


-- !query
DROP VIEW regr_test
-- !query analysis
DropTempViewCommand regr_test


-- !query
SELECT count(four) AS cnt_1000 FROM onek
-- !query analysis
Aggregate [count(four#x) AS cnt_1000#xL]
+- SubqueryAlias spark_catalog.default.onek
   +- Relation spark_catalog.default.onek[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
SELECT count(DISTINCT four) AS cnt_4 FROM onek
-- !query analysis
Aggregate [count(distinct four#x) AS cnt_4#xL]
+- SubqueryAlias spark_catalog.default.onek
   +- Relation spark_catalog.default.onek[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select ten, count(*), sum(four) from onek
group by ten order by ten
-- !query analysis
Sort [ten#x ASC NULLS FIRST], true
+- Aggregate [ten#x], [ten#x, count(1) AS count(1)#xL, sum(four#x) AS sum(four)#xL]
   +- SubqueryAlias spark_catalog.default.onek
      +- Relation spark_catalog.default.onek[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select ten, count(four), sum(DISTINCT four) from onek
group by ten order by ten
-- !query analysis
Sort [ten#x ASC NULLS FIRST], true
+- Aggregate [ten#x], [ten#x, count(four#x) AS count(four)#xL, sum(distinct four#x) AS sum(DISTINCT four)#xL]
   +- SubqueryAlias spark_catalog.default.onek
      +- Relation spark_catalog.default.onek[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select ten, sum(distinct four) from onek a
group by ten
having exists (select 1 from onek b where sum(distinct a.four) = b.four)
-- !query analysis
Filter exists#x [sum(DISTINCT four)#xL]
:  +- Project [1 AS 1#x]
:     +- Filter (outer(sum(DISTINCT four)#xL) = cast(four#x as bigint))
:        +- SubqueryAlias b
:           +- SubqueryAlias spark_catalog.default.onek
:              +- Relation spark_catalog.default.onek[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet
+- Aggregate [ten#x], [ten#x, sum(distinct four#x) AS sum(DISTINCT four)#xL]
   +- SubqueryAlias a
      +- SubqueryAlias spark_catalog.default.onek
         +- Relation spark_catalog.default.onek[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select ten, sum(distinct four) from onek a
group by ten
having exists (select 1 from onek b
               where sum(distinct a.four + b.four) = b.four)
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "UNSUPPORTED_SUBQUERY_EXPRESSION_CATEGORY.AGGREGATE_FUNCTION_MIXED_OUTER_LOCAL_REFERENCES",
  "sqlState" : "0A000",
  "messageParameters" : {
    "function" : "sum(DISTINCT (outer(a.four) + b.four))"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 114,
    "stopIndex" : 142,
    "fragment" : "sum(distinct a.four + b.four)"
  } ]
}


-- !query
select
  (select max((select i.unique2 from tenk1 i where i.unique1 = o.unique1)))
from tenk1 o
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "UNRESOLVED_COLUMN.WITH_SUGGESTION",
  "sqlState" : "42703",
  "messageParameters" : {
    "objectName" : "`o`.`unique1`",
    "proposal" : "`i`.`unique1`, `i`.`unique2`, `i`.`even`, `i`.`four`, `i`.`odd`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 71,
    "stopIndex" : 79,
    "fragment" : "o.unique1"
  } ]
}
