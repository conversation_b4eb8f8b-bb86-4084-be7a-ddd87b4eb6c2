-- Automatically generated by SQLQueryTestSuite
-- !query
SELECT string('this is a text string') = string('this is a text string') AS true
-- !query analysis
Project [(cast(this is a text string as string) = cast(this is a text string as string)) AS true#x]
+- OneRowRelation


-- !query
SELECT string('this is a text string') = string('this is a text strin') AS `false`
-- !query analysis
Project [(cast(this is a text string as string) = cast(this is a text strin as string)) AS false#x]
+- OneRowRelation


-- !query
CREATE TABLE TEXT_TBL (f1 string) USING parquet
-- !query analysis
CreateDataSourceTableCommand `spark_catalog`.`default`.`TEXT_TBL`, false


-- !query
INSERT INTO TEXT_TBL VALUES ('doh!')
-- !query analysis
InsertIntoHadoopFsRelationCommand file:[not included in comparison]/{warehouse_dir}/text_tbl, false, Parquet, [path=file:[not included in comparison]/{warehouse_dir}/text_tbl], Append, `spark_catalog`.`default`.`text_tbl`, org.apache.spark.sql.execution.datasources.InMemoryFileIndex(file:[not included in comparison]/{warehouse_dir}/text_tbl), [f1]
+- Project [cast(col1#x as string) AS f1#x]
   +- LocalRelation [col1#x]


-- !query
INSERT INTO TEXT_TBL VALUES ('hi de ho neighbor')
-- !query analysis
InsertIntoHadoopFsRelationCommand file:[not included in comparison]/{warehouse_dir}/text_tbl, false, Parquet, [path=file:[not included in comparison]/{warehouse_dir}/text_tbl], Append, `spark_catalog`.`default`.`text_tbl`, org.apache.spark.sql.execution.datasources.InMemoryFileIndex(file:[not included in comparison]/{warehouse_dir}/text_tbl), [f1]
+- Project [cast(col1#x as string) AS f1#x]
   +- LocalRelation [col1#x]


-- !query
SELECT '' AS two, * FROM TEXT_TBL
-- !query analysis
Project [ AS two#x, f1#x]
+- SubqueryAlias spark_catalog.default.text_tbl
   +- Relation spark_catalog.default.text_tbl[f1#x] parquet


-- !query
select length(42)
-- !query analysis
Project [length(cast(42 as string)) AS length(42)#x]
+- OneRowRelation


-- !query
select string('four: ') || 2+2
-- !query analysis
Project [(cast(concat(cast(four:  as string), cast(2 as string)) as bigint) + cast(2 as bigint)) AS (concat(four: , 2) + 2)#xL]
+- OneRowRelation


-- !query
select 'four: ' || 2+2
-- !query analysis
Project [(cast(concat(four: , cast(2 as string)) as bigint) + cast(2 as bigint)) AS (concat(four: , 2) + 2)#xL]
+- OneRowRelation


-- !query
select 3 || 4.0
-- !query analysis
Project [concat(cast(3 as string), cast(4.0 as string)) AS concat(3, 4.0)#x]
+- OneRowRelation


-- !query
/*
 * various string functions
 */
select concat('one')
-- !query analysis
Project [concat(one) AS concat(one)#x]
+- OneRowRelation


-- !query
select concat(1,2,3,'hello',true, false, to_date('20100309','yyyyMMdd'))
-- !query analysis
Project [concat(cast(1 as string), cast(2 as string), cast(3 as string), hello, cast(true as string), cast(false as string), cast(to_date(20100309, Some(yyyyMMdd), Some(America/Los_Angeles), true) as string)) AS concat(1, 2, 3, hello, true, false, to_date(20100309, yyyyMMdd))#x]
+- OneRowRelation


-- !query
select concat_ws('#','one')
-- !query analysis
Project [concat_ws(#, one) AS concat_ws(#, one)#x]
+- OneRowRelation


-- !query
select concat_ws('#',1,2,3,'hello',true, false, to_date('20100309','yyyyMMdd'))
-- !query analysis
Project [concat_ws(#, cast(1 as string), cast(2 as string), cast(3 as string), hello, cast(true as string), cast(false as string), cast(to_date(20100309, Some(yyyyMMdd), Some(America/Los_Angeles), true) as string)) AS concat_ws(#, 1, 2, 3, hello, true, false, to_date(20100309, yyyyMMdd))#x]
+- OneRowRelation


-- !query
select concat_ws(',',10,20,null,30)
-- !query analysis
Project [concat_ws(,, cast(10 as string), cast(20 as string), cast(null as array<string>), cast(30 as string)) AS concat_ws(,, 10, 20, NULL, 30)#x]
+- OneRowRelation


-- !query
select concat_ws('',10,20,null,30)
-- !query analysis
Project [concat_ws(, cast(10 as string), cast(20 as string), cast(null as array<string>), cast(30 as string)) AS concat_ws(, 10, 20, NULL, 30)#x]
+- OneRowRelation


-- !query
select concat_ws(NULL,10,20,null,30) is null
-- !query analysis
Project [isnull(concat_ws(cast(null as string), cast(10 as string), cast(20 as string), cast(null as array<string>), cast(30 as string))) AS (concat_ws(NULL, 10, 20, NULL, 30) IS NULL)#x]
+- OneRowRelation


-- !query
select reverse('abcde')
-- !query analysis
Project [reverse(abcde) AS reverse(abcde)#x]
+- OneRowRelation


-- !query
select i, left('ahoj', i), right('ahoj', i) from range(-5, 6) t(i) order by i
-- !query analysis
Sort [i#xL ASC NULLS FIRST], true
+- Project [i#xL, left(ahoj, cast(i#xL as int)) AS left(ahoj, i)#x, right(ahoj, cast(i#xL as int)) AS right(ahoj, i)#x]
   +- SubqueryAlias t
      +- Project [id#xL AS i#xL]
         +- Range (-5, 6, step=1, splits=None)


-- !query
/*
 * format
 */
select format_string(NULL)
-- !query analysis
Project [format_string(cast(null as string)) AS format_string(NULL)#x]
+- OneRowRelation


-- !query
select format_string('Hello')
-- !query analysis
Project [format_string(Hello) AS format_string(Hello)#x]
+- OneRowRelation


-- !query
select format_string('Hello %s', 'World')
-- !query analysis
Project [format_string(Hello %s, World) AS format_string(Hello %s, World)#x]
+- OneRowRelation


-- !query
select format_string('Hello %%')
-- !query analysis
Project [format_string(Hello %%) AS format_string(Hello %%)#x]
+- OneRowRelation


-- !query
select format_string('Hello %%%%')
-- !query analysis
Project [format_string(Hello %%%%) AS format_string(Hello %%%%)#x]
+- OneRowRelation


-- !query
select format_string('Hello %s %s', 'World')
-- !query analysis
Project [format_string(Hello %s %s, World) AS format_string(Hello %s %s, World)#x]
+- OneRowRelation


-- !query
select format_string('Hello %s')
-- !query analysis
Project [format_string(Hello %s) AS format_string(Hello %s)#x]
+- OneRowRelation


-- !query
select format_string('Hello %x', 20)
-- !query analysis
Project [format_string(Hello %x, 20) AS format_string(Hello %x, 20)#x]
+- OneRowRelation


-- !query
select format_string('%1$s %3$s', 1, 2, 3)
-- !query analysis
Project [format_string(%1$s %3$s, 1, 2, 3) AS format_string(%1$s %3$s, 1, 2, 3)#x]
+- OneRowRelation


-- !query
select format_string('%1$s %12$s', 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12)
-- !query analysis
Project [format_string(%1$s %12$s, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12) AS format_string(%1$s %12$s, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12)#x]
+- OneRowRelation


-- !query
select format_string('%1$s %4$s', 1, 2, 3)
-- !query analysis
Project [format_string(%1$s %4$s, 1, 2, 3) AS format_string(%1$s %4$s, 1, 2, 3)#x]
+- OneRowRelation


-- !query
select format_string('%1$s %13$s', 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12)
-- !query analysis
Project [format_string(%1$s %13$s, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12) AS format_string(%1$s %13$s, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12)#x]
+- OneRowRelation


-- !query
select format_string('%0$s', 'Hello')
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "INVALID_PARAMETER_VALUE.ZERO_INDEX",
  "sqlState" : "22023",
  "messageParameters" : {
    "functionName" : "`format_string`",
    "parameter" : "`strfmt`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 37,
    "fragment" : "format_string('%0$s', 'Hello')"
  } ]
}


-- !query
select format_string('Hello %s %1$s %s', 'World', 'Hello again')
-- !query analysis
Project [format_string(Hello %s %1$s %s, World, Hello again) AS format_string(Hello %s %1$s %s, World, Hello again)#x]
+- OneRowRelation


-- !query
select format_string('Hello %s %s, %2$s %2$s', 'World', 'Hello again')
-- !query analysis
Project [format_string(Hello %s %s, %2$s %2$s, World, Hello again) AS format_string(Hello %s %s, %2$s %2$s, World, Hello again)#x]
+- OneRowRelation


-- !query
select format_string('>>%10s<<', 'Hello')
-- !query analysis
Project [format_string(>>%10s<<, Hello) AS format_string(>>%10s<<, Hello)#x]
+- OneRowRelation


-- !query
select format_string('>>%10s<<', NULL)
-- !query analysis
Project [format_string(>>%10s<<, null) AS format_string(>>%10s<<, NULL)#x]
+- OneRowRelation


-- !query
select format_string('>>%10s<<', '')
-- !query analysis
Project [format_string(>>%10s<<, ) AS format_string(>>%10s<<, )#x]
+- OneRowRelation


-- !query
select format_string('>>%-10s<<', '')
-- !query analysis
Project [format_string(>>%-10s<<, ) AS format_string(>>%-10s<<, )#x]
+- OneRowRelation


-- !query
select format_string('>>%-10s<<', 'Hello')
-- !query analysis
Project [format_string(>>%-10s<<, Hello) AS format_string(>>%-10s<<, Hello)#x]
+- OneRowRelation


-- !query
select format_string('>>%-10s<<', NULL)
-- !query analysis
Project [format_string(>>%-10s<<, null) AS format_string(>>%-10s<<, NULL)#x]
+- OneRowRelation


-- !query
select format_string('>>%1$10s<<', 'Hello')
-- !query analysis
Project [format_string(>>%1$10s<<, Hello) AS format_string(>>%1$10s<<, Hello)#x]
+- OneRowRelation


-- !query
DROP TABLE TEXT_TBL
-- !query analysis
DropTableCommand `spark_catalog`.`default`.`TEXT_TBL`, false, false, false
