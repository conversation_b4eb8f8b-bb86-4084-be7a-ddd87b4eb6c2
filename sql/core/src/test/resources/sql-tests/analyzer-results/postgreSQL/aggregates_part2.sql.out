-- Automatically generated by SQLQueryTestSuite
-- !query
create temporary view int4_tbl as select * from values
  (0),
  (123456),
  (-123456),
  (2147483647),
  (-2147483647)
  as int4_tbl(f1)
-- !query analysis
CreateViewCommand `int4_tbl`, select * from values
  (0),
  (123456),
  (-123456),
  (2147483647),
  (-2147483647)
  as int4_tbl(f1), false, false, LocalTempView, true
   +- Project [f1#x]
      +- SubqueryAlias int4_tbl
         +- LocalRelation [f1#x]


-- !query
CREATE OR REPLACE TEMPORARY VIEW bitwise_test AS SELECT * FROM VALUES
  (1, 1, 1, 1L),
  (3, 3, 3, null),
  (7, 7, 7, 3L) AS bitwise_test(b1, b2, b3, b4)
-- !query analysis
CreateViewCommand `bitwise_test`, SELECT * FROM VALUES
  (1, 1, 1, 1L),
  (3, 3, 3, null),
  (7, 7, 7, 3L) AS bitwise_test(b1, b2, b3, b4), false, true, LocalTempView, true
   +- Project [b1#x, b2#x, b3#x, b4#xL]
      +- SubqueryAlias bitwise_test
         +- LocalRelation [b1#x, b2#x, b3#x, b4#xL]


-- !query
SELECT BIT_AND(b1) AS n1, BIT_OR(b2)  AS n2 FROM bitwise_test where 1 = 0
-- !query analysis
Aggregate [bit_and(b1#x) AS n1#x, bit_or(b2#x) AS n2#x]
+- Filter (1 = 0)
   +- SubqueryAlias bitwise_test
      +- View (`bitwise_test`, [b1#x,b2#x,b3#x,b4#xL])
         +- Project [cast(b1#x as int) AS b1#x, cast(b2#x as int) AS b2#x, cast(b3#x as int) AS b3#x, cast(b4#xL as bigint) AS b4#xL]
            +- Project [b1#x, b2#x, b3#x, b4#xL]
               +- SubqueryAlias bitwise_test
                  +- LocalRelation [b1#x, b2#x, b3#x, b4#xL]


-- !query
SELECT BIT_AND(b4) AS n1, BIT_OR(b4)  AS n2 FROM bitwise_test where b4 is null
-- !query analysis
Aggregate [bit_and(b4#xL) AS n1#xL, bit_or(b4#xL) AS n2#xL]
+- Filter isnull(b4#xL)
   +- SubqueryAlias bitwise_test
      +- View (`bitwise_test`, [b1#x,b2#x,b3#x,b4#xL])
         +- Project [cast(b1#x as int) AS b1#x, cast(b2#x as int) AS b2#x, cast(b3#x as int) AS b3#x, cast(b4#xL as bigint) AS b4#xL]
            +- Project [b1#x, b2#x, b3#x, b4#xL]
               +- SubqueryAlias bitwise_test
                  +- LocalRelation [b1#x, b2#x, b3#x, b4#xL]


-- !query
SELECT
 BIT_AND(cast(b1 as tinyint)) AS a1,
 BIT_AND(cast(b2 as smallint)) AS b1,
 BIT_AND(b3) AS c1,
 BIT_AND(b4) AS d1,
 BIT_OR(cast(b1 as tinyint))  AS e7,
 BIT_OR(cast(b2 as smallint))  AS f7,
 BIT_OR(b3)  AS g7,
 BIT_OR(b4)  AS h3
FROM bitwise_test
-- !query analysis
Aggregate [bit_and(cast(b1#x as tinyint)) AS a1#x, bit_and(cast(b2#x as smallint)) AS b1#x, bit_and(b3#x) AS c1#x, bit_and(b4#xL) AS d1#xL, bit_or(cast(b1#x as tinyint)) AS e7#x, bit_or(cast(b2#x as smallint)) AS f7#x, bit_or(b3#x) AS g7#x, bit_or(b4#xL) AS h3#xL]
+- SubqueryAlias bitwise_test
   +- View (`bitwise_test`, [b1#x,b2#x,b3#x,b4#xL])
      +- Project [cast(b1#x as int) AS b1#x, cast(b2#x as int) AS b2#x, cast(b3#x as int) AS b3#x, cast(b4#xL as bigint) AS b4#xL]
         +- Project [b1#x, b2#x, b3#x, b4#xL]
            +- SubqueryAlias bitwise_test
               +- LocalRelation [b1#x, b2#x, b3#x, b4#xL]


-- !query
SELECT b1 , bit_and(b2), bit_or(b4) FROM bitwise_test GROUP BY b1
-- !query analysis
Aggregate [b1#x], [b1#x, bit_and(b2#x) AS bit_and(b2)#x, bit_or(b4#xL) AS bit_or(b4)#xL]
+- SubqueryAlias bitwise_test
   +- View (`bitwise_test`, [b1#x,b2#x,b3#x,b4#xL])
      +- Project [cast(b1#x as int) AS b1#x, cast(b2#x as int) AS b2#x, cast(b3#x as int) AS b3#x, cast(b4#xL as bigint) AS b4#xL]
         +- Project [b1#x, b2#x, b3#x, b4#xL]
            +- SubqueryAlias bitwise_test
               +- LocalRelation [b1#x, b2#x, b3#x, b4#xL]


-- !query
SELECT b1, bit_and(b2) FROM bitwise_test GROUP BY b1 HAVING bit_and(b2) < 7
-- !query analysis
Filter (bit_and(b2)#x < 7)
+- Aggregate [b1#x], [b1#x, bit_and(b2#x) AS bit_and(b2)#x]
   +- SubqueryAlias bitwise_test
      +- View (`bitwise_test`, [b1#x,b2#x,b3#x,b4#xL])
         +- Project [cast(b1#x as int) AS b1#x, cast(b2#x as int) AS b2#x, cast(b3#x as int) AS b3#x, cast(b4#xL as bigint) AS b4#xL]
            +- Project [b1#x, b2#x, b3#x, b4#xL]
               +- SubqueryAlias bitwise_test
                  +- LocalRelation [b1#x, b2#x, b3#x, b4#xL]


-- !query
SELECT b1, b2, bit_and(b2) OVER (PARTITION BY b1 ORDER BY b2) FROM bitwise_test
-- !query analysis
Project [b1#x, b2#x, bit_and(b2) OVER (PARTITION BY b1 ORDER BY b2 ASC NULLS FIRST RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)#x]
+- Project [b1#x, b2#x, bit_and(b2) OVER (PARTITION BY b1 ORDER BY b2 ASC NULLS FIRST RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)#x, bit_and(b2) OVER (PARTITION BY b1 ORDER BY b2 ASC NULLS FIRST RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)#x]
   +- Window [bit_and(b2#x) windowspecdefinition(b1#x, b2#x ASC NULLS FIRST, specifiedwindowframe(RangeFrame, unboundedpreceding$(), currentrow$())) AS bit_and(b2) OVER (PARTITION BY b1 ORDER BY b2 ASC NULLS FIRST RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)#x], [b1#x], [b2#x ASC NULLS FIRST]
      +- Project [b1#x, b2#x]
         +- SubqueryAlias bitwise_test
            +- View (`bitwise_test`, [b1#x,b2#x,b3#x,b4#xL])
               +- Project [cast(b1#x as int) AS b1#x, cast(b2#x as int) AS b2#x, cast(b3#x as int) AS b3#x, cast(b4#xL as bigint) AS b4#xL]
                  +- Project [b1#x, b2#x, b3#x, b4#xL]
                     +- SubqueryAlias bitwise_test
                        +- LocalRelation [b1#x, b2#x, b3#x, b4#xL]


-- !query
SELECT b1, b2, bit_or(b2) OVER (PARTITION BY b1 ORDER BY b2) FROM bitwise_test
-- !query analysis
Project [b1#x, b2#x, bit_or(b2) OVER (PARTITION BY b1 ORDER BY b2 ASC NULLS FIRST RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)#x]
+- Project [b1#x, b2#x, bit_or(b2) OVER (PARTITION BY b1 ORDER BY b2 ASC NULLS FIRST RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)#x, bit_or(b2) OVER (PARTITION BY b1 ORDER BY b2 ASC NULLS FIRST RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)#x]
   +- Window [bit_or(b2#x) windowspecdefinition(b1#x, b2#x ASC NULLS FIRST, specifiedwindowframe(RangeFrame, unboundedpreceding$(), currentrow$())) AS bit_or(b2) OVER (PARTITION BY b1 ORDER BY b2 ASC NULLS FIRST RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)#x], [b1#x], [b2#x ASC NULLS FIRST]
      +- Project [b1#x, b2#x]
         +- SubqueryAlias bitwise_test
            +- View (`bitwise_test`, [b1#x,b2#x,b3#x,b4#xL])
               +- Project [cast(b1#x as int) AS b1#x, cast(b2#x as int) AS b2#x, cast(b3#x as int) AS b3#x, cast(b4#xL as bigint) AS b4#xL]
                  +- Project [b1#x, b2#x, b3#x, b4#xL]
                     +- SubqueryAlias bitwise_test
                        +- LocalRelation [b1#x, b2#x, b3#x, b4#xL]


-- !query
SELECT
  (NULL AND NULL) IS NULL AS `t`,
  (TRUE AND NULL) IS NULL AS `t`,
  (FALSE AND NULL) IS NULL AS `t`,
  (NULL AND TRUE) IS NULL AS `t`,
  (NULL AND FALSE) IS NULL AS `t`,
  (TRUE AND TRUE) AS `t`,
  NOT (TRUE AND FALSE) AS `t`,
  NOT (FALSE AND TRUE) AS `t`,
  NOT (FALSE AND FALSE) AS `t`
-- !query analysis
Project [isnull((null AND null)) AS t#x, isnull((true AND cast(null as boolean))) AS t#x, isnull((false AND cast(null as boolean))) AS t#x, isnull((cast(null as boolean) AND true)) AS t#x, isnull((cast(null as boolean) AND false)) AS t#x, (true AND true) AS t#x, NOT (true AND false) AS t#x, NOT (false AND true) AS t#x, NOT (false AND false) AS t#x]
+- OneRowRelation


-- !query
SELECT
  (NULL OR NULL) IS NULL AS `t`,
  (TRUE OR NULL) IS NULL AS `t`,
  (FALSE OR NULL) IS NULL AS `t`,
  (NULL OR TRUE) IS NULL AS `t`,
  (NULL OR FALSE) IS NULL AS `t`,
  (TRUE OR TRUE) AS `t`,
  (TRUE OR FALSE) AS `t`,
  (FALSE OR TRUE) AS `t`,
  NOT (FALSE OR FALSE) AS `t`
-- !query analysis
Project [isnull((null OR null)) AS t#x, isnull((true OR cast(null as boolean))) AS t#x, isnull((false OR cast(null as boolean))) AS t#x, isnull((cast(null as boolean) OR true)) AS t#x, isnull((cast(null as boolean) OR false)) AS t#x, (true OR true) AS t#x, (true OR false) AS t#x, (false OR true) AS t#x, NOT (false OR false) AS t#x]
+- OneRowRelation


-- !query
CREATE OR REPLACE TEMPORARY VIEW bool_test AS SELECT * FROM VALUES
  (TRUE, null, FALSE, null),
  (FALSE, TRUE, null, null),
  (null, TRUE, FALSE, null) AS bool_test(b1, b2, b3, b4)
-- !query analysis
CreateViewCommand `bool_test`, SELECT * FROM VALUES
  (TRUE, null, FALSE, null),
  (FALSE, TRUE, null, null),
  (null, TRUE, FALSE, null) AS bool_test(b1, b2, b3, b4), false, true, LocalTempView, true
   +- Project [b1#x, b2#x, b3#x, b4#x]
      +- SubqueryAlias bool_test
         +- LocalRelation [b1#x, b2#x, b3#x, b4#x]


-- !query
SELECT BOOL_AND(b1) AS n1, BOOL_OR(b3) AS n2 FROM bool_test WHERE 1 = 0
-- !query analysis
Aggregate [bool_and(b1#x) AS n1#x, bool_or(b3#x) AS n2#x]
+- Filter (1 = 0)
   +- SubqueryAlias bool_test
      +- View (`bool_test`, [b1#x,b2#x,b3#x,b4#x])
         +- Project [cast(b1#x as boolean) AS b1#x, cast(b2#x as boolean) AS b2#x, cast(b3#x as boolean) AS b3#x, cast(b4#x as void) AS b4#x]
            +- Project [b1#x, b2#x, b3#x, b4#x]
               +- SubqueryAlias bool_test
                  +- LocalRelation [b1#x, b2#x, b3#x, b4#x]


-- !query
SELECT
  BOOL_AND(b1)     AS f1,
  BOOL_AND(b2)     AS t2,
  BOOL_AND(b3)     AS f3,
  BOOL_AND(b4)     AS n4,
  BOOL_AND(NOT b2) AS f5,
  BOOL_AND(NOT b3) AS t6
FROM bool_test
-- !query analysis
Aggregate [bool_and(b1#x) AS f1#x, bool_and(b2#x) AS t2#x, bool_and(b3#x) AS f3#x, bool_and(cast(b4#x as boolean)) AS n4#x, bool_and(NOT b2#x) AS f5#x, bool_and(NOT b3#x) AS t6#x]
+- SubqueryAlias bool_test
   +- View (`bool_test`, [b1#x,b2#x,b3#x,b4#x])
      +- Project [cast(b1#x as boolean) AS b1#x, cast(b2#x as boolean) AS b2#x, cast(b3#x as boolean) AS b3#x, cast(b4#x as void) AS b4#x]
         +- Project [b1#x, b2#x, b3#x, b4#x]
            +- SubqueryAlias bool_test
               +- LocalRelation [b1#x, b2#x, b3#x, b4#x]


-- !query
SELECT
  EVERY(b1)     AS f1,
  EVERY(b2)     AS t2,
  EVERY(b3)     AS f3,
  EVERY(b4)     AS n4,
  EVERY(NOT b2) AS f5,
  EVERY(NOT b3) AS t6
FROM bool_test
-- !query analysis
Aggregate [every(b1#x) AS f1#x, every(b2#x) AS t2#x, every(b3#x) AS f3#x, every(cast(b4#x as boolean)) AS n4#x, every(NOT b2#x) AS f5#x, every(NOT b3#x) AS t6#x]
+- SubqueryAlias bool_test
   +- View (`bool_test`, [b1#x,b2#x,b3#x,b4#x])
      +- Project [cast(b1#x as boolean) AS b1#x, cast(b2#x as boolean) AS b2#x, cast(b3#x as boolean) AS b3#x, cast(b4#x as void) AS b4#x]
         +- Project [b1#x, b2#x, b3#x, b4#x]
            +- SubqueryAlias bool_test
               +- LocalRelation [b1#x, b2#x, b3#x, b4#x]


-- !query
SELECT
  BOOL_OR(b1)      AS t1,
  BOOL_OR(b2)      AS t2,
  BOOL_OR(b3)      AS f3,
  BOOL_OR(b4)      AS n4,
  BOOL_OR(NOT b2)  AS f5,
  BOOL_OR(NOT b3)  AS t6
FROM bool_test
-- !query analysis
Aggregate [bool_or(b1#x) AS t1#x, bool_or(b2#x) AS t2#x, bool_or(b3#x) AS f3#x, bool_or(cast(b4#x as boolean)) AS n4#x, bool_or(NOT b2#x) AS f5#x, bool_or(NOT b3#x) AS t6#x]
+- SubqueryAlias bool_test
   +- View (`bool_test`, [b1#x,b2#x,b3#x,b4#x])
      +- Project [cast(b1#x as boolean) AS b1#x, cast(b2#x as boolean) AS b2#x, cast(b3#x as boolean) AS b3#x, cast(b4#x as void) AS b4#x]
         +- Project [b1#x, b2#x, b3#x, b4#x]
            +- SubqueryAlias bool_test
               +- LocalRelation [b1#x, b2#x, b3#x, b4#x]


-- !query
select min(unique1) from tenk1
-- !query analysis
Aggregate [min(unique1#x) AS min(unique1)#x]
+- SubqueryAlias spark_catalog.default.tenk1
   +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select max(unique1) from tenk1
-- !query analysis
Aggregate [max(unique1#x) AS max(unique1)#x]
+- SubqueryAlias spark_catalog.default.tenk1
   +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select max(unique1) from tenk1 where unique1 < 42
-- !query analysis
Aggregate [max(unique1#x) AS max(unique1)#x]
+- Filter (unique1#x < 42)
   +- SubqueryAlias spark_catalog.default.tenk1
      +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select max(unique1) from tenk1 where unique1 > 42
-- !query analysis
Aggregate [max(unique1#x) AS max(unique1)#x]
+- Filter (unique1#x > 42)
   +- SubqueryAlias spark_catalog.default.tenk1
      +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select max(unique1) from tenk1 where unique1 > 42000
-- !query analysis
Aggregate [max(unique1#x) AS max(unique1)#x]
+- Filter (unique1#x > 42000)
   +- SubqueryAlias spark_catalog.default.tenk1
      +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select max(tenthous) from tenk1 where thousand = 33
-- !query analysis
Aggregate [max(tenthous#x) AS max(tenthous)#x]
+- Filter (thousand#x = 33)
   +- SubqueryAlias spark_catalog.default.tenk1
      +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select min(tenthous) from tenk1 where thousand = 33
-- !query analysis
Aggregate [min(tenthous#x) AS min(tenthous)#x]
+- Filter (thousand#x = 33)
   +- SubqueryAlias spark_catalog.default.tenk1
      +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select distinct max(unique2) from tenk1
-- !query analysis
Distinct
+- Aggregate [max(unique2#x) AS max(unique2)#x]
   +- SubqueryAlias spark_catalog.default.tenk1
      +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select max(unique2) from tenk1 order by 1
-- !query analysis
Sort [max(unique2)#x ASC NULLS FIRST], true
+- Aggregate [max(unique2#x) AS max(unique2)#x]
   +- SubqueryAlias spark_catalog.default.tenk1
      +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select max(unique2) from tenk1 order by max(unique2)
-- !query analysis
Sort [max(unique2)#x ASC NULLS FIRST], true
+- Aggregate [max(unique2#x) AS max(unique2)#x]
   +- SubqueryAlias spark_catalog.default.tenk1
      +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select max(unique2) from tenk1 order by max(unique2)+1
-- !query analysis
Sort [(max(unique2)#x + 1) ASC NULLS FIRST], true
+- Aggregate [max(unique2#x) AS max(unique2)#x]
   +- SubqueryAlias spark_catalog.default.tenk1
      +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select t1.max_unique2, g from (select max(unique2) as max_unique2 FROM tenk1) t1 LATERAL VIEW explode(array(1,2,3)) t2 AS g order by g desc
-- !query analysis
Sort [g#x DESC NULLS LAST], true
+- Project [max_unique2#x, g#x]
   +- Generate explode(array(1, 2, 3)), false, t2, [g#x]
      +- SubqueryAlias t1
         +- Aggregate [max(unique2#x) AS max_unique2#x]
            +- SubqueryAlias spark_catalog.default.tenk1
               +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet


-- !query
select max(100) from tenk1
-- !query analysis
Aggregate [max(100) AS max(100)#x]
+- SubqueryAlias spark_catalog.default.tenk1
   +- Relation spark_catalog.default.tenk1[unique1#x,unique2#x,two#x,four#x,ten#x,twenty#x,hundred#x,thousand#x,twothousand#x,fivethous#x,tenthous#x,odd#x,even#x,stringu1#x,stringu2#x,string4#x] parquet
