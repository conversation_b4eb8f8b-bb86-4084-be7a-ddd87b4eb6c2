-- Automatically generated by SQLQueryTestSuite
-- !query
select timestamp_ntz'2016-12-31 00:12:00', timestamp_ntz'2016-12-31'
-- !query analysis
Project [2016-12-31 00:12:00 AS TIMESTAMP_NTZ '2016-12-31 00:12:00'#x, 2016-12-31 00:00:00 AS TIMESTAMP_NTZ '2016-12-31 00:00:00'#x]
+- OneRowRelation


-- !query
select to_timestamp_ntz(null), to_timestamp_ntz('2016-12-31 00:12:00'), to_timestamp_ntz('2016-12-31', 'yyyy-MM-dd')
-- !query analysis
Project [to_timestamp_ntz(cast(null as string), None, TimestampNTZType, Some(America/Los_Angeles), false) AS to_timestamp_ntz(NULL)#x, to_timestamp_ntz(2016-12-31 00:12:00, None, TimestampNTZType, Some(America/Los_Angeles), false) AS to_timestamp_ntz(2016-12-31 00:12:00)#x, to_timestamp_ntz(2016-12-31, <PERSON>(yyyy-MM-dd), TimestampNTZType, Some(America/Los_Angeles), false) AS to_timestamp_ntz(2016-12-31, yyyy-MM-dd)#x]
+- OneRowRelation


-- !query
select to_timestamp_ntz(to_date(null)), to_timestamp_ntz(to_date('2016-12-31'))
-- !query analysis
Project [to_timestamp_ntz(to_date(cast(null as string), None, Some(America/Los_Angeles), false), None, TimestampNTZType, Some(America/Los_Angeles), false) AS to_timestamp_ntz(to_date(NULL))#x, to_timestamp_ntz(to_date(2016-12-31, None, Some(America/Los_Angeles), false), None, TimestampNTZType, Some(America/Los_Angeles), false) AS to_timestamp_ntz(to_date(2016-12-31))#x]
+- OneRowRelation


-- !query
select to_timestamp_ntz(to_timestamp_ltz(null)), to_timestamp_ntz(to_timestamp_ltz('2016-12-31 00:12:00'))
-- !query analysis
Project [to_timestamp_ntz(to_timestamp_ltz(cast(null as string), None, TimestampType, Some(America/Los_Angeles), false), None, TimestampNTZType, Some(America/Los_Angeles), false) AS to_timestamp_ntz(to_timestamp_ltz(NULL))#x, to_timestamp_ntz(to_timestamp_ltz(2016-12-31 00:12:00, None, TimestampType, Some(America/Los_Angeles), false), None, TimestampNTZType, Some(America/Los_Angeles), false) AS to_timestamp_ntz(to_timestamp_ltz(2016-12-31 00:12:00))#x]
+- OneRowRelation


-- !query
SELECT make_timestamp_ntz(2021, 07, 11, 6, 30, 45.678)
-- !query analysis
Project [make_timestamp_ntz(2021, 7, 11, 6, 30, cast(45.678 as decimal(16,6)), None, Some(America/Los_Angeles), false, TimestampNTZType) AS make_timestamp_ntz(2021, 7, 11, 6, 30, 45.678)#x]
+- OneRowRelation


-- !query
SELECT make_timestamp_ntz(2021, 07, 11, 6, 30, 45.678, 'CET')
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "WRONG_NUM_ARGS.WITHOUT_SUGGESTION",
  "sqlState" : "42605",
  "messageParameters" : {
    "actualNum" : "7",
    "docroot" : "https://spark.apache.org/docs/latest",
    "expectedNum" : "6",
    "functionName" : "`make_timestamp_ntz`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 61,
    "fragment" : "make_timestamp_ntz(2021, 07, 11, 6, 30, 45.678, 'CET')"
  } ]
}


-- !query
SELECT make_timestamp_ntz(2021, 07, 11, 6, 30, 60.007)
-- !query analysis
Project [make_timestamp_ntz(2021, 7, 11, 6, 30, cast(60.007 as decimal(16,6)), None, Some(America/Los_Angeles), false, TimestampNTZType) AS make_timestamp_ntz(2021, 7, 11, 6, 30, 60.007)#x]
+- OneRowRelation


-- !query
SELECT convert_timezone('Europe/Moscow', 'America/Los_Angeles', timestamp_ntz'2022-01-01 00:00:00')
-- !query analysis
Project [convert_timezone(Europe/Moscow, America/Los_Angeles, 2022-01-01 00:00:00) AS convert_timezone(Europe/Moscow, America/Los_Angeles, TIMESTAMP_NTZ '2022-01-01 00:00:00')#x]
+- OneRowRelation


-- !query
SELECT convert_timezone('Europe/Brussels', timestamp_ntz'2022-03-23 00:00:00')
-- !query analysis
Project [convert_timezone(current_timezone(), Europe/Brussels, 2022-03-23 00:00:00) AS convert_timezone(current_timezone(), Europe/Brussels, TIMESTAMP_NTZ '2022-03-23 00:00:00')#x]
+- OneRowRelation


-- !query
select timestampdiff(QUARTER, timestamp_ntz'2022-01-01 01:02:03', timestamp_ntz'2022-05-02 05:06:07')
-- !query analysis
Project [timestampdiff(QUARTER, cast(2022-01-01 01:02:03 as timestamp), cast(2022-05-02 05:06:07 as timestamp), Some(America/Los_Angeles)) AS timestampdiff(QUARTER, TIMESTAMP_NTZ '2022-01-01 01:02:03', TIMESTAMP_NTZ '2022-05-02 05:06:07')#xL]
+- OneRowRelation


-- !query
select timestampdiff(HOUR, timestamp_ntz'2022-02-14 01:02:03', timestamp_ltz'2022-02-14 02:03:04')
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
select timestampdiff(YEAR, date'2022-02-15', timestamp_ntz'2023-02-15 10:11:12')
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
select timestampdiff(MILLISECOND, timestamp_ntz'2022-02-14 23:59:59.123', date'2022-02-15')
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
select timestamp_ntz'2022-01-01 00:00:00' = date'2022-01-01'
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
select timestamp_ntz'2022-01-01 00:00:00' > date'2022-01-01'
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
select timestamp_ntz'2022-01-01 00:00:00' < date'2022-01-01'
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
select timestamp_ntz'2022-01-01 00:00:00' = timestamp_ltz'2022-01-01 00:00:00'
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
select timestamp_ntz'2022-01-01 00:00:00' > timestamp_ltz'2022-01-01 00:00:00'
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
select timestamp_ntz'2022-01-01 00:00:00' < timestamp_ltz'2022-01-01 00:00:00'
-- !query analysis
[Analyzer test output redacted due to nondeterminism]
