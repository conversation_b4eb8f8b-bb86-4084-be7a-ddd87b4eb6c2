-- Automatically generated by SQLQueryTestSuite
-- !query
create temporary view data as select * from values
  (0, 1),
  (0, 2),
  (1, 3),
  (1, NULL)
  as data(g, i)
-- !query analysis
CreateViewCommand `data`, select * from values
  (0, 1),
  (0, 2),
  (1, 3),
  (1, NULL)
  as data(g, i), false, false, LocalTempView, true
   +- Project [g#x, i#x]
      +- SubqueryAlias data
         +- LocalRelation [g#x, i#x]


-- !query
select g from data order by all
-- !query analysis
Sort [g#x ASC NULLS FIRST], true
+- Project [g#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data order by all
-- !query analysis
Sort [g#x ASC NULLS FIRST, i#x ASC NULLS FIRST], true
+- Project [g#x, i#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data order by aLl
-- !query analysis
Sort [g#x ASC NULLS FIRST, i#x ASC NULLS FIRST], true
+- Project [g#x, i#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data order by all asc
-- !query analysis
Sort [g#x ASC NULLS FIRST, i#x ASC NULLS FIRST], true
+- Project [g#x, i#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data order by all desc
-- !query analysis
Sort [g#x DESC NULLS LAST, i#x DESC NULLS LAST], true
+- Project [g#x, i#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data order by all nulls first
-- !query analysis
Sort [g#x ASC NULLS FIRST, i#x ASC NULLS FIRST], true
+- Project [g#x, i#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data order by all nulls last
-- !query analysis
Sort [g#x ASC NULLS LAST, i#x ASC NULLS LAST], true
+- Project [g#x, i#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data order by all asc nulls first
-- !query analysis
Sort [g#x ASC NULLS FIRST, i#x ASC NULLS FIRST], true
+- Project [g#x, i#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data order by all desc nulls first
-- !query analysis
Sort [g#x DESC NULLS FIRST, i#x DESC NULLS FIRST], true
+- Project [g#x, i#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data order by all asc nulls last
-- !query analysis
Sort [g#x ASC NULLS LAST, i#x ASC NULLS LAST], true
+- Project [g#x, i#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data order by all desc nulls last
-- !query analysis
Sort [g#x DESC NULLS LAST, i#x DESC NULLS LAST], true
+- Project [g#x, i#x]
   +- SubqueryAlias data
      +- View (`data`, [g#x,i#x])
         +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
            +- Project [g#x, i#x]
               +- SubqueryAlias data
                  +- LocalRelation [g#x, i#x]


-- !query
select * from data union all select * from data order by all
-- !query analysis
Sort [g#x ASC NULLS FIRST, i#x ASC NULLS FIRST], true
+- Union false, false
   :- Project [g#x, i#x]
   :  +- SubqueryAlias data
   :     +- View (`data`, [g#x,i#x])
   :        +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
   :           +- Project [g#x, i#x]
   :              +- SubqueryAlias data
   :                 +- LocalRelation [g#x, i#x]
   +- Project [g#x, i#x]
      +- SubqueryAlias data
         +- View (`data`, [g#x,i#x])
            +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
               +- Project [g#x, i#x]
                  +- SubqueryAlias data
                     +- LocalRelation [g#x, i#x]


-- !query
select * from data union select * from data order by all
-- !query analysis
Sort [g#x ASC NULLS FIRST, i#x ASC NULLS FIRST], true
+- Distinct
   +- Union false, false
      :- Project [g#x, i#x]
      :  +- SubqueryAlias data
      :     +- View (`data`, [g#x,i#x])
      :        +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
      :           +- Project [g#x, i#x]
      :              +- SubqueryAlias data
      :                 +- LocalRelation [g#x, i#x]
      +- Project [g#x, i#x]
         +- SubqueryAlias data
            +- View (`data`, [g#x,i#x])
               +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
                  +- Project [g#x, i#x]
                     +- SubqueryAlias data
                        +- LocalRelation [g#x, i#x]


-- !query
select * from data order by all limit 2
-- !query analysis
GlobalLimit 2
+- LocalLimit 2
   +- Sort [g#x ASC NULLS FIRST, i#x ASC NULLS FIRST], true
      +- Project [g#x, i#x]
         +- SubqueryAlias data
            +- View (`data`, [g#x,i#x])
               +- Project [cast(g#x as int) AS g#x, cast(i#x as int) AS i#x]
                  +- Project [g#x, i#x]
                     +- SubqueryAlias data
                        +- LocalRelation [g#x, i#x]


-- !query
select * from values("z", 1), ("y", 2), ("x", 3) AS T(col1, all) order by all
-- !query analysis
Sort [all#x ASC NULLS FIRST], true
+- Project [col1#x, all#x]
   +- SubqueryAlias T
      +- LocalRelation [col1#x, all#x]


-- !query
select name, dept, rank() over (partition by dept order by all) as rank
from values('Lisa', 'Sales', 10000, 35) as T(name, dept, salary, age)
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "UNRESOLVED_COLUMN.WITH_SUGGESTION",
  "sqlState" : "42703",
  "messageParameters" : {
    "objectName" : "`all`",
    "proposal" : "`age`, `name`, `dept`, `salary`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 60,
    "stopIndex" : 62,
    "fragment" : "all"
  } ]
}
