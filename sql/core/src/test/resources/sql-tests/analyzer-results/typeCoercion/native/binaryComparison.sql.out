-- Automatically generated by SQLQueryTestSuite
-- !query
CREATE TEMPORARY VIEW t AS SELECT 1
-- !query analysis
CreateViewCommand `t`, SELECT 1, false, false, LocalTempView, true
   +- Project [1 AS 1#x]
      +- OneRowRelation


-- !query
SELECT cast(1 as binary) = '1' FROM t
-- !query analysis
Project [(cast(1 as binary) = cast(1 as binary)) AS (CAST(1 AS BINARY) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) > '2' FROM t
-- !query analysis
Project [(cast(1 as binary) > cast(2 as binary)) AS (CAST(1 AS BINARY) > 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) >= '2' FROM t
-- !query analysis
Project [(cast(1 as binary) >= cast(2 as binary)) AS (CAST(1 AS BINARY) >= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) < '2' FROM t
-- !query analysis
Project [(cast(1 as binary) < cast(2 as binary)) AS (CAST(1 AS BINARY) < 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) <= '2' FROM t
-- !query analysis
Project [(cast(1 as binary) <= cast(2 as binary)) AS (CAST(1 AS BINARY) <= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) <> '2' FROM t
-- !query analysis
Project [NOT (cast(1 as binary) = cast(2 as binary)) AS (NOT (CAST(1 AS BINARY) = 2))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) = cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as binary) = cast(cast(null as string) as binary)) AS (CAST(1 AS BINARY) = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) > cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as binary) > cast(cast(null as string) as binary)) AS (CAST(1 AS BINARY) > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) >= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as binary) >= cast(cast(null as string) as binary)) AS (CAST(1 AS BINARY) >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) < cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as binary) < cast(cast(null as string) as binary)) AS (CAST(1 AS BINARY) < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) <= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as binary) <= cast(cast(null as string) as binary)) AS (CAST(1 AS BINARY) <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as binary) <> cast(null as string) FROM t
-- !query analysis
Project [NOT (cast(1 as binary) = cast(cast(null as string) as binary)) AS (NOT (CAST(1 AS BINARY) = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as binary) FROM t
-- !query analysis
Project [(cast(1 as binary) = cast(1 as binary)) AS (1 = CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' > cast(1 as binary) FROM t
-- !query analysis
Project [(cast(2 as binary) > cast(1 as binary)) AS (2 > CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' >= cast(1 as binary) FROM t
-- !query analysis
Project [(cast(2 as binary) >= cast(1 as binary)) AS (2 >= CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' < cast(1 as binary) FROM t
-- !query analysis
Project [(cast(2 as binary) < cast(1 as binary)) AS (2 < CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <= cast(1 as binary) FROM t
-- !query analysis
Project [(cast(2 as binary) <= cast(1 as binary)) AS (2 <= CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <> cast(1 as binary) FROM t
-- !query analysis
Project [NOT (cast(2 as binary) = cast(1 as binary)) AS (NOT (2 = CAST(1 AS BINARY)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = cast(1 as binary) FROM t
-- !query analysis
Project [(cast(cast(null as string) as binary) = cast(1 as binary)) AS (CAST(NULL AS STRING) = CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) > cast(1 as binary) FROM t
-- !query analysis
Project [(cast(cast(null as string) as binary) > cast(1 as binary)) AS (CAST(NULL AS STRING) > CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) >= cast(1 as binary) FROM t
-- !query analysis
Project [(cast(cast(null as string) as binary) >= cast(1 as binary)) AS (CAST(NULL AS STRING) >= CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) < cast(1 as binary) FROM t
-- !query analysis
Project [(cast(cast(null as string) as binary) < cast(1 as binary)) AS (CAST(NULL AS STRING) < CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <= cast(1 as binary) FROM t
-- !query analysis
Project [(cast(cast(null as string) as binary) <= cast(1 as binary)) AS (CAST(NULL AS STRING) <= CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <> cast(1 as binary) FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as binary) = cast(1 as binary)) AS (NOT (CAST(NULL AS STRING) = CAST(1 AS BINARY)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) = '1' FROM t
-- !query analysis
Project [(cast(1 as tinyint) = cast(1 as tinyint)) AS (CAST(1 AS TINYINT) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) > '2' FROM t
-- !query analysis
Project [(cast(1 as tinyint) > cast(2 as tinyint)) AS (CAST(1 AS TINYINT) > 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) >= '2' FROM t
-- !query analysis
Project [(cast(1 as tinyint) >= cast(2 as tinyint)) AS (CAST(1 AS TINYINT) >= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) < '2' FROM t
-- !query analysis
Project [(cast(1 as tinyint) < cast(2 as tinyint)) AS (CAST(1 AS TINYINT) < 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <= '2' FROM t
-- !query analysis
Project [(cast(1 as tinyint) <= cast(2 as tinyint)) AS (CAST(1 AS TINYINT) <= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <> '2' FROM t
-- !query analysis
Project [NOT (cast(1 as tinyint) = cast(2 as tinyint)) AS (NOT (CAST(1 AS TINYINT) = 2))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) = cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as tinyint) = cast(cast(null as string) as tinyint)) AS (CAST(1 AS TINYINT) = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) > cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as tinyint) > cast(cast(null as string) as tinyint)) AS (CAST(1 AS TINYINT) > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) >= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as tinyint) >= cast(cast(null as string) as tinyint)) AS (CAST(1 AS TINYINT) >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) < cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as tinyint) < cast(cast(null as string) as tinyint)) AS (CAST(1 AS TINYINT) < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as tinyint) <= cast(cast(null as string) as tinyint)) AS (CAST(1 AS TINYINT) <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <> cast(null as string) FROM t
-- !query analysis
Project [NOT (cast(1 as tinyint) = cast(cast(null as string) as tinyint)) AS (NOT (CAST(1 AS TINYINT) = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as tinyint) = cast(1 as tinyint)) AS (1 = CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' > cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(2 as tinyint) > cast(1 as tinyint)) AS (2 > CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' >= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(2 as tinyint) >= cast(1 as tinyint)) AS (2 >= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' < cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(2 as tinyint) < cast(1 as tinyint)) AS (2 < CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(2 as tinyint) <= cast(1 as tinyint)) AS (2 <= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <> cast(1 as tinyint) FROM t
-- !query analysis
Project [NOT (cast(2 as tinyint) = cast(1 as tinyint)) AS (NOT (2 = CAST(1 AS TINYINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as tinyint) = cast(1 as tinyint)) AS (CAST(NULL AS STRING) = CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) > cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as tinyint) > cast(1 as tinyint)) AS (CAST(NULL AS STRING) > CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) >= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as tinyint) >= cast(1 as tinyint)) AS (CAST(NULL AS STRING) >= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) < cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as tinyint) < cast(1 as tinyint)) AS (CAST(NULL AS STRING) < CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as tinyint) <= cast(1 as tinyint)) AS (CAST(NULL AS STRING) <= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <> cast(1 as tinyint) FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as tinyint) = cast(1 as tinyint)) AS (NOT (CAST(NULL AS STRING) = CAST(1 AS TINYINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) = '1' FROM t
-- !query analysis
Project [(cast(1 as smallint) = cast(1 as smallint)) AS (CAST(1 AS SMALLINT) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) > '2' FROM t
-- !query analysis
Project [(cast(1 as smallint) > cast(2 as smallint)) AS (CAST(1 AS SMALLINT) > 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) >= '2' FROM t
-- !query analysis
Project [(cast(1 as smallint) >= cast(2 as smallint)) AS (CAST(1 AS SMALLINT) >= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) < '2' FROM t
-- !query analysis
Project [(cast(1 as smallint) < cast(2 as smallint)) AS (CAST(1 AS SMALLINT) < 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <= '2' FROM t
-- !query analysis
Project [(cast(1 as smallint) <= cast(2 as smallint)) AS (CAST(1 AS SMALLINT) <= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <> '2' FROM t
-- !query analysis
Project [NOT (cast(1 as smallint) = cast(2 as smallint)) AS (NOT (CAST(1 AS SMALLINT) = 2))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) = cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as smallint) = cast(cast(null as string) as smallint)) AS (CAST(1 AS SMALLINT) = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) > cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as smallint) > cast(cast(null as string) as smallint)) AS (CAST(1 AS SMALLINT) > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) >= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as smallint) >= cast(cast(null as string) as smallint)) AS (CAST(1 AS SMALLINT) >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) < cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as smallint) < cast(cast(null as string) as smallint)) AS (CAST(1 AS SMALLINT) < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as smallint) <= cast(cast(null as string) as smallint)) AS (CAST(1 AS SMALLINT) <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <> cast(null as string) FROM t
-- !query analysis
Project [NOT (cast(1 as smallint) = cast(cast(null as string) as smallint)) AS (NOT (CAST(1 AS SMALLINT) = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as smallint) = cast(1 as smallint)) AS (1 = CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' > cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(2 as smallint) > cast(1 as smallint)) AS (2 > CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' >= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(2 as smallint) >= cast(1 as smallint)) AS (2 >= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' < cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(2 as smallint) < cast(1 as smallint)) AS (2 < CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(2 as smallint) <= cast(1 as smallint)) AS (2 <= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <> cast(1 as smallint) FROM t
-- !query analysis
Project [NOT (cast(2 as smallint) = cast(1 as smallint)) AS (NOT (2 = CAST(1 AS SMALLINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as smallint) = cast(1 as smallint)) AS (CAST(NULL AS STRING) = CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) > cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as smallint) > cast(1 as smallint)) AS (CAST(NULL AS STRING) > CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) >= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as smallint) >= cast(1 as smallint)) AS (CAST(NULL AS STRING) >= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) < cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as smallint) < cast(1 as smallint)) AS (CAST(NULL AS STRING) < CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as smallint) <= cast(1 as smallint)) AS (CAST(NULL AS STRING) <= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <> cast(1 as smallint) FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as smallint) = cast(1 as smallint)) AS (NOT (CAST(NULL AS STRING) = CAST(1 AS SMALLINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) = '1' FROM t
-- !query analysis
Project [(cast(1 as int) = cast(1 as int)) AS (CAST(1 AS INT) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) > '2' FROM t
-- !query analysis
Project [(cast(1 as int) > cast(2 as int)) AS (CAST(1 AS INT) > 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) >= '2' FROM t
-- !query analysis
Project [(cast(1 as int) >= cast(2 as int)) AS (CAST(1 AS INT) >= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) < '2' FROM t
-- !query analysis
Project [(cast(1 as int) < cast(2 as int)) AS (CAST(1 AS INT) < 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <= '2' FROM t
-- !query analysis
Project [(cast(1 as int) <= cast(2 as int)) AS (CAST(1 AS INT) <= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <> '2' FROM t
-- !query analysis
Project [NOT (cast(1 as int) = cast(2 as int)) AS (NOT (CAST(1 AS INT) = 2))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) = cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as int) = cast(cast(null as string) as int)) AS (CAST(1 AS INT) = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) > cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as int) > cast(cast(null as string) as int)) AS (CAST(1 AS INT) > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) >= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as int) >= cast(cast(null as string) as int)) AS (CAST(1 AS INT) >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) < cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as int) < cast(cast(null as string) as int)) AS (CAST(1 AS INT) < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as int) <= cast(cast(null as string) as int)) AS (CAST(1 AS INT) <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <> cast(null as string) FROM t
-- !query analysis
Project [NOT (cast(1 as int) = cast(cast(null as string) as int)) AS (NOT (CAST(1 AS INT) = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as int) = cast(1 as int)) AS (1 = CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' > cast(1 as int) FROM t
-- !query analysis
Project [(cast(2 as int) > cast(1 as int)) AS (2 > CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' >= cast(1 as int) FROM t
-- !query analysis
Project [(cast(2 as int) >= cast(1 as int)) AS (2 >= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' < cast(1 as int) FROM t
-- !query analysis
Project [(cast(2 as int) < cast(1 as int)) AS (2 < CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <> cast(1 as int) FROM t
-- !query analysis
Project [NOT (cast(2 as int) = cast(1 as int)) AS (NOT (2 = CAST(1 AS INT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <= cast(1 as int) FROM t
-- !query analysis
Project [(cast(2 as int) <= cast(1 as int)) AS (2 <= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(null as string) as int) = cast(1 as int)) AS (CAST(NULL AS STRING) = CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) > cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(null as string) as int) > cast(1 as int)) AS (CAST(NULL AS STRING) > CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) >= cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(null as string) as int) >= cast(1 as int)) AS (CAST(NULL AS STRING) >= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) < cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(null as string) as int) < cast(1 as int)) AS (CAST(NULL AS STRING) < CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <> cast(1 as int) FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as int) = cast(1 as int)) AS (NOT (CAST(NULL AS STRING) = CAST(1 AS INT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <= cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(null as string) as int) <= cast(1 as int)) AS (CAST(NULL AS STRING) <= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) = '1' FROM t
-- !query analysis
Project [(cast(1 as bigint) = cast(1 as bigint)) AS (CAST(1 AS BIGINT) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) > '2' FROM t
-- !query analysis
Project [(cast(1 as bigint) > cast(2 as bigint)) AS (CAST(1 AS BIGINT) > 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) >= '2' FROM t
-- !query analysis
Project [(cast(1 as bigint) >= cast(2 as bigint)) AS (CAST(1 AS BIGINT) >= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) < '2' FROM t
-- !query analysis
Project [(cast(1 as bigint) < cast(2 as bigint)) AS (CAST(1 AS BIGINT) < 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <= '2' FROM t
-- !query analysis
Project [(cast(1 as bigint) <= cast(2 as bigint)) AS (CAST(1 AS BIGINT) <= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <> '2' FROM t
-- !query analysis
Project [NOT (cast(1 as bigint) = cast(2 as bigint)) AS (NOT (CAST(1 AS BIGINT) = 2))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) = cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as bigint) = cast(cast(null as string) as bigint)) AS (CAST(1 AS BIGINT) = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) > cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as bigint) > cast(cast(null as string) as bigint)) AS (CAST(1 AS BIGINT) > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) >= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as bigint) >= cast(cast(null as string) as bigint)) AS (CAST(1 AS BIGINT) >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) < cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as bigint) < cast(cast(null as string) as bigint)) AS (CAST(1 AS BIGINT) < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as bigint) <= cast(cast(null as string) as bigint)) AS (CAST(1 AS BIGINT) <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <> cast(null as string) FROM t
-- !query analysis
Project [NOT (cast(1 as bigint) = cast(cast(null as string) as bigint)) AS (NOT (CAST(1 AS BIGINT) = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as bigint) = cast(1 as bigint)) AS (1 = CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' > cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(2 as bigint) > cast(1 as bigint)) AS (2 > CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' >= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(2 as bigint) >= cast(1 as bigint)) AS (2 >= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' < cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(2 as bigint) < cast(1 as bigint)) AS (2 < CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(2 as bigint) <= cast(1 as bigint)) AS (2 <= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <> cast(1 as bigint) FROM t
-- !query analysis
Project [NOT (cast(2 as bigint) = cast(1 as bigint)) AS (NOT (2 = CAST(1 AS BIGINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as bigint) = cast(1 as bigint)) AS (CAST(NULL AS STRING) = CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) > cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as bigint) > cast(1 as bigint)) AS (CAST(NULL AS STRING) > CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) >= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as bigint) >= cast(1 as bigint)) AS (CAST(NULL AS STRING) >= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) < cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as bigint) < cast(1 as bigint)) AS (CAST(NULL AS STRING) < CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(null as string) as bigint) <= cast(1 as bigint)) AS (CAST(NULL AS STRING) <= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <> cast(1 as bigint) FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as bigint) = cast(1 as bigint)) AS (NOT (CAST(NULL AS STRING) = CAST(1 AS BIGINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) = cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > '2' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) > cast(2 as double)) AS (CAST(1 AS DECIMAL(10,0)) > 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= '2' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) >= cast(2 as double)) AS (CAST(1 AS DECIMAL(10,0)) >= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < '2' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) < cast(2 as double)) AS (CAST(1 AS DECIMAL(10,0)) < 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> '2' FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(10,0)) as double) = cast(2 as double)) AS (NOT (CAST(1 AS DECIMAL(10,0)) = 2))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= '2' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) <= cast(2 as double)) AS (CAST(1 AS DECIMAL(10,0)) <= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(null as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) = cast(cast(null as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(null as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) > cast(cast(null as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(null as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) >= cast(cast(null as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(null as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) < cast(cast(null as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(null as string) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(10,0)) as double) = cast(cast(null as string) as double)) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(null as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) <= cast(cast(null as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) = cast(cast(1 as decimal(10,0)) as double)) AS (1 = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(2 as double) > cast(cast(1 as decimal(10,0)) as double)) AS (2 > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(2 as double) >= cast(cast(1 as decimal(10,0)) as double)) AS (2 >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(2 as double) < cast(cast(1 as decimal(10,0)) as double)) AS (2 < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(2 as double) <= cast(cast(1 as decimal(10,0)) as double)) AS (2 <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(2 as double) = cast(cast(1 as decimal(10,0)) as double)) AS (NOT (2 = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(null as string) as double) = cast(cast(1 as decimal(10,0)) as double)) AS (CAST(NULL AS STRING) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(null as string) as double) > cast(cast(1 as decimal(10,0)) as double)) AS (CAST(NULL AS STRING) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(null as string) as double) >= cast(cast(1 as decimal(10,0)) as double)) AS (CAST(NULL AS STRING) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(null as string) as double) < cast(cast(1 as decimal(10,0)) as double)) AS (CAST(NULL AS STRING) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(null as string) as double) <= cast(cast(1 as decimal(10,0)) as double)) AS (CAST(NULL AS STRING) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as double) = cast(cast(1 as decimal(10,0)) as double)) AS (NOT (CAST(NULL AS STRING) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) = '1' FROM t
-- !query analysis
Project [(cast(1 as double) = cast(1 as double)) AS (CAST(1 AS DOUBLE) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) > '2' FROM t
-- !query analysis
Project [(cast(1 as double) > cast(2 as double)) AS (CAST(1 AS DOUBLE) > 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) >= '2' FROM t
-- !query analysis
Project [(cast(1 as double) >= cast(2 as double)) AS (CAST(1 AS DOUBLE) >= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) < '2' FROM t
-- !query analysis
Project [(cast(1 as double) < cast(2 as double)) AS (CAST(1 AS DOUBLE) < 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <= '2' FROM t
-- !query analysis
Project [(cast(1 as double) <= cast(2 as double)) AS (CAST(1 AS DOUBLE) <= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <> '2' FROM t
-- !query analysis
Project [NOT (cast(1 as double) = cast(2 as double)) AS (NOT (CAST(1 AS DOUBLE) = 2))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) = cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as double) = cast(cast(null as string) as double)) AS (CAST(1 AS DOUBLE) = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) > cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as double) > cast(cast(null as string) as double)) AS (CAST(1 AS DOUBLE) > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) >= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as double) >= cast(cast(null as string) as double)) AS (CAST(1 AS DOUBLE) >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) < cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as double) < cast(cast(null as string) as double)) AS (CAST(1 AS DOUBLE) < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as double) <= cast(cast(null as string) as double)) AS (CAST(1 AS DOUBLE) <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <> cast(null as string) FROM t
-- !query analysis
Project [NOT (cast(1 as double) = cast(cast(null as string) as double)) AS (NOT (CAST(1 AS DOUBLE) = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as double) FROM t
-- !query analysis
Project [(cast(1 as double) = cast(1 as double)) AS (1 = CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' > cast(1 as double) FROM t
-- !query analysis
Project [(cast(2 as double) > cast(1 as double)) AS (2 > CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' >= cast(1 as double) FROM t
-- !query analysis
Project [(cast(2 as double) >= cast(1 as double)) AS (2 >= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' < cast(1 as double) FROM t
-- !query analysis
Project [(cast(2 as double) < cast(1 as double)) AS (2 < CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <= cast(1 as double) FROM t
-- !query analysis
Project [(cast(2 as double) <= cast(1 as double)) AS (2 <= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <> cast(1 as double) FROM t
-- !query analysis
Project [NOT (cast(2 as double) = cast(1 as double)) AS (NOT (2 = CAST(1 AS DOUBLE)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(null as string) as double) = cast(1 as double)) AS (CAST(NULL AS STRING) = CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) > cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(null as string) as double) > cast(1 as double)) AS (CAST(NULL AS STRING) > CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) >= cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(null as string) as double) >= cast(1 as double)) AS (CAST(NULL AS STRING) >= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) < cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(null as string) as double) < cast(1 as double)) AS (CAST(NULL AS STRING) < CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <= cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(null as string) as double) <= cast(1 as double)) AS (CAST(NULL AS STRING) <= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <> cast(1 as double) FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as double) = cast(1 as double)) AS (NOT (CAST(NULL AS STRING) = CAST(1 AS DOUBLE)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) = '1' FROM t
-- !query analysis
Project [(cast(1 as float) = cast(1 as float)) AS (CAST(1 AS FLOAT) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) > '2' FROM t
-- !query analysis
Project [(cast(1 as float) > cast(2 as float)) AS (CAST(1 AS FLOAT) > 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) >= '2' FROM t
-- !query analysis
Project [(cast(1 as float) >= cast(2 as float)) AS (CAST(1 AS FLOAT) >= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) < '2' FROM t
-- !query analysis
Project [(cast(1 as float) < cast(2 as float)) AS (CAST(1 AS FLOAT) < 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <= '2' FROM t
-- !query analysis
Project [(cast(1 as float) <= cast(2 as float)) AS (CAST(1 AS FLOAT) <= 2)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <> '2' FROM t
-- !query analysis
Project [NOT (cast(1 as float) = cast(2 as float)) AS (NOT (CAST(1 AS FLOAT) = 2))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) = cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as float) = cast(cast(null as string) as float)) AS (CAST(1 AS FLOAT) = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) > cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as float) > cast(cast(null as string) as float)) AS (CAST(1 AS FLOAT) > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) >= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as float) >= cast(cast(null as string) as float)) AS (CAST(1 AS FLOAT) >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) < cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as float) < cast(cast(null as string) as float)) AS (CAST(1 AS FLOAT) < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <= cast(null as string) FROM t
-- !query analysis
Project [(cast(1 as float) <= cast(cast(null as string) as float)) AS (CAST(1 AS FLOAT) <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <> cast(null as string) FROM t
-- !query analysis
Project [NOT (cast(1 as float) = cast(cast(null as string) as float)) AS (NOT (CAST(1 AS FLOAT) = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as float) FROM t
-- !query analysis
Project [(cast(1 as float) = cast(1 as float)) AS (1 = CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' > cast(1 as float) FROM t
-- !query analysis
Project [(cast(2 as float) > cast(1 as float)) AS (2 > CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' >= cast(1 as float) FROM t
-- !query analysis
Project [(cast(2 as float) >= cast(1 as float)) AS (2 >= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' < cast(1 as float) FROM t
-- !query analysis
Project [(cast(2 as float) < cast(1 as float)) AS (2 < CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <= cast(1 as float) FROM t
-- !query analysis
Project [(cast(2 as float) <= cast(1 as float)) AS (2 <= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '2' <> cast(1 as float) FROM t
-- !query analysis
Project [NOT (cast(2 as float) = cast(1 as float)) AS (NOT (2 = CAST(1 AS FLOAT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(null as string) as float) = cast(1 as float)) AS (CAST(NULL AS STRING) = CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) > cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(null as string) as float) > cast(1 as float)) AS (CAST(NULL AS STRING) > CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) >= cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(null as string) as float) >= cast(1 as float)) AS (CAST(NULL AS STRING) >= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) < cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(null as string) as float) < cast(1 as float)) AS (CAST(NULL AS STRING) < CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <= cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(null as string) as float) <= cast(1 as float)) AS (CAST(NULL AS STRING) <= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <> cast(1 as float) FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as float) = cast(1 as float)) AS (NOT (CAST(NULL AS STRING) = CAST(1 AS FLOAT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-09-09' = date('1996-09-09') FROM t
-- !query analysis
Project [(cast(1996-09-09 as date) = cast(1996-09-09 as date)) AS (1996-09-09 = 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-9-10' > date('1996-09-09') FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) > cast(1996-09-09 as date)) AS (1996-9-10 > 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-9-10' >= date('1996-09-09') FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) >= cast(1996-09-09 as date)) AS (1996-9-10 >= 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-9-10' < date('1996-09-09') FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) < cast(1996-09-09 as date)) AS (1996-9-10 < 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-9-10' <= date('1996-09-09') FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) <= cast(1996-09-09 as date)) AS (1996-9-10 <= 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-9-10' <> date('1996-09-09') FROM t
-- !query analysis
Project [NOT (cast(1996-9-10 as date) = cast(1996-09-09 as date)) AS (NOT (1996-9-10 = 1996-09-09))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = date('1996-09-09') FROM t
-- !query analysis
Project [(cast(cast(null as string) as date) = cast(1996-09-09 as date)) AS (CAST(NULL AS STRING) = 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string)> date('1996-09-09') FROM t
-- !query analysis
Project [(cast(cast(null as string) as date) > cast(1996-09-09 as date)) AS (CAST(NULL AS STRING) > 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string)>= date('1996-09-09') FROM t
-- !query analysis
Project [(cast(cast(null as string) as date) >= cast(1996-09-09 as date)) AS (CAST(NULL AS STRING) >= 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string)< date('1996-09-09') FROM t
-- !query analysis
Project [(cast(cast(null as string) as date) < cast(1996-09-09 as date)) AS (CAST(NULL AS STRING) < 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string)<= date('1996-09-09') FROM t
-- !query analysis
Project [(cast(cast(null as string) as date) <= cast(1996-09-09 as date)) AS (CAST(NULL AS STRING) <= 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string)<> date('1996-09-09') FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as date) = cast(1996-09-09 as date)) AS (NOT (CAST(NULL AS STRING) = 1996-09-09))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-09-09') = '1996-09-09' FROM t
-- !query analysis
Project [(cast(1996-09-09 as date) = cast(1996-09-09 as date)) AS (1996-09-09 = 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-9-10') > '1996-09-09' FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) > cast(1996-09-09 as date)) AS (1996-9-10 > 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-9-10') >= '1996-09-09' FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) >= cast(1996-09-09 as date)) AS (1996-9-10 >= 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-9-10') < '1996-09-09' FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) < cast(1996-09-09 as date)) AS (1996-9-10 < 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-9-10') <= '1996-09-09' FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) <= cast(1996-09-09 as date)) AS (1996-9-10 <= 1996-09-09)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-9-10') <> '1996-09-09' FROM t
-- !query analysis
Project [NOT (cast(1996-9-10 as date) = cast(1996-09-09 as date)) AS (NOT (1996-9-10 = 1996-09-09))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-09-09') = cast(null as string) FROM t
-- !query analysis
Project [(cast(1996-09-09 as date) = cast(cast(null as string) as date)) AS (1996-09-09 = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-9-10') > cast(null as string) FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) > cast(cast(null as string) as date)) AS (1996-9-10 > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-9-10') >= cast(null as string) FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) >= cast(cast(null as string) as date)) AS (1996-9-10 >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-9-10') < cast(null as string) FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) < cast(cast(null as string) as date)) AS (1996-9-10 < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-9-10') <= cast(null as string) FROM t
-- !query analysis
Project [(cast(1996-9-10 as date) <= cast(cast(null as string) as date)) AS (1996-9-10 <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT date('1996-9-10') <> cast(null as string) FROM t
-- !query analysis
Project [NOT (cast(1996-9-10 as date) = cast(cast(null as string) as date)) AS (NOT (1996-9-10 = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-09-09 12:12:12.4' = timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.4 as timestamp) = cast(1996-09-09 12:12:12.4 as timestamp)) AS (1996-09-09 12:12:12.4 = 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-09-09 12:12:12.5' > timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) > cast(1996-09-09 12:12:12.4 as timestamp)) AS (1996-09-09 12:12:12.5 > 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-09-09 12:12:12.5' >= timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) >= cast(1996-09-09 12:12:12.4 as timestamp)) AS (1996-09-09 12:12:12.5 >= 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-09-09 12:12:12.5' < timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) < cast(1996-09-09 12:12:12.4 as timestamp)) AS (1996-09-09 12:12:12.5 < 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-09-09 12:12:12.5' <= timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) <= cast(1996-09-09 12:12:12.4 as timestamp)) AS (1996-09-09 12:12:12.5 <= 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1996-09-09 12:12:12.5' <> timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [NOT (cast(1996-09-09 12:12:12.5 as timestamp) = cast(1996-09-09 12:12:12.4 as timestamp)) AS (NOT (1996-09-09 12:12:12.5 = 1996-09-09 12:12:12.4))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [(cast(cast(null as string) as timestamp) = cast(1996-09-09 12:12:12.4 as timestamp)) AS (CAST(NULL AS STRING) = 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) > timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [(cast(cast(null as string) as timestamp) > cast(1996-09-09 12:12:12.4 as timestamp)) AS (CAST(NULL AS STRING) > 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) >= timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [(cast(cast(null as string) as timestamp) >= cast(1996-09-09 12:12:12.4 as timestamp)) AS (CAST(NULL AS STRING) >= 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) < timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [(cast(cast(null as string) as timestamp) < cast(1996-09-09 12:12:12.4 as timestamp)) AS (CAST(NULL AS STRING) < 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <= timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [(cast(cast(null as string) as timestamp) <= cast(1996-09-09 12:12:12.4 as timestamp)) AS (CAST(NULL AS STRING) <= 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <> timestamp('1996-09-09 12:12:12.4') FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as timestamp) = cast(1996-09-09 12:12:12.4 as timestamp)) AS (NOT (CAST(NULL AS STRING) = 1996-09-09 12:12:12.4))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.4' )= '1996-09-09 12:12:12.4' FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.4 as timestamp) = cast(1996-09-09 12:12:12.4 as timestamp)) AS (1996-09-09 12:12:12.4 = 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.5' )> '1996-09-09 12:12:12.4' FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) > cast(1996-09-09 12:12:12.4 as timestamp)) AS (1996-09-09 12:12:12.5 > 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.5' )>= '1996-09-09 12:12:12.4' FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) >= cast(1996-09-09 12:12:12.4 as timestamp)) AS (1996-09-09 12:12:12.5 >= 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.5' )< '1996-09-09 12:12:12.4' FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) < cast(1996-09-09 12:12:12.4 as timestamp)) AS (1996-09-09 12:12:12.5 < 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.5' )<= '1996-09-09 12:12:12.4' FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) <= cast(1996-09-09 12:12:12.4 as timestamp)) AS (1996-09-09 12:12:12.5 <= 1996-09-09 12:12:12.4)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.5' )<> '1996-09-09 12:12:12.4' FROM t
-- !query analysis
Project [NOT (cast(1996-09-09 12:12:12.5 as timestamp) = cast(1996-09-09 12:12:12.4 as timestamp)) AS (NOT (1996-09-09 12:12:12.5 = 1996-09-09 12:12:12.4))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.4' )= cast(null as string) FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.4 as timestamp) = cast(cast(null as string) as timestamp)) AS (1996-09-09 12:12:12.4 = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.5' )> cast(null as string) FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) > cast(cast(null as string) as timestamp)) AS (1996-09-09 12:12:12.5 > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.5' )>= cast(null as string) FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) >= cast(cast(null as string) as timestamp)) AS (1996-09-09 12:12:12.5 >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.5' )< cast(null as string) FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) < cast(cast(null as string) as timestamp)) AS (1996-09-09 12:12:12.5 < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.5' )<= cast(null as string) FROM t
-- !query analysis
Project [(cast(1996-09-09 12:12:12.5 as timestamp) <= cast(cast(null as string) as timestamp)) AS (1996-09-09 12:12:12.5 <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT timestamp('1996-09-09 12:12:12.5' )<> cast(null as string) FROM t
-- !query analysis
Project [NOT (cast(1996-09-09 12:12:12.5 as timestamp) = cast(cast(null as string) as timestamp)) AS (NOT (1996-09-09 12:12:12.5 = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT ' ' = X'0020' FROM t
-- !query analysis
Project [(cast(  as binary) = 0x0020) AS (  = X'0020')#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT ' ' > X'001F' FROM t
-- !query analysis
Project [(cast(  as binary) > 0x001F) AS (  > X'001F')#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT ' ' >= X'001F' FROM t
-- !query analysis
Project [(cast(  as binary) >= 0x001F) AS (  >= X'001F')#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT ' ' < X'001F' FROM t
-- !query analysis
Project [(cast(  as binary) < 0x001F) AS (  < X'001F')#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT ' ' <= X'001F' FROM t
-- !query analysis
Project [(cast(  as binary) <= 0x001F) AS (  <= X'001F')#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT ' ' <> X'001F' FROM t
-- !query analysis
Project [NOT (cast(  as binary) = 0x001F) AS (NOT (  = X'001F'))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) = X'0020' FROM t
-- !query analysis
Project [(cast(cast(null as string) as binary) = 0x0020) AS (CAST(NULL AS STRING) = X'0020')#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) > X'001F' FROM t
-- !query analysis
Project [(cast(cast(null as string) as binary) > 0x001F) AS (CAST(NULL AS STRING) > X'001F')#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) >= X'001F' FROM t
-- !query analysis
Project [(cast(cast(null as string) as binary) >= 0x001F) AS (CAST(NULL AS STRING) >= X'001F')#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) < X'001F' FROM t
-- !query analysis
Project [(cast(cast(null as string) as binary) < 0x001F) AS (CAST(NULL AS STRING) < X'001F')#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <= X'001F' FROM t
-- !query analysis
Project [(cast(cast(null as string) as binary) <= 0x001F) AS (CAST(NULL AS STRING) <= X'001F')#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(null as string) <> X'001F' FROM t
-- !query analysis
Project [NOT (cast(cast(null as string) as binary) = 0x001F) AS (NOT (CAST(NULL AS STRING) = X'001F'))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'0020' = ' ' FROM t
-- !query analysis
Project [(0x0020 = cast(  as binary)) AS (X'0020' =  )#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'001F' > ' ' FROM t
-- !query analysis
Project [(0x001F > cast(  as binary)) AS (X'001F' >  )#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'001F' >= ' ' FROM t
-- !query analysis
Project [(0x001F >= cast(  as binary)) AS (X'001F' >=  )#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'001F' < ' ' FROM t
-- !query analysis
Project [(0x001F < cast(  as binary)) AS (X'001F' <  )#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'001F' <= ' ' FROM t
-- !query analysis
Project [(0x001F <= cast(  as binary)) AS (X'001F' <=  )#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'001F' <> ' ' FROM t
-- !query analysis
Project [NOT (0x001F = cast(  as binary)) AS (NOT (X'001F' =  ))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'0020' = cast(null as string) FROM t
-- !query analysis
Project [(0x0020 = cast(cast(null as string) as binary)) AS (X'0020' = CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'001F' > cast(null as string) FROM t
-- !query analysis
Project [(0x001F > cast(cast(null as string) as binary)) AS (X'001F' > CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'001F' >= cast(null as string) FROM t
-- !query analysis
Project [(0x001F >= cast(cast(null as string) as binary)) AS (X'001F' >= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'001F' < cast(null as string) FROM t
-- !query analysis
Project [(0x001F < cast(cast(null as string) as binary)) AS (X'001F' < CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'001F' <= cast(null as string) FROM t
-- !query analysis
Project [(0x001F <= cast(cast(null as string) as binary)) AS (X'001F' <= CAST(NULL AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT X'001F' <> cast(null as string) FROM t
-- !query analysis
Project [NOT (0x001F = cast(cast(null as string) as binary)) AS (NOT (X'001F' = CAST(NULL AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation
