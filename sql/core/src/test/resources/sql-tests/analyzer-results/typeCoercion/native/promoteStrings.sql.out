-- Automatically generated by SQLQueryTestSuite
-- !query
CREATE TEMPORARY VIEW t AS SELECT 1
-- !query analysis
CreateViewCommand `t`, SELECT 1, false, false, LocalTempView, true
   +- Project [1 AS 1#x]
      +- OneRowRelation


-- !query
SELECT '1' + cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as double) + cast(cast(1 as tinyint) as double)) AS (1 + CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' + cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as double) + cast(cast(1 as smallint) as double)) AS (1 + CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' + cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as double) + cast(cast(1 as int) as double)) AS (1 + CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' + cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as double) + cast(cast(1 as bigint) as double)) AS (1 + CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' + cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as double) + cast(cast(1 as float) as double)) AS (1 + CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' + cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) + cast(1 as double)) AS (1 + CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' + cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) + cast(cast(1 as decimal(10,0)) as double)) AS (1 + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' + '1'                                        FROM t
-- !query analysis
Project [(cast(1 as double) + cast(1 as double)) AS (1 + 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' + cast('1' as binary)                        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(1 + CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 32,
    "fragment" : "'1' + cast('1' as binary)"
  } ]
}


-- !query
SELECT '1' + cast(1 as boolean)                         FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(1 + CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 31,
    "fragment" : "'1' + cast(1 as boolean)"
  } ]
}


-- !query
SELECT '1' + cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(1 + CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "'1' + cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT '1' + cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"1\"",
    "inputType" : "\"DOUBLE\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 48,
    "fragment" : "'1' + cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT '1' - cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as double) - cast(cast(1 as tinyint) as double)) AS (1 - CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' - cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as double) - cast(cast(1 as smallint) as double)) AS (1 - CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' - cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as double) - cast(cast(1 as int) as double)) AS (1 - CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' - cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as double) - cast(cast(1 as bigint) as double)) AS (1 - CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' - cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as double) - cast(cast(1 as float) as double)) AS (1 - CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' - cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) - cast(1 as double)) AS (1 - CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' - cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) - cast(cast(1 as decimal(10,0)) as double)) AS (1 - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' - '1'                                        FROM t
-- !query analysis
Project [(cast(1 as double) - cast(1 as double)) AS (1 - 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' - cast('1' as binary)                        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(1 - CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 32,
    "fragment" : "'1' - cast('1' as binary)"
  } ]
}


-- !query
SELECT '1' - cast(1 as boolean)                         FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(1 - CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 31,
    "fragment" : "'1' - cast(1 as boolean)"
  } ]
}


-- !query
SELECT '1' - cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"1\"",
    "inputType" : "\"STRING\"",
    "paramIndex" : "1",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(1 - CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "'1' - cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT '1' - cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
Project [(cast(1 as date) - cast(2017-12-11 09:30:00 as date)) AS (1 - CAST(2017-12-11 09:30:00 AS DATE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' * cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as double) * cast(cast(1 as tinyint) as double)) AS (1 * CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' * cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as double) * cast(cast(1 as smallint) as double)) AS (1 * CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' * cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as double) * cast(cast(1 as int) as double)) AS (1 * CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' * cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as double) * cast(cast(1 as bigint) as double)) AS (1 * CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' * cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as double) * cast(cast(1 as float) as double)) AS (1 * CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' * cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) * cast(1 as double)) AS (1 * CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' * cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) * cast(cast(1 as decimal(10,0)) as double)) AS (1 * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' * '1'                                        FROM t
-- !query analysis
Project [(cast(1 as double) * cast(1 as double)) AS (1 * 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' * cast('1' as binary)                        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(1 * CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 32,
    "fragment" : "'1' * cast('1' as binary)"
  } ]
}


-- !query
SELECT '1' * cast(1 as boolean)                         FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(1 * CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 31,
    "fragment" : "'1' * cast(1 as boolean)"
  } ]
}


-- !query
SELECT '1' * cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(1 * CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "'1' * cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT '1' * cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(1 * CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 48,
    "fragment" : "'1' * cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT '1' / cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as double) / cast(cast(1 as tinyint) as double)) AS (1 / CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' / cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as double) / cast(cast(1 as smallint) as double)) AS (1 / CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' / cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as double) / cast(cast(1 as int) as double)) AS (1 / CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' / cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as double) / cast(cast(1 as bigint) as double)) AS (1 / CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' / cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as double) / cast(cast(1 as float) as double)) AS (1 / CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' / cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) / cast(1 as double)) AS (1 / CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' / cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) / cast(cast(1 as decimal(10,0)) as double)) AS (1 / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' / '1'                                        FROM t
-- !query analysis
Project [(cast(1 as double) / cast(1 as double)) AS (1 / 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' / cast('1' as binary)                        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(1 / CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 32,
    "fragment" : "'1' / cast('1' as binary)"
  } ]
}


-- !query
SELECT '1' / cast(1 as boolean)                         FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(1 / CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 31,
    "fragment" : "'1' / cast(1 as boolean)"
  } ]
}


-- !query
SELECT '1' / cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(1 / CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "'1' / cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT '1' / cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(1 / CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 48,
    "fragment" : "'1' / cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT '1' % cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as double) % cast(cast(1 as tinyint) as double)) AS (1 % CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' % cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as double) % cast(cast(1 as smallint) as double)) AS (1 % CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' % cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as double) % cast(cast(1 as int) as double)) AS (1 % CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' % cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as double) % cast(cast(1 as bigint) as double)) AS (1 % CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' % cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as double) % cast(cast(1 as float) as double)) AS (1 % CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' % cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) % cast(1 as double)) AS (1 % CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' % cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) % cast(cast(1 as decimal(10,0)) as double)) AS (1 % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' % '1'                                        FROM t
-- !query analysis
Project [(cast(1 as double) % cast(1 as double)) AS (1 % 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' % cast('1' as binary)                        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(1 % CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 32,
    "fragment" : "'1' % cast('1' as binary)"
  } ]
}


-- !query
SELECT '1' % cast(1 as boolean)                         FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(1 % CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 31,
    "fragment" : "'1' % cast(1 as boolean)"
  } ]
}


-- !query
SELECT '1' % cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(1 % CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "'1' % cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT '1' % cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(1 % CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 48,
    "fragment" : "'1' % cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT pmod('1', cast(1 as tinyint))                         FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(cast(1 as tinyint) as double)) AS pmod(1, CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod('1', cast(1 as smallint))                        FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(cast(1 as smallint) as double)) AS pmod(1, CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod('1', cast(1 as int))                             FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(cast(1 as int) as double)) AS pmod(1, CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod('1', cast(1 as bigint))                          FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(cast(1 as bigint) as double)) AS pmod(1, CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod('1', cast(1 as float))                           FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(cast(1 as float) as double)) AS pmod(1, CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod('1', cast(1 as double))                          FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(1 as double)) AS pmod(1, CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod('1', cast(1 as decimal(10, 0)))                  FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(cast(1 as decimal(10,0)) as double)) AS pmod(1, CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod('1', '1')                                        FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(1 as double)) AS pmod(1, 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod('1', cast('1' as binary))                        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"pmod(1, CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 37,
    "fragment" : "pmod('1', cast('1' as binary))"
  } ]
}


-- !query
SELECT pmod('1', cast(1 as boolean))                         FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"pmod(1, CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 36,
    "fragment" : "pmod('1', cast(1 as boolean))"
  } ]
}


-- !query
SELECT pmod('1', cast('2017-12-11 09:30:00.0' as timestamp)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"pmod(1, CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 60,
    "fragment" : "pmod('1', cast('2017-12-11 09:30:00.0' as timestamp))"
  } ]
}


-- !query
SELECT pmod('1', cast('2017-12-11 09:30:00' as date))        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"pmod(1, CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "pmod('1', cast('2017-12-11 09:30:00' as date))"
  } ]
}


-- !query
SELECT cast(1 as tinyint)                         + '1' FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as double) + cast(1 as double)) AS (CAST(1 AS TINYINT) + 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        + '1' FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as double) + cast(1 as double)) AS (CAST(1 AS SMALLINT) + 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             + '1' FROM t
-- !query analysis
Project [(cast(cast(1 as int) as double) + cast(1 as double)) AS (CAST(1 AS INT) + 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          + '1' FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as double) + cast(1 as double)) AS (CAST(1 AS BIGINT) + 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           + '1' FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) + cast(1 as double)) AS (CAST(1 AS FLOAT) + 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          + '1' FROM t
-- !query analysis
Project [(cast(1 as double) + cast(1 as double)) AS (CAST(1 AS DOUBLE) + 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  + '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) + cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) + 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        + '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) + 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary)                        + '1'"
  } ]
}


-- !query
SELECT cast(1 as boolean)                         + '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BOOLEAN\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(1 AS BOOLEAN) + 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as boolean)                         + '1'"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) + '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) + 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) + '1'"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        + '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"1\"",
    "inputType" : "\"DOUBLE\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('2017-12-11 09:30:00' as date)        + '1'"
  } ]
}


-- !query
SELECT cast(1 as tinyint)                         - '1' FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as double) - cast(1 as double)) AS (CAST(1 AS TINYINT) - 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        - '1' FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as double) - cast(1 as double)) AS (CAST(1 AS SMALLINT) - 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             - '1' FROM t
-- !query analysis
Project [(cast(cast(1 as int) as double) - cast(1 as double)) AS (CAST(1 AS INT) - 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          - '1' FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as double) - cast(1 as double)) AS (CAST(1 AS BIGINT) - 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           - '1' FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) - cast(1 as double)) AS (CAST(1 AS FLOAT) - 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          - '1' FROM t
-- !query analysis
Project [(cast(1 as double) - cast(1 as double)) AS (CAST(1 AS DOUBLE) - 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  - '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) - cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) - 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        - '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) - 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary)                        - '1'"
  } ]
}


-- !query
SELECT cast(1 as boolean)                         - '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BOOLEAN\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(1 AS BOOLEAN) - 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as boolean)                         - '1'"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) - '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"1\"",
    "inputType" : "\"STRING\"",
    "paramIndex" : "2",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) - 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) - '1'"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        - '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"1\"",
    "inputType" : "\"DOUBLE\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_sub(CAST(2017-12-11 09:30:00 AS DATE), 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('2017-12-11 09:30:00' as date)        - '1'"
  } ]
}


-- !query
SELECT cast(1 as tinyint)                         * '1' FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as double) * cast(1 as double)) AS (CAST(1 AS TINYINT) * 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        * '1' FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as double) * cast(1 as double)) AS (CAST(1 AS SMALLINT) * 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             * '1' FROM t
-- !query analysis
Project [(cast(cast(1 as int) as double) * cast(1 as double)) AS (CAST(1 AS INT) * 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          * '1' FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as double) * cast(1 as double)) AS (CAST(1 AS BIGINT) * 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           * '1' FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) * cast(1 as double)) AS (CAST(1 AS FLOAT) * 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          * '1' FROM t
-- !query analysis
Project [(cast(1 as double) * cast(1 as double)) AS (CAST(1 AS DOUBLE) * 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  * '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) * cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) * 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        * '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) * 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary)                        * '1'"
  } ]
}


-- !query
SELECT cast(1 as boolean)                         * '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BOOLEAN\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(1 AS BOOLEAN) * 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as boolean)                         * '1'"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) * '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) * 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) * '1'"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        * '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) * 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('2017-12-11 09:30:00' as date)        * '1'"
  } ]
}


-- !query
SELECT cast(1 as tinyint)                         / '1' FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as double) / cast(cast(1 as double) as double)) AS (CAST(1 AS TINYINT) / 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        / '1' FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as double) / cast(cast(1 as double) as double)) AS (CAST(1 AS SMALLINT) / 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             / '1' FROM t
-- !query analysis
Project [(cast(cast(1 as int) as double) / cast(cast(1 as double) as double)) AS (CAST(1 AS INT) / 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          / '1' FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as double) / cast(cast(1 as double) as double)) AS (CAST(1 AS BIGINT) / 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           / '1' FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) / cast(cast(1 as double) as double)) AS (CAST(1 AS FLOAT) / 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          / '1' FROM t
-- !query analysis
Project [(cast(1 as double) / cast(1 as double)) AS (CAST(1 AS DOUBLE) / 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  / '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) / cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) / 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        / '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) / 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary)                        / '1'"
  } ]
}


-- !query
SELECT cast(1 as boolean)                         / '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BOOLEAN\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(1 AS BOOLEAN) / 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as boolean)                         / '1'"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) / '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) / 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) / '1'"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        / '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) / 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('2017-12-11 09:30:00' as date)        / '1'"
  } ]
}


-- !query
SELECT cast(1 as tinyint)                         % '1' FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as double) % cast(1 as double)) AS (CAST(1 AS TINYINT) % 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        % '1' FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as double) % cast(1 as double)) AS (CAST(1 AS SMALLINT) % 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             % '1' FROM t
-- !query analysis
Project [(cast(cast(1 as int) as double) % cast(1 as double)) AS (CAST(1 AS INT) % 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          % '1' FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as double) % cast(1 as double)) AS (CAST(1 AS BIGINT) % 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           % '1' FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) % cast(1 as double)) AS (CAST(1 AS FLOAT) % 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          % '1' FROM t
-- !query analysis
Project [(cast(1 as double) % cast(1 as double)) AS (CAST(1 AS DOUBLE) % 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  % '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) % cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) % 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        % '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) % 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary)                        % '1'"
  } ]
}


-- !query
SELECT cast(1 as boolean)                         % '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BOOLEAN\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(1 AS BOOLEAN) % 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as boolean)                         % '1'"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) % '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) % 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) % '1'"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        % '1' FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) % 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('2017-12-11 09:30:00' as date)        % '1'"
  } ]
}


-- !query
SELECT pmod(cast(1 as tinyint), '1')                         FROM t
-- !query analysis
Project [pmod(cast(cast(1 as tinyint) as double), cast(1 as double)) AS pmod(CAST(1 AS TINYINT), 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as smallint), '1')                        FROM t
-- !query analysis
Project [pmod(cast(cast(1 as smallint) as double), cast(1 as double)) AS pmod(CAST(1 AS SMALLINT), 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as int), '1')                             FROM t
-- !query analysis
Project [pmod(cast(cast(1 as int) as double), cast(1 as double)) AS pmod(CAST(1 AS INT), 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as bigint), '1')                          FROM t
-- !query analysis
Project [pmod(cast(cast(1 as bigint) as double), cast(1 as double)) AS pmod(CAST(1 AS BIGINT), 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as float), '1')                           FROM t
-- !query analysis
Project [pmod(cast(cast(1 as float) as double), cast(1 as double)) AS pmod(CAST(1 AS FLOAT), 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as double), '1')                          FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(1 as double)) AS pmod(CAST(1 AS DOUBLE), 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), '1')                  FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(10,0)) as double), cast(1 as double)) AS pmod(CAST(1 AS DECIMAL(10,0)), 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast('1' as binary), '1')                        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"pmod(CAST(1 AS BINARY), 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 37,
    "fragment" : "pmod(cast('1' as binary), '1')"
  } ]
}


-- !query
SELECT pmod(cast(1 as boolean), '1')                         FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BOOLEAN\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"pmod(CAST(1 AS BOOLEAN), 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 36,
    "fragment" : "pmod(cast(1 as boolean), '1')"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00.0' as timestamp), '1') FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP), 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 60,
    "fragment" : "pmod(cast('2017-12-11 09:30:00.0' as timestamp), '1')"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00' as date), '1')        FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00 AS DATE), 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "pmod(cast('2017-12-11 09:30:00' as date), '1')"
  } ]
}


-- !query
SELECT '1' = cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as tinyint) = cast(1 as tinyint)) AS (1 = CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as smallint) = cast(1 as smallint)) AS (1 = CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as int) = cast(1 as int)) AS (1 = CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as bigint) = cast(1 as bigint)) AS (1 = CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as float) = cast(1 as float)) AS (1 = CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) = cast(1 as double)) AS (1 = CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) = cast(cast(1 as decimal(10,0)) as double)) AS (1 = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = '1'                                        FROM t
-- !query analysis
Project [(1 = 1) AS (1 = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast('1' as binary)                        FROM t
-- !query analysis
Project [(cast(1 as binary) = cast(1 as binary)) AS (1 = CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast(1 as boolean)                         FROM t
-- !query analysis
Project [(cast(1 as boolean) = cast(1 as boolean)) AS (1 = CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
Project [(cast(1 as timestamp) = cast(2017-12-11 09:30:00.0 as timestamp)) AS (1 = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' = cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
Project [(cast(1 as date) = cast(2017-12-11 09:30:00 as date)) AS (1 = CAST(2017-12-11 09:30:00 AS DATE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint)                         = '1' FROM t
-- !query analysis
Project [(cast(1 as tinyint) = cast(1 as tinyint)) AS (CAST(1 AS TINYINT) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        = '1' FROM t
-- !query analysis
Project [(cast(1 as smallint) = cast(1 as smallint)) AS (CAST(1 AS SMALLINT) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             = '1' FROM t
-- !query analysis
Project [(cast(1 as int) = cast(1 as int)) AS (CAST(1 AS INT) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          = '1' FROM t
-- !query analysis
Project [(cast(1 as bigint) = cast(1 as bigint)) AS (CAST(1 AS BIGINT) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           = '1' FROM t
-- !query analysis
Project [(cast(1 as float) = cast(1 as float)) AS (CAST(1 AS FLOAT) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          = '1' FROM t
-- !query analysis
Project [(cast(1 as double) = cast(1 as double)) AS (CAST(1 AS DOUBLE) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  = '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) = cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        = '1' FROM t
-- !query analysis
Project [(cast(1 as binary) = cast(1 as binary)) AS (CAST(1 AS BINARY) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as boolean)                         = '1' FROM t
-- !query analysis
Project [(cast(1 as boolean) = cast(1 as boolean)) AS (CAST(1 AS BOOLEAN) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) = '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00.0 as timestamp) = cast(1 as timestamp)) AS (CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        = '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00 as date) = cast(1 as date)) AS (CAST(2017-12-11 09:30:00 AS DATE) = 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as tinyint) <=> cast(1 as tinyint)) AS (1 <=> CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as smallint) <=> cast(1 as smallint)) AS (1 <=> CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as int) <=> cast(1 as int)) AS (1 <=> CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as bigint) <=> cast(1 as bigint)) AS (1 <=> CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as float) <=> cast(1 as float)) AS (1 <=> CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) <=> cast(1 as double)) AS (1 <=> CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) <=> cast(cast(1 as decimal(10,0)) as double)) AS (1 <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> '1'                                        FROM t
-- !query analysis
Project [(1 <=> 1) AS (1 <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast('1' as binary)                        FROM t
-- !query analysis
Project [(cast(1 as binary) <=> cast(1 as binary)) AS (1 <=> CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast(1 as boolean)                         FROM t
-- !query analysis
Project [(cast(1 as boolean) <=> cast(1 as boolean)) AS (1 <=> CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
Project [(cast(1 as timestamp) <=> cast(2017-12-11 09:30:00.0 as timestamp)) AS (1 <=> CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <=> cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
Project [(cast(1 as date) <=> cast(2017-12-11 09:30:00 as date)) AS (1 <=> CAST(2017-12-11 09:30:00 AS DATE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint)                         <=> '1' FROM t
-- !query analysis
Project [(cast(1 as tinyint) <=> cast(1 as tinyint)) AS (CAST(1 AS TINYINT) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        <=> '1' FROM t
-- !query analysis
Project [(cast(1 as smallint) <=> cast(1 as smallint)) AS (CAST(1 AS SMALLINT) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             <=> '1' FROM t
-- !query analysis
Project [(cast(1 as int) <=> cast(1 as int)) AS (CAST(1 AS INT) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          <=> '1' FROM t
-- !query analysis
Project [(cast(1 as bigint) <=> cast(1 as bigint)) AS (CAST(1 AS BIGINT) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           <=> '1' FROM t
-- !query analysis
Project [(cast(1 as float) <=> cast(1 as float)) AS (CAST(1 AS FLOAT) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          <=> '1' FROM t
-- !query analysis
Project [(cast(1 as double) <=> cast(1 as double)) AS (CAST(1 AS DOUBLE) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  <=> '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) <=> cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        <=> '1' FROM t
-- !query analysis
Project [(cast(1 as binary) <=> cast(1 as binary)) AS (CAST(1 AS BINARY) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as boolean)                         <=> '1' FROM t
-- !query analysis
Project [(cast(1 as boolean) <=> cast(1 as boolean)) AS (CAST(1 AS BOOLEAN) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <=> '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00.0 as timestamp) <=> cast(1 as timestamp)) AS (CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        <=> '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00 as date) <=> cast(1 as date)) AS (CAST(2017-12-11 09:30:00 AS DATE) <=> 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as tinyint) < cast(1 as tinyint)) AS (1 < CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as smallint) < cast(1 as smallint)) AS (1 < CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as int) < cast(1 as int)) AS (1 < CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as bigint) < cast(1 as bigint)) AS (1 < CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as float) < cast(1 as float)) AS (1 < CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) < cast(1 as double)) AS (1 < CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) < cast(cast(1 as decimal(10,0)) as double)) AS (1 < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < '1'                                        FROM t
-- !query analysis
Project [(1 < 1) AS (1 < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast('1' as binary)                        FROM t
-- !query analysis
Project [(cast(1 as binary) < cast(1 as binary)) AS (1 < CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast(1 as boolean)                         FROM t
-- !query analysis
Project [(cast(1 as boolean) < cast(1 as boolean)) AS (1 < CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
Project [(cast(1 as timestamp) < cast(2017-12-11 09:30:00.0 as timestamp)) AS (1 < CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' < cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
Project [(cast(1 as date) < cast(2017-12-11 09:30:00 as date)) AS (1 < CAST(2017-12-11 09:30:00 AS DATE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as tinyint) <= cast(1 as tinyint)) AS (1 <= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as smallint) <= cast(1 as smallint)) AS (1 <= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as int) <= cast(1 as int)) AS (1 <= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as bigint) <= cast(1 as bigint)) AS (1 <= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as float) <= cast(1 as float)) AS (1 <= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) <= cast(1 as double)) AS (1 <= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) <= cast(cast(1 as decimal(10,0)) as double)) AS (1 <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= '1'                                        FROM t
-- !query analysis
Project [(1 <= 1) AS (1 <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast('1' as binary)                        FROM t
-- !query analysis
Project [(cast(1 as binary) <= cast(1 as binary)) AS (1 <= CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast(1 as boolean)                         FROM t
-- !query analysis
Project [(cast(1 as boolean) <= cast(1 as boolean)) AS (1 <= CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
Project [(cast(1 as timestamp) <= cast(2017-12-11 09:30:00.0 as timestamp)) AS (1 <= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <= cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
Project [(cast(1 as date) <= cast(2017-12-11 09:30:00 as date)) AS (1 <= CAST(2017-12-11 09:30:00 AS DATE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as tinyint) > cast(1 as tinyint)) AS (1 > CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as smallint) > cast(1 as smallint)) AS (1 > CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as int) > cast(1 as int)) AS (1 > CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as bigint) > cast(1 as bigint)) AS (1 > CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as float) > cast(1 as float)) AS (1 > CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) > cast(1 as double)) AS (1 > CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) > cast(cast(1 as decimal(10,0)) as double)) AS (1 > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > '1'                                        FROM t
-- !query analysis
Project [(1 > 1) AS (1 > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast('1' as binary)                        FROM t
-- !query analysis
Project [(cast(1 as binary) > cast(1 as binary)) AS (1 > CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast(1 as boolean)                         FROM t
-- !query analysis
Project [(cast(1 as boolean) > cast(1 as boolean)) AS (1 > CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
Project [(cast(1 as timestamp) > cast(2017-12-11 09:30:00.0 as timestamp)) AS (1 > CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' > cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
Project [(cast(1 as date) > cast(2017-12-11 09:30:00 as date)) AS (1 > CAST(2017-12-11 09:30:00 AS DATE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast(1 as tinyint)                         FROM t
-- !query analysis
Project [(cast(1 as tinyint) >= cast(1 as tinyint)) AS (1 >= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast(1 as smallint)                        FROM t
-- !query analysis
Project [(cast(1 as smallint) >= cast(1 as smallint)) AS (1 >= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast(1 as int)                             FROM t
-- !query analysis
Project [(cast(1 as int) >= cast(1 as int)) AS (1 >= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast(1 as bigint)                          FROM t
-- !query analysis
Project [(cast(1 as bigint) >= cast(1 as bigint)) AS (1 >= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast(1 as float)                           FROM t
-- !query analysis
Project [(cast(1 as float) >= cast(1 as float)) AS (1 >= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast(1 as double)                          FROM t
-- !query analysis
Project [(cast(1 as double) >= cast(1 as double)) AS (1 >= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [(cast(1 as double) >= cast(cast(1 as decimal(10,0)) as double)) AS (1 >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= '1'                                        FROM t
-- !query analysis
Project [(1 >= 1) AS (1 >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast('1' as binary)                        FROM t
-- !query analysis
Project [(cast(1 as binary) >= cast(1 as binary)) AS (1 >= CAST(1 AS BINARY))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast(1 as boolean)                         FROM t
-- !query analysis
Project [(cast(1 as boolean) >= cast(1 as boolean)) AS (1 >= CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
Project [(cast(1 as timestamp) >= cast(2017-12-11 09:30:00.0 as timestamp)) AS (1 >= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' >= cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
Project [(cast(1 as date) >= cast(2017-12-11 09:30:00 as date)) AS (1 >= CAST(2017-12-11 09:30:00 AS DATE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast(1 as tinyint)                         FROM t
-- !query analysis
Project [NOT (cast(1 as tinyint) = cast(1 as tinyint)) AS (NOT (1 = CAST(1 AS TINYINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast(1 as smallint)                        FROM t
-- !query analysis
Project [NOT (cast(1 as smallint) = cast(1 as smallint)) AS (NOT (1 = CAST(1 AS SMALLINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast(1 as int)                             FROM t
-- !query analysis
Project [NOT (cast(1 as int) = cast(1 as int)) AS (NOT (1 = CAST(1 AS INT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast(1 as bigint)                          FROM t
-- !query analysis
Project [NOT (cast(1 as bigint) = cast(1 as bigint)) AS (NOT (1 = CAST(1 AS BIGINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast(1 as float)                           FROM t
-- !query analysis
Project [NOT (cast(1 as float) = cast(1 as float)) AS (NOT (1 = CAST(1 AS FLOAT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast(1 as double)                          FROM t
-- !query analysis
Project [NOT (cast(1 as double) = cast(1 as double)) AS (NOT (1 = CAST(1 AS DOUBLE)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast(1 as decimal(10, 0))                  FROM t
-- !query analysis
Project [NOT (cast(1 as double) = cast(cast(1 as decimal(10,0)) as double)) AS (NOT (1 = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> '1'                                        FROM t
-- !query analysis
Project [NOT (1 = 1) AS (NOT (1 = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast('1' as binary)                        FROM t
-- !query analysis
Project [NOT (cast(1 as binary) = cast(1 as binary)) AS (NOT (1 = CAST(1 AS BINARY)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast(1 as boolean)                         FROM t
-- !query analysis
Project [NOT (cast(1 as boolean) = cast(1 as boolean)) AS (NOT (1 = CAST(1 AS BOOLEAN)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
Project [NOT (cast(1 as timestamp) = cast(2017-12-11 09:30:00.0 as timestamp)) AS (NOT (1 = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1' <> cast('2017-12-11 09:30:00' as date)        FROM t
-- !query analysis
Project [NOT (cast(1 as date) = cast(2017-12-11 09:30:00 as date)) AS (NOT (1 = CAST(2017-12-11 09:30:00 AS DATE)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint)                         < '1' FROM t
-- !query analysis
Project [(cast(1 as tinyint) < cast(1 as tinyint)) AS (CAST(1 AS TINYINT) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        < '1' FROM t
-- !query analysis
Project [(cast(1 as smallint) < cast(1 as smallint)) AS (CAST(1 AS SMALLINT) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             < '1' FROM t
-- !query analysis
Project [(cast(1 as int) < cast(1 as int)) AS (CAST(1 AS INT) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          < '1' FROM t
-- !query analysis
Project [(cast(1 as bigint) < cast(1 as bigint)) AS (CAST(1 AS BIGINT) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           < '1' FROM t
-- !query analysis
Project [(cast(1 as float) < cast(1 as float)) AS (CAST(1 AS FLOAT) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          < '1' FROM t
-- !query analysis
Project [(cast(1 as double) < cast(1 as double)) AS (CAST(1 AS DOUBLE) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  < '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) < cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1'                                        < '1' FROM t
-- !query analysis
Project [(1 < 1) AS (1 < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        < '1' FROM t
-- !query analysis
Project [(cast(1 as binary) < cast(1 as binary)) AS (CAST(1 AS BINARY) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as boolean)                         < '1' FROM t
-- !query analysis
Project [(cast(1 as boolean) < cast(1 as boolean)) AS (CAST(1 AS BOOLEAN) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) < '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00.0 as timestamp) < cast(1 as timestamp)) AS (CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        < '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00 as date) < cast(1 as date)) AS (CAST(2017-12-11 09:30:00 AS DATE) < 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint)                         <= '1' FROM t
-- !query analysis
Project [(cast(1 as tinyint) <= cast(1 as tinyint)) AS (CAST(1 AS TINYINT) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        <= '1' FROM t
-- !query analysis
Project [(cast(1 as smallint) <= cast(1 as smallint)) AS (CAST(1 AS SMALLINT) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             <= '1' FROM t
-- !query analysis
Project [(cast(1 as int) <= cast(1 as int)) AS (CAST(1 AS INT) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          <= '1' FROM t
-- !query analysis
Project [(cast(1 as bigint) <= cast(1 as bigint)) AS (CAST(1 AS BIGINT) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           <= '1' FROM t
-- !query analysis
Project [(cast(1 as float) <= cast(1 as float)) AS (CAST(1 AS FLOAT) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          <= '1' FROM t
-- !query analysis
Project [(cast(1 as double) <= cast(1 as double)) AS (CAST(1 AS DOUBLE) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  <= '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) <= cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1'                                        <= '1' FROM t
-- !query analysis
Project [(1 <= 1) AS (1 <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        <= '1' FROM t
-- !query analysis
Project [(cast(1 as binary) <= cast(1 as binary)) AS (CAST(1 AS BINARY) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as boolean)                         <= '1' FROM t
-- !query analysis
Project [(cast(1 as boolean) <= cast(1 as boolean)) AS (CAST(1 AS BOOLEAN) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <= '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00.0 as timestamp) <= cast(1 as timestamp)) AS (CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        <= '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00 as date) <= cast(1 as date)) AS (CAST(2017-12-11 09:30:00 AS DATE) <= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint)                         > '1' FROM t
-- !query analysis
Project [(cast(1 as tinyint) > cast(1 as tinyint)) AS (CAST(1 AS TINYINT) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        > '1' FROM t
-- !query analysis
Project [(cast(1 as smallint) > cast(1 as smallint)) AS (CAST(1 AS SMALLINT) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             > '1' FROM t
-- !query analysis
Project [(cast(1 as int) > cast(1 as int)) AS (CAST(1 AS INT) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          > '1' FROM t
-- !query analysis
Project [(cast(1 as bigint) > cast(1 as bigint)) AS (CAST(1 AS BIGINT) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           > '1' FROM t
-- !query analysis
Project [(cast(1 as float) > cast(1 as float)) AS (CAST(1 AS FLOAT) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          > '1' FROM t
-- !query analysis
Project [(cast(1 as double) > cast(1 as double)) AS (CAST(1 AS DOUBLE) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  > '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) > cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1'                                        > '1' FROM t
-- !query analysis
Project [(1 > 1) AS (1 > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        > '1' FROM t
-- !query analysis
Project [(cast(1 as binary) > cast(1 as binary)) AS (CAST(1 AS BINARY) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as boolean)                         > '1' FROM t
-- !query analysis
Project [(cast(1 as boolean) > cast(1 as boolean)) AS (CAST(1 AS BOOLEAN) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) > '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00.0 as timestamp) > cast(1 as timestamp)) AS (CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        > '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00 as date) > cast(1 as date)) AS (CAST(2017-12-11 09:30:00 AS DATE) > 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint)                         >= '1' FROM t
-- !query analysis
Project [(cast(1 as tinyint) >= cast(1 as tinyint)) AS (CAST(1 AS TINYINT) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        >= '1' FROM t
-- !query analysis
Project [(cast(1 as smallint) >= cast(1 as smallint)) AS (CAST(1 AS SMALLINT) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             >= '1' FROM t
-- !query analysis
Project [(cast(1 as int) >= cast(1 as int)) AS (CAST(1 AS INT) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          >= '1' FROM t
-- !query analysis
Project [(cast(1 as bigint) >= cast(1 as bigint)) AS (CAST(1 AS BIGINT) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           >= '1' FROM t
-- !query analysis
Project [(cast(1 as float) >= cast(1 as float)) AS (CAST(1 AS FLOAT) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          >= '1' FROM t
-- !query analysis
Project [(cast(1 as double) >= cast(1 as double)) AS (CAST(1 AS DOUBLE) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  >= '1' FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) >= cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1'                                        >= '1' FROM t
-- !query analysis
Project [(1 >= 1) AS (1 >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        >= '1' FROM t
-- !query analysis
Project [(cast(1 as binary) >= cast(1 as binary)) AS (CAST(1 AS BINARY) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as boolean)                         >= '1' FROM t
-- !query analysis
Project [(cast(1 as boolean) >= cast(1 as boolean)) AS (CAST(1 AS BOOLEAN) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) >= '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00.0 as timestamp) >= cast(1 as timestamp)) AS (CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        >= '1' FROM t
-- !query analysis
Project [(cast(2017-12-11 09:30:00 as date) >= cast(1 as date)) AS (CAST(2017-12-11 09:30:00 AS DATE) >= 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint)                         <> '1' FROM t
-- !query analysis
Project [NOT (cast(1 as tinyint) = cast(1 as tinyint)) AS (NOT (CAST(1 AS TINYINT) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint)                        <> '1' FROM t
-- !query analysis
Project [NOT (cast(1 as smallint) = cast(1 as smallint)) AS (NOT (CAST(1 AS SMALLINT) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int)                             <> '1' FROM t
-- !query analysis
Project [NOT (cast(1 as int) = cast(1 as int)) AS (NOT (CAST(1 AS INT) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint)                          <> '1' FROM t
-- !query analysis
Project [NOT (cast(1 as bigint) = cast(1 as bigint)) AS (NOT (CAST(1 AS BIGINT) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float)                           <> '1' FROM t
-- !query analysis
Project [NOT (cast(1 as float) = cast(1 as float)) AS (NOT (CAST(1 AS FLOAT) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double)                          <> '1' FROM t
-- !query analysis
Project [NOT (cast(1 as double) = cast(1 as double)) AS (NOT (CAST(1 AS DOUBLE) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0))                  <> '1' FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(10,0)) as double) = cast(1 as double)) AS (NOT (CAST(1 AS DECIMAL(10,0)) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT '1'                                        <> '1' FROM t
-- !query analysis
Project [NOT (1 = 1) AS (NOT (1 = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary)                        <> '1' FROM t
-- !query analysis
Project [NOT (cast(1 as binary) = cast(1 as binary)) AS (NOT (CAST(1 AS BINARY) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as boolean)                         <> '1' FROM t
-- !query analysis
Project [NOT (cast(1 as boolean) = cast(1 as boolean)) AS (NOT (CAST(1 AS BOOLEAN) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <> '1' FROM t
-- !query analysis
Project [NOT (cast(2017-12-11 09:30:00.0 as timestamp) = cast(1 as timestamp)) AS (NOT (CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('2017-12-11 09:30:00' as date)        <> '1' FROM t
-- !query analysis
Project [NOT (cast(2017-12-11 09:30:00 as date) = cast(1 as date)) AS (NOT (CAST(2017-12-11 09:30:00 AS DATE) = 1))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT abs('1') FROM t
-- !query analysis
Project [abs(cast(1 as double)) AS abs(1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT sum('1') FROM t
-- !query analysis
Aggregate [sum(cast(1 as double)) AS sum(1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT avg('1') FROM t
-- !query analysis
Aggregate [avg(cast(1 as double)) AS avg(1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT stddev_pop('1') FROM t
-- !query analysis
Aggregate [stddev_pop(cast(1 as double)) AS stddev_pop(1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT stddev_samp('1') FROM t
-- !query analysis
Aggregate [stddev_samp(cast(1 as double)) AS stddev_samp(1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT - '1' FROM t
-- !query analysis
Project [-cast(1 as double) AS (- 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT + '1' FROM t
-- !query analysis
Project [positive(cast(1 as double)) AS (+ 1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT var_pop('1') FROM t
-- !query analysis
Aggregate [var_pop(cast(1 as double)) AS var_pop(1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT var_samp('1') FROM t
-- !query analysis
Aggregate [var_samp(cast(1 as double)) AS var_samp(1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT skewness('1') FROM t
-- !query analysis
Aggregate [skewness(cast(1 as double)) AS skewness(1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT kurtosis('1') FROM t
-- !query analysis
Aggregate [kurtosis(cast(1 as double)) AS kurtosis(1)#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation
