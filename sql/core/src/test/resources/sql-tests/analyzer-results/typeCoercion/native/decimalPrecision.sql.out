-- Automatically generated by SQLQueryTestSuite
-- !query
CREATE TEMPORARY VIEW t AS SELECT 1
-- !query analysis
CreateViewCommand `t`, SELECT 1, false, false, LocalTempView, true
   +- Project [1 AS 1#x]
      +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) + cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) + cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) + CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) + cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) + cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) + CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) + cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) + cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) + cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) + CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) + cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) + cast(1 as decimal(3,0))) AS (CAST(1 AS SMALLINT) + CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) + cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) + cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) + CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) + cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) + cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) + cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) + CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) + cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) + cast(1 as decimal(3,0))) AS (CAST(1 AS INT) + CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) + cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) + cast(1 as decimal(5,0))) AS (CAST(1 AS INT) + CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) + cast(1 as decimal(10,0))) AS (CAST(1 AS INT) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) + cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) + cast(1 as decimal(20,0))) AS (CAST(1 AS INT) + CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) + cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) + cast(1 as decimal(3,0))) AS (CAST(1 AS BIGINT) + CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) + cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) + cast(1 as decimal(5,0))) AS (CAST(1 AS BIGINT) + CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) + cast(1 as decimal(10,0))) AS (CAST(1 AS BIGINT) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) + cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) + cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) + CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) + cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) + cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS FLOAT) + CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) + cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) + cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS FLOAT) + CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) + cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS FLOAT) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) + cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) + cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS FLOAT) + CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) + cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) + cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) + CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) + cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) + cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) + CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) + cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) + cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) + cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) + CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) + cast(1 as decimal(3,0))) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) + cast(1 as decimal(5,0))) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) + cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) + cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) + cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) + CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) + cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) + cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) + CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) + cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) + CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) + cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) + cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) + CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) + cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) + CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) + CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) + CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) + CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) + cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) + CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) + cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(5,0)) + CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) + cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) + cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(20,0)) + CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) + cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) + CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) + cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) + CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) + cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) + cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(20,0)) + CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) + cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) + CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) + cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) + CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) + cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) + cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(20,0)) + CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) + cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) + CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) + cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) + CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) + cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) + cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) + CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) + cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) + CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) + cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) + CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) + cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) + cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) + CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) + cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) + CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) + cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) + CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) + cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) + cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) + CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) + cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) + cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) + cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) + cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(20,0)) + CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) + cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) + CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) + cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) + CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) + cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) + CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) + cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) + CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  + cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  + cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  + cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  + cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) + cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) + cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) + cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) + cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  + cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  + cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) + cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) + cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  + cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) + CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  + cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  + cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) + CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  + cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) + cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) + CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) + cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) + cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) + CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) + cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  + cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  + cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  + cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  + cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) + cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) + cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) + cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) + cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) - cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) - cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) - CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) - cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) - cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) - CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) - cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) - cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) - cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) - CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) - cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) - cast(1 as decimal(3,0))) AS (CAST(1 AS SMALLINT) - CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) - cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) - cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) - CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) - cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) - cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) - cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) - CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) - cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) - cast(1 as decimal(3,0))) AS (CAST(1 AS INT) - CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) - cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) - cast(1 as decimal(5,0))) AS (CAST(1 AS INT) - CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) - cast(1 as decimal(10,0))) AS (CAST(1 AS INT) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) - cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) - cast(1 as decimal(20,0))) AS (CAST(1 AS INT) - CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) - cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) - cast(1 as decimal(3,0))) AS (CAST(1 AS BIGINT) - CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) - cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) - cast(1 as decimal(5,0))) AS (CAST(1 AS BIGINT) - CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) - cast(1 as decimal(10,0))) AS (CAST(1 AS BIGINT) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) - cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) - cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) - CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) - cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) - cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS FLOAT) - CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) - cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) - cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS FLOAT) - CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) - cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS FLOAT) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) - cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) - cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS FLOAT) - CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) - cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) - cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) - CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) - cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) - cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) - CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) - cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) - cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) - cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) - CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) - cast(1 as decimal(3,0))) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) - cast(1 as decimal(5,0))) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) - cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) - cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) - cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) - CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) - cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) - cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) - CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) - cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) - CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) - cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) - cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) - CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) - cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "2",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) - CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "2",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) - CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "2",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) - CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "2",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) - CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_sub(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_sub(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_sub(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_sub(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) - cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) - CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) - cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(5,0)) - CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) - cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) - cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(20,0)) - CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) - cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) - CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) - cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) - CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) - cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) - cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(20,0)) - CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) - cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) - CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) - cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) - CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) - cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) - cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(20,0)) - CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) - cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) - CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) - cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) - CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) - cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) - cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) - CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) - cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) - CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) - cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) - CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) - cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) - cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) - CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) - cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) - CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) - cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) - CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) - cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) - cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) - CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) - cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) - cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) - cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) - cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(20,0)) - CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) - cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) - CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) - cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) - CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) - cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) - CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) - cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) - CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  - cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  - cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  - cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  - cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) - cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) - cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) - cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) - cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  - cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  - cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) - cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) - cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  - cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) - CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  - cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  - cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) - CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  - cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) - cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) - CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) - cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) - cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) - CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) - cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  - cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) - CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  - cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  - cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) - CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  - cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) - cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) - CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) - cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) - cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) - CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) - cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) * cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) * cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) * CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) * cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) * cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) * CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) * cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) * cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) * cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) * CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) * cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) * cast(1 as decimal(3,0))) AS (CAST(1 AS SMALLINT) * CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) * cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) * cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) * CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) * cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) * cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) * cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) * CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) * cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) * cast(1 as decimal(3,0))) AS (CAST(1 AS INT) * CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) * cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) * cast(1 as decimal(5,0))) AS (CAST(1 AS INT) * CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) * cast(1 as decimal(10,0))) AS (CAST(1 AS INT) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) * cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) * cast(1 as decimal(20,0))) AS (CAST(1 AS INT) * CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) * cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) * cast(1 as decimal(3,0))) AS (CAST(1 AS BIGINT) * CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) * cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) * cast(1 as decimal(5,0))) AS (CAST(1 AS BIGINT) * CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) * cast(1 as decimal(10,0))) AS (CAST(1 AS BIGINT) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) * cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) * cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) * CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) * cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) * cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS FLOAT) * CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) * cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) * cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS FLOAT) * CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) * cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS FLOAT) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) * cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) * cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS FLOAT) * CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) * cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) * cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) * CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) * cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) * cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) * CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) * cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) * cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) * cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) * CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) * cast(1 as decimal(3,0))) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) * cast(1 as decimal(5,0))) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) * cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) * cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) * cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) * CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) * cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) * cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) * CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) * cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) * CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) * cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) * cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) * CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) * cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00.0 AS TIMESTAMP) * CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00.0 AS TIMESTAMP) * CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00.0 AS TIMESTAMP) * CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00.0 AS TIMESTAMP) * CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00 AS DATE) * CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00 AS DATE) * CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00 AS DATE) * CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00 AS DATE) * CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) * cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) * CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) * cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(5,0)) * CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) * cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) * cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(20,0)) * CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) * cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) * CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) * cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) * CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) * cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) * cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(20,0)) * CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) * cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) * CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) * cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) * CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) * cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) * cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(20,0)) * CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) * cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) * CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) * cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) * CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) * cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) * cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) * CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) * cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) * CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) * cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) * CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) * cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) * cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) * CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) * cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) * CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) * cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) * CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) * cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) * cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) * CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) * cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) * cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) * cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) * cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(20,0)) * CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) * cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) * CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) * cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) * CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) * cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) * CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) * cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) * CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  * cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  * cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  * cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  * cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) * cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) * cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) * cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) * cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  * cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  * cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) * cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) * cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  * cast('2017*12*11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) * CAST(2017*12*11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  * cast('2017*12*11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  * cast('2017*12*11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) * CAST(2017*12*11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  * cast('2017*12*11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) * cast('2017*12*11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) * CAST(2017*12*11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) * cast('2017*12*11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) * cast('2017*12*11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) * CAST(2017*12*11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) * cast('2017*12*11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  * cast('2017*12*11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) * CAST(2017*12*11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  * cast('2017*12*11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  * cast('2017*12*11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) * CAST(2017*12*11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  * cast('2017*12*11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) * cast('2017*12*11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) * CAST(2017*12*11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) * cast('2017*12*11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) * cast('2017*12*11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) * CAST(2017*12*11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) * cast('2017*12*11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) / cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) / cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) / CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) / cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) / cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) / CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) / cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) / cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) / cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) / CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) / cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) / cast(1 as decimal(3,0))) AS (CAST(1 AS SMALLINT) / CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) / cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) / cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) / CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) / cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) / cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) / cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) / CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) / cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) / cast(1 as decimal(3,0))) AS (CAST(1 AS INT) / CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) / cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) / cast(1 as decimal(5,0))) AS (CAST(1 AS INT) / CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) / cast(1 as decimal(10,0))) AS (CAST(1 AS INT) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) / cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) / cast(1 as decimal(20,0))) AS (CAST(1 AS INT) / CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) / cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) / cast(1 as decimal(3,0))) AS (CAST(1 AS BIGINT) / CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) / cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) / cast(1 as decimal(5,0))) AS (CAST(1 AS BIGINT) / CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) / cast(1 as decimal(10,0))) AS (CAST(1 AS BIGINT) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) / cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) / cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) / CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) / cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) / cast(cast(cast(1 as decimal(3,0)) as double) as double)) AS (CAST(1 AS FLOAT) / CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) / cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) / cast(cast(cast(1 as decimal(5,0)) as double) as double)) AS (CAST(1 AS FLOAT) / CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) / cast(cast(cast(1 as decimal(10,0)) as double) as double)) AS (CAST(1 AS FLOAT) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) / cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) / cast(cast(cast(1 as decimal(20,0)) as double) as double)) AS (CAST(1 AS FLOAT) / CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) / cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) / cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) / CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) / cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) / cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) / CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) / cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) / cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) / cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) / CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) / cast(1 as decimal(3,0))) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) / cast(1 as decimal(5,0))) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) / cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) / cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) / cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) / CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) / cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) / cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) / CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) / cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) / CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) / cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) / cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) / CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) / cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00.0 AS TIMESTAMP) / CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00.0 AS TIMESTAMP) / CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00.0 AS TIMESTAMP) / CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00.0 AS TIMESTAMP) / CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00 AS DATE) / CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00 AS DATE) / CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00 AS DATE) / CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00 AS DATE) / CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) / cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) / CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) / cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(5,0)) / CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) / cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) / cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(20,0)) / CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) / cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) / CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) / cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) / CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) / cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) / cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(20,0)) / CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) / cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) / CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) / cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) / CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) / cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) / cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(20,0)) / CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) / cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) / CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) / cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) / CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) / cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) / cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) / CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) / cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) / CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) / cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) / CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) / cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) / cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) / CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) / cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) / CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) / cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) / CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) / cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) / cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) / CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) / cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) / cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) / cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) / cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(20,0)) / CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) / cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) / CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) / cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) / CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) / cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) / CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) / cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) / CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  / cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  / cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  / cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  / cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) / cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) / cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) / cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) / cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  / cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  / cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) / cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) / cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  / cast('2017/12/11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) / CAST(2017/12/11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  / cast('2017/12/11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  / cast('2017/12/11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) / CAST(2017/12/11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  / cast('2017/12/11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) / cast('2017/12/11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) / CAST(2017/12/11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) / cast('2017/12/11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) / cast('2017/12/11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) / CAST(2017/12/11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) / cast('2017/12/11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  / cast('2017/12/11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) / CAST(2017/12/11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  / cast('2017/12/11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  / cast('2017/12/11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) / CAST(2017/12/11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  / cast('2017/12/11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) / cast('2017/12/11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) / CAST(2017/12/11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) / cast('2017/12/11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) / cast('2017/12/11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) / CAST(2017/12/11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) / cast('2017/12/11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) % cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) % cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) % CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) % cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) % cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) % CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) % cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) % cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) % cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) % CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) % cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) % cast(1 as decimal(3,0))) AS (CAST(1 AS SMALLINT) % CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) % cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) % cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) % CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) % cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) % cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) % cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) % CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) % cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) % cast(1 as decimal(3,0))) AS (CAST(1 AS INT) % CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) % cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) % cast(1 as decimal(5,0))) AS (CAST(1 AS INT) % CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) % cast(1 as decimal(10,0))) AS (CAST(1 AS INT) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) % cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) % cast(1 as decimal(20,0))) AS (CAST(1 AS INT) % CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) % cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) % cast(1 as decimal(3,0))) AS (CAST(1 AS BIGINT) % CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) % cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) % cast(1 as decimal(5,0))) AS (CAST(1 AS BIGINT) % CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) % cast(1 as decimal(10,0))) AS (CAST(1 AS BIGINT) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) % cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) % cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) % CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) % cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) % cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS FLOAT) % CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) % cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) % cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS FLOAT) % CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) % cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS FLOAT) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) % cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) % cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS FLOAT) % CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) % cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) % cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) % CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) % cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) % cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) % CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) % cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) % cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) % cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) % CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) % cast(1 as decimal(3,0))) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) % cast(1 as decimal(5,0))) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) % cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) % cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) % cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) % CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) % cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) % cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) % CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) % cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) % CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) % cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) % cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) % CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) % cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) % CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) % CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) % CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) % CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) % CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) % CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) % CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) % CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) % cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) % CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) % cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(5,0)) % CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) % cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) % cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(20,0)) % CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) % cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) % CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) % cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) % CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) % cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) % cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(20,0)) % CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) % cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) % CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) % cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) % CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) % cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) % cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(20,0)) % CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) % cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) % CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) % cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) % CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) % cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) % cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) % CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) % cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) % CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) % cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) % CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) % cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) % cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) % CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) % cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) % CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) % cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) % CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) % cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) % cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) % CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) % cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) % cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) % cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) % cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(20,0)) % CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) % cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) % CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) % cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) % CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) % cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) % CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) % cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) % CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  % cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  % cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  % cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  % cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) % cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) % cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) % cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) % cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  % cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  % cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) % cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) % cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  % cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) % CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  % cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  % cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) % CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  % cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) % cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) % CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) % cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) % cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) % CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) % cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  % cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) % CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  % cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  % cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) % CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  % cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) % cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) % CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) % cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) % cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) % CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) % cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT pmod(cast(1 as tinyint), cast(1 as decimal(3, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as tinyint) as decimal(3,0)), cast(1 as decimal(3,0))) AS pmod(CAST(1 AS TINYINT), CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as tinyint), cast(1 as decimal(5, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as tinyint) as decimal(3,0)), cast(1 as decimal(5,0))) AS pmod(CAST(1 AS TINYINT), CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as tinyint), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as tinyint) as decimal(3,0)), cast(1 as decimal(10,0))) AS pmod(CAST(1 AS TINYINT), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as tinyint), cast(1 as decimal(20, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as tinyint) as decimal(3,0)), cast(1 as decimal(20,0))) AS pmod(CAST(1 AS TINYINT), CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as smallint), cast(1 as decimal(3, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as smallint) as decimal(5,0)), cast(1 as decimal(3,0))) AS pmod(CAST(1 AS SMALLINT), CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as smallint), cast(1 as decimal(5, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as smallint) as decimal(5,0)), cast(1 as decimal(5,0))) AS pmod(CAST(1 AS SMALLINT), CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as smallint), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as smallint) as decimal(5,0)), cast(1 as decimal(10,0))) AS pmod(CAST(1 AS SMALLINT), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as smallint), cast(1 as decimal(20, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as smallint) as decimal(5,0)), cast(1 as decimal(20,0))) AS pmod(CAST(1 AS SMALLINT), CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as int), cast(1 as decimal(3, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as int) as decimal(10,0)), cast(1 as decimal(3,0))) AS pmod(CAST(1 AS INT), CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as int), cast(1 as decimal(5, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as int) as decimal(10,0)), cast(1 as decimal(5,0))) AS pmod(CAST(1 AS INT), CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as int), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as int) as decimal(10,0)), cast(1 as decimal(10,0))) AS pmod(CAST(1 AS INT), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as int), cast(1 as decimal(20, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as int) as decimal(10,0)), cast(1 as decimal(20,0))) AS pmod(CAST(1 AS INT), CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as bigint), cast(1 as decimal(3, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as bigint) as decimal(20,0)), cast(1 as decimal(3,0))) AS pmod(CAST(1 AS BIGINT), CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as bigint), cast(1 as decimal(5, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as bigint) as decimal(20,0)), cast(1 as decimal(5,0))) AS pmod(CAST(1 AS BIGINT), CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as bigint), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as bigint) as decimal(20,0)), cast(1 as decimal(10,0))) AS pmod(CAST(1 AS BIGINT), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as bigint), cast(1 as decimal(20, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as bigint) as decimal(20,0)), cast(1 as decimal(20,0))) AS pmod(CAST(1 AS BIGINT), CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as float), cast(1 as decimal(3, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as float) as double), cast(cast(1 as decimal(3,0)) as double)) AS pmod(CAST(1 AS FLOAT), CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as float), cast(1 as decimal(5, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as float) as double), cast(cast(1 as decimal(5,0)) as double)) AS pmod(CAST(1 AS FLOAT), CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as float), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as float) as double), cast(cast(1 as decimal(10,0)) as double)) AS pmod(CAST(1 AS FLOAT), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as float), cast(1 as decimal(20, 0))) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as float) as double), cast(cast(1 as decimal(20,0)) as double)) AS pmod(CAST(1 AS FLOAT), CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as double), cast(1 as decimal(3, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(cast(1 as decimal(3,0)) as double)) AS pmod(CAST(1 AS DOUBLE), CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as double), cast(1 as decimal(5, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(cast(1 as decimal(5,0)) as double)) AS pmod(CAST(1 AS DOUBLE), CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as double), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(cast(1 as decimal(10,0)) as double)) AS pmod(CAST(1 AS DOUBLE), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as double), cast(1 as decimal(20, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as double), cast(cast(1 as decimal(20,0)) as double)) AS pmod(CAST(1 AS DOUBLE), CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as decimal(3, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(10,0)), cast(1 as decimal(3,0))) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as decimal(5, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(10,0)), cast(1 as decimal(5,0))) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(10,0)), cast(1 as decimal(10,0))) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as decimal(20, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(10,0)), cast(1 as decimal(20,0))) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast('1' as binary), cast(1 as decimal(3, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"pmod(CAST(1 AS BINARY), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast('1' as binary), cast(1 as decimal(3, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('1' as binary), cast(1 as decimal(5, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"pmod(CAST(1 AS BINARY), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast('1' as binary), cast(1 as decimal(5, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('1' as binary), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"pmod(CAST(1 AS BINARY), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast('1' as binary), cast(1 as decimal(10, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('1' as binary), cast(1 as decimal(20, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"pmod(CAST(1 AS BINARY), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast('1' as binary), cast(1 as decimal(20, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(3, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 81,
    "fragment" : "pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(3, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(5, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 81,
    "fragment" : "pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(5, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(10, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(20, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(20, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(3, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 74,
    "fragment" : "pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(3, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(5, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 74,
    "fragment" : "pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(5, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(10, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(20, 0))) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(20, 0)))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as tinyint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(3,0)), cast(cast(1 as tinyint) as decimal(3,0))) AS pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as tinyint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(5,0)), cast(cast(1 as tinyint) as decimal(3,0))) AS pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as tinyint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(10,0)), cast(cast(1 as tinyint) as decimal(3,0))) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as tinyint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(20,0)), cast(cast(1 as tinyint) as decimal(3,0))) AS pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as smallint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(3,0)), cast(cast(1 as smallint) as decimal(5,0))) AS pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as smallint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(5,0)), cast(cast(1 as smallint) as decimal(5,0))) AS pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as smallint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(10,0)), cast(cast(1 as smallint) as decimal(5,0))) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as smallint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(20,0)), cast(cast(1 as smallint) as decimal(5,0))) AS pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as int)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(3,0)), cast(cast(1 as int) as decimal(10,0))) AS pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as int)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(5,0)), cast(cast(1 as int) as decimal(10,0))) AS pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as int)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(10,0)), cast(cast(1 as int) as decimal(10,0))) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as int)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(20,0)), cast(cast(1 as int) as decimal(10,0))) AS pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as bigint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(3,0)), cast(cast(1 as bigint) as decimal(20,0))) AS pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as bigint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(5,0)), cast(cast(1 as bigint) as decimal(20,0))) AS pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as bigint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(10,0)), cast(cast(1 as bigint) as decimal(20,0))) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as bigint)) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(20,0)), cast(cast(1 as bigint) as decimal(20,0))) AS pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as float)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(3,0)) as double), cast(cast(1 as float) as double)) AS pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as float)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(5,0)) as double), cast(cast(1 as float) as double)) AS pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as float)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(10,0)) as double), cast(cast(1 as float) as double)) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as float)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(20,0)) as double), cast(cast(1 as float) as double)) AS pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as double)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(3,0)) as double), cast(1 as double)) AS pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as double)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(5,0)) as double), cast(1 as double)) AS pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as double)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(10,0)) as double), cast(1 as double)) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as double)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(20,0)) as double), cast(1 as double)) AS pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(3,0)), cast(1 as decimal(10,0))) AS pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(5,0)), cast(1 as decimal(10,0))) AS pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(10,0)), cast(1 as decimal(10,0))) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as decimal(10, 0))) FROM t
-- !query analysis
Project [pmod(cast(1 as decimal(20,0)), cast(1 as decimal(10,0))) AS pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as string)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(3,0)) as double), cast(cast(1 as string) as double)) AS pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as string)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(5,0)) as double), cast(cast(1 as string) as double)) AS pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as string)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(10,0)) as double), cast(cast(1 as string) as double)) AS pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as string)) FROM t
-- !query analysis
Project [pmod(cast(cast(1 as decimal(20,0)) as double), cast(cast(1 as string) as double)) AS pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast('1' as binary)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast(1 as decimal(3, 0)) , cast('1' as binary))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast('1' as binary)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast(1 as decimal(5, 0)) , cast('1' as binary))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast('1' as binary)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast(1 as decimal(10, 0)), cast('1' as binary))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast('1' as binary)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast(1 as decimal(20, 0)), cast('1' as binary))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as boolean)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast(1 as decimal(3, 0)) , cast(1 as boolean))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as boolean)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast(1 as decimal(5, 0)) , cast(1 as boolean))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as boolean)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast(1 as decimal(10, 0)), cast(1 as boolean))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as boolean)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast(1 as decimal(20, 0)), cast(1 as boolean))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast('2017-12-11 09:30:00.0' as timestamp)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(3,0)), CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast(1 as decimal(3, 0)) , cast('2017-12-11 09:30:00.0' as timestamp))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast('2017-12-11 09:30:00.0' as timestamp)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(5,0)), CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast(1 as decimal(5, 0)) , cast('2017-12-11 09:30:00.0' as timestamp))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast('2017-12-11 09:30:00.0' as timestamp)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(10,0)), CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast(1 as decimal(10, 0)), cast('2017-12-11 09:30:00.0' as timestamp))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast('2017-12-11 09:30:00.0' as timestamp)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(20,0)), CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast(1 as decimal(20, 0)), cast('2017-12-11 09:30:00.0' as timestamp))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast('2017-12-11 09:30:00' as date)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(3,0)), CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast(1 as decimal(3, 0)) , cast('2017-12-11 09:30:00' as date))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast('2017-12-11 09:30:00' as date)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(5,0)), CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast(1 as decimal(5, 0)) , cast('2017-12-11 09:30:00' as date))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast('2017-12-11 09:30:00' as date)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(10,0)), CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast(1 as decimal(10, 0)), cast('2017-12-11 09:30:00' as date))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast('2017-12-11 09:30:00' as date)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(20,0)), CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast(1 as decimal(20, 0)), cast('2017-12-11 09:30:00' as date))"
  } ]
}


-- !query
SELECT cast(1 as tinyint) = cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) = cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) = cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0)) = cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0)) = cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) = cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0)) = cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) = cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) = cast(cast(1 as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) = cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) = cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0)) = cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) = cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0)) = cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) = cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) = cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS INT) = CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) = cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) = cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS INT) = CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) = cast(1 as decimal(10,0))) AS (CAST(1 AS INT) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) = cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0)) = cast(1 as decimal(20,0))) AS (CAST(1 AS INT) = CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) = cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) = cast(cast(1 as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) = cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) = cast(cast(1 as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) = cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) = cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) = cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) = cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) = cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) = cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) = cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) = cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) = cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) = cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) = cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) = cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) = cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) = cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) = cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) = cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) = cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) = cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) = cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) = cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) = cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) = cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) = cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) = cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) = cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) = cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) = cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) = cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) = cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) = cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) = cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) = cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(5,0)) = cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) = cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) = cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) = cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) = cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) = cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) = cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) = cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(20,0)) = cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(20,0)) = cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) = cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) = cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) = cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) = cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) = cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) = cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) = cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) = cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) = cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) = cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) = cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) = cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) = cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) = cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) = cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) = cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) = cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) = cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  = cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  = cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  = cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  = cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) = cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) = cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) = cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) = cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as boolean) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) = cast(cast(1 as boolean) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as boolean) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) = cast(cast(1 as boolean) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as boolean) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) = cast(cast(1 as boolean) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as boolean) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) = cast(cast(1 as boolean) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  = cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  = cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  = cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  = cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) = cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) = cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) = cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) = cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  = cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  = cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  = cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  = cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) = cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) = cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) = cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) = cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) <=> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) <=> cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) <=> CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <=> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0)) <=> cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) <=> CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0)) <=> cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <=> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0)) <=> cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) <=> CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <=> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) <=> cast(cast(1 as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS SMALLINT) <=> CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <=> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) <=> cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) <=> CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0)) <=> cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <=> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0)) <=> cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) <=> CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <=> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) <=> cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS INT) <=> CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <=> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) <=> cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS INT) <=> CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) <=> cast(1 as decimal(10,0))) AS (CAST(1 AS INT) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <=> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0)) <=> cast(1 as decimal(20,0))) AS (CAST(1 AS INT) <=> CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <=> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) <=> cast(cast(1 as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) <=> CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <=> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) <=> cast(cast(1 as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) <=> CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) <=> cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <=> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) <=> cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) <=> CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <=> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) <=> cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS FLOAT) <=> CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <=> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) <=> cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS FLOAT) <=> CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) <=> cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS FLOAT) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <=> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) <=> cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS FLOAT) <=> CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <=> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) <=> cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) <=> CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <=> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) <=> cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) <=> CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) <=> cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <=> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) <=> cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) <=> CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <=> cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <=> cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <=> cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) <=> cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) <=> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <=> CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <=> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <=> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <=> CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <=> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <=> CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast('1' as binary) <=> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <=> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <=> CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast('1' as binary) <=> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <=> CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <=> CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <=> CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <=> CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <=> CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <=> CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <=> CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <=> CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) <=> cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) <=> cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <=> cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <=> cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(5,0)) <=> cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) <=> cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <=> cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <=> cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) <=> cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) <=> cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <=> cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <=> cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(20,0)) <=> cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(20,0)) <=> cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) <=> cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <=> cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) <=> cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) <=> cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) <=> cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) <=> cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) <=> cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) <=> cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) <=> cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) <=> cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) <=> cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) <=> cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <=> cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <=> cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) <=> cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) <=> cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) <=> cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) <=> cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast(1 as decimal(3, 0))  <=> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast(1 as decimal(5, 0))  <=> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast(1 as decimal(10, 0)) <=> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast(1 as decimal(20, 0)) <=> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as boolean) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) <=> cast(cast(1 as boolean) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as boolean) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) <=> cast(cast(1 as boolean) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as boolean) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <=> cast(cast(1 as boolean) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as boolean) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <=> cast(cast(1 as boolean) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS BOOLEAN))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <=> CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast(1 as decimal(3, 0))  <=> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <=> CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast(1 as decimal(5, 0))  <=> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <=> CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast(1 as decimal(10, 0)) <=> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <=> CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast(1 as decimal(20, 0)) <=> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <=> CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast(1 as decimal(3, 0))  <=> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <=> CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast(1 as decimal(5, 0))  <=> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <=> CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast(1 as decimal(10, 0)) <=> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <=> CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast(1 as decimal(20, 0)) <=> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) < cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) < cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) < CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) < cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0)) < cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) < CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0)) < cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) < cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0)) < cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) < CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) < cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) < cast(cast(1 as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS SMALLINT) < CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) < cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) < cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) < CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0)) < cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) < cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0)) < cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) < CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) < cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) < cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS INT) < CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) < cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) < cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS INT) < CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) < cast(1 as decimal(10,0))) AS (CAST(1 AS INT) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) < cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0)) < cast(1 as decimal(20,0))) AS (CAST(1 AS INT) < CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) < cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) < cast(cast(1 as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) < CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) < cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) < cast(cast(1 as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) < CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) < cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) < cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) < cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) < CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) < cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) < cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS FLOAT) < CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) < cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) < cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS FLOAT) < CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) < cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS FLOAT) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) < cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) < cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS FLOAT) < CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) < cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) < cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) < CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) < cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) < cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) < CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) < cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) < cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) < cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) < CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) < cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) < cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) < cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) < cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) < cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) < CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) < cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) < cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) < CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) < cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) < CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) < cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) < cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) < CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) < cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) < CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) < CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) < CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) < CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) < CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) < CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) < CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) < CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) < cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) < CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) < cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) < CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) < cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) < cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) < CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(5,0)) < cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) < CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) < cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) < CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) < cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) < cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) < CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) < cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) < CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) < cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) < CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) < cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) < cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) < CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(20,0)) < cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) < CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(20,0)) < cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) < CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) < cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) < cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) < CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) < cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) < CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) < cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) < CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) < cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) < cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) < CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) < cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) < CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) < cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) < CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) < cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) < cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) < CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) < cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) < cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) < cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) < cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) < CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) < cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) < CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) < cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) < CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) < cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) < CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) < cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) < CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  < cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  < cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  < cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  < cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) < cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) < cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) < cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) < cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  < cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  < cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) < cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) < cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  < cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) < CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  < cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  < cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) < CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  < cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) < cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) < CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) < cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) < cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) < CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) < cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  < cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) < CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  < cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  < cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) < CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  < cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) < cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) < CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) < cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) < cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) < CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) < cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) <= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) <= cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) <= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0)) <= cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) <= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0)) <= cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0)) <= cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) <= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) <= cast(cast(1 as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS SMALLINT) <= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) <= cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) <= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0)) <= cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0)) <= cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) <= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) <= cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS INT) <= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) <= cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS INT) <= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) <= cast(1 as decimal(10,0))) AS (CAST(1 AS INT) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0)) <= cast(1 as decimal(20,0))) AS (CAST(1 AS INT) <= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) <= cast(cast(1 as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) <= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) <= cast(cast(1 as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) <= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) <= cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) <= cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) <= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) <= cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS FLOAT) <= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) <= cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS FLOAT) <= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) <= cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS FLOAT) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) <= cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS FLOAT) <= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) <= cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) <= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) <= cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) <= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) <= cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) <= cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) <= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <= cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <= cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <= cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) <= cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) <= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) <= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) <= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) <= cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) <= cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <= cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <= cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(5,0)) <= cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) <= cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <= cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <= cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) <= cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) <= cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <= cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <= cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(20,0)) <= cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(20,0)) <= cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) <= cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <= cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) <= cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) <= cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) <= cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) <= cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) <= cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) <= cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) <= cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) <= cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) <= cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) <= cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) <= cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) <= cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) <= cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) <= cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) <= cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) <= cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(3, 0))  <= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(5, 0))  <= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(10, 0)) <= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(20, 0)) <= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  <= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  <= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) <= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) <= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(3, 0))  <= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(5, 0))  <= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(10, 0)) <= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(20, 0)) <= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(3, 0))  <= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(5, 0))  <= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(10, 0)) <= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(20, 0)) <= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) > cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) > cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) > CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) > cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0)) > cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) > CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0)) > cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) > cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0)) > cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) > CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) > cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) > cast(cast(1 as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS SMALLINT) > CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) > cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) > cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) > CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0)) > cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) > cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0)) > cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) > CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) > cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) > cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS INT) > CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) > cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) > cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS INT) > CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) > cast(1 as decimal(10,0))) AS (CAST(1 AS INT) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) > cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0)) > cast(1 as decimal(20,0))) AS (CAST(1 AS INT) > CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) > cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) > cast(cast(1 as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) > CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) > cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) > cast(cast(1 as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) > CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) > cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) > cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) > cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) > CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) > cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) > cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS FLOAT) > CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) > cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) > cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS FLOAT) > CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) > cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS FLOAT) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) > cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) > cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS FLOAT) > CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) > cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) > cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) > CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) > cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) > cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) > CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) > cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) > cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) > cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) > CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) > cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) > cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) > cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) > cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) > cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) > CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) > cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) > cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) > CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) > cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) > CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) > cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) > cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) > CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) > cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) > CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) > CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) > CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) > CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) > CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) > CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) > CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) > CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) > cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) > CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) > cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) > CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) > cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) > cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) > CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(5,0)) > cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) > CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) > cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) > CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) > cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) > cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) > CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) > cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) > CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) > cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) > CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) > cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) > cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) > CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(20,0)) > cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) > CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(20,0)) > cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) > CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) > cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) > cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) > CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) > cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) > CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) > cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) > CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) > cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) > cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) > CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) > cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) > CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) > cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) > CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) > cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) > cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) > CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) > cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) > cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) > cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) > cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) > CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) > cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) > CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) > cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) > CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) > cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) > CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) > cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) > CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  > cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  > cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  > cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  > cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) > cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) > cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) > cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) > cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  > cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  > cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) > cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) > cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  > cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) > CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  > cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  > cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) > CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  > cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) > cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) > CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) > cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) > cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) > CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) > cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  > cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) > CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  > cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  > cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) > CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  > cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) > cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) > CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) > cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) > cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) > CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) > cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) >= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as tinyint) as decimal(3,0)) >= cast(1 as decimal(3,0))) AS (CAST(1 AS TINYINT) >= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) >= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0)) >= cast(1 as decimal(5,0))) AS (CAST(1 AS TINYINT) >= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0)) >= cast(1 as decimal(10,0))) AS (CAST(1 AS TINYINT) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) >= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0)) >= cast(1 as decimal(20,0))) AS (CAST(1 AS TINYINT) >= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) >= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) >= cast(cast(1 as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS SMALLINT) >= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) >= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as smallint) as decimal(5,0)) >= cast(1 as decimal(5,0))) AS (CAST(1 AS SMALLINT) >= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0)) >= cast(1 as decimal(10,0))) AS (CAST(1 AS SMALLINT) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) >= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0)) >= cast(1 as decimal(20,0))) AS (CAST(1 AS SMALLINT) >= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) >= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) >= cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS INT) >= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) >= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) >= cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS INT) >= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as int) as decimal(10,0)) >= cast(1 as decimal(10,0))) AS (CAST(1 AS INT) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) >= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0)) >= cast(1 as decimal(20,0))) AS (CAST(1 AS INT) >= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) >= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) >= cast(cast(1 as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) >= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) >= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) >= cast(cast(1 as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) >= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) >= cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS BIGINT) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) >= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as bigint) as decimal(20,0)) >= cast(1 as decimal(20,0))) AS (CAST(1 AS BIGINT) >= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) >= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) >= cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS FLOAT) >= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) >= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) >= cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS FLOAT) >= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) >= cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS FLOAT) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) >= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as float) as double) >= cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS FLOAT) >= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) >= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) >= cast(cast(1 as decimal(3,0)) as double)) AS (CAST(1 AS DOUBLE) >= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) >= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) >= cast(cast(1 as decimal(5,0)) as double)) AS (CAST(1 AS DOUBLE) >= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) >= cast(cast(1 as decimal(10,0)) as double)) AS (CAST(1 AS DOUBLE) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) >= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(1 as double) >= cast(cast(1 as decimal(20,0)) as double)) AS (CAST(1 AS DOUBLE) >= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) >= cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DECIMAL(3,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) >= cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DECIMAL(5,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) >= cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) >= cast(1 as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DECIMAL(20,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) >= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) >= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) >= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) >= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) >= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) >= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) >= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) >= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) >= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) >= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) >= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) >= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) >= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) >= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) >= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) >= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) >= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) >= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) >= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(3,0)) >= cast(cast(1 as tinyint) as decimal(3,0))) AS (CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) >= cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) >= cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as tinyint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) >= cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS TINYINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(5,0)) >= cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(5,0)) >= cast(cast(1 as smallint) as decimal(5,0))) AS (CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) >= cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as smallint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) >= cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS SMALLINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) >= cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as int) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) >= cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) >= cast(cast(1 as int) as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as int) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) >= cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS INT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(20,0)) >= cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(20,0)) >= cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as decimal(20,0)) >= cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as bigint) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) >= cast(cast(1 as bigint) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS BIGINT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) >= cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) >= cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) >= cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as float) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) >= cast(cast(1 as float) as double)) AS (CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS FLOAT))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) >= cast(1 as double)) AS (CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) >= cast(1 as double)) AS (CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) >= cast(1 as double)) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as double) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) >= cast(1 as double)) AS (CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS DOUBLE))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as decimal(10,0)) >= cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as decimal(10,0)) >= cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(10,0)) >= cast(1 as decimal(10,0))) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [(cast(1 as decimal(20,0)) >= cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS DECIMAL(10,0)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(3,0)) as double) >= cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(5,0)) as double) >= cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(10,0)) as double) >= cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as string) FROM t
-- !query analysis
Project [(cast(cast(1 as decimal(20,0)) as double) >= cast(cast(1 as string) as double)) AS (CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS STRING))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(3, 0))  >= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(5, 0))  >= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(10, 0)) >= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(20, 0)) >= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  >= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  >= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) >= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as boolean) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) >= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) >= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(3, 0))  >= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) >= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(5, 0))  >= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) >= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(10, 0)) >= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) >= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(20, 0)) >= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) >= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(3, 0))  >= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) >= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(5, 0))  >= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) >= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(10, 0)) >= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) >= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(20, 0)) >= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) <> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as tinyint) as decimal(3,0)) = cast(1 as decimal(3,0))) AS (NOT (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(3,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0)) = cast(1 as decimal(5,0))) AS (NOT (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(5,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0)) = cast(1 as decimal(10,0))) AS (NOT (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as tinyint) <> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0)) = cast(1 as decimal(20,0))) AS (NOT (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(20,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as smallint) as decimal(5,0)) = cast(cast(1 as decimal(3,0)) as decimal(5,0))) AS (NOT (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(3,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as smallint) as decimal(5,0)) = cast(1 as decimal(5,0))) AS (NOT (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(5,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0)) = cast(1 as decimal(10,0))) AS (NOT (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as smallint) <> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0)) = cast(1 as decimal(20,0))) AS (NOT (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(20,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as int) as decimal(10,0)) = cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (NOT (CAST(1 AS INT) = CAST(1 AS DECIMAL(3,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as int) as decimal(10,0)) = cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (NOT (CAST(1 AS INT) = CAST(1 AS DECIMAL(5,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as int) as decimal(10,0)) = cast(1 as decimal(10,0))) AS (NOT (CAST(1 AS INT) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as int) <> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0)) = cast(1 as decimal(20,0))) AS (NOT (CAST(1 AS INT) = CAST(1 AS DECIMAL(20,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as bigint) as decimal(20,0)) = cast(cast(1 as decimal(3,0)) as decimal(20,0))) AS (NOT (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(3,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as bigint) as decimal(20,0)) = cast(cast(1 as decimal(5,0)) as decimal(20,0))) AS (NOT (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(5,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as bigint) as decimal(20,0)) = cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (NOT (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as bigint) <> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as bigint) as decimal(20,0)) = cast(1 as decimal(20,0))) AS (NOT (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(20,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as float) as double) = cast(cast(1 as decimal(3,0)) as double)) AS (NOT (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(3,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as float) as double) = cast(cast(1 as decimal(5,0)) as double)) AS (NOT (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(5,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as float) as double) = cast(cast(1 as decimal(10,0)) as double)) AS (NOT (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as float) <> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as float) as double) = cast(cast(1 as decimal(20,0)) as double)) AS (NOT (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(20,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [NOT (cast(1 as double) = cast(cast(1 as decimal(3,0)) as double)) AS (NOT (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(3,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [NOT (cast(1 as double) = cast(cast(1 as decimal(5,0)) as double)) AS (NOT (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(5,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(1 as double) = cast(cast(1 as decimal(10,0)) as double)) AS (NOT (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as double) <> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [NOT (cast(1 as double) = cast(cast(1 as decimal(20,0)) as double)) AS (NOT (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(20,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(10,0)) = cast(cast(1 as decimal(3,0)) as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(3,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(10,0)) = cast(cast(1 as decimal(5,0)) as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(5,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(10,0)) = cast(1 as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(10,0)) as decimal(20,0)) = cast(1 as decimal(20,0))) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(20,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast('1' as binary) <> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) <> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) <> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(3, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(5, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(20, 0)) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as tinyint) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(3,0)) = cast(cast(1 as tinyint) as decimal(3,0))) AS (NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS TINYINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as tinyint) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(5,0)) = cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(5,0))) AS (NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS TINYINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as tinyint) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(10,0)) = cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS TINYINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as tinyint) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(20,0)) = cast(cast(cast(1 as tinyint) as decimal(3,0)) as decimal(20,0))) AS (NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS TINYINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as smallint) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(3,0)) as decimal(5,0)) = cast(cast(1 as smallint) as decimal(5,0))) AS (NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS SMALLINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as smallint) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(5,0)) = cast(cast(1 as smallint) as decimal(5,0))) AS (NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS SMALLINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as smallint) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(10,0)) = cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS SMALLINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as smallint) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(20,0)) = cast(cast(cast(1 as smallint) as decimal(5,0)) as decimal(20,0))) AS (NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS SMALLINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as int) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(3,0)) as decimal(10,0)) = cast(cast(1 as int) as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS INT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as int) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(5,0)) as decimal(10,0)) = cast(cast(1 as int) as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS INT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as int) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(10,0)) = cast(cast(1 as int) as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS INT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as int) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(20,0)) = cast(cast(cast(1 as int) as decimal(10,0)) as decimal(20,0))) AS (NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS INT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as bigint) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(3,0)) as decimal(20,0)) = cast(cast(1 as bigint) as decimal(20,0))) AS (NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BIGINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as bigint) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(5,0)) as decimal(20,0)) = cast(cast(1 as bigint) as decimal(20,0))) AS (NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BIGINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as bigint) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(10,0)) as decimal(20,0)) = cast(cast(1 as bigint) as decimal(20,0))) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BIGINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as bigint) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(20,0)) = cast(cast(1 as bigint) as decimal(20,0))) AS (NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BIGINT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as float) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(3,0)) as double) = cast(cast(1 as float) as double)) AS (NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS FLOAT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as float) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(5,0)) as double) = cast(cast(1 as float) as double)) AS (NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS FLOAT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as float) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(10,0)) as double) = cast(cast(1 as float) as double)) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS FLOAT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as float) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(20,0)) as double) = cast(cast(1 as float) as double)) AS (NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS FLOAT)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as double) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(3,0)) as double) = cast(1 as double)) AS (NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS DOUBLE)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as double) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(5,0)) as double) = cast(1 as double)) AS (NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS DOUBLE)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as double) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(10,0)) as double) = cast(1 as double)) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DOUBLE)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as double) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(20,0)) as double) = cast(1 as double)) AS (NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS DOUBLE)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(3,0)) as decimal(10,0)) = cast(1 as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(5,0)) as decimal(10,0)) = cast(1 as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(10,0)) = cast(1 as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as decimal(10, 0)) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(20,0)) = cast(cast(1 as decimal(10,0)) as decimal(20,0))) AS (NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS DECIMAL(10,0))))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as string) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(3,0)) as double) = cast(cast(1 as string) as double)) AS (NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as string) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(5,0)) as double) = cast(cast(1 as string) as double)) AS (NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as string) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(10,0)) as double) = cast(cast(1 as string) as double)) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as string) FROM t
-- !query analysis
Project [NOT (cast(cast(1 as decimal(20,0)) as double) = cast(cast(1 as string) as double)) AS (NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS STRING)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(3, 0))  <> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(5, 0))  <> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(10, 0)) <> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast('1' as binary) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(20, 0)) <> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as boolean) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(3,0)) = cast(cast(1 as boolean) as decimal(3,0))) AS (NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BOOLEAN)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as boolean) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(5,0)) = cast(cast(1 as boolean) as decimal(5,0))) AS (NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BOOLEAN)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as boolean) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(10,0)) = cast(cast(1 as boolean) as decimal(10,0))) AS (NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BOOLEAN)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as boolean) FROM t
-- !query analysis
Project [NOT (cast(1 as decimal(20,0)) = cast(cast(1 as boolean) as decimal(20,0))) AS (NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BOOLEAN)))#x]
+- SubqueryAlias t
   +- View (`t`, [1#x])
      +- Project [cast(1#x as int) AS 1#x]
         +- Project [1 AS 1#x]
            +- OneRowRelation


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(3, 0))  <> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(5, 0))  <> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(10, 0)) <> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(20, 0)) <> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(3, 0))  <> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(5, 0))  <> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(10, 0)) <> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast('2017-12-11 09:30:00' as date) FROM t
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(20, 0)) <> cast('2017-12-11 09:30:00' as date)"
  } ]
}
