-- Automatically generated by SQLQueryTestSuite
-- !query
CREATE OR REPLACE TEMPORARY VIEW INT4_TBL AS SELECT * FROM
  (VALUES (0), (123456), (-123456), (2147483647), (-2147483647))
  AS v(f1)
-- !query schema
struct<>
-- !query output



-- !query
CREATE OR REPLACE TEMPORARY VIEW INT8_TBL AS SELECT * FROM
  (VALUES
    (123, 456),
    (123, 4567890123456789),
    (4567890123456789, 123),
    (4567890123456789, 4567890123456789),
    (4567890123456789, -4567890123456789))
  AS v(q1, q2)
-- !query schema
struct<>
-- !query output



-- !query
CREATE OR REPLACE TEMPORARY VIEW FLOAT8_TBL AS SELECT * FROM
  (VALUES (0.0), (-34.84), (-1004.30),
    (CAST('-1.2345678901234e+200' AS DOUBLE)), (CAST('-1.2345678901234e-200' AS DOUBLE)))
  AS v(f1)
-- !query schema
struct<>
-- !query output



-- !query
SELECT 1 AS two UNION SELECT 2 ORDER BY 1
-- !query schema
struct<two:int>
-- !query output
1
2


-- !query
SELECT 1 AS one UNION SELECT 1 ORDER BY 1
-- !query schema
struct<one:int>
-- !query output
1


-- !query
SELECT 1 AS two UNION ALL SELECT 2
-- !query schema
struct<two:int>
-- !query output
1
2


-- !query
SELECT 1 AS two UNION ALL SELECT 1
-- !query schema
struct<two:int>
-- !query output
1
1


-- !query
SELECT 1 AS three UNION SELECT 2 UNION SELECT 3 ORDER BY 1
-- !query schema
struct<three:int>
-- !query output
1
2
3


-- !query
SELECT 1 AS two UNION SELECT 2 UNION SELECT 2 ORDER BY 1
-- !query schema
struct<two:int>
-- !query output
1
2


-- !query
SELECT 1 AS three UNION SELECT 2 UNION ALL SELECT 2 ORDER BY 1
-- !query schema
struct<three:int>
-- !query output
1
2
2


-- !query
SELECT 1.1 AS two UNION SELECT 2.2 ORDER BY 1
-- !query schema
struct<two:decimal(2,1)>
-- !query output
1.1
2.2


-- !query
SELECT 1.1 AS two UNION SELECT 2 ORDER BY 1
-- !query schema
struct<two:decimal(11,1)>
-- !query output
1.1
2.0


-- !query
SELECT 1 AS two UNION SELECT 2.2 ORDER BY 1
-- !query schema
struct<two:decimal(11,1)>
-- !query output
1.0
2.2


-- !query
SELECT 1 AS one UNION SELECT double(1.0) ORDER BY 1
-- !query schema
struct<one:double>
-- !query output
1.0


-- !query
SELECT 1.1 AS two UNION ALL SELECT 2 ORDER BY 1
-- !query schema
struct<two:decimal(11,1)>
-- !query output
1.1
2.0


-- !query
SELECT double(1.0) AS two UNION ALL SELECT 1 ORDER BY 1
-- !query schema
struct<two:double>
-- !query output
1.0
1.0


-- !query
SELECT 1.1 AS three UNION SELECT 2 UNION SELECT 3 ORDER BY 1
-- !query schema
struct<three:decimal(11,1)>
-- !query output
1.1
2.0
3.0


-- !query
SELECT double(1.1) AS two UNION SELECT 2 UNION SELECT double(2.0) ORDER BY 1
-- !query schema
struct<two:double>
-- !query output
1.1
2.0


-- !query
SELECT 1.1 AS three UNION SELECT 2 UNION ALL SELECT 2 ORDER BY 1
-- !query schema
struct<three:decimal(11,1)>
-- !query output
1.1
2.0
2.0


-- !query
SELECT 1.1 AS two UNION (SELECT 2 UNION ALL SELECT 2) ORDER BY 1
-- !query schema
struct<two:decimal(11,1)>
-- !query output
1.1
2.0


-- !query
SELECT f1 AS five FROM FLOAT8_TBL
UNION
SELECT f1 FROM FLOAT8_TBL
ORDER BY 1
-- !query schema
struct<five:double>
-- !query output
-1.2345678901234E200
-1004.3
-34.84
-1.2345678901234E-200
0.0


-- !query
SELECT f1 AS ten FROM FLOAT8_TBL
UNION ALL
SELECT f1 FROM FLOAT8_TBL
-- !query schema
struct<ten:double>
-- !query output
-1.2345678901234E-200
-1.2345678901234E-200
-1.2345678901234E200
-1.2345678901234E200
-1004.3
-1004.3
-34.84
-34.84
0.0
0.0


-- !query
SELECT f1 AS nine FROM FLOAT8_TBL
UNION
SELECT f1 FROM INT4_TBL
ORDER BY 1
-- !query schema
struct<nine:double>
-- !query output
-1.2345678901234E200
-2.147483647E9
-123456.0
-1004.3
-34.84
-1.2345678901234E-200
0.0
123456.0
2.147483647E9


-- !query
SELECT f1 AS ten FROM FLOAT8_TBL
UNION ALL
SELECT f1 FROM INT4_TBL
-- !query schema
struct<ten:double>
-- !query output
-1.2345678901234E-200
-1.2345678901234E200
-1004.3
-123456.0
-2.147483647E9
-34.84
0.0
0.0
123456.0
2.147483647E9


-- !query
SELECT f1 AS five FROM FLOAT8_TBL
  WHERE f1 BETWEEN -1e6 AND 1e6
UNION
SELECT f1 FROM INT4_TBL
  WHERE f1 BETWEEN 0 AND 1000000
ORDER BY 1
-- !query schema
struct<five:double>
-- !query output
-1004.3
-34.84
-1.2345678901234E-200
0.0
123456.0


-- !query
SELECT q2 FROM int8_tbl INTERSECT SELECT q1 FROM int8_tbl ORDER BY 1
-- !query schema
struct<q2:bigint>
-- !query output
123
4567890123456789


-- !query
SELECT q2 FROM int8_tbl INTERSECT ALL SELECT q1 FROM int8_tbl ORDER BY 1
-- !query schema
struct<q2:bigint>
-- !query output
123
4567890123456789
4567890123456789


-- !query
SELECT q2 FROM int8_tbl EXCEPT SELECT q1 FROM int8_tbl ORDER BY 1
-- !query schema
struct<q2:bigint>
-- !query output
-4567890123456789
456


-- !query
SELECT q2 FROM int8_tbl EXCEPT ALL SELECT q1 FROM int8_tbl ORDER BY 1
-- !query schema
struct<q2:bigint>
-- !query output
-4567890123456789
456


-- !query
SELECT q2 FROM int8_tbl EXCEPT ALL SELECT DISTINCT q1 FROM int8_tbl ORDER BY 1
-- !query schema
struct<q2:bigint>
-- !query output
-4567890123456789
456
4567890123456789


-- !query
SELECT q1 FROM int8_tbl EXCEPT SELECT q2 FROM int8_tbl ORDER BY 1
-- !query schema
struct<q1:bigint>
-- !query output



-- !query
SELECT q1 FROM int8_tbl EXCEPT ALL SELECT q2 FROM int8_tbl ORDER BY 1
-- !query schema
struct<q1:bigint>
-- !query output
123
4567890123456789


-- !query
SELECT q1 FROM int8_tbl EXCEPT ALL SELECT DISTINCT q2 FROM int8_tbl ORDER BY 1
-- !query schema
struct<q1:bigint>
-- !query output
123
4567890123456789
4567890123456789


-- !query
(SELECT 1,2,3 UNION SELECT 4,5,6) INTERSECT SELECT 4,5,6
-- !query schema
struct<1:int,2:int,3:int>
-- !query output
4	5	6


-- !query
(SELECT 1,2,3 UNION SELECT 4,5,6 ORDER BY 1,2) INTERSECT SELECT 4,5,6
-- !query schema
struct<1:int,2:int,3:int>
-- !query output
4	5	6


-- !query
(SELECT 1,2,3 UNION SELECT 4,5,6) EXCEPT SELECT 4,5,6
-- !query schema
struct<1:int,2:int,3:int>
-- !query output
1	2	3


-- !query
(SELECT 1,2,3 UNION SELECT 4,5,6 ORDER BY 1,2) EXCEPT SELECT 4,5,6
-- !query schema
struct<1:int,2:int,3:int>
-- !query output
1	2	3


-- !query
select count(*) from
  ( select unique1 from tenk1 intersect select fivethous from tenk1 ) ss
-- !query schema
struct<count(1):bigint>
-- !query output
5000


-- !query
select unique1 from tenk1 except select unique2 from tenk1 where unique2 != 10
-- !query schema
struct<unique1:int>
-- !query output
10


-- !query
select count(*) from
  ( select unique1 from tenk1 intersect select fivethous from tenk1 ) ss
-- !query schema
struct<count(1):bigint>
-- !query output
5000


-- !query
select unique1 from tenk1 except select unique2 from tenk1 where unique2 != 10
-- !query schema
struct<unique1:int>
-- !query output
10


-- !query
SELECT f1 FROM float8_tbl INTERSECT SELECT f1 FROM int4_tbl ORDER BY 1
-- !query schema
struct<f1:double>
-- !query output
0.0


-- !query
SELECT f1 FROM float8_tbl EXCEPT SELECT f1 FROM int4_tbl ORDER BY 1
-- !query schema
struct<f1:double>
-- !query output
-1.2345678901234E200
-1004.3
-34.84
-1.2345678901234E-200


-- !query
SELECT q1 FROM int8_tbl INTERSECT SELECT q2 FROM int8_tbl UNION ALL SELECT q2 FROM int8_tbl  ORDER BY 1
-- !query schema
struct<q1:bigint>
-- !query output
-4567890123456789
123
123
456
4567890123456789
4567890123456789
4567890123456789


-- !query
SELECT q1 FROM int8_tbl INTERSECT (((SELECT q2 FROM int8_tbl UNION ALL SELECT q2 FROM int8_tbl))) ORDER BY 1
-- !query schema
struct<q1:bigint>
-- !query output
123
4567890123456789


-- !query
(((SELECT q1 FROM int8_tbl INTERSECT SELECT q2 FROM int8_tbl ORDER BY 1))) UNION ALL SELECT q2 FROM int8_tbl
-- !query schema
struct<q1:bigint>
-- !query output
123
4567890123456789
456
4567890123456789
123
4567890123456789
-4567890123456789


-- !query
SELECT q1 FROM int8_tbl UNION ALL SELECT q2 FROM int8_tbl EXCEPT SELECT q1 FROM int8_tbl ORDER BY 1
-- !query schema
struct<q1:bigint>
-- !query output
-4567890123456789
456


-- !query
SELECT q1 FROM int8_tbl UNION ALL (((SELECT q2 FROM int8_tbl EXCEPT SELECT q1 FROM int8_tbl ORDER BY 1)))
-- !query schema
struct<q1:bigint>
-- !query output
123
123
4567890123456789
4567890123456789
4567890123456789
-4567890123456789
456


-- !query
(((SELECT q1 FROM int8_tbl UNION ALL SELECT q2 FROM int8_tbl))) EXCEPT SELECT q1 FROM int8_tbl ORDER BY 1
-- !query schema
struct<q1:bigint>
-- !query output
-4567890123456789
456


-- !query
SELECT q1,q2 FROM int8_tbl EXCEPT SELECT q2,q1 FROM int8_tbl
ORDER BY q2,q1
-- !query schema
struct<q1:bigint,q2:bigint>
-- !query output
4567890123456789	-4567890123456789
123	456


-- !query
SELECT q1 FROM int8_tbl EXCEPT SELECT q2 FROM int8_tbl ORDER BY q2 LIMIT 1
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "UNRESOLVED_COLUMN.WITH_SUGGESTION",
  "sqlState" : "42703",
  "messageParameters" : {
    "objectName" : "`q2`",
    "proposal" : "`q1`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 65,
    "stopIndex" : 66,
    "fragment" : "q2"
  } ]
}


-- !query
SELECT q1 FROM int8_tbl EXCEPT (((SELECT q2 FROM int8_tbl ORDER BY q2 LIMIT 1))) ORDER BY 1
-- !query schema
struct<q1:bigint>
-- !query output
123
4567890123456789


-- !query
(((((select * from int8_tbl)))))
-- !query schema
struct<q1:bigint,q2:bigint>
-- !query output
123	456
123	4567890123456789
4567890123456789	-4567890123456789
4567890123456789	123
4567890123456789	4567890123456789


-- !query
select * from range(1,5) union select * from range(1,3)
-- !query schema
struct<id:bigint>
-- !query output
1
2
3
4


-- !query
select * from range(1,6) union all select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
1
1
2
2
3
3
4
5


-- !query
select * from range(1,6) intersect select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
1
2
3


-- !query
select * from range(1,6) intersect all select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
1
2
3


-- !query
select * from range(1,6) except select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
4
5


-- !query
select * from range(1,6) except all select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
4
5


-- !query
select * from range(1,6) union select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
1
2
3
4
5


-- !query
select * from range(1,6) union all select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
1
1
2
2
3
3
4
5


-- !query
select * from range(1,6) intersect select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
1
2
3


-- !query
select * from range(1,6) intersect all select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
1
2
3


-- !query
select * from range(1,6) except select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
4
5


-- !query
select * from range(1,6) except all select * from range(1,4)
-- !query schema
struct<id:bigint>
-- !query output
4
5


-- !query
SELECT cast('3.4' as decimal(38, 18)) UNION SELECT 'foo'
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkNumberFormatException
{
  "errorClass" : "CAST_INVALID_INPUT",
  "sqlState" : "22018",
  "messageParameters" : {
    "ansiConfig" : "\"spark.sql.ansi.enabled\"",
    "expression" : "'foo'",
    "sourceType" : "\"STRING\"",
    "targetType" : "\"DOUBLE\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 1,
    "stopIndex" : 56,
    "fragment" : "SELECT cast('3.4' as decimal(38, 18)) UNION SELECT 'foo'"
  } ]
}


-- !query
SELECT * FROM
  (SELECT 1 AS t, 2 AS x
   UNION
   SELECT 2 AS t, 4 AS x) ss
WHERE x < 4
ORDER BY x
-- !query schema
struct<t:int,x:int>
-- !query output
1	2


-- !query
SELECT * FROM
  (SELECT 1 AS t, id as x from range(1,11)
   UNION
   SELECT 2 AS t, 4 AS x) ss
WHERE x < 4
ORDER BY x
-- !query schema
struct<t:int,x:bigint>
-- !query output
1	1
1	2
1	3


-- !query
SELECT * FROM
  (SELECT 1 AS t, int((random()*3)) AS x
   UNION
   SELECT 2 AS t, 4 AS x) ss
WHERE x > 3
ORDER BY x
-- !query schema
struct<t:int,x:int>
-- !query output
2	4


-- !query
select distinct q1 from
  (select distinct * from int8_tbl i81
   union all
   select distinct * from int8_tbl i82) ss
where q2 = q2
-- !query schema
struct<q1:bigint>
-- !query output
123
4567890123456789


-- !query
select distinct q1 from
  (select distinct * from int8_tbl i81
   union all
   select distinct * from int8_tbl i82) ss
where -q1 = q2
-- !query schema
struct<q1:bigint>
-- !query output
4567890123456789


-- !query
select * from
  (select *, 0 as x from int8_tbl a
   union all
   select *, 1 as x from int8_tbl b) ss
where (x = 0) or (q1 >= q2 and q1 <= q2)
-- !query schema
struct<q1:bigint,q2:bigint,x:int>
-- !query output
123	456	0
123	4567890123456789	0
4567890123456789	-4567890123456789	0
4567890123456789	123	0
4567890123456789	4567890123456789	0
4567890123456789	4567890123456789	1
