-- Automatically generated by SQLQueryTestSuite
-- !query
SELECT avg(four) AS avg_1 FROM onek
-- !query schema
struct<avg_1:double>
-- !query output
1.5


-- !query
SELECT avg(a) AS avg_32 FROM aggtest WHERE a < 100
-- !query schema
struct<avg_32:double>
-- !query output
32.666666666666664


-- !query
select CAST(avg(b) AS Decimal(10,3)) AS avg_107_943 FROM aggtest
-- !query schema
struct<avg_107_943:decimal(10,3)>
-- !query output
107.943


-- !query
SELECT sum(four) AS sum_1500 FROM onek
-- !query schema
struct<sum_1500:bigint>
-- !query output
1500


-- !query
SELECT sum(a) AS sum_198 FROM aggtest
-- !query schema
struct<sum_198:bigint>
-- !query output
198


-- !query
SELECT sum(b) AS avg_431_773 FROM aggtest
-- !query schema
struct<avg_431_773:double>
-- !query output
431.77260909229517


-- !query
SELECT max(four) AS max_3 FROM onek
-- !query schema
struct<max_3:int>
-- !query output
3


-- !query
SELECT max(a) AS max_100 FROM aggtest
-- !query schema
struct<max_100:int>
-- !query output
100


-- !query
SELECT max(aggtest.b) AS max_324_78 FROM aggtest
-- !query schema
struct<max_324_78:float>
-- !query output
324.78


-- !query
SELECT stddev_pop(b) FROM aggtest
-- !query schema
struct<stddev_pop(b):double>
-- !query output
131.10703231895047


-- !query
SELECT stddev_samp(b) FROM aggtest
-- !query schema
struct<stddev_samp(b):double>
-- !query output
151.38936080399804


-- !query
SELECT var_pop(b) FROM aggtest
-- !query schema
struct<var_pop(b):double>
-- !query output
17189.053923482323


-- !query
SELECT var_samp(b) FROM aggtest
-- !query schema
struct<var_samp(b):double>
-- !query output
22918.738564643096


-- !query
SELECT stddev_pop(CAST(b AS Decimal(38,0))) FROM aggtest
-- !query schema
struct<stddev_pop(CAST(b AS DECIMAL(38,0))):double>
-- !query output
131.18117242958306


-- !query
SELECT stddev_samp(CAST(b AS Decimal(38,0))) FROM aggtest
-- !query schema
struct<stddev_samp(CAST(b AS DECIMAL(38,0))):double>
-- !query output
151.47497042966097


-- !query
SELECT var_pop(CAST(b AS Decimal(38,0))) FROM aggtest
-- !query schema
struct<var_pop(CAST(b AS DECIMAL(38,0))):double>
-- !query output
17208.5


-- !query
SELECT var_samp(CAST(b AS Decimal(38,0))) FROM aggtest
-- !query schema
struct<var_samp(CAST(b AS DECIMAL(38,0))):double>
-- !query output
22944.666666666668


-- !query
SELECT var_pop(1.0), var_samp(2.0)
-- !query schema
struct<var_pop(1.0):double,var_samp(2.0):double>
-- !query output
0.0	NULL


-- !query
SELECT stddev_pop(CAST(3.0 AS Decimal(38,0))), stddev_samp(CAST(4.0 AS Decimal(38,0)))
-- !query schema
struct<stddev_pop(CAST(3.0 AS DECIMAL(38,0))):double,stddev_samp(CAST(4.0 AS DECIMAL(38,0))):double>
-- !query output
0.0	NULL


-- !query
select sum(CAST(null AS int)) from range(1,4)
-- !query schema
struct<sum(CAST(NULL AS INT)):bigint>
-- !query output
NULL


-- !query
select sum(CAST(null AS long)) from range(1,4)
-- !query schema
struct<sum(CAST(NULL AS BIGINT)):bigint>
-- !query output
NULL


-- !query
select sum(CAST(null AS Decimal(38,0))) from range(1,4)
-- !query schema
struct<sum(CAST(NULL AS DECIMAL(38,0))):decimal(38,0)>
-- !query output
NULL


-- !query
select sum(CAST(null AS DOUBLE)) from range(1,4)
-- !query schema
struct<sum(CAST(NULL AS DOUBLE)):double>
-- !query output
NULL


-- !query
select avg(CAST(null AS int)) from range(1,4)
-- !query schema
struct<avg(CAST(NULL AS INT)):double>
-- !query output
NULL


-- !query
select avg(CAST(null AS long)) from range(1,4)
-- !query schema
struct<avg(CAST(NULL AS BIGINT)):double>
-- !query output
NULL


-- !query
select avg(CAST(null AS Decimal(38,0))) from range(1,4)
-- !query schema
struct<avg(CAST(NULL AS DECIMAL(38,0))):decimal(38,4)>
-- !query output
NULL


-- !query
select avg(CAST(null AS DOUBLE)) from range(1,4)
-- !query schema
struct<avg(CAST(NULL AS DOUBLE)):double>
-- !query output
NULL


-- !query
select sum(CAST('NaN' AS DOUBLE)) from range(1,4)
-- !query schema
struct<sum(CAST(NaN AS DOUBLE)):double>
-- !query output
NaN


-- !query
select avg(CAST('NaN' AS DOUBLE)) from range(1,4)
-- !query schema
struct<avg(CAST(NaN AS DOUBLE)):double>
-- !query output
NaN


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES (CAST('1' AS DOUBLE)), (CAST('infinity' AS DOUBLE))) v(x)
-- !query schema
struct<avg(CAST(x AS DOUBLE)):double,var_pop(CAST(x AS DOUBLE)):double>
-- !query output
Infinity	NaN


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES ('infinity'), ('1')) v(x)
-- !query schema
struct<avg(CAST(x AS DOUBLE)):double,var_pop(CAST(x AS DOUBLE)):double>
-- !query output
Infinity	NaN


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES ('infinity'), ('infinity')) v(x)
-- !query schema
struct<avg(CAST(x AS DOUBLE)):double,var_pop(CAST(x AS DOUBLE)):double>
-- !query output
Infinity	NaN


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES ('-infinity'), ('infinity')) v(x)
-- !query schema
struct<avg(CAST(x AS DOUBLE)):double,var_pop(CAST(x AS DOUBLE)):double>
-- !query output
NaN	NaN


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES (100000003), (100000004), (100000006), (100000007)) v(x)
-- !query schema
struct<avg(CAST(x AS DOUBLE)):double,var_pop(CAST(x AS DOUBLE)):double>
-- !query output
1.00000005E8	2.5


-- !query
SELECT avg(CAST(x AS DOUBLE)), var_pop(CAST(x AS DOUBLE))
FROM (VALUES (7000000000005), (7000000000007)) v(x)
-- !query schema
struct<avg(CAST(x AS DOUBLE)):double,var_pop(CAST(x AS DOUBLE)):double>
-- !query output
7.000000000006E12	1.0


-- !query
SELECT regr_count(b, a) FROM aggtest
-- !query schema
struct<regr_count(b, a):bigint>
-- !query output
4


-- !query
SELECT regr_sxx(b, a) FROM aggtest
-- !query schema
struct<regr_sxx(b, a):double>
-- !query output
5099.0


-- !query
SELECT regr_syy(b, a) FROM aggtest
-- !query schema
struct<regr_syy(b, a):double>
-- !query output
68756.21569392929


-- !query
SELECT regr_sxy(b, a) FROM aggtest
-- !query schema
struct<regr_sxy(b, a):double>
-- !query output
2614.5158215500414


-- !query
SELECT regr_avgx(b, a), regr_avgy(b, a) FROM aggtest
-- !query schema
struct<regr_avgx(b, a):double,regr_avgy(b, a):double>
-- !query output
49.5	107.94315227307379


-- !query
SELECT regr_r2(b, a) FROM aggtest
-- !query schema
struct<regr_r2(b, a):double>
-- !query output
0.019497798203180258


-- !query
SELECT regr_slope(b, a), regr_intercept(b, a) FROM aggtest
-- !query schema
struct<regr_slope(b, a):double,regr_intercept(b, a):double>
-- !query output
0.5127507004412711	82.56199260123087


-- !query
SELECT covar_pop(b, a), covar_samp(b, a) FROM aggtest
-- !query schema
struct<covar_pop(b, a):double,covar_samp(b, a):double>
-- !query output
653.6289553875104	871.5052738500139


-- !query
SELECT corr(b, a) FROM aggtest
-- !query schema
struct<corr(b, a):double>
-- !query output
0.1396345165178734


-- !query
CREATE TEMPORARY VIEW regr_test AS SELECT * FROM VALUES (10,150),(20,250),(30,350),(80,540),(100,200) AS regr_test (x, y)
-- !query schema
struct<>
-- !query output



-- !query
SELECT count(*), sum(x), regr_sxx(y,x), sum(y),regr_syy(y,x), regr_sxy(y,x)
FROM regr_test WHERE x IN (10,20,30,80)
-- !query schema
struct<count(1):bigint,sum(x):bigint,regr_sxx(y, x):double,sum(y):bigint,regr_syy(y, x):double,regr_sxy(y, x):double>
-- !query output
4	140	2900.0	1290	83075.0	15050.0


-- !query
SELECT count(*), sum(x), regr_sxx(y,x), sum(y),regr_syy(y,x), regr_sxy(y,x)
FROM regr_test
-- !query schema
struct<count(1):bigint,sum(x):bigint,regr_sxx(y, x):double,sum(y):bigint,regr_syy(y, x):double,regr_sxy(y, x):double>
-- !query output
5	240	6280.0	1490	95080.0	8680.0


-- !query
SELECT count(*), sum(x), regr_sxx(y,x), sum(y),regr_syy(y,x), regr_sxy(y,x)
FROM regr_test WHERE x IN (10,20,30)
-- !query schema
struct<count(1):bigint,sum(x):bigint,regr_sxx(y, x):double,sum(y):bigint,regr_syy(y, x):double,regr_sxy(y, x):double>
-- !query output
3	60	200.0	750	20000.0	2000.0


-- !query
SELECT count(*), sum(x), regr_sxx(y,x), sum(y),regr_syy(y,x), regr_sxy(y,x)
FROM regr_test WHERE x IN (80,100)
-- !query schema
struct<count(1):bigint,sum(x):bigint,regr_sxx(y, x):double,sum(y):bigint,regr_syy(y, x):double,regr_sxy(y, x):double>
-- !query output
2	180	200.0	740	57800.0	-3400.0


-- !query
DROP VIEW regr_test
-- !query schema
struct<>
-- !query output



-- !query
SELECT count(four) AS cnt_1000 FROM onek
-- !query schema
struct<cnt_1000:bigint>
-- !query output
1000


-- !query
SELECT count(DISTINCT four) AS cnt_4 FROM onek
-- !query schema
struct<cnt_4:bigint>
-- !query output
4


-- !query
select ten, count(*), sum(four) from onek
group by ten order by ten
-- !query schema
struct<ten:int,count(1):bigint,sum(four):bigint>
-- !query output
0	100	100
1	100	200
2	100	100
3	100	200
4	100	100
5	100	200
6	100	100
7	100	200
8	100	100
9	100	200


-- !query
select ten, count(four), sum(DISTINCT four) from onek
group by ten order by ten
-- !query schema
struct<ten:int,count(four):bigint,sum(DISTINCT four):bigint>
-- !query output
0	100	2
1	100	4
2	100	2
3	100	4
4	100	2
5	100	4
6	100	2
7	100	4
8	100	2
9	100	4


-- !query
select ten, sum(distinct four) from onek a
group by ten
having exists (select 1 from onek b where sum(distinct a.four) = b.four)
-- !query schema
struct<ten:int,sum(DISTINCT four):bigint>
-- !query output
0	2
2	2
4	2
6	2
8	2


-- !query
select ten, sum(distinct four) from onek a
group by ten
having exists (select 1 from onek b
               where sum(distinct a.four + b.four) = b.four)
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "UNSUPPORTED_SUBQUERY_EXPRESSION_CATEGORY.AGGREGATE_FUNCTION_MIXED_OUTER_LOCAL_REFERENCES",
  "sqlState" : "0A000",
  "messageParameters" : {
    "function" : "sum(DISTINCT (outer(a.four) + b.four))"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 114,
    "stopIndex" : 142,
    "fragment" : "sum(distinct a.four + b.four)"
  } ]
}


-- !query
select
  (select max((select i.unique2 from tenk1 i where i.unique1 = o.unique1)))
from tenk1 o
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "UNRESOLVED_COLUMN.WITH_SUGGESTION",
  "sqlState" : "42703",
  "messageParameters" : {
    "objectName" : "`o`.`unique1`",
    "proposal" : "`i`.`unique1`, `i`.`unique2`, `i`.`even`, `i`.`four`, `i`.`odd`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 71,
    "stopIndex" : 79,
    "fragment" : "o.unique1"
  } ]
}
