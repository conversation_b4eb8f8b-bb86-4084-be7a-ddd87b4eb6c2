-- Automatically generated by SQLQueryTestSuite
-- !query
CREATE OR REPLACE TEMPORARY VIEW testData AS SELECT * FROM VALUES
(1, 1), (1, 2), (2, 1), (1, 1), (null, 2), (1, null), (null, null)
AS testData(a, b)
-- !query schema
struct<>
-- !query output



-- !query
SELECT
  udf(count(*)), udf(count(1)), udf(count(null)), udf(count(a)), udf(count(b)), udf(count(a + b)), udf(count((a, b)))
FROM testData
-- !query schema
struct<udf(count(1)):bigint,udf(count(1)):bigint,udf(count(NULL)):bigint,udf(count(a)):bigint,udf(count(b)):bigint,udf(count((a + b))):bigint,udf(count(named_struct(a, a, b, b))):bigint>
-- !query output
7	7	0	5	5	4	7


-- !query
SELECT
  udf(count(DISTINCT 1)),
  udf(count(DISTINCT null)),
  udf(count(DISTINCT a)),
  udf(count(DISTINCT b)),
  udf(count(DISTINCT (a + b))),
  udf(count(DISTINCT (a, b)))
FROM testData
-- !query schema
struct<udf(count(DISTINCT 1)):bigint,udf(count(DISTINCT NULL)):bigint,udf(count(DISTINCT a)):bigint,udf(count(DISTINCT b)):bigint,udf(count(DISTINCT (a + b))):bigint,udf(count(DISTINCT named_struct(a, a, b, b))):bigint>
-- !query output
1	0	2	2	2	6


-- !query
SELECT udf(count(a, b)), udf(count(b, a)), udf(count(testData.*, testData.*)) FROM testData
-- !query schema
struct<udf(count(a, b)):bigint,udf(count(b, a)):bigint,udf(count(a, b, a, b)):bigint>
-- !query output
4	4	4


-- !query
SELECT
  udf(count(DISTINCT a, b)), udf(count(DISTINCT b, a)), udf(count(DISTINCT *)), udf(count(DISTINCT testData.*, testData.*))
FROM testData
-- !query schema
struct<udf(count(DISTINCT a, b)):bigint,udf(count(DISTINCT b, a)):bigint,udf(count(DISTINCT a, b)):bigint,udf(count(DISTINCT a, b, a, b)):bigint>
-- !query output
3	3	3	3
