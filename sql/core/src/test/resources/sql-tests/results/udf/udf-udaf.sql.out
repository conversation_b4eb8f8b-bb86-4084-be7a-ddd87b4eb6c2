-- Automatically generated by SQLQueryTestSuite
-- !query
CREATE OR REPLACE TEMPORARY VIEW t1 AS SELECT * FROM VALUES
(1), (2), (3), (4)
as t1(int_col1)
-- !query schema
struct<>
-- !query output



-- !query
CREATE FUNCTION myDoubleAvg AS 'test.org.apache.spark.sql.MyDoubleAvg'
-- !query schema
struct<>
-- !query output



-- !query
SELECT default.myDoubleAvg(udf(int_col1)) as my_avg, udf(default.myDoubleAvg(udf(int_col1))) as my_avg2, udf(default.myDoubleAvg(int_col1)) as my_avg3 from t1
-- !query schema
struct<my_avg:double,my_avg2:double,my_avg3:double>
-- !query output
102.5	102.5	102.5


-- !query
SELECT default.myDoubleAvg(udf(int_col1), udf(3)) as my_avg from t1
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "WRONG_NUM_ARGS.WITHOUT_SUGGESTION",
  "sqlState" : "42605",
  "messageParameters" : {
    "actualNum" : "2",
    "docroot" : "https://spark.apache.org/docs/latest",
    "expectedNum" : "1",
    "functionName" : "`spark_catalog`.`default`.`mydoubleavg`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 49,
    "fragment" : "default.myDoubleAvg(udf(int_col1), udf(3))"
  } ]
}


-- !query
CREATE FUNCTION udaf1 AS 'test.non.existent.udaf'
-- !query schema
struct<>
-- !query output



-- !query
SELECT default.udaf1(udf(int_col1)) as udaf1, udf(default.udaf1(udf(int_col1))) as udaf2, udf(default.udaf1(int_col1)) as udaf3 from t1
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "CANNOT_LOAD_FUNCTION_CLASS",
  "messageParameters" : {
    "className" : "test.non.existent.udaf",
    "functionName" : "`spark_catalog`.`default`.`udaf1`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 95,
    "stopIndex" : 117,
    "fragment" : "default.udaf1(int_col1)"
  } ]
}


-- !query
DROP FUNCTION myDoubleAvg
-- !query schema
struct<>
-- !query output



-- !query
DROP FUNCTION udaf1
-- !query schema
struct<>
-- !query output

