-- Automatically generated by SQLQueryTestSuite
-- !query
CREATE OR REPLACE TEMPORARY VIEW ilike_all_table AS SELECT * FROM (VALUES
  ('gOOgle', '%oo%'),
  ('facebook', '%OO%'),
  ('liNkedin', '%In'))
  as t1(company, pat)
-- !query schema
struct<>
-- !query output



-- !query
SELECT company FROM ilike_all_table WHERE company ILIKE ALL ('%oO%', '%Go%')
-- !query schema
struct<company:string>
-- !query output
gOOgle


-- !query
SELECT company FROM ilike_all_table WHERE company ILIKE ALL ('microsoft', '%yoo%')
-- !query schema
struct<company:string>
-- !query output



-- !query
SELECT 
    company,
    CASE
        WHEN company ILIKE ALL ('%oo%', '%GO%') THEN 'Y'
        ELSE 'N'
    END AS is_available,
    CASE
        WHEN company ILIKE ALL ('%OO%', 'go%') OR company ILIKE ALL ('%IN', 'ms%') THEN 'Y'
        ELSE 'N'
    END AS mix
FROM ilike_all_table
-- !query schema
struct<company:string,is_available:string,mix:string>
-- !query output
facebook	N	N
gOOgle	Y	Y
liNkedin	N	N


-- !query
SELECT company FROM ilike_all_table WHERE company ILIKE ALL ('%oo%', pat)
-- !query schema
struct<company:string>
-- !query output
facebook
gOOgle


-- !query
SELECT company FROM ilike_all_table WHERE company NOT ILIKE ALL ('%oo%', '%In', 'Fa%')
-- !query schema
struct<company:string>
-- !query output



-- !query
SELECT company FROM ilike_all_table WHERE company NOT ILIKE ALL ('microsoft', '%yoo%')
-- !query schema
struct<company:string>
-- !query output
facebook
gOOgle
liNkedin


-- !query
SELECT company FROM ilike_all_table WHERE company NOT ILIKE ALL ('%oo%', 'fA%')
-- !query schema
struct<company:string>
-- !query output
liNkedin


-- !query
SELECT company FROM ilike_all_table WHERE NOT company ILIKE ALL ('%oO%', 'fa%')
-- !query schema
struct<company:string>
-- !query output
gOOgle
liNkedin


-- !query
SELECT company FROM ilike_all_table WHERE company ILIKE ALL ('%OO%', NULL)
-- !query schema
struct<company:string>
-- !query output



-- !query
SELECT company FROM ilike_all_table WHERE company NOT ILIKE ALL ('%Oo%', NULL)
-- !query schema
struct<company:string>
-- !query output



-- !query
SELECT company FROM ilike_all_table WHERE company ILIKE ALL (NULL, NULL)
-- !query schema
struct<company:string>
-- !query output



-- !query
SELECT company FROM ilike_all_table WHERE company NOT ILIKE ALL (NULL, NULL)
-- !query schema
struct<company:string>
-- !query output



-- !query
SELECT company FROM ilike_any_table WHERE company ILIKE ALL ()
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "_LEGACY_ERROR_TEMP_0064",
  "messageParameters" : {
    "msg" : "Expected something between '(' and ')'."
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 51,
    "stopIndex" : 62,
    "fragment" : "ILIKE ALL ()"
  } ]
}
