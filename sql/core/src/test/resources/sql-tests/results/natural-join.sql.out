-- Automatically generated by SQLQueryTestSuite
-- !query
create temporary view nt1 as select * from values
  ("one", 1),
  ("two", 2),
  ("three", 3)
  as nt1(k, v1)
-- !query schema
struct<>
-- !query output



-- !query
create temporary view nt2 as select * from values
  ("one", 1),
  ("two", 22),
  ("one", 5)
  as nt2(k, v2)
-- !query schema
struct<>
-- !query output



-- !query
create temporary view nt3 as select * from values
  ("one", 4),
  ("two", 5),
  ("one", 6)
  as nt3(k, v3)
-- !query schema
struct<>
-- !query output



-- !query
create temporary view nt4 as select * from values
  ("one", 7),
  ("two", 8),
  ("one", 9)
  as nt4(k, v4)
-- !query schema
struct<>
-- !query output



-- !query
SELECT * FROM nt1 natural join nt2
-- !query schema
struct<k:string,v1:int,v2:int>
-- !query output
one	1	1
one	1	5
two	2	22


-- !query
SELECT * FROM nt1 natural join nt2 where k = "one"
-- !query schema
struct<k:string,v1:int,v2:int>
-- !query output
one	1	1
one	1	5


-- !query
SELECT * FROM nt1 natural left join nt2 order by v1, v2
-- !query schema
struct<k:string,v1:int,v2:int>
-- !query output
one	1	1
one	1	5
two	2	22
three	3	NULL


-- !query
SELECT * FROM nt1 natural right join nt2 order by v1, v2
-- !query schema
struct<k:string,v1:int,v2:int>
-- !query output
one	1	1
one	1	5
two	2	22


-- !query
SELECT count(*) FROM nt1 natural full outer join nt2
-- !query schema
struct<count(1):bigint>
-- !query output
4


-- !query
SELECT k FROM nt1 natural join nt2
-- !query schema
struct<k:string>
-- !query output
one
one
two


-- !query
SELECT k FROM nt1 natural join nt2 where k = "one"
-- !query schema
struct<k:string>
-- !query output
one
one


-- !query
SELECT nt1.* FROM nt1 natural join nt2
-- !query schema
struct<k:string,v1:int>
-- !query output
one	1
one	1
two	2


-- !query
SELECT nt2.* FROM nt1 natural join nt2
-- !query schema
struct<k:string,v2:int>
-- !query output
one	1
one	5
two	22


-- !query
SELECT sbq.* from (SELECT * FROM nt1 natural join nt2) sbq
-- !query schema
struct<k:string,v1:int,v2:int>
-- !query output
one	1	1
one	1	5
two	2	22


-- !query
SELECT sbq.k from (SELECT * FROM nt1 natural join nt2) sbq
-- !query schema
struct<k:string>
-- !query output
one
one
two


-- !query
SELECT nt1.*, nt2.* FROM nt1 natural join nt2
-- !query schema
struct<k:string,v1:int,k:string,v2:int>
-- !query output
one	1	one	1
one	1	one	5
two	2	two	22


-- !query
SELECT *, nt2.k FROM nt1 natural join nt2
-- !query schema
struct<k:string,v1:int,v2:int,k:string>
-- !query output
one	1	1	one
one	1	5	one
two	2	22	two


-- !query
SELECT nt1.k, nt2.k FROM nt1 natural join nt2
-- !query schema
struct<k:string,k:string>
-- !query output
one	one
one	one
two	two


-- !query
SELECT k FROM (SELECT nt2.k FROM nt1 natural join nt2)
-- !query schema
struct<k:string>
-- !query output
one
one
two


-- !query
SELECT nt2.k AS key FROM nt1 natural join nt2 ORDER BY key
-- !query schema
struct<key:string>
-- !query output
one
one
two


-- !query
SELECT nt1.k, nt2.k FROM nt1 natural join nt2 where k = "one"
-- !query schema
struct<k:string,k:string>
-- !query output
one	one
one	one


-- !query
SELECT * FROM (SELECT * FROM nt1 natural join nt2)
-- !query schema
struct<k:string,v1:int,v2:int>
-- !query output
one	1	1
one	1	5
two	2	22


-- !query
SELECT * FROM (SELECT nt1.*, nt2.* FROM nt1 natural join nt2)
-- !query schema
struct<k:string,v1:int,k:string,v2:int>
-- !query output
one	1	one	1
one	1	one	5
two	2	two	22


-- !query
SELECT * FROM (SELECT nt1.v1, nt2.k FROM nt1 natural join nt2)
-- !query schema
struct<v1:int,k:string>
-- !query output
1	one
1	one
2	two


-- !query
SELECT nt2.k FROM (SELECT * FROM nt1 natural join nt2)
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "UNRESOLVED_COLUMN.WITH_SUGGESTION",
  "sqlState" : "42703",
  "messageParameters" : {
    "objectName" : "`nt2`.`k`",
    "proposal" : "`k`, `v1`, `v2`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 12,
    "fragment" : "nt2.k"
  } ]
}


-- !query
SELECT * FROM nt1 natural join nt2 natural join nt3
-- !query schema
struct<k:string,v1:int,v2:int,v3:int>
-- !query output
one	1	1	4
one	1	1	6
one	1	5	4
one	1	5	6
two	2	22	5


-- !query
SELECT nt1.*, nt2.*, nt3.* FROM nt1 natural join nt2 natural join nt3
-- !query schema
struct<k:string,v1:int,k:string,v2:int,k:string,v3:int>
-- !query output
one	1	one	1	one	4
one	1	one	1	one	6
one	1	one	5	one	4
one	1	one	5	one	6
two	2	two	22	two	5


-- !query
SELECT nt1.*, nt2.*, nt3.* FROM nt1 natural join nt2 join nt3 on nt2.k = nt3.k
-- !query schema
struct<k:string,v1:int,k:string,v2:int,k:string,v3:int>
-- !query output
one	1	one	1	one	4
one	1	one	1	one	6
one	1	one	5	one	4
one	1	one	5	one	6
two	2	two	22	two	5


-- !query
SELECT * FROM nt1 natural join nt2 join nt3 on nt1.k = nt3.k
-- !query schema
struct<k:string,v1:int,v2:int,k:string,v3:int>
-- !query output
one	1	1	one	4
one	1	1	one	6
one	1	5	one	4
one	1	5	one	6
two	2	22	two	5


-- !query
SELECT * FROM nt1 natural join nt2 join nt3 on nt2.k = nt3.k
-- !query schema
struct<k:string,v1:int,v2:int,k:string,v3:int>
-- !query output
one	1	1	one	4
one	1	1	one	6
one	1	5	one	4
one	1	5	one	6
two	2	22	two	5


-- !query
SELECT nt1.*, nt2.*, nt3.*, nt4.* FROM nt1 natural join nt2 natural join nt3 natural join nt4
-- !query schema
struct<k:string,v1:int,k:string,v2:int,k:string,v3:int,k:string,v4:int>
-- !query output
one	1	one	1	one	4	one	7
one	1	one	1	one	4	one	9
one	1	one	1	one	6	one	7
one	1	one	1	one	6	one	9
one	1	one	5	one	4	one	7
one	1	one	5	one	4	one	9
one	1	one	5	one	6	one	7
one	1	one	5	one	6	one	9
two	2	two	22	two	5	two	8
