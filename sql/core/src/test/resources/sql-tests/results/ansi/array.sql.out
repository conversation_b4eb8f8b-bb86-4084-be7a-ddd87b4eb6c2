-- Automatically generated by SQLQueryTestSuite
-- !query
create temporary view data as select * from values
  ("one", array(11, 12, 13), array(array(111, 112, 113), array(121, 122, 123))),
  ("two", array(21, 22, 23), array(array(211, 212, 213), array(221, 222, 223)))
  as data(a, b, c)
-- !query schema
struct<>
-- !query output



-- !query
select * from data
-- !query schema
struct<a:string,b:array<int>,c:array<array<int>>>
-- !query output
one	[11,12,13]	[[111,112,113],[121,122,123]]
two	[21,22,23]	[[211,212,213],[221,222,223]]


-- !query
select a, b[0], b[0] + b[1] from data
-- !query schema
struct<a:string,b[0]:int,(b[0] + b[1]):int>
-- !query output
one	11	23
two	21	43


-- !query
select a, c[0][0] + c[0][0 + 1] from data
-- !query schema
struct<a:string,(c[0][0] + c[0][(0 + 1)]):int>
-- !query output
one	223
two	423


-- !query
create temporary view primitive_arrays as select * from values (
  array(true),
  array(2Y, 1Y),
  array(2S, 1S),
  array(2, 1),
  array(2L, 1L),
  array(9223372036854775809, 9223372036854775808),
  array(2.0D, 1.0D),
  array(float(2.0), float(1.0)),
  array(date '2016-03-14', date '2016-03-13'),
  array(timestamp '2016-11-15 20:54:00.000',  timestamp '2016-11-12 20:54:00.000')
) as primitive_arrays(
  boolean_array,
  tinyint_array,
  smallint_array,
  int_array,
  bigint_array,
  decimal_array,
  double_array,
  float_array,
  date_array,
  timestamp_array
)
-- !query schema
struct<>
-- !query output



-- !query
select * from primitive_arrays
-- !query schema
struct<boolean_array:array<boolean>,tinyint_array:array<tinyint>,smallint_array:array<smallint>,int_array:array<int>,bigint_array:array<bigint>,decimal_array:array<decimal(19,0)>,double_array:array<double>,float_array:array<float>,date_array:array<date>,timestamp_array:array<timestamp>>
-- !query output
[true]	[2,1]	[2,1]	[2,1]	[2,1]	[9223372036854775809,9223372036854775808]	[2.0,1.0]	[2.0,1.0]	[2016-03-14,2016-03-13]	[2016-11-15 20:54:00,2016-11-12 20:54:00]


-- !query
select
  array_contains(boolean_array, true), array_contains(boolean_array, false),
  array_contains(tinyint_array, 2Y), array_contains(tinyint_array, 0Y),
  array_contains(smallint_array, 2S), array_contains(smallint_array, 0S),
  array_contains(int_array, 2), array_contains(int_array, 0),
  array_contains(bigint_array, 2L), array_contains(bigint_array, 0L),
  array_contains(decimal_array, 9223372036854775809), array_contains(decimal_array, 1),
  array_contains(double_array, 2.0D), array_contains(double_array, 0.0D),
  array_contains(float_array, float(2.0)), array_contains(float_array, float(0.0)),
  array_contains(date_array, date '2016-03-14'), array_contains(date_array, date '2016-01-01'),
  array_contains(timestamp_array, timestamp '2016-11-15 20:54:00.000'), array_contains(timestamp_array, timestamp '2016-01-01 20:54:00.000')
from primitive_arrays
-- !query schema
struct<array_contains(boolean_array, true):boolean,array_contains(boolean_array, false):boolean,array_contains(tinyint_array, 2):boolean,array_contains(tinyint_array, 0):boolean,array_contains(smallint_array, 2):boolean,array_contains(smallint_array, 0):boolean,array_contains(int_array, 2):boolean,array_contains(int_array, 0):boolean,array_contains(bigint_array, 2):boolean,array_contains(bigint_array, 0):boolean,array_contains(decimal_array, 9223372036854775809):boolean,array_contains(decimal_array, 1):boolean,array_contains(double_array, 2.0):boolean,array_contains(double_array, 0.0):boolean,array_contains(float_array, 2.0):boolean,array_contains(float_array, 0.0):boolean,array_contains(date_array, DATE '2016-03-14'):boolean,array_contains(date_array, DATE '2016-01-01'):boolean,array_contains(timestamp_array, TIMESTAMP '2016-11-15 20:54:00'):boolean,array_contains(timestamp_array, TIMESTAMP '2016-01-01 20:54:00'):boolean>
-- !query output
true	false	true	false	true	false	true	false	true	false	true	false	true	false	true	false	true	false	true	false


-- !query
select array_contains(b, 11), array_contains(c, array(111, 112, 113)) from data
-- !query schema
struct<array_contains(b, 11):boolean,array_contains(c, array(111, 112, 113)):boolean>
-- !query output
false	false
true	true


-- !query
select
  sort_array(boolean_array),
  sort_array(tinyint_array),
  sort_array(smallint_array),
  sort_array(int_array),
  sort_array(bigint_array),
  sort_array(decimal_array),
  sort_array(double_array),
  sort_array(float_array),
  sort_array(date_array),
  sort_array(timestamp_array)
from primitive_arrays
-- !query schema
struct<sort_array(boolean_array, true):array<boolean>,sort_array(tinyint_array, true):array<tinyint>,sort_array(smallint_array, true):array<smallint>,sort_array(int_array, true):array<int>,sort_array(bigint_array, true):array<bigint>,sort_array(decimal_array, true):array<decimal(19,0)>,sort_array(double_array, true):array<double>,sort_array(float_array, true):array<float>,sort_array(date_array, true):array<date>,sort_array(timestamp_array, true):array<timestamp>>
-- !query output
[true]	[1,2]	[1,2]	[1,2]	[1,2]	[9223372036854775808,9223372036854775809]	[1.0,2.0]	[1.0,2.0]	[2016-03-13,2016-03-14]	[2016-11-12 20:54:00,2016-11-15 20:54:00]


-- !query
select sort_array(array('b', 'd'), '1')
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"1\"",
    "inputType" : "\"STRING\"",
    "paramIndex" : "2",
    "requiredType" : "\"BOOLEAN\"",
    "sqlExpr" : "\"sort_array(array(b, d), 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 39,
    "fragment" : "sort_array(array('b', 'd'), '1')"
  } ]
}


-- !query
select sort_array(array('b', 'd'), cast(NULL as boolean))
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(NULL AS BOOLEAN)\"",
    "inputType" : "\"BOOLEAN\"",
    "paramIndex" : "2",
    "requiredType" : "\"BOOLEAN\"",
    "sqlExpr" : "\"sort_array(array(b, d), CAST(NULL AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 57,
    "fragment" : "sort_array(array('b', 'd'), cast(NULL as boolean))"
  } ]
}


-- !query
select
  size(boolean_array),
  size(tinyint_array),
  size(smallint_array),
  size(int_array),
  size(bigint_array),
  size(decimal_array),
  size(double_array),
  size(float_array),
  size(date_array),
  size(timestamp_array)
from primitive_arrays
-- !query schema
struct<size(boolean_array):int,size(tinyint_array):int,size(smallint_array):int,size(int_array):int,size(bigint_array):int,size(decimal_array):int,size(double_array):int,size(float_array):int,size(date_array):int,size(timestamp_array):int>
-- !query output
1	2	2	2	2	2	2	2	2	2


-- !query
select element_at(array(1, 2, 3), 5)
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkArrayIndexOutOfBoundsException
{
  "errorClass" : "INVALID_ARRAY_INDEX_IN_ELEMENT_AT",
  "sqlState" : "22003",
  "messageParameters" : {
    "ansiConfig" : "\"spark.sql.ansi.enabled\"",
    "arraySize" : "3",
    "indexValue" : "5"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 36,
    "fragment" : "element_at(array(1, 2, 3), 5)"
  } ]
}


-- !query
select element_at(array(1, 2, 3), -5)
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkArrayIndexOutOfBoundsException
{
  "errorClass" : "INVALID_ARRAY_INDEX_IN_ELEMENT_AT",
  "sqlState" : "22003",
  "messageParameters" : {
    "ansiConfig" : "\"spark.sql.ansi.enabled\"",
    "arraySize" : "3",
    "indexValue" : "-5"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 37,
    "fragment" : "element_at(array(1, 2, 3), -5)"
  } ]
}


-- !query
select element_at(array(1, 2, 3), 0)
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkRuntimeException
{
  "errorClass" : "INVALID_INDEX_OF_ZERO",
  "sqlState" : "22003",
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 36,
    "fragment" : "element_at(array(1, 2, 3), 0)"
  } ]
}


-- !query
select elt(4, '123', '456')
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkArrayIndexOutOfBoundsException
{
  "errorClass" : "INVALID_ARRAY_INDEX",
  "sqlState" : "22003",
  "messageParameters" : {
    "ansiConfig" : "\"spark.sql.ansi.enabled\"",
    "arraySize" : "2",
    "indexValue" : "4"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 27,
    "fragment" : "elt(4, '123', '456')"
  } ]
}


-- !query
select elt(0, '123', '456')
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkArrayIndexOutOfBoundsException
{
  "errorClass" : "INVALID_ARRAY_INDEX",
  "sqlState" : "22003",
  "messageParameters" : {
    "ansiConfig" : "\"spark.sql.ansi.enabled\"",
    "arraySize" : "2",
    "indexValue" : "0"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 27,
    "fragment" : "elt(0, '123', '456')"
  } ]
}


-- !query
select elt(-1, '123', '456')
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkArrayIndexOutOfBoundsException
{
  "errorClass" : "INVALID_ARRAY_INDEX",
  "sqlState" : "22003",
  "messageParameters" : {
    "ansiConfig" : "\"spark.sql.ansi.enabled\"",
    "arraySize" : "2",
    "indexValue" : "-1"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 28,
    "fragment" : "elt(-1, '123', '456')"
  } ]
}


-- !query
select elt(null, '123', '456')
-- !query schema
struct<elt(NULL, 123, 456):string>
-- !query output
NULL


-- !query
select elt(null, '123', null)
-- !query schema
struct<elt(NULL, 123, NULL):string>
-- !query output
NULL


-- !query
select elt(1, '123', null)
-- !query schema
struct<elt(1, 123, NULL):string>
-- !query output
123


-- !query
select elt(2, '123', null)
-- !query schema
struct<elt(2, 123, NULL):string>
-- !query output
NULL


-- !query
select array(1, 2, 3)[5]
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkArrayIndexOutOfBoundsException
{
  "errorClass" : "INVALID_ARRAY_INDEX",
  "sqlState" : "22003",
  "messageParameters" : {
    "ansiConfig" : "\"spark.sql.ansi.enabled\"",
    "arraySize" : "3",
    "indexValue" : "5"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 24,
    "fragment" : "array(1, 2, 3)[5]"
  } ]
}


-- !query
select array(1, 2, 3)[-1]
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkArrayIndexOutOfBoundsException
{
  "errorClass" : "INVALID_ARRAY_INDEX",
  "sqlState" : "22003",
  "messageParameters" : {
    "ansiConfig" : "\"spark.sql.ansi.enabled\"",
    "arraySize" : "3",
    "indexValue" : "-1"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 25,
    "fragment" : "array(1, 2, 3)[-1]"
  } ]
}


-- !query
select array_size(array())
-- !query schema
struct<array_size(array()):int>
-- !query output
0


-- !query
select array_size(array(true))
-- !query schema
struct<array_size(array(true)):int>
-- !query output
1


-- !query
select array_size(array(2, 1))
-- !query schema
struct<array_size(array(2, 1)):int>
-- !query output
2


-- !query
select array_size(NULL)
-- !query schema
struct<array_size(NULL):int>
-- !query output
NULL


-- !query
select array_size(map('a', 1, 'b', 2))
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"map(a, 1, b, 2)\"",
    "inputType" : "\"MAP<STRING, INT>\"",
    "paramIndex" : "1",
    "requiredType" : "\"ARRAY\"",
    "sqlExpr" : "\"array_size(map(a, 1, b, 2))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 38,
    "fragment" : "array_size(map('a', 1, 'b', 2))"
  } ]
}


-- !query
select size(arrays_zip(array(1, 2, 3), array(4), array(7, 8, 9, 10)))
-- !query schema
struct<size(arrays_zip(array(1, 2, 3), array(4), array(7, 8, 9, 10))):int>
-- !query output
4


-- !query
select size(arrays_zip(array(), array(1, 2, 3), array(4), array(7, 8, 9, 10)))
-- !query schema
struct<size(arrays_zip(array(), array(1, 2, 3), array(4), array(7, 8, 9, 10))):int>
-- !query output
4


-- !query
select size(arrays_zip(array(1, 2, 3), array(4), null, array(7, 8, 9, 10)))
-- !query schema
struct<size(arrays_zip(array(1, 2, 3), array(4), NULL, array(7, 8, 9, 10))):int>
-- !query output
NULL


-- !query
select isnotnull(arrays_zip(array(), array(4), array(7, 8, 9, 10)))
-- !query schema
struct<(arrays_zip(array(), array(4), array(7, 8, 9, 10)) IS NOT NULL):boolean>
-- !query output
true


-- !query
select isnotnull(arrays_zip(array(1, 2, 3), array(4), array(7, 8, 9, 10)))
-- !query schema
struct<(arrays_zip(array(1, 2, 3), array(4), array(7, 8, 9, 10)) IS NOT NULL):boolean>
-- !query output
true


-- !query
select isnotnull(arrays_zip(array(1, 2, 3), NULL, array(4), array(7, 8, 9, 10)))
-- !query schema
struct<(arrays_zip(array(1, 2, 3), NULL, array(4), array(7, 8, 9, 10)) IS NOT NULL):boolean>
-- !query output
false


-- !query
select get(array(1, 2, 3), 0)
-- !query schema
struct<get(array(1, 2, 3), 0):int>
-- !query output
1


-- !query
select get(array(1, 2, 3), 3)
-- !query schema
struct<get(array(1, 2, 3), 3):int>
-- !query output
NULL


-- !query
select get(array(1, 2, 3), null)
-- !query schema
struct<get(array(1, 2, 3), NULL):int>
-- !query output
NULL


-- !query
select get(array(1, 2, 3), -1)
-- !query schema
struct<get(array(1, 2, 3), -1):int>
-- !query output
NULL


-- !query
select array_insert(array(1, 2, 3), 3, 4)
-- !query schema
struct<array_insert(array(1, 2, 3), 3, 4):array<int>>
-- !query output
[1,2,4,3]


-- !query
select array_insert(array(2, 3, 4), 0, 1)
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkRuntimeException
{
  "errorClass" : "INVALID_INDEX_OF_ZERO",
  "sqlState" : "22003",
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 41,
    "fragment" : "array_insert(array(2, 3, 4), 0, 1)"
  } ]
}


-- !query
select array_insert(array(2, 3, 4), 1, 1)
-- !query schema
struct<array_insert(array(2, 3, 4), 1, 1):array<int>>
-- !query output
[1,2,3,4]


-- !query
select array_insert(array(1, 3, 4), -2, 2)
-- !query schema
struct<array_insert(array(1, 3, 4), -2, 2):array<int>>
-- !query output
[1,2,3,4]


-- !query
select array_insert(array(1, 2, 3), 3, "4")
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.ARRAY_FUNCTION_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "dataType" : "\"ARRAY\"",
    "functionName" : "`array_insert`",
    "leftType" : "\"ARRAY<INT>\"",
    "rightType" : "\"STRING\"",
    "sqlExpr" : "\"array_insert(array(1, 2, 3), 3, 4)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 43,
    "fragment" : "array_insert(array(1, 2, 3), 3, \"4\")"
  } ]
}


-- !query
select array_insert(cast(NULL as ARRAY<INT>), 1, 1)
-- !query schema
struct<array_insert(NULL, 1, 1):array<int>>
-- !query output
NULL


-- !query
select array_insert(array(1, 2, 3, NULL), cast(NULL as INT), 4)
-- !query schema
struct<array_insert(array(1, 2, 3, NULL), CAST(NULL AS INT), 4):array<int>>
-- !query output
NULL


-- !query
select array_insert(array(1, 2, 3, NULL), 4, cast(NULL as INT))
-- !query schema
struct<array_insert(array(1, 2, 3, NULL), 4, CAST(NULL AS INT)):array<int>>
-- !query output
[1,2,3,null,null]


-- !query
select array_insert(array(2, 3, NULL, 4), 5, 5)
-- !query schema
struct<array_insert(array(2, 3, NULL, 4), 5, 5):array<int>>
-- !query output
[2,3,null,4,5]


-- !query
select array_insert(array(2, 3, NULL, 4), -5, 1)
-- !query schema
struct<array_insert(array(2, 3, NULL, 4), -5, 1):array<int>>
-- !query output
[1,null,2,3,null,4]


-- !query
select array_compact(id) from values (1) as t(id)
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"id\"",
    "inputType" : "\"INT\"",
    "paramIndex" : "1",
    "requiredType" : "\"ARRAY\"",
    "sqlExpr" : "\"array_compact(id)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 24,
    "fragment" : "array_compact(id)"
  } ]
}


-- !query
select array_compact(array("1", null, "2", null))
-- !query schema
struct<array_compact(array(1, NULL, 2, NULL)):array<string>>
-- !query output
["1","2"]


-- !query
select array_compact(array("a", "b", "c"))
-- !query schema
struct<array_compact(array(a, b, c)):array<string>>
-- !query output
["a","b","c"]


-- !query
select array_compact(array(1D, null, 2D, null))
-- !query schema
struct<array_compact(array(1.0, NULL, 2.0, NULL)):array<double>>
-- !query output
[1.0,2.0]


-- !query
select array_compact(array(array(1, 2, 3, null), null, array(4, null, 6)))
-- !query schema
struct<array_compact(array(array(1, 2, 3, NULL), NULL, array(4, NULL, 6))):array<array<int>>>
-- !query output
[[1,2,3,null],[4,null,6]]


-- !query
select array_compact(array(null))
-- !query schema
struct<array_compact(array(NULL)):array<void>>
-- !query output
[]


-- !query
select array_append(array(1, 2, 3), 4)
-- !query schema
struct<array_append(array(1, 2, 3), 4):array<int>>
-- !query output
[1,2,3,4]


-- !query
select array_append(array('a', 'b', 'c'), 'd')
-- !query schema
struct<array_append(array(a, b, c), d):array<string>>
-- !query output
["a","b","c","d"]


-- !query
select array_append(array(1, 2, 3, NULL), NULL)
-- !query schema
struct<array_append(array(1, 2, 3, NULL), NULL):array<int>>
-- !query output
[1,2,3,null,null]


-- !query
select array_append(array('a', 'b', 'c', NULL), NULL)
-- !query schema
struct<array_append(array(a, b, c, NULL), NULL):array<string>>
-- !query output
["a","b","c",null,null]


-- !query
select array_append(CAST(null AS ARRAY<String>), 'a')
-- !query schema
struct<array_append(NULL, a):array<string>>
-- !query output
NULL


-- !query
select array_append(CAST(null AS ARRAY<String>), CAST(null as String))
-- !query schema
struct<array_append(NULL, CAST(NULL AS STRING)):array<string>>
-- !query output
NULL


-- !query
select array_append(array(), 1)
-- !query schema
struct<array_append(array(), 1):array<int>>
-- !query output
[1]


-- !query
select array_append(CAST(array() AS ARRAY<String>), CAST(NULL AS String))
-- !query schema
struct<array_append(array(), CAST(NULL AS STRING)):array<string>>
-- !query output
[null]


-- !query
select array_append(array(CAST(NULL AS String)), CAST(NULL AS String))
-- !query schema
struct<array_append(array(CAST(NULL AS STRING)), CAST(NULL AS STRING)):array<string>>
-- !query output
[null,null]


-- !query
select array_prepend(array(1, 2, 3), 4)
-- !query schema
struct<array_prepend(array(1, 2, 3), 4):array<int>>
-- !query output
[4,1,2,3]


-- !query
select array_prepend(array('a', 'b', 'c'), 'd')
-- !query schema
struct<array_prepend(array(a, b, c), d):array<string>>
-- !query output
["d","a","b","c"]


-- !query
select array_prepend(array(1, 2, 3, NULL), NULL)
-- !query schema
struct<array_prepend(array(1, 2, 3, NULL), NULL):array<int>>
-- !query output
[null,1,2,3,null]


-- !query
select array_prepend(array('a', 'b', 'c', NULL), NULL)
-- !query schema
struct<array_prepend(array(a, b, c, NULL), NULL):array<string>>
-- !query output
[null,"a","b","c",null]


-- !query
select array_prepend(CAST(null AS ARRAY<String>), 'a')
-- !query schema
struct<array_prepend(NULL, a):array<string>>
-- !query output
NULL


-- !query
select array_prepend(CAST(null AS ARRAY<String>), CAST(null as String))
-- !query schema
struct<array_prepend(NULL, CAST(NULL AS STRING)):array<string>>
-- !query output
NULL


-- !query
select array_prepend(array(), 1)
-- !query schema
struct<array_prepend(array(), 1):array<int>>
-- !query output
[1]


-- !query
select array_prepend(CAST(array() AS ARRAY<String>), CAST(NULL AS String))
-- !query schema
struct<array_prepend(array(), CAST(NULL AS STRING)):array<string>>
-- !query output
[null]


-- !query
select array_prepend(array(CAST(NULL AS String)), CAST(NULL AS String))
-- !query schema
struct<array_prepend(array(CAST(NULL AS STRING)), CAST(NULL AS STRING)):array<string>>
-- !query output
[null,null]
