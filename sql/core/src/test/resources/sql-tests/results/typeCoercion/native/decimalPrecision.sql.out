-- Automatically generated by SQLQueryTestSuite
-- !query
CREATE TEMPORARY VIEW t AS SELECT 1
-- !query schema
struct<>
-- !query output



-- !query
SELECT cast(1 as tinyint) + cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) + CAST(1 AS DECIMAL(3,0))):decimal(4,0)>
-- !query output
2


-- !query
SELECT cast(1 as tinyint) + cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) + CAST(1 AS DECIMAL(5,0))):decimal(6,0)>
-- !query output
2


-- !query
SELECT cast(1 as tinyint) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) + CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as tinyint) + cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) + CAST(1 AS DECIMAL(20,0))):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as smallint) + cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) + CAST(1 AS DECIMAL(3,0))):decimal(6,0)>
-- !query output
2


-- !query
SELECT cast(1 as smallint) + cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) + CAST(1 AS DECIMAL(5,0))):decimal(6,0)>
-- !query output
2


-- !query
SELECT cast(1 as smallint) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) + CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as smallint) + cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) + CAST(1 AS DECIMAL(20,0))):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as int) + cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) + CAST(1 AS DECIMAL(3,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as int) + cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) + CAST(1 AS DECIMAL(5,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as int) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) + CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as int) + cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) + CAST(1 AS DECIMAL(20,0))):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as bigint) + cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) + CAST(1 AS DECIMAL(3,0))):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as bigint) + cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) + CAST(1 AS DECIMAL(5,0))):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as bigint) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) + CAST(1 AS DECIMAL(10,0))):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as bigint) + cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) + CAST(1 AS DECIMAL(20,0))):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as float) + cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) + CAST(1 AS DECIMAL(3,0))):double>
-- !query output
2.0


-- !query
SELECT cast(1 as float) + cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) + CAST(1 AS DECIMAL(5,0))):double>
-- !query output
2.0


-- !query
SELECT cast(1 as float) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) + CAST(1 AS DECIMAL(10,0))):double>
-- !query output
2.0


-- !query
SELECT cast(1 as float) + cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) + CAST(1 AS DECIMAL(20,0))):double>
-- !query output
2.0


-- !query
SELECT cast(1 as double) + cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) + CAST(1 AS DECIMAL(3,0))):double>
-- !query output
2.0


-- !query
SELECT cast(1 as double) + cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) + CAST(1 AS DECIMAL(5,0))):double>
-- !query output
2.0


-- !query
SELECT cast(1 as double) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) + CAST(1 AS DECIMAL(10,0))):double>
-- !query output
2.0


-- !query
SELECT cast(1 as double) + cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) + CAST(1 AS DECIMAL(20,0))):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DECIMAL(3,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DECIMAL(5,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DECIMAL(20,0))):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast('1' as binary) + cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) + CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) + cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) + cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) + CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) + cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) + CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) + cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) + cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) + CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) + cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) + CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) + CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) + CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) + CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) + cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) + cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS TINYINT)):decimal(4,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS TINYINT)):decimal(6,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS TINYINT)):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS TINYINT)):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS SMALLINT)):decimal(6,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS SMALLINT)):decimal(6,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS SMALLINT)):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS SMALLINT)):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS INT)):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS INT)):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS INT)):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS INT)):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS BIGINT)):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS BIGINT)):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS BIGINT)):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS BIGINT)):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS FLOAT)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS FLOAT)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS FLOAT)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS FLOAT)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS DOUBLE)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS DOUBLE)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DOUBLE)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS DOUBLE)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS DECIMAL(10,0))):decimal(21,0)>
-- !query output
2


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS STRING)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS STRING)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS STRING)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS STRING)):double>
-- !query output
2.0


-- !query
SELECT cast(1 as decimal(3, 0))  + cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  + cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  + cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  + cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) + cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) + cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) + cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) + cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  + cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) + CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  + cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  + cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) + CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  + cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) + cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) + CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) + cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) + cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) + CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) + cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  + cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) + CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  + cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  + cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) + CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  + cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) + cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) + CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) + cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) + cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) + CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) + cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  + cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  + cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  + cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  + cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) + cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) + cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) + cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_add(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) + cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) - cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) - CAST(1 AS DECIMAL(3,0))):decimal(4,0)>
-- !query output
0


-- !query
SELECT cast(1 as tinyint) - cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) - CAST(1 AS DECIMAL(5,0))):decimal(6,0)>
-- !query output
0


-- !query
SELECT cast(1 as tinyint) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) - CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as tinyint) - cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) - CAST(1 AS DECIMAL(20,0))):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as smallint) - cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) - CAST(1 AS DECIMAL(3,0))):decimal(6,0)>
-- !query output
0


-- !query
SELECT cast(1 as smallint) - cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) - CAST(1 AS DECIMAL(5,0))):decimal(6,0)>
-- !query output
0


-- !query
SELECT cast(1 as smallint) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) - CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as smallint) - cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) - CAST(1 AS DECIMAL(20,0))):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as int) - cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) - CAST(1 AS DECIMAL(3,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as int) - cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) - CAST(1 AS DECIMAL(5,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as int) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) - CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as int) - cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) - CAST(1 AS DECIMAL(20,0))):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as bigint) - cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) - CAST(1 AS DECIMAL(3,0))):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as bigint) - cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) - CAST(1 AS DECIMAL(5,0))):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as bigint) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) - CAST(1 AS DECIMAL(10,0))):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as bigint) - cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) - CAST(1 AS DECIMAL(20,0))):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as float) - cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) - CAST(1 AS DECIMAL(3,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as float) - cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) - CAST(1 AS DECIMAL(5,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as float) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) - CAST(1 AS DECIMAL(10,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as float) - cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) - CAST(1 AS DECIMAL(20,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as double) - cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) - CAST(1 AS DECIMAL(3,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as double) - cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) - CAST(1 AS DECIMAL(5,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as double) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) - CAST(1 AS DECIMAL(10,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as double) - cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) - CAST(1 AS DECIMAL(20,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DECIMAL(3,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DECIMAL(5,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DECIMAL(20,0))):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast('1' as binary) - cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) - CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) - cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) - cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) - CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) - cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) - CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) - cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) - cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) - CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) - cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "2",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) - CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "2",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) - CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "2",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) - CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "2",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) - CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) - cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_sub(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_sub(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_sub(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "2",
    "requiredType" : "(\"INT\" or \"SMALLINT\" or \"TINYINT\")",
    "sqlExpr" : "\"date_sub(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) - cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS TINYINT)):decimal(4,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS TINYINT)):decimal(6,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS TINYINT)):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS TINYINT)):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS SMALLINT)):decimal(6,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS SMALLINT)):decimal(6,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS SMALLINT)):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS SMALLINT)):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS INT)):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS INT)):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS INT)):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS INT)):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS BIGINT)):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS BIGINT)):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS BIGINT)):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS BIGINT)):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS DECIMAL(10,0))):decimal(11,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS DECIMAL(10,0))):decimal(21,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(3, 0))  - cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  - cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  - cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  - cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) - cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) - cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) - cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) - cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  - cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) - CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  - cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  - cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) - CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  - cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) - cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) - CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) - cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) - cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) - CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) - cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  - cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) - CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  - cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  - cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) - CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  - cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) - cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) - CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) - cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) - cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) - CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) - cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  - cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(3,0))\"",
    "inputType" : "\"DECIMAL(3,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) - CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  - cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  - cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(5,0))\"",
    "inputType" : "\"DECIMAL(5,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) - CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  - cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) - cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(10,0))\"",
    "inputType" : "\"DECIMAL(10,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) - CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) - cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) - cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"CAST(1 AS DECIMAL(20,0))\"",
    "inputType" : "\"DECIMAL(20,0)\"",
    "paramIndex" : "1",
    "requiredType" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) - CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) - cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) * cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) * CAST(1 AS DECIMAL(3,0))):decimal(7,0)>
-- !query output
1


-- !query
SELECT cast(1 as tinyint) * cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) * CAST(1 AS DECIMAL(5,0))):decimal(9,0)>
-- !query output
1


-- !query
SELECT cast(1 as tinyint) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) * CAST(1 AS DECIMAL(10,0))):decimal(14,0)>
-- !query output
1


-- !query
SELECT cast(1 as tinyint) * cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) * CAST(1 AS DECIMAL(20,0))):decimal(24,0)>
-- !query output
1


-- !query
SELECT cast(1 as smallint) * cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) * CAST(1 AS DECIMAL(3,0))):decimal(9,0)>
-- !query output
1


-- !query
SELECT cast(1 as smallint) * cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) * CAST(1 AS DECIMAL(5,0))):decimal(11,0)>
-- !query output
1


-- !query
SELECT cast(1 as smallint) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) * CAST(1 AS DECIMAL(10,0))):decimal(16,0)>
-- !query output
1


-- !query
SELECT cast(1 as smallint) * cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) * CAST(1 AS DECIMAL(20,0))):decimal(26,0)>
-- !query output
1


-- !query
SELECT cast(1 as int) * cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) * CAST(1 AS DECIMAL(3,0))):decimal(14,0)>
-- !query output
1


-- !query
SELECT cast(1 as int) * cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) * CAST(1 AS DECIMAL(5,0))):decimal(16,0)>
-- !query output
1


-- !query
SELECT cast(1 as int) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) * CAST(1 AS DECIMAL(10,0))):decimal(21,0)>
-- !query output
1


-- !query
SELECT cast(1 as int) * cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) * CAST(1 AS DECIMAL(20,0))):decimal(31,0)>
-- !query output
1


-- !query
SELECT cast(1 as bigint) * cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) * CAST(1 AS DECIMAL(3,0))):decimal(24,0)>
-- !query output
1


-- !query
SELECT cast(1 as bigint) * cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) * CAST(1 AS DECIMAL(5,0))):decimal(26,0)>
-- !query output
1


-- !query
SELECT cast(1 as bigint) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) * CAST(1 AS DECIMAL(10,0))):decimal(31,0)>
-- !query output
1


-- !query
SELECT cast(1 as bigint) * cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) * CAST(1 AS DECIMAL(20,0))):decimal(38,0)>
-- !query output
1


-- !query
SELECT cast(1 as float) * cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) * CAST(1 AS DECIMAL(3,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as float) * cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) * CAST(1 AS DECIMAL(5,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as float) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) * CAST(1 AS DECIMAL(10,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as float) * cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) * CAST(1 AS DECIMAL(20,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as double) * cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) * CAST(1 AS DECIMAL(3,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as double) * cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) * CAST(1 AS DECIMAL(5,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as double) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) * CAST(1 AS DECIMAL(10,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as double) * cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) * CAST(1 AS DECIMAL(20,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DECIMAL(3,0))):decimal(14,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DECIMAL(5,0))):decimal(16,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DECIMAL(10,0))):decimal(21,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DECIMAL(20,0))):decimal(31,0)>
-- !query output
1


-- !query
SELECT cast('1' as binary) * cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) * CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) * cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) * cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) * CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) * cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) * CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) * cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) * cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) * CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) * cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00.0 AS TIMESTAMP) * CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00.0 AS TIMESTAMP) * CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00.0 AS TIMESTAMP) * CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00.0 AS TIMESTAMP) * CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017*12*11 09:30:00.0' as timestamp) * cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00 AS DATE) * CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00 AS DATE) * CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00 AS DATE) * CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017*12*11 09:30:00 AS DATE) * CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017*12*11 09:30:00' as date) * cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS TINYINT)):decimal(7,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS TINYINT)):decimal(9,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS TINYINT)):decimal(14,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS TINYINT)):decimal(24,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS SMALLINT)):decimal(9,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS SMALLINT)):decimal(11,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS SMALLINT)):decimal(16,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS SMALLINT)):decimal(26,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS INT)):decimal(14,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS INT)):decimal(16,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS INT)):decimal(21,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS INT)):decimal(31,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS BIGINT)):decimal(24,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS BIGINT)):decimal(26,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS BIGINT)):decimal(31,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS BIGINT)):decimal(38,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS FLOAT)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS FLOAT)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS FLOAT)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS FLOAT)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS DOUBLE)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS DOUBLE)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DOUBLE)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS DOUBLE)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS DECIMAL(10,0))):decimal(14,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS DECIMAL(10,0))):decimal(16,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS DECIMAL(10,0))):decimal(21,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS DECIMAL(10,0))):decimal(31,0)>
-- !query output
1


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS STRING)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS STRING)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS STRING)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS STRING)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(3, 0))  * cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  * cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  * cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  * cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) * cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) * cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) * cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) * cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  * cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) * CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  * cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  * cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) * CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  * cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) * cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) * CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) * cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) * cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) * CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) * cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  * cast('2017*12*11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) * CAST(2017*12*11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  * cast('2017*12*11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  * cast('2017*12*11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) * CAST(2017*12*11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  * cast('2017*12*11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) * cast('2017*12*11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) * CAST(2017*12*11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) * cast('2017*12*11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) * cast('2017*12*11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) * CAST(2017*12*11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) * cast('2017*12*11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  * cast('2017*12*11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) * CAST(2017*12*11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  * cast('2017*12*11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  * cast('2017*12*11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) * CAST(2017*12*11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  * cast('2017*12*11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) * cast('2017*12*11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) * CAST(2017*12*11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) * cast('2017*12*11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) * cast('2017*12*11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) * CAST(2017*12*11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) * cast('2017*12*11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) / cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) / CAST(1 AS DECIMAL(3,0))):decimal(9,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as tinyint) / cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) / CAST(1 AS DECIMAL(5,0))):decimal(9,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as tinyint) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) / CAST(1 AS DECIMAL(10,0))):decimal(14,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as tinyint) / cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) / CAST(1 AS DECIMAL(20,0))):decimal(24,21)>
-- !query output
1.000000000000000000000


-- !query
SELECT cast(1 as smallint) / cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) / CAST(1 AS DECIMAL(3,0))):decimal(11,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as smallint) / cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) / CAST(1 AS DECIMAL(5,0))):decimal(11,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as smallint) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) / CAST(1 AS DECIMAL(10,0))):decimal(16,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as smallint) / cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) / CAST(1 AS DECIMAL(20,0))):decimal(26,21)>
-- !query output
1.000000000000000000000


-- !query
SELECT cast(1 as int) / cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) / CAST(1 AS DECIMAL(3,0))):decimal(16,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as int) / cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) / CAST(1 AS DECIMAL(5,0))):decimal(16,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as int) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) / CAST(1 AS DECIMAL(10,0))):decimal(21,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as int) / cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) / CAST(1 AS DECIMAL(20,0))):decimal(31,21)>
-- !query output
1.000000000000000000000


-- !query
SELECT cast(1 as bigint) / cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) / CAST(1 AS DECIMAL(3,0))):decimal(26,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as bigint) / cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) / CAST(1 AS DECIMAL(5,0))):decimal(26,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as bigint) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) / CAST(1 AS DECIMAL(10,0))):decimal(31,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as bigint) / cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) / CAST(1 AS DECIMAL(20,0))):decimal(38,18)>
-- !query output
1.000000000000000000


-- !query
SELECT cast(1 as float) / cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) / CAST(1 AS DECIMAL(3,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as float) / cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) / CAST(1 AS DECIMAL(5,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as float) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) / CAST(1 AS DECIMAL(10,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as float) / cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) / CAST(1 AS DECIMAL(20,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as double) / cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) / CAST(1 AS DECIMAL(3,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as double) / cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) / CAST(1 AS DECIMAL(5,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as double) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) / CAST(1 AS DECIMAL(10,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as double) / cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) / CAST(1 AS DECIMAL(20,0))):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DECIMAL(3,0))):decimal(16,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DECIMAL(5,0))):decimal(16,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DECIMAL(10,0))):decimal(21,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DECIMAL(20,0))):decimal(31,21)>
-- !query output
1.000000000000000000000


-- !query
SELECT cast('1' as binary) / cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) / CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) / cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) / cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) / CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) / cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) / CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) / cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) / cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) / CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) / cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00.0 AS TIMESTAMP) / CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00.0 AS TIMESTAMP) / CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00.0 AS TIMESTAMP) / CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00.0 AS TIMESTAMP) / CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017/12/11 09:30:00.0' as timestamp) / cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00 AS DATE) / CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00 AS DATE) / CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00 AS DATE) / CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017/12/11 09:30:00 AS DATE) / CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017/12/11 09:30:00' as date) / cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS TINYINT)):decimal(9,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS TINYINT)):decimal(11,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS TINYINT)):decimal(16,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS TINYINT)):decimal(26,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS SMALLINT)):decimal(9,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS SMALLINT)):decimal(11,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS SMALLINT)):decimal(16,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS SMALLINT)):decimal(26,6)>
-- !query output
1.000000


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS INT)):decimal(14,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS INT)):decimal(16,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS INT)):decimal(21,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS INT)):decimal(31,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS BIGINT)):decimal(24,21)>
-- !query output
1.000000000000000000000


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS BIGINT)):decimal(26,21)>
-- !query output
1.000000000000000000000


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS BIGINT)):decimal(31,21)>
-- !query output
1.000000000000000000000


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS BIGINT)):decimal(38,18)>
-- !query output
1.000000000000000000


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS FLOAT)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS FLOAT)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS FLOAT)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS FLOAT)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS DOUBLE)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS DOUBLE)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DOUBLE)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS DOUBLE)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS DECIMAL(10,0))):decimal(14,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS DECIMAL(10,0))):decimal(16,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS DECIMAL(10,0))):decimal(21,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS DECIMAL(10,0))):decimal(31,11)>
-- !query output
1.00000000000


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS STRING)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS STRING)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS STRING)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS STRING)):double>
-- !query output
1.0


-- !query
SELECT cast(1 as decimal(3, 0))  / cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  / cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  / cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  / cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) / cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) / cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) / cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) / cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  / cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) / CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  / cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  / cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) / CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  / cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) / cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) / CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) / cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) / cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) / CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) / cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  / cast('2017/12/11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) / CAST(2017/12/11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  / cast('2017/12/11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  / cast('2017/12/11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) / CAST(2017/12/11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  / cast('2017/12/11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) / cast('2017/12/11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) / CAST(2017/12/11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) / cast('2017/12/11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) / cast('2017/12/11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) / CAST(2017/12/11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) / cast('2017/12/11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  / cast('2017/12/11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) / CAST(2017/12/11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  / cast('2017/12/11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  / cast('2017/12/11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) / CAST(2017/12/11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  / cast('2017/12/11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) / cast('2017/12/11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) / CAST(2017/12/11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) / cast('2017/12/11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) / cast('2017/12/11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) / CAST(2017/12/11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) / cast('2017/12/11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) % cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) % CAST(1 AS DECIMAL(3,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as tinyint) % cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) % CAST(1 AS DECIMAL(5,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as tinyint) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) % CAST(1 AS DECIMAL(10,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as tinyint) % cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) % CAST(1 AS DECIMAL(20,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as smallint) % cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) % CAST(1 AS DECIMAL(3,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as smallint) % cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) % CAST(1 AS DECIMAL(5,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as smallint) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) % CAST(1 AS DECIMAL(10,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as smallint) % cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) % CAST(1 AS DECIMAL(20,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as int) % cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) % CAST(1 AS DECIMAL(3,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as int) % cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) % CAST(1 AS DECIMAL(5,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as int) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) % CAST(1 AS DECIMAL(10,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT cast(1 as int) % cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) % CAST(1 AS DECIMAL(20,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT cast(1 as bigint) % cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) % CAST(1 AS DECIMAL(3,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as bigint) % cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) % CAST(1 AS DECIMAL(5,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as bigint) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) % CAST(1 AS DECIMAL(10,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT cast(1 as bigint) % cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) % CAST(1 AS DECIMAL(20,0))):decimal(20,0)>
-- !query output
0


-- !query
SELECT cast(1 as float) % cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) % CAST(1 AS DECIMAL(3,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as float) % cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) % CAST(1 AS DECIMAL(5,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as float) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) % CAST(1 AS DECIMAL(10,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as float) % cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) % CAST(1 AS DECIMAL(20,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as double) % cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) % CAST(1 AS DECIMAL(3,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as double) % cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) % CAST(1 AS DECIMAL(5,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as double) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) % CAST(1 AS DECIMAL(10,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as double) % cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) % CAST(1 AS DECIMAL(20,0))):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DECIMAL(3,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DECIMAL(5,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DECIMAL(10,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DECIMAL(20,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT cast('1' as binary) % cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) % CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) % cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) % cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) % CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) % cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) % CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) % cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) % cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) % CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) % cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) % CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) % CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) % CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) % CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) % cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) % CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) % CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) % CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) % CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) % cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS TINYINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS TINYINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS TINYINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS TINYINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS SMALLINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS SMALLINT)):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS SMALLINT)):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS SMALLINT)):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS INT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS INT)):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS INT)):decimal(10,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS INT)):decimal(10,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS BIGINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS BIGINT)):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS BIGINT)):decimal(10,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS BIGINT)):decimal(20,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS DECIMAL(10,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS DECIMAL(10,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS DECIMAL(10,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS DECIMAL(10,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT cast(1 as decimal(3, 0))  % cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  % cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  % cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  % cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) % cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) % cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) % cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) % cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  % cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) % CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  % cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  % cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) % CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  % cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) % cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) % CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) % cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) % cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) % CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) % cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  % cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) % CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  % cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  % cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) % CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  % cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) % cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) % CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) % cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) % cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) % CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) % cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  % cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) % CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  % cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  % cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) % CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  % cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) % cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) % CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) % cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) % cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) % CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) % cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT pmod(cast(1 as tinyint), cast(1 as decimal(3, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS TINYINT), CAST(1 AS DECIMAL(3,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as tinyint), cast(1 as decimal(5, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS TINYINT), CAST(1 AS DECIMAL(5,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as tinyint), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS TINYINT), CAST(1 AS DECIMAL(10,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as tinyint), cast(1 as decimal(20, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS TINYINT), CAST(1 AS DECIMAL(20,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as smallint), cast(1 as decimal(3, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS SMALLINT), CAST(1 AS DECIMAL(3,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as smallint), cast(1 as decimal(5, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS SMALLINT), CAST(1 AS DECIMAL(5,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as smallint), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS SMALLINT), CAST(1 AS DECIMAL(10,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as smallint), cast(1 as decimal(20, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS SMALLINT), CAST(1 AS DECIMAL(20,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as int), cast(1 as decimal(3, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS INT), CAST(1 AS DECIMAL(3,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as int), cast(1 as decimal(5, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS INT), CAST(1 AS DECIMAL(5,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as int), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS INT), CAST(1 AS DECIMAL(10,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as int), cast(1 as decimal(20, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS INT), CAST(1 AS DECIMAL(20,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as bigint), cast(1 as decimal(3, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS BIGINT), CAST(1 AS DECIMAL(3,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as bigint), cast(1 as decimal(5, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS BIGINT), CAST(1 AS DECIMAL(5,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as bigint), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS BIGINT), CAST(1 AS DECIMAL(10,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as bigint), cast(1 as decimal(20, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS BIGINT), CAST(1 AS DECIMAL(20,0))):decimal(20,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as float), cast(1 as decimal(3, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS FLOAT), CAST(1 AS DECIMAL(3,0))):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as float), cast(1 as decimal(5, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS FLOAT), CAST(1 AS DECIMAL(5,0))):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as float), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS FLOAT), CAST(1 AS DECIMAL(10,0))):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as float), cast(1 as decimal(20, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS FLOAT), CAST(1 AS DECIMAL(20,0))):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as double), cast(1 as decimal(3, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DOUBLE), CAST(1 AS DECIMAL(3,0))):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as double), cast(1 as decimal(5, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DOUBLE), CAST(1 AS DECIMAL(5,0))):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as double), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DOUBLE), CAST(1 AS DECIMAL(10,0))):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as double), cast(1 as decimal(20, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DOUBLE), CAST(1 AS DECIMAL(20,0))):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as decimal(3, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DECIMAL(3,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as decimal(5, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DECIMAL(5,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DECIMAL(10,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as decimal(20, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DECIMAL(20,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT pmod(cast('1' as binary), cast(1 as decimal(3, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"pmod(CAST(1 AS BINARY), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast('1' as binary), cast(1 as decimal(3, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('1' as binary), cast(1 as decimal(5, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"pmod(CAST(1 AS BINARY), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast('1' as binary), cast(1 as decimal(5, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('1' as binary), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"pmod(CAST(1 AS BINARY), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast('1' as binary), cast(1 as decimal(10, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('1' as binary), cast(1 as decimal(20, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"pmod(CAST(1 AS BINARY), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast('1' as binary), cast(1 as decimal(20, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(3, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 81,
    "fragment" : "pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(3, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(5, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 81,
    "fragment" : "pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(5, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(10, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(20, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast('2017-12-11 09:30:00.0' as timestamp), cast(1 as decimal(20, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(3, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 74,
    "fragment" : "pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(3, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(5, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 74,
    "fragment" : "pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(5, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(10, 0)))"
  } ]
}


-- !query
SELECT pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(20, 0))) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"pmod(CAST(2017-12-11 09:30:00 AS DATE), CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast('2017-12-11 09:30:00' as date), cast(1 as decimal(20, 0)))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as tinyint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS TINYINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as tinyint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS TINYINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as tinyint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS TINYINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as tinyint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS TINYINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as smallint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS SMALLINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as smallint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS SMALLINT)):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as smallint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS SMALLINT)):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as smallint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS SMALLINT)):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as int)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS INT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as int)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS INT)):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as int)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS INT)):decimal(10,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as int)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS INT)):decimal(10,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as bigint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS BIGINT)):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as bigint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS BIGINT)):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as bigint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS BIGINT)):decimal(10,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as bigint)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS BIGINT)):decimal(20,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as float)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as float)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as float)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as float)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS FLOAT)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as double)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as double)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as double)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as double)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS DOUBLE)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS DECIMAL(10,0))):decimal(3,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS DECIMAL(10,0))):decimal(5,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS DECIMAL(10,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as decimal(10, 0))) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS DECIMAL(10,0))):decimal(10,0)>
-- !query output
0


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as string)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as string)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as string)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as string)) FROM t
-- !query schema
struct<pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS STRING)):double>
-- !query output
0.0


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast('1' as binary)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast(1 as decimal(3, 0)) , cast('1' as binary))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast('1' as binary)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast(1 as decimal(5, 0)) , cast('1' as binary))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast('1' as binary)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast(1 as decimal(10, 0)), cast('1' as binary))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast('1' as binary)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "pmod(cast(1 as decimal(20, 0)), cast('1' as binary))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast(1 as boolean)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(3,0)), CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast(1 as decimal(3, 0)) , cast(1 as boolean))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast(1 as boolean)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(5,0)), CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast(1 as decimal(5, 0)) , cast(1 as boolean))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast(1 as boolean)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(10,0)), CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast(1 as decimal(10, 0)), cast(1 as boolean))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast(1 as boolean)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(20,0)), CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 58,
    "fragment" : "pmod(cast(1 as decimal(20, 0)), cast(1 as boolean))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast('2017-12-11 09:30:00.0' as timestamp)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(3,0)), CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast(1 as decimal(3, 0)) , cast('2017-12-11 09:30:00.0' as timestamp))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast('2017-12-11 09:30:00.0' as timestamp)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(5,0)), CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast(1 as decimal(5, 0)) , cast('2017-12-11 09:30:00.0' as timestamp))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast('2017-12-11 09:30:00.0' as timestamp)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(10,0)), CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast(1 as decimal(10, 0)), cast('2017-12-11 09:30:00.0' as timestamp))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast('2017-12-11 09:30:00.0' as timestamp)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(20,0)), CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 82,
    "fragment" : "pmod(cast(1 as decimal(20, 0)), cast('2017-12-11 09:30:00.0' as timestamp))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(3, 0)) , cast('2017-12-11 09:30:00' as date)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(3,0)), CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast(1 as decimal(3, 0)) , cast('2017-12-11 09:30:00' as date))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(5, 0)) , cast('2017-12-11 09:30:00' as date)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(5,0)), CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast(1 as decimal(5, 0)) , cast('2017-12-11 09:30:00' as date))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(10, 0)), cast('2017-12-11 09:30:00' as date)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(10,0)), CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast(1 as decimal(10, 0)), cast('2017-12-11 09:30:00' as date))"
  } ]
}


-- !query
SELECT pmod(cast(1 as decimal(20, 0)), cast('2017-12-11 09:30:00' as date)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"pmod(CAST(1 AS DECIMAL(20,0)), CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 75,
    "fragment" : "pmod(cast(1 as decimal(20, 0)), cast('2017-12-11 09:30:00' as date))"
  } ]
}


-- !query
SELECT cast(1 as tinyint) = cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) = cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) = cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) = cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) = cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) = cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) = cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) = CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) = cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) = CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) = cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) = CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) = cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) = cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) = cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) = cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) = cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) = cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) = cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) = cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) = cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast('1' as binary) = cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) = cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) = cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) = cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) = cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) = cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) = cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) = cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) = cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  = cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  = cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  = cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  = cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) = cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) = cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) = cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) = cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  = cast(1 as boolean) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BOOLEAN)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  = cast(1 as boolean) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BOOLEAN)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) = cast(1 as boolean) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BOOLEAN)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) = cast(1 as boolean) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BOOLEAN)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  = cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  = cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  = cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  = cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) = cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) = cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) = cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) = cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  = cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  = cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  = cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  = cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) = cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) = cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) = cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) = cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) <=> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) <=> CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) <=> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) <=> CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) <=> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) <=> CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) <=> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) <=> CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) <=> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) <=> CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) <=> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) <=> CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) <=> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) <=> CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) <=> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) <=> CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) <=> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) <=> CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) <=> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) <=> CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) <=> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) <=> CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) <=> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) <=> CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) <=> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) <=> CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) <=> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) <=> CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) <=> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) <=> CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) <=> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) <=> CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) <=> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) <=> CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) <=> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) <=> CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast('1' as binary) <=> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <=> CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <=> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <=> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <=> CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <=> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <=> CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast('1' as binary) <=> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <=> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <=> CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast('1' as binary) <=> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <=> CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <=> CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <=> CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <=> CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <=> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <=> CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <=> CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <=> CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <=> CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <=> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast(1 as decimal(3, 0))  <=> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast(1 as decimal(5, 0))  <=> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast(1 as decimal(10, 0)) <=> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 56,
    "fragment" : "cast(1 as decimal(20, 0)) <=> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast(1 as boolean) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <=> CAST(1 AS BOOLEAN)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast(1 as boolean) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <=> CAST(1 AS BOOLEAN)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast(1 as boolean) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <=> CAST(1 AS BOOLEAN)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast(1 as boolean) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <=> CAST(1 AS BOOLEAN)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <=> CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast(1 as decimal(3, 0))  <=> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <=> CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast(1 as decimal(5, 0))  <=> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <=> CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast(1 as decimal(10, 0)) <=> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <=> CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 79,
    "fragment" : "cast(1 as decimal(20, 0)) <=> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <=> cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <=> CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast(1 as decimal(3, 0))  <=> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <=> cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <=> CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast(1 as decimal(5, 0))  <=> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <=> cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <=> CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast(1 as decimal(10, 0)) <=> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <=> cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <=> CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 72,
    "fragment" : "cast(1 as decimal(20, 0)) <=> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) < cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) < CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as tinyint) < cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) < CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as tinyint) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as tinyint) < cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) < CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) < cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) < CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) < cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) < CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) < cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) < CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) < cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) < CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) < cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) < CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) < cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) < CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) < cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) < CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) < cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) < CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) < cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) < CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) < cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) < CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) < cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) < CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) < cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) < CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) < cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) < CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) < cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) < CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) < cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) < CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast('1' as binary) < cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) < CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) < cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) < cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) < CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) < cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) < CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) < cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) < cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) < CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) < cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) < CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) < CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) < CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) < CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) < cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) < CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) < CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) < CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) < CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) < cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS TINYINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS TINYINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS TINYINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS TINYINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS SMALLINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS SMALLINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS SMALLINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS SMALLINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS INT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS INT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS INT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS INT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS BIGINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS BIGINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS BIGINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS BIGINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS FLOAT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS FLOAT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS FLOAT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS FLOAT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS DOUBLE)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS DOUBLE)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DOUBLE)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS DOUBLE)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS STRING)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS STRING)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS STRING)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS STRING)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  < cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  < cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  < cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  < cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) < cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) < cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) < cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) < cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  < cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) < CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  < cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  < cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) < CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  < cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) < cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) < CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) < cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) < cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) < CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) < cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  < cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) < CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  < cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  < cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) < CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  < cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) < cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) < CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) < cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) < cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) < CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) < cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  < cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) < CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  < cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  < cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) < CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  < cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) < cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) < CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) < cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) < cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) < CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) < cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) <= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) <= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) <= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) <= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) <= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) <= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) <= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) <= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) <= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) <= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) <= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) <= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) <= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) <= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) <= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) <= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) <= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) <= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) <= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) <= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) <= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) <= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) <= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) <= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) <= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) <= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) <= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) <= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) <= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) <= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) <= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) <= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) <= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) <= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) <= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) <= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast('1' as binary) <= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) <= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) <= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) <= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) <= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) <= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(3, 0))  <= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(5, 0))  <= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(10, 0)) <= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(20, 0)) <= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  <= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  <= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) <= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) <= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(3, 0))  <= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(5, 0))  <= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(10, 0)) <= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(20, 0)) <= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <= cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) <= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(3, 0))  <= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <= cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) <= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(5, 0))  <= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <= cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) <= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(10, 0)) <= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <= cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) <= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(20, 0)) <= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) > cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) > CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as tinyint) > cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) > CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as tinyint) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as tinyint) > cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) > CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) > cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) > CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) > cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) > CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) > cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) > CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) > cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) > CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) > cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) > CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) > cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) > CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) > cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) > CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) > cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) > CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) > cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) > CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) > cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) > CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) > cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) > CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) > cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) > CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) > cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) > CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) > cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) > CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) > cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) > CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
false


-- !query
SELECT cast('1' as binary) > cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) > CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) > cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) > cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) > CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast('1' as binary) > cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) > CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) > cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) > cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) > CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) > cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) > CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) > CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 76,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) > CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) > CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) > cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) > CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) > CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 69,
    "fragment" : "cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) > CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) > CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) > cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS TINYINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS TINYINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS TINYINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS TINYINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS SMALLINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS SMALLINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS SMALLINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS SMALLINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS INT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS INT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS INT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS INT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS BIGINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS BIGINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS BIGINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS BIGINT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS FLOAT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS FLOAT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS FLOAT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS FLOAT)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS DOUBLE)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS DOUBLE)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DOUBLE)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS DOUBLE)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS STRING)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS STRING)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS STRING)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS STRING)):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  > cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  > cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  > cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  > cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) > cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) > cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) > cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) > cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  > cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) > CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(3, 0))  > cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  > cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) > CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(5, 0))  > cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) > cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) > CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(10, 0)) > cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) > cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) > CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 53,
    "fragment" : "cast(1 as decimal(20, 0)) > cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  > cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) > CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(3, 0))  > cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  > cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) > CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(5, 0))  > cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) > cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) > CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(10, 0)) > cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) > cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) > CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast(1 as decimal(20, 0)) > cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  > cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) > CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(3, 0))  > cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  > cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) > CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(5, 0))  > cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) > cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) > CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(10, 0)) > cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) > cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) > CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast(1 as decimal(20, 0)) > cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) >= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) >= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) >= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) >= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as tinyint) >= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS TINYINT) >= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) >= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) >= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) >= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) >= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as smallint) >= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS SMALLINT) >= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) >= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) >= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) >= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) >= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as int) >= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS INT) >= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) >= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) >= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) >= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) >= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as bigint) >= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS BIGINT) >= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) >= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) >= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) >= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) >= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as float) >= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS FLOAT) >= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) >= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) >= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) >= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) >= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as double) >= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DOUBLE) >= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DECIMAL(3,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DECIMAL(5,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DECIMAL(20,0))):boolean>
-- !query output
true


-- !query
SELECT cast('1' as binary) >= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) >= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) >= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) >= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) >= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) >= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) >= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) >= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) >= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) >= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) >= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) >= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) >= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) >= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) >= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) >= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) >= CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) >= CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) >= CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) >= CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) >= cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as tinyint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS TINYINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as smallint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS SMALLINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as int) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS INT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as bigint) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS BIGINT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as float) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS FLOAT)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as double) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS DOUBLE)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS DECIMAL(10,0))):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as string) FROM t
-- !query schema
struct<(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS STRING)):boolean>
-- !query output
true


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(3, 0))  >= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(5, 0))  >= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(10, 0)) >= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(20, 0)) >= cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) >= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(3, 0))  >= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) >= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(5, 0))  >= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) >= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(10, 0)) >= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast(1 as boolean) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BOOLEAN\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) >= CAST(1 AS BOOLEAN))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast(1 as decimal(20, 0)) >= cast(1 as boolean)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) >= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(3, 0))  >= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) >= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(5, 0))  >= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) >= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(10, 0)) >= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) >= CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(20, 0)) >= cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  >= cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) >= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(3, 0))  >= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  >= cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) >= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(5, 0))  >= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) >= cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) >= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(10, 0)) >= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) >= cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) >= CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(20, 0)) >= cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as tinyint) <> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(3,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as tinyint) <> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(5,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as tinyint) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as tinyint) <> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS TINYINT) = CAST(1 AS DECIMAL(20,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) <> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(3,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) <> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(5,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as smallint) <> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS SMALLINT) = CAST(1 AS DECIMAL(20,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) <> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS INT) = CAST(1 AS DECIMAL(3,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) <> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS INT) = CAST(1 AS DECIMAL(5,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS INT) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as int) <> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS INT) = CAST(1 AS DECIMAL(20,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) <> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(3,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) <> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(5,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as bigint) <> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS BIGINT) = CAST(1 AS DECIMAL(20,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) <> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(3,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) <> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(5,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as float) <> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS FLOAT) = CAST(1 AS DECIMAL(20,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) <> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(3,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) <> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(5,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as double) <> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DOUBLE) = CAST(1 AS DECIMAL(20,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(3,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(5,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(20,0)))):boolean>
-- !query output
false


-- !query
SELECT cast('1' as binary) <> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) <> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 54,
    "fragment" : "cast('1' as binary) <> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('1' as binary) <> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"BINARY\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(1 AS BINARY) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast('1' as binary) <> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 77,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00.0 AS TIMESTAMP) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast('2017-12-11 09:30:00.0' as timestamp) <> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(3, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(3,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(3,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(3, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(5, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(5,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(5,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 70,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(5, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(10,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(10,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(10, 0))"
  } ]
}


-- !query
SELECT cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(20, 0)) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DATE\"",
    "right" : "\"DECIMAL(20,0)\"",
    "sqlExpr" : "\"(CAST(2017-12-11 09:30:00 AS DATE) = CAST(1 AS DECIMAL(20,0)))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast('2017-12-11 09:30:00' as date) <> cast(1 as decimal(20, 0))"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as tinyint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS TINYINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as tinyint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS TINYINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as tinyint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS TINYINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as tinyint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS TINYINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as smallint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS SMALLINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as smallint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS SMALLINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as smallint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS SMALLINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as smallint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS SMALLINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as int) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS INT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as int) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS INT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as int) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS INT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as int) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS INT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as bigint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BIGINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as bigint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BIGINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as bigint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BIGINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as bigint) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BIGINT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as float) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS FLOAT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as float) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS FLOAT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as float) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS FLOAT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as float) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS FLOAT))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as double) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS DOUBLE))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as double) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS DOUBLE))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as double) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DOUBLE))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as double) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS DOUBLE))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as decimal(10, 0)) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS DECIMAL(10,0)))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as string) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS STRING))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as string) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS STRING))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as string) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS STRING))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as string) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS STRING))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(3, 0))  <> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(5, 0))  <> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(10, 0)) <> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast('1' as binary) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"BINARY\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BINARY))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "cast(1 as decimal(20, 0)) <> cast('1' as binary)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast(1 as boolean) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(3,0)) = CAST(1 AS BOOLEAN))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast(1 as boolean) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(5,0)) = CAST(1 AS BOOLEAN))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast(1 as boolean) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(10,0)) = CAST(1 AS BOOLEAN))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast(1 as boolean) FROM t
-- !query schema
struct<(NOT (CAST(1 AS DECIMAL(20,0)) = CAST(1 AS BOOLEAN))):boolean>
-- !query output
false


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(3, 0))  <> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(5, 0))  <> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(10, 0)) <> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast('2017-12-11 09:30:00.0' as timestamp) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(2017-12-11 09:30:00.0 AS TIMESTAMP))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 78,
    "fragment" : "cast(1 as decimal(20, 0)) <> cast('2017-12-11 09:30:00.0' as timestamp)"
  } ]
}


-- !query
SELECT cast(1 as decimal(3, 0))  <> cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(3,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(3,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(3, 0))  <> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(5, 0))  <> cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(5,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(5,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(5, 0))  <> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(10, 0)) <> cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(10,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(10,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(10, 0)) <> cast('2017-12-11 09:30:00' as date)"
  } ]
}


-- !query
SELECT cast(1 as decimal(20, 0)) <> cast('2017-12-11 09:30:00' as date) FROM t
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DECIMAL(20,0)\"",
    "right" : "\"DATE\"",
    "sqlExpr" : "\"(CAST(1 AS DECIMAL(20,0)) = CAST(2017-12-11 09:30:00 AS DATE))\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 71,
    "fragment" : "cast(1 as decimal(20, 0)) <> cast('2017-12-11 09:30:00' as date)"
  } ]
}
