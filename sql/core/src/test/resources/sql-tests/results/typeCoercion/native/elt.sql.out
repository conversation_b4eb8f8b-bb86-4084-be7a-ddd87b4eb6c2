-- Automatically generated by SQLQueryTestSuite
-- !query
SELECT elt(2, col1, col2, col3, col4, col5) col
FROM (
  SELECT
    'prefix_' col1,
    id col2,
    string(id + 1) col3,
    encode(string(id + 2), 'utf-8') col4,
    CAST(id AS DOUBLE) col5
  FROM range(10)
)
-- !query schema
struct<col:string>
-- !query output
0
1
2
3
4
5
6
7
8
9


-- !query
SELECT elt(3, col1, col2, col3, col4) col
FROM (
  SELECT
    string(id) col1,
    string(id + 1) col2,
    encode(string(id + 2), 'utf-8') col3,
    encode(string(id + 3), 'utf-8') col4
  FROM range(10)
)
-- !query schema
struct<col:string>
-- !query output
10
11
2
3
4
5
6
7
8
9


-- !query
set spark.sql.function.eltOutputAsString=true
-- !query schema
struct<key:string,value:string>
-- !query output
spark.sql.function.eltOutputAsString	true


-- !query
SELECT elt(1, col1, col2) col
FROM (
  SELECT
    encode(string(id), 'utf-8') col1,
    encode(string(id + 1), 'utf-8') col2
  FROM range(10)
)
-- !query schema
struct<col:string>
-- !query output
0
1
2
3
4
5
6
7
8
9


-- !query
set spark.sql.function.eltOutputAsString=false
-- !query schema
struct<key:string,value:string>
-- !query output
spark.sql.function.eltOutputAsString	false


-- !query
SELECT elt(2, col1, col2) col
FROM (
  SELECT
    encode(string(id), 'utf-8') col1,
    encode(string(id + 1), 'utf-8') col2
  FROM range(10)
)
-- !query schema
struct<col:binary>
-- !query output
1
10
2
3
4
5
6
7
8
9
