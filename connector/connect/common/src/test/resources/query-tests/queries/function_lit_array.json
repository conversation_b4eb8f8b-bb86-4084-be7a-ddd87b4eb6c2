{"common": {"planId": "1"}, "project": {"input": {"common": {"planId": "0"}, "localRelation": {"schema": "struct<id:bigint,a:int,b:double>"}}, "expressions": [{"literal": {"array": {"elementType": {"double": {}}}}}, {"literal": {"array": {"elementType": {"array": {"elementType": {"integer": {}}, "containsNull": true}}, "element": [{"array": {"elementType": {"integer": {}}, "element": [{"integer": 1}]}}, {"array": {"elementType": {"integer": {}}, "element": [{"integer": 2}]}}, {"array": {"elementType": {"integer": {}}, "element": [{"integer": 3}]}}]}}}, {"literal": {"array": {"elementType": {"array": {"elementType": {"array": {"elementType": {"integer": {}}, "containsNull": true}}, "containsNull": true}}, "element": [{"array": {"elementType": {"array": {"elementType": {"integer": {}}, "containsNull": true}}, "element": [{"array": {"elementType": {"integer": {}}, "element": [{"integer": 1}]}}]}}, {"array": {"elementType": {"array": {"elementType": {"integer": {}}, "containsNull": true}}, "element": [{"array": {"elementType": {"integer": {}}, "element": [{"integer": 2}]}}]}}, {"array": {"elementType": {"array": {"elementType": {"integer": {}}, "containsNull": true}}, "element": [{"array": {"elementType": {"integer": {}}, "element": [{"integer": 3}]}}]}}]}}}, {"literal": {"array": {"elementType": {"boolean": {}}, "element": [{"boolean": true}, {"boolean": false}]}}}, {"literal": {"binary": "Q0RF"}}, {"literal": {"array": {"elementType": {"short": {}}, "element": [{"short": 9872}, {"short": 9873}, {"short": 9874}]}}}, {"literal": {"array": {"elementType": {"integer": {}}, "element": [{"integer": -8726532}, {"integer": 8726532}, {"integer": -8726533}]}}}, {"literal": {"array": {"elementType": {"long": {}}, "element": [{"long": "7834609328726531"}, {"long": "7834609328726532"}, {"long": "7834609328726533"}]}}}, {"literal": {"array": {"elementType": {"double": {}}, "element": [{"double": 2.718281828459045}, {"double": 1.0}, {"double": 2.0}]}}}, {"literal": {"array": {"elementType": {"float": {}}, "element": [{"float": -0.8}, {"float": -0.7}, {"float": -0.9}]}}}, {"literal": {"array": {"elementType": {"decimal": {"scale": 18, "precision": 38}}, "element": [{"decimal": {"value": "89.97620", "precision": 7, "scale": 5}}, {"decimal": {"value": "89.97621", "precision": 7, "scale": 5}}]}}}, {"literal": {"array": {"elementType": {"decimal": {"scale": 18, "precision": 38}}, "element": [{"decimal": {"value": "89889.7667231", "precision": 12, "scale": 7}}, {"decimal": {"value": "89889.7667231", "precision": 12, "scale": 7}}]}}}, {"literal": {"array": {"elementType": {"string": {}}, "element": [{"string": "connect!"}, {"string": "disconnect!"}]}}}, {"literal": {"string": "TF"}}, {"literal": {"array": {"elementType": {"string": {}}, "element": [{"string": "ABCDEFGHIJ"}, {"string": "BCDEFGHIJK"}]}}}, {"literal": {"array": {"elementType": {"date": {}}, "element": [{"date": 18545}, {"date": 18546}]}}}, {"literal": {"array": {"elementType": {"timestamp": {}}, "element": [{"timestamp": "1677155519808000"}, {"timestamp": "1677155519809000"}]}}}, {"literal": {"array": {"elementType": {"timestamp": {}}, "element": [{"timestamp": "12345000"}, {"timestamp": "23456000"}]}}}, {"literal": {"array": {"elementType": {"timestampNtz": {}}, "element": [{"timestampNtz": "1677184560000000"}, {"timestampNtz": "1677188160000000"}]}}}, {"literal": {"array": {"elementType": {"date": {}}, "element": [{"date": 19411}, {"date": 19417}]}}}, {"literal": {"array": {"elementType": {"dayTimeInterval": {"startField": 0, "endField": 3}}, "element": [{"dayTimeInterval": "100000000"}, {"dayTimeInterval": "200000000"}]}}}, {"literal": {"array": {"elementType": {"yearMonthInterval": {"startField": 0, "endField": 1}}, "element": [{"yearMonthInterval": 0}, {"yearMonthInterval": 0}]}}}, {"literal": {"array": {"elementType": {"calendarInterval": {}}, "element": [{"calendarInterval": {"months": 2, "days": 20, "microseconds": "100"}}, {"calendarInterval": {"months": 2, "days": 21, "microseconds": "200"}}]}}}]}}