{"common": {"planId": "1"}, "project": {"input": {"common": {"planId": "0"}, "localRelation": {"schema": "struct<id:bigint,a:int,b:double>"}}, "expressions": [{"unresolvedFunction": {"functionName": "struct", "arguments": [{"unresolvedAttribute": {"unparsedIdentifier": "id"}}, {"unresolvedAttribute": {"unparsedIdentifier": "a"}}]}}, {"unresolvedFunction": {"functionName": "struct", "arguments": [{"unresolvedAttribute": {"unparsedIdentifier": "id"}}, {"unresolvedAttribute": {"unparsedIdentifier": "a"}}]}}, {"unresolvedFunction": {"functionName": "struct", "arguments": [{"unresolvedAttribute": {"unparsedIdentifier": "id"}}, {"unresolvedAttribute": {"unparsedIdentifier": "a"}}]}}, {"unresolvedFunction": {"functionName": "struct", "arguments": [{"unresolvedAttribute": {"unparsedIdentifier": "id"}}, {"unresolvedAttribute": {"unparsedIdentifier": "a"}}]}}]}}