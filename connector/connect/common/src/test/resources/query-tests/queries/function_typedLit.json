{"common": {"planId": "1"}, "project": {"input": {"common": {"planId": "0"}, "localRelation": {"schema": "struct<id:bigint,a:int,b:double>"}}, "expressions": [{"unresolvedAttribute": {"unparsedIdentifier": "id"}}, {"unresolvedAttribute": {"unparsedIdentifier": "id"}}, {"literal": {"integer": 1}}, {"literal": {"null": {"null": {}}}}, {"literal": {"boolean": true}}, {"literal": {"byte": 68}}, {"literal": {"short": 9872}}, {"literal": {"integer": -8726532}}, {"literal": {"long": "7834609328726532"}}, {"literal": {"double": 2.718281828459045}}, {"literal": {"float": -0.8}}, {"literal": {"decimal": {"value": "89.97620", "precision": 7, "scale": 5}}}, {"literal": {"decimal": {"value": "89889.7667231", "precision": 12, "scale": 7}}}, {"literal": {"string": "connect!"}}, {"literal": {"string": "T"}}, {"literal": {"string": "ABCDEFGHIJ"}}, {"literal": {"binary": "eHl6e3x9fn+AgYKDhIWGh4iJiouMjY4="}}, {"literal": {"binary": "CAY="}}, {"literal": {"null": {"null": {}}}}, {"literal": {"date": 18545}}, {"literal": {"decimal": {"value": "8.997620", "precision": 7, "scale": 6}}}, {"literal": {"timestamp": "1677155519808000"}}, {"literal": {"timestamp": "12345000"}}, {"literal": {"timestampNtz": "1677184560000000"}}, {"literal": {"date": 19411}}, {"literal": {"dayTimeInterval": "200000000"}}, {"literal": {"yearMonthInterval": 0}}, {"literal": {"calendarInterval": {"months": 2, "days": 20, "microseconds": "100"}}}, {"literal": {"integer": 1}}, {"literal": {"array": {"elementType": {"integer": {}}, "elements": [{"integer": 1}, {"integer": 2}, {"integer": 3}]}}}, {"literal": {"array": {"elementType": {"integer": {}}, "elements": [{"integer": 1}, {"integer": 2}, {"integer": 3}]}}}, {"literal": {"map": {"keyType": {"string": {}}, "valueType": {"integer": {}}, "keys": [{"string": "a"}, {"string": "b"}], "values": [{"integer": 1}, {"integer": 2}]}}}, {"literal": {"struct": {"structType": {"struct": {"fields": [{"name": "_1", "dataType": {"string": {}}, "nullable": true}, {"name": "_2", "dataType": {"integer": {}}}, {"name": "_3", "dataType": {"double": {}}}]}}, "elements": [{"string": "a"}, {"integer": 2}, {"double": 1.0}]}}}, {"literal": {"null": {"integer": {}}}}, {"literal": {"array": {"elementType": {"integer": {}}, "elements": [{"integer": 1}]}}}, {"literal": {"map": {"keyType": {"integer": {}}, "valueType": {"integer": {}}, "keys": [{"integer": 1}], "values": [{"null": {"integer": {}}}]}}}, {"literal": {"map": {"keyType": {"integer": {}}, "valueType": {"integer": {}}, "keys": [{"integer": 1}], "values": [{"null": {"integer": {}}}]}}}, {"literal": {"map": {"keyType": {"integer": {}}, "valueType": {"integer": {}}, "keys": [{"integer": 1}], "values": [{"null": {"integer": {}}}]}}}, {"literal": {"array": {"elementType": {"array": {"elementType": {"integer": {}}}}, "elements": [{"array": {"elementType": {"integer": {}}, "elements": [{"integer": 1}, {"integer": 2}, {"integer": 3}]}}, {"array": {"elementType": {"integer": {}}, "elements": [{"integer": 4}, {"integer": 5}, {"integer": 6}]}}, {"array": {"elementType": {"integer": {}}, "elements": [{"integer": 7}, {"integer": 8}, {"integer": 9}]}}]}}}, {"literal": {"array": {"elementType": {"map": {"keyType": {"string": {}}, "valueType": {"integer": {}}}}, "elements": [{"map": {"keyType": {"string": {}}, "valueType": {"integer": {}}, "keys": [{"string": "a"}, {"string": "b"}], "values": [{"integer": 1}, {"integer": 2}]}}, {"map": {"keyType": {"string": {}}, "valueType": {"integer": {}}, "keys": [{"string": "a"}, {"string": "b"}], "values": [{"integer": 3}, {"integer": 4}]}}, {"map": {"keyType": {"string": {}}, "valueType": {"integer": {}}, "keys": [{"string": "a"}, {"string": "b"}], "values": [{"integer": 5}, {"integer": 6}]}}]}}}, {"literal": {"map": {"keyType": {"integer": {}}, "valueType": {"map": {"keyType": {"string": {}}, "valueType": {"integer": {}}}}, "keys": [{"integer": 1}, {"integer": 2}], "values": [{"map": {"keyType": {"string": {}}, "valueType": {"integer": {}}, "keys": [{"string": "a"}, {"string": "b"}], "values": [{"integer": 1}, {"integer": 2}]}}, {"map": {"keyType": {"string": {}}, "valueType": {"integer": {}}, "keys": [{"string": "a"}, {"string": "b"}], "values": [{"integer": 3}, {"integer": 4}]}}]}}}, {"literal": {"struct": {"structType": {"struct": {"fields": [{"name": "_1", "dataType": {"array": {"elementType": {"integer": {}}}}, "nullable": true}, {"name": "_2", "dataType": {"map": {"keyType": {"string": {}}, "valueType": {"integer": {}}}}, "nullable": true}, {"name": "_3", "dataType": {"struct": {"fields": [{"name": "_1", "dataType": {"string": {}}, "nullable": true}, {"name": "_2", "dataType": {"map": {"keyType": {"integer": {}}, "valueType": {"string": {}}, "valueContainsNull": true}}, "nullable": true}]}}, "nullable": true}]}}, "elements": [{"array": {"elementType": {"integer": {}}, "elements": [{"integer": 1}, {"integer": 2}, {"integer": 3}]}}, {"map": {"keyType": {"string": {}}, "valueType": {"integer": {}}, "keys": [{"string": "a"}, {"string": "b"}], "values": [{"integer": 1}, {"integer": 2}]}}, {"struct": {"structType": {"struct": {"fields": [{"name": "_1", "dataType": {"string": {}}, "nullable": true}, {"name": "_2", "dataType": {"map": {"keyType": {"integer": {}}, "valueType": {"string": {}}, "valueContainsNull": true}}, "nullable": true}]}}, "elements": [{"string": "a"}, {"map": {"keyType": {"integer": {}}, "valueType": {"string": {}}, "keys": [{"integer": 1}, {"integer": 2}], "values": [{"string": "a"}, {"string": "b"}]}}]}}]}}}]}}