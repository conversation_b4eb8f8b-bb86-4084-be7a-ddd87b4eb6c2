{"common": {"planId": "1"}, "project": {"input": {"common": {"planId": "0"}, "localRelation": {"schema": "struct<id:bigint,a:int,b:double,d:struct<id:bigint,a:int,b:double>,e:array<int>,f:map<string,struct<id:bigint,a:int,b:double>>,g:string>"}}, "expressions": [{"window": {"windowFunction": {"unresolvedFunction": {"functionName": "lead", "arguments": [{"unresolvedAttribute": {"unparsedIdentifier": "g"}}, {"literal": {"integer": 2}}, {"literal": {"string": "dv"}}, {"literal": {"boolean": true}}]}}, "partitionSpec": [{"unresolvedAttribute": {"unparsedIdentifier": "a"}}], "orderSpec": [{"child": {"unresolvedAttribute": {"unparsedIdentifier": "id"}}, "direction": "SORT_DIRECTION_ASCENDING", "nullOrdering": "SORT_NULLS_FIRST"}]}}]}}