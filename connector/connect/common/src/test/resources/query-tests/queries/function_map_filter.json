{"common": {"planId": "1"}, "project": {"input": {"common": {"planId": "0"}, "localRelation": {"schema": "struct<id:bigint,a:int,b:double,d:struct<id:bigint,a:int,b:double>,e:array<int>,f:map<string,struct<id:bigint,a:int,b:double>>,g:string>"}}, "expressions": [{"unresolvedFunction": {"functionName": "map_filter", "arguments": [{"unresolvedAttribute": {"unparsedIdentifier": "f"}}, {"lambdaFunction": {"function": {"unresolvedFunction": {"functionName": "contains", "arguments": [{"unresolvedNamedLambdaVariable": {"nameParts": ["x"]}}, {"literal": {"string": "baz"}}]}}, "arguments": [{"nameParts": ["x"]}, {"nameParts": ["y"]}]}}]}}]}}