{"class": "org.apache.spark.ml.classification.DecisionTreeClassificationModel", "timestamp": 1608687929358, "sparkVersion": "2.4.7", "uid": "dtc_bc7ad285bb73", "paramMap": {}, "defaultParamMap": {"impurity": "gini", "maxDepth": 5, "labelCol": "label", "maxMemoryInMB": 256, "featuresCol": "features", "predictionCol": "prediction", "minInfoGain": 0.0, "seed": 159147643, "rawPredictionCol": "rawPrediction", "minInstancesPerNode": 1, "cacheNodeIds": false, "probabilityCol": "probability", "maxBins": 32, "checkpointInterval": 10}, "numFeatures": 692, "numClasses": 2}