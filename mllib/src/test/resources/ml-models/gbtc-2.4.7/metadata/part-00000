{"class": "org.apache.spark.ml.classification.GBTClassificationModel", "timestamp": 1608687932103, "sparkVersion": "2.4.7", "uid": "gbtc_81db008b4f25", "paramMap": {"maxIter": 2}, "defaultParamMap": {"seed": -1287390502, "maxMemoryInMB": 256, "stepSize": 0.1, "validationTol": 0.01, "maxBins": 32, "checkpointInterval": 10, "predictionCol": "prediction", "lossType": "logistic", "rawPredictionCol": "rawPrediction", "featuresCol": "features", "cacheNodeIds": false, "maxIter": 20, "featureSubsetStrategy": "all", "impurity": "gini", "minInstancesPerNode": 1, "minInfoGain": 0.0, "maxDepth": 5, "subsamplingRate": 1.0, "labelCol": "label", "probabilityCol": "probability"}, "numFeatures": 692, "numTrees": 2}