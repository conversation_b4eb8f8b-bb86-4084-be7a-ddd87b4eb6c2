


body {
  color: #666666;
  font-family: 'DM Sans', sans-serif;
  font-style: normal;
  font-weight: 400;
  overflow-wrap: anywhere;
  overflow-x: hidden;
  padding-top: 80px;
}

a {
  color: #2fa4e7;
  text-decoration: none;
}

a:hover, a:focus {
  color: #157ab5;
  text-decoration: underline;
}

.navbar {
  border-radius: 0;
  z-index: 9999;
}

.navbar {
  transition: none !important;
}

.navbar .nav-item:hover .dropdown-menu, .navbar .nav-item .dropdown-menu, .navbar .dropdown-menu.fade-down, .navbar-toggler, .collapse, .collapsing {
  transition: none !important;
  transform: none !important;
}

@media all and (min-width: 992px) {
  .navbar .nav-item .dropdown-menu {
      display: block;
      opacity: 0;
      visibility: hidden;
      transition: none !important;
      margin-top: 0;
  }

  .navbar .dropdown-menu.fade-down {
      top: 80%;
      transform: none !important;
      transform-origin: 0% 0%;
  }

  .navbar .dropdown-menu.fade-up {
      top: 180%;
  }

  .navbar .nav-item:hover .dropdown-menu {
      opacity: 1;
      visibility: visible;
      top: 100%;
      font-size: calc(0.51rem + 0.55vw);
  }
}

.navbar-nav {
  font-style: normal;
  font-weight: 500;
  font-size: calc(0.51rem + 0.55vw);
  line-height: 20px;
}

.navbar-nav a {
  text-decoration: none !important;
}

.navbar-dark .navbar-nav .nav-link.active, .navbar-dark .navbar-nav .show > .nav-link, .navbar-dark .navbar-nav .nav-link {
  color: #ffffff;
  border: 1px solid transparent;
}

.navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-link:active, .navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-link:hover {
  color: #ffffff;
  background-color: #1b618e;
  border: 1px solid transparent;
  text-decoration: none;
}

section {
  position: relative;
}

.navbar-dark .navbar-toggler {
  color: #fff;
  border: none;
  border-color: transparent;
}

.hero-banner .bg {
  background: url(/img/spark-hero-thin-light.jpg) no-repeat;
  transform: translate(36%, 0%);
  height: 475px;
  top: 0;
  position: absolute;
  right: 0;
  width: 100%;
  opacity: 50%;
}

.hero-banner h1 {
  color: #0B9ACE;
  font-style: normal;
  font-weight: normal;
  font-size: 48px;
  line-height: 63px;
  letter-spacing: -0.045em;
}

.hero-banner h2 {
  font-style: normal;
  font-weight: bold;
  font-size: 32px;
  line-height: 42px;
}

.what-is-spark {
  font-style: normal;
  font-weight: normal;
  font-size: 17px;
  line-height: 24px;
}

.btn-cta {
  background: #F55B14;
  border-radius: 4px;
  font-style: normal;
  font-weight: bold;
  font-size: 16px;
  line-height: 23px;
  text-align: center;
  letter-spacing: 1px;
  text-transform: uppercase;
  color: #FFFFFF;
}

.btn-cta:hover {
  background: #CA4000;
  color: #ffffff;
  text-decoration: none;
}

.spark-star-bg {
  background: url(../images/spark-start.svg) no-repeat;
  width: 907.5px;
  height: 726px;
  position: absolute;
  right: 70px;
}

.apache-spark-motto {
  margin-top: 150px;
  position: relative;
  font-style: normal;
  font-weight: bold;
  font-size: calc(3.2rem + 3.3vw);
  line-height: 113px;
  letter-spacing: -0.05em;
  color: #8A8A8A;
}

@media screen and (min-width: 1900px) {
.apache-spark-motto {
  font-size: 7.3rem;
}
}

.features .title {
  margin-top: 24px;
  font-style: normal;
  font-weight: bold;
  font-size: 21px;
  line-height: 33px;
  color: #0B9ACE;
}

.features .details {
  color: #000000;
  margin-top: 20px;
  font-style: normal;
  font-weight: normal;
  font-size: 17px;
  line-height: 24px;
  margin-right: 20px;
}

.spark-run-now {
  background-color: #1D6890;
}

.spark-run-now nav {
  background-color: #1B4257;
  color: #ffffff;
}

.spark-run-now .nav-link:focus, .nav-link:hover {
  color: #ffffff;
  border: 1px solid transparent;
}

.spark-run-now .nav-tabs .nav-item.show .nav-link, .spark-run-now .nav-tabs .nav-link.active {
  color: #ffffff;
  background-color: #1d6890;
  border: none;
}

.spark-run-now .nav-tabs .nav-link:focus, .spark-run-now .nav-tabs .nav-link:hover {
  outline: none;
  background-color: #1D6890;
  border: 1px solid transparent;
}

.spark-run-now .nav-link {
  color: #ffffff;
  border-radius: 0;
  padding: 10px;
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 33px;
  text-align: center;
}

.spark-run-now .tab-content {
  color: #ffffff;
}

.spark-run-now .title {
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 42px;
}

.spark-code {
  border-top: 2px solid #000000;
}

.spark-install .code {
  color: #000000;
  background-color: #ffffff;
  padding: 15px;
  font-family: "Menlo", "Lucida Console", Consolas, monospace;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 1.428571429;
}

.spark-install .code p {
  margin: 0;
}

.spark-install .code .orange {
  color: #df584e;
}

.spark-install .code .green {
  color: #579f52;
}

.spark-install .code .blue {
  color: #2088bb;
}

.spark-install .code .purple {
  color: #a73ea7;
}

.spark-install .code .brown {
  color: #976716;
}

.examples {
  color: #666666;
  padding: 12px 0 0 0;
  background-color: #ffffff;
  border-radius: 10px;
}

.examples .nav-tabs {
  background-color: #F0F0F0;
}

.spark-run-now .examples .tab-content {
  color: #666666;
}

.spark-run-now .examples nav {
  border: 1px solid #000000;
}

.spark-run-now .examples .nav-link {
  padding: 6px 5px;
  border-right: 1px solid #000000;
  font-style: normal;
  font-weight: normal;
  font-size: 15px;
  line-height: 19px;
}

.spark-run-now .examples .nav-link {
  color: #666666;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}

.spark-run-now .examples .nav-link:hover {
  color: #ffffff;
}

.window {
  height: 30px;
  padding: 0 10px;
}

.circle {
  border-radius: 50%;
  height: 17px;
  width: 17px;
  display: inline-block;
  margin: 2px;
}

.circle.red {
  background-color: #fb615a;
}

.circle.yellow {
  background-color: #fdbc40;
}

.circle.green {
  background-color: #3ec648;
}

.nav-tabs .nav-link {
  margin-bottom: 0;
}

.spark-run-now .examples .nav-tabs .nav-link:focus, .spark-run-now .examples .nav-tabs .nav-link:hover, .spark-run-now .examples .nav-tabs .nav-link:active {
  border-right: 1px solid;
}

.spark-run-now .examples .nav-tabs .nav-item.show .nav-link, .spark-run-now .examples .nav-tabs .nav-link.active {
  border-right: 1px solid #666666;
}

.btn {
  padding: 11px 23px 11px 23px;
}

.card {
  box-shadow: 0px 4px 30px rgb(27 49 57 / 10%);
  border: 2px solid transparent;
  font-size: 19px;
  line-height: 25px;
}

.card:hover {
  border-bottom: 2px solid #F55B14;
  cursor: pointer;
  box-shadow: 0 14px 40px rgb(27 49 57 / 15%);
}

.card a {
  text-decoration: none;
  color: #666666;
}

.card a:hover {
  text-decoration: none;
  color: #666666;
}

.card-body img {
  margin-right: 20px;
}

.tm {
  font-size: 12px;
  bottom: 5px;
  position: relative;
}

.ecosystem-title {
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 33px;
  text-align: center;
  color: #F55B14;
}

.scalable-data-science a {
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 33px;
  color: #0B9ACE;
  text-decoration: none;
}

.scalable-data-science p {
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 24px;
}

@media (max-width: 768px) {
  .hero-banner {
      background-size: 390px;
      background-position: center right;
      background-position-y: 40px;
  }

  .hero-banner .bg {
      transform: translate(46%, -5%);
  }

  .hero-banner h1 {
      margin-bottom: 30px;
  }

  .apache-spark-motto {
      font-size: 90px;
      margin: 120px 0;
  }

  .features {
      margin-top: 53px;
  }

  .features:first-child {
      margin-top: 0;
  }

  .border-end {
      border-right: 1px solid transparent !important;
      border-bottom: 1px solid #dee2e6 !important;
  }

  .hero-banner .bg {
      display: none;
  }
}

@media (max-width: 320px) {
  .hero-banner {
      background-size: 230px;
      background-position: center right;
      background-position-y: 160px;
      background: none;
  }

  .navbar-dark .navbar-nav .nav-link.active, .navbar-dark .navbar-nav .show > .nav-link, .navbar-dark .navbar-nav .nav-link {
      padding-left: 1rem;
  }

  .hero-banner h1 {
      line-height: 65px;
  }

  .apache-spark-motto {
      font-size: 70px;
      margin: 0 0 50px 0;
      line-height: 80px;
  }
}

a {
  background: transparent;
}

a:active, a:hover {
  outline: 0;
}

@media print {
  * {
      color: #000 !important;
      text-shadow: none !important;
      background: transparent !important;
      box-shadow: none !important;
  }

  a, a:visited {
      text-decoration: underline;
  }

  a[href]:after {
      content: " (" attr(href) ")";
  }

  p {
      orphans: 3;
      widows: 3;
  }

  pre {
      border: 1px solid #999;
      page-break-inside: avoid;
  }

  .global pre {
      border: 1px solid #999;
      page-break-inside: avoid;
  }

  h3 {
      orphans: 3;
      widows: 3;
  }

  h3 {
      page-break-after: avoid;
  }
}

*, *:before, *:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

a {
  color: #2fa4e7;
  text-decoration: none;
}

a:hover, a:focus {
  color: #157ab5;
  text-decoration: underline;
}

a:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

h5 {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: 500;
  line-height: 1.1;
  color: #317eac;
}

h5 {
  margin-top: 10px;
  margin-bottom: 10px;
}

h5 {
  font-size: 14px;
}

p {
  margin: 0 0 10px;
}

.small {
  font-size: 85%;
}

ul {
  margin-top: 0;
  margin-bottom: 10px;
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.news {
  background: #FFF6ED;
  border-radius: 6px;
  padding: 4px 12px 1px 12px;
}

.news h5 {
  color: rgb(85, 85, 85);
}

.news ul li {
  margin-bottom: 6px;
}

.news li a, .news li a:hover, .news li a:visited {
  color: rgb(85, 85, 85);
}

.news li .small {
  color: #888;
  font-size: 12px;
}

.news h5 {
  font-size: 16px;
}

code, pre {
  font-family: monospace, serif;
  font-size: 1em;
}

pre {
  white-space: pre-wrap;
}

code, pre {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}

code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  white-space: nowrap;
  background-color: #f9f2f4;
  border-radius: 4px;
}

pre {
  display: block;
  padding: 20px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.428571429;
  color: #333;
  word-break: break-all;
  word-wrap: break-word;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px;
}

pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}

code {
  font-family: "Menlo", "Lucida Console", Consolas, monospace;
  background: transparent;
  padding: 0;
  color: inherit;
}

.code .sparkop {
  color: #1663a8;
}

.code .closure {
  color: #c1130e;
}

h3 {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: 500;
  line-height: 1.1;
  color: #317eac;
}

h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 20px;
}
ul {
  margin-top: 0;
  margin-bottom: 10px;
}

.list-none {
  list-style: none;
  padding: 0;
}

.jumbotron {
  padding: 30px;
  margin-bottom: 30px;
  font-size: 21px;
  font-weight: 200;
  line-height: 2.1428571435;
  color: inherit;
  background-color: #eee;
}

.container .jumbotron {
  border-radius: 6px;
}

@media screen and (min-width: 768px) {
  .jumbotron {
      padding-top: 48px;
      padding-bottom: 48px;
  }

  .container .jumbotron {
      padding-right: 60px;
      padding-left: 60px;
  }
}

@media screen and (max-width: 990px) {
  .navbar-nav {
      font-size: 16px;
  }

  .features .details {
      margin-right: 0 !important;
  }
}

@media screen and (min-width: 1441px) {
  .navbar-nav, .navbar .nav-item:hover .dropdown-menu {
      font-size: 16px;
  }
}

.jumbotron {
  padding: 20px 20px;
  margin-bottom: 20px;
  color: rgb(85, 85, 85);
  background: #eef6fd;
  text-align: center;
  line-height: inherit;
}

.jumbotron {
  font-size: 18px;
}

@media (min-width: 768px) {
  .jumbotron {
      font-size: 19px;
  }
}

@media (min-width: 992px) {
  .jumbotron {
      font-size: 21px;
  }
}

.col-padded-top {
  margin-top: 30px;
  margin-bottom: 18px;
}

.col-center {
  text-align: center;
}

.nav > li > a {
  position: relative;
  display: block;
  padding: 10px 15px;
}

.nav > li > a:hover, .nav > li > a:focus {
  text-decoration: none;
  background-color: #eee;
}

.nav-tabs > li > a {
  margin-right: 2px;
  line-height: 1.428571429;
  border: 1px solid transparent;
  border-radius: 4px 4px 0 0;
}

.nav-tabs > li > a:hover {
  border-color: #eee #eee #ddd;
}

.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
  color: #555;
  cursor: default;
  background-color: #fff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
}

.nav.nav-tabs > li > a {
  padding: 6px 11px;
  font-size: 14px;
  border-radius: 0;
}

.global .row-padded {
  margin-top: 30px;
}

.global .col-padded {
  margin-top: 30px;
  margin-bottom: 30px;
}

.global h1, .global h2, .global h3, .global h4, .global h5, .global h6, .global .h1, .global .h2, .global .h3, .global .h4, .global .h5, .global .h6 {
  color: #317eac;
  /*display: inline-block;*/
  /*text-transform: lowercase;*/
}

.global h1, .global h2, .global h3 {
  margin-top: 20px;
  margin-bottom: 10px;
}

.global h2, .global .h2 {
  font-size: 30px;
}

.global h3 {
  font-size: 24px !important;
}

.global h4 {
  font-size: 18px !important;
}


.global h1:first-letter, .global h2:first-letter, .global h3:first-letter, .global h4:first-letter, .global h5:first-letter, .global h6:first-letter, .global .h1:first-letter, .global .h2:first-letter, .global .h3:first-letter, .global .h4:first-letter, .global .h5:first-letter, .global .h6:first-letter {
  text-transform: uppercase;
}

.global .caption {
  width: 100%;
  margin-top: 20px;
  text-align: center;
  color: #8f8f8f;
}

.global .code {
  font-family: "Menlo", "Lucida Console", Consolas, monospace;
  font-size: 12px;
}

@media (min-width: 1200px) {
  .global .code {
      font-size: 13px;
  }
}

.global .code .string {
  color: #2b8eeb;
}

.global .code .sparkop {
  color: #1663a8;
}

/*! CSS Used from: http://spark.apache.org/css/cerulean.min.css */
.global figure {
  display: block;
}

.global code, .global pre {
  font-family: monospace, serif;
  font-size: 1em;
}

.global pre {
  white-space: pre-wrap;
}

.global figure {
  margin: 0;
}

.global code, .global pre {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}

.global code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  white-space: nowrap;
  background-color: #f9f2f4;
  border-radius: 4px;
}

.global pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.428571429;
  color: #333;
  word-break: break-all;
  word-wrap: break-word;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.global pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}

.global .code {
  font-family: "Menlo", "Lucida Console", Consolas, monospace;
  font-size: 12px;
}

@media (min-width: 1200px) {
  .global .code {
      font-size: 13px;
  }
}

.global code {
  font-family: "Menlo", "Lucida Console", Consolas, monospace;
  background: transparent;
  padding: 0;
  color: inherit;
}

.global .code-tab {
  margin-bottom: 10px;
  border-bottom: 1px solid #ddd;
}

footer {
  text-align: center;
  color: #8f8f8f;
  padding-bottom: 18px;
  font-size: 80%;
}

footer a {
  display: contents;
}

h1 .tm, h2 .tm, h3 .tm {
  bottom: 10px;
}



.question {
  font-size: 16px;
  margin-top: 20px;
  color: #555;
  font-weight: 500;
}

/* GitHub style blockquote */
blockquote {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 40px;
  margin-inline-end: 40px;
  padding: 0 1em;
  color: rgba(0, 0, 0, .5);
  border-left: 0.25em solid rgba(0, 0, 0, .1)
}


.content {
  z-index: 1;
  position: relative;
  background-color: #FFF;
  max-width: 914px;
  line-height: 1.6; /* Inspired by GitHub's wiki style */
  /*padding-left: 15px;*/
}

.content-with-sidebar {
  z-index: 1;
  position: relative;
  background-color: #FFF;
  max-width: 914px;
  line-height: 1.6; /* Inspired by GitHub's wiki style */
  padding-left: 30px;
  min-height: 100vh;
}

/**
 * The left navigation bar.
 */
 .left-menu-wrapper {
  margin-left: 0px;
  margin-right: 0px;
  border-top-width: 0px;
  border-left-width: 0px;
  border-bottom-width: 0px;
  margin-top: 0px;
  width: 220px;
  height: 80%;
  float: left;
  position: fixed;
  overflow-y: scroll;
  padding-right: 20px;
  font-size: 0.9em !important;
}

.left-menu h3 {
  margin-left: 10px;
  line-height: 30px;
}

/**
 * The collapsing button for the navigation bar.
 */
.nav-trigger {
  position: fixed;
  clip: rect(0, 0, 0, 0);
}

.nav-trigger + label:after {
  content: '»';
}

label {
  z-index: 10;
}

label[for="nav-trigger"] {
  position: fixed;
  margin-left: 0px;
  padding-top: 100px;
  padding-left: 5px;
  width: 10px;
  height: 80%;
  cursor: pointer;
  background-size: contain;
  background-color: #e2e2e2;
  box-sizing: content-box;
}

label[for="nav-trigger"]:hover {
  background-color: #d0cdcd;
}

.nav-trigger:checked + label {
  margin-left: 200px;
}

.nav-trigger:checked + label:after {
  content: '«';
}

.nav-trigger:checked ~ div.content-with-sidebar {
  margin-left: 200px;
}

.nav-trigger + label, div.content-with-sidebar {
  transition: left 0.4s;
}

/**
 * Rules to collapse the menu automatically when the screen becomes too thin.
 */

@media all and (max-width: 780px) {

  div.content-with-sidebar {
    margin-left: 200px;
  }
  .nav-trigger + label:after {
    content: '«';
  }
  label[for="nav-trigger"] {
    margin-left: 200px;
  }

  .nav-trigger:checked + label {
    margin-left: 0px;
  }
  .nav-trigger:checked + label:after {
    content: '»';
  }
  .nav-trigger:checked ~ div.content-with-sidebar {
    margin-left: 0px;
  }

  div.container-index {
    margin-left: -215px;
  }

}

img {
  max-width: 100%;
}

table {
  width: 100%;
  overflow-wrap: normal;
}

