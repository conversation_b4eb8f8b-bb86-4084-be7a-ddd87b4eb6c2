---
# defaults file for jenkins-worker
anaconda_installer: https://repo.continuum.io/archive/{{ anaconda_version }}
anaconda_version: Anaconda2-2019.10-Linux-x86_64.sh
anaconda_home: /home/<USER>/anaconda2
anaconda_py3_pip_requirements: base-py3-pip.txt

spark_py36_environment: spark-py36-spec.txt
spark_py3k_environment: spark-py3k-spec.txt
spark_py2_pip_requirements: spark-py2-pip.txt

jenkins_home: /home/<USER>

minikube_version: 1.18.1
minikube_checksum: sha256:1a7960b845301107cb6a0c29001c8df310d7bce586cf88ceacfc78f22b622ba5
minikube_mirror: https://github.com/kubernetes/minikube/releases/download
minikube_target: "minikube_{{ minikube_version }}-0_amd64.deb"

k8s_version: 1.17.3

kubectl_version: v1.17.3
kubectl_mirror: https://dl.k8s.io/release
kubectl_target: 'kubectl'
kubectl_install_dir: /usr/local/bin

pypy_pip_module: get-pip.py
pypy_pip_mirror: https://bootstrap.pypa.io

r_cran_repo: "deb https://cloud.r-project.org/bin/linux/ubuntu bionic-cran35/"
r_cran_repo_key: E298A3A825C0D65DFD57CBB651716619E084DAB9
